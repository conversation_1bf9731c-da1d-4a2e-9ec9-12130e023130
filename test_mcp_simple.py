#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test semplice per verificare che la connessione MCP funzioni.
"""

import requests
import json

def test_mcp_chat():
    """Testa la funzionalità di chat MCP."""
    print("🧪 TEST SEMPLICE CONNESSIONE MCP")
    print("=" * 40)

    try:
        # Test 1: Verifica server MCP
        print("1️⃣ Verifica server MCP...")
        mcp_response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        if mcp_response.status_code == 200:
            print("   ✅ Server MCP operativo")
        else:
            print(f"   ❌ Server MCP non risponde: {mcp_response.status_code}")
            return False

        # Test 2: Verifica app principale
        print("2️⃣ Verifica app principale...")
        app_response = requests.get("http://127.0.0.1:5000", timeout=5)
        if app_response.status_code == 200:
            print("   ✅ App principale operativa")
        else:
            print(f"   ❌ App principale non risponde: {app_response.status_code}")
            return False

        # Test 3: Test chat MCP
        print("3️⃣ Test chat MCP...")
        chat_data = {
            "message": "Ciao, questo è un test di connessione MCP"
        }

        chat_response = requests.post(
            "http://127.0.0.1:5000/api/chat/send",
            json=chat_data,
            timeout=15
        )

        if chat_response.status_code == 200:
            result = chat_response.json()
            # Verifica se c'è una risposta valida (non un errore)
            if result.get("response") and not result.get("error"):
                print("   ✅ Chat MCP funziona!")
                print(f"   📥 Risposta: {result.get('response', 'N/A')[:100]}...")
                print(f"   🔗 Fonte: {result.get('source', 'N/A')}")
                return True
            else:
                print(f"   ❌ Errore nella chat: {result.get('error', 'Risposta non valida')}")
                return False
        else:
            print(f"   ❌ Errore HTTP: {chat_response.status_code}")
            print(f"   📄 Risposta: {chat_response.text[:200]}...")
            return False

    except Exception as e:
        print(f"❌ Errore nel test: {str(e)}")
        return False

def main():
    """Esegue il test."""
    success = test_mcp_chat()

    print("\n" + "=" * 40)
    if success:
        print("🎉 TEST SUPERATO!")
        print("✅ Problema di timeout MCP RISOLTO!")
        print("✅ Connessione MCP stabile e funzionante")
    else:
        print("❌ TEST FALLITO")
        print("🔧 Verificare i problemi rilevati sopra")

    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
