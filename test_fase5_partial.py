#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test parziale della Fase 5: Ottimizzazione Performance e Caching (Task 5.1 e 5.2)
"""

import time
import sys
from datetime import datetime

# Aggiungi il percorso corrente per gli import
sys.path.append('.')

def test_fase5_partial():
    """Test dei primi due task della Fase 5."""
    print('🚀 TEST PARZIALE FASE 5: OTTIMIZZAZIONE PERFORMANCE E CACHING')
    print('=' * 65)
    
    # Test 1: IntelligentCacheSystem
    print('\n🧠 TEST 1: IntelligentCacheSystem')
    print('-' * 40)
    
    try:
        from intelligent_cache_system import (
            IntelligentCacheSystem, 
            CacheStrategy, 
            smart_cache,
            intelligent_cache
        )
        
        # Test inizializzazione
        cache_system = IntelligentCacheSystem()
        print(f'✅ IntelligentCacheSystem inizializzato')
        print(f'   Strategia: {cache_system.cache_strategy.value}')
        print(f'   Dimensione cache memoria: {cache_system.MEMORY_CACHE_SIZE}')
        print(f'   TTL default: {cache_system.DEFAULT_TTL_SECONDS}s')
        print(f'   Redis disponibile: {cache_system.redis_cache is not None}')
        
        # Test operazioni cache
        test_data = {
            "user_1": {"name": "Mario Rossi", "role": "admin"},
            "user_2": {"name": "Luigi Verdi", "role": "user"},
            "config_1": {"theme": "dark", "language": "it"}
        }
        
        # Test set/get
        for key, value in test_data.items():
            cache_system.set(key, value, ttl=300, tags=["test"], dependencies=[])
        
        # Test recupero
        cache_hits = 0
        for key in test_data.keys():
            cached_value = cache_system.get(key)
            if cached_value is not None:
                cache_hits += 1
        
        print(f'   📊 Cache hits: {cache_hits}/{len(test_data)}')
        
        # Test invalidazione per tag
        invalidated = cache_system.invalidate_by_tags(["test"])
        print(f'   🗑️ Invalidate per tag: {invalidated} entry')
        
        # Test statistiche
        stats = cache_system.get_cache_statistics()
        print(f'   📈 Statistiche cache:')
        print(f'      Hit rate: {stats["performance"]["hit_rate_percentage"]:.1f}%')
        print(f'      Entry in memoria: {stats["cache_levels"]["memory_entries"]}')
        
        # Test decoratore smart_cache
        @smart_cache(ttl=60, tags=["function_cache"])
        def expensive_function(x, y):
            time.sleep(0.1)  # Simula operazione costosa
            return x * y + 42
        
        # Prima chiamata (cache miss)
        start_time = time.time()
        result1 = expensive_function(10, 20)
        time1 = (time.time() - start_time) * 1000
        
        # Seconda chiamata (cache hit)
        start_time = time.time()
        result2 = expensive_function(10, 20)
        time2 = (time.time() - start_time) * 1000
        
        print(f'   ⚡ Test decoratore:')
        print(f'      Prima chiamata: {time1:.1f}ms (cache miss)')
        print(f'      Seconda chiamata: {time2:.1f}ms (cache hit)')
        print(f'      Speedup: {time1/time2:.1f}x')
        
        cache_test_result = {
            "status": "success",
            "component": "IntelligentCacheSystem",
            "cache_hits": cache_hits,
            "speedup": time1/time2 if time2 > 0 else 1.0
        }
        
    except Exception as e:
        print(f'❌ Errore IntelligentCacheSystem: {e}')
        cache_test_result = {"status": "error", "error": str(e)}
    
    # Test 2: QueryOptimizer
    print('\n⚡ TEST 2: QueryOptimizer')
    print('-' * 40)
    
    try:
        from query_optimizer import QueryOptimizer, QueryType
        
        # Test inizializzazione
        optimizer = QueryOptimizer()
        print(f'✅ QueryOptimizer inizializzato')
        print(f'   Soglia query lente: {optimizer.SLOW_QUERY_THRESHOLD_MS}ms')
        print(f'   Soglia cache: {optimizer.CACHE_THRESHOLD_EXECUTIONS} esecuzioni')
        print(f'   Dimensione batch: {optimizer.BATCH_SIZE_THRESHOLD}')
        
        # Simula metriche di query
        test_queries = [
            {"table": "users", "type": QueryType.SELECT, "time": 150.0, "rows": 10},
            {"table": "users", "type": QueryType.SELECT, "time": 1200.0, "rows": 100},  # Lenta
            {"table": "orders", "type": QueryType.INSERT, "time": 80.0, "rows": 1},
            {"table": "users", "type": QueryType.SELECT, "time": 200.0, "rows": 15},
            {"table": "products", "type": QueryType.SELECT, "time": 2500.0, "rows": 500},  # Molto lenta
        ]
        
        # Registra metriche simulate
        for i, query in enumerate(test_queries):
            optimizer._record_metrics(
                query_hash=f"query_{i}",
                query_type=query["type"],
                table_name=query["table"],
                execution_time=query["time"],
                rows_affected=query["rows"],
                cache_hit=False
            )
        
        # Test report performance
        report = optimizer.get_performance_report()
        
        print(f'   📊 Report Performance:')
        print(f'      Query totali: {report["summary"]["total_queries"]}')
        print(f'      Tempo medio: {report["summary"]["avg_execution_time_ms"]:.1f}ms')
        print(f'      Query lente: {report["summary"]["slow_queries_count"]}')
        print(f'      % Query lente: {report["summary"]["slow_queries_percentage"]:.1f}%')
        
        # Test suggerimenti ottimizzazione
        suggestions = optimizer.suggest_optimizations()
        print(f'   💡 Suggerimenti: {len(suggestions)}')
        for suggestion in suggestions[:3]:  # Mostra primi 3
            print(f'      - {suggestion["type"]}: {suggestion["message"][:60]}...')
        
        # Test operazioni batch
        batch_operations = [
            {"type": "insert", "data": {"name": f"User {i}", "email": f"user{i}@test.com"}}
            for i in range(15)  # Supera soglia batch
        ]
        
        batch_result = optimizer.optimize_batch_operations(batch_operations, "users")
        print(f'   🔄 Batch operations: {len(batch_operations)} operazioni')
        print(f'      Risultato: {list(batch_result.keys())}')
        
        optimizer_test_result = {
            "status": "success",
            "component": "QueryOptimizer",
            "total_queries": report["summary"]["total_queries"],
            "slow_queries": report["summary"]["slow_queries_count"],
            "suggestions": len(suggestions)
        }
        
    except Exception as e:
        print(f'❌ Errore QueryOptimizer: {e}')
        optimizer_test_result = {"status": "error", "error": str(e)}
    
    # Test Integrazione
    print('\n🔗 TEST 3: Integrazione Cache + Query Optimizer')
    print('-' * 40)
    
    integration_score = 0
    total_tests = 2
    
    test_results = [cache_test_result, optimizer_test_result]
    
    for result in test_results:
        if result["status"] == "success":
            integration_score += 1
            print(f'   ✅ {result["component"]}: OK')
        else:
            print(f'   ❌ {result["component"]}: ERRORE')
    
    integration_percentage = (integration_score / total_tests) * 100
    
    # Test integrazione cache + optimizer
    try:
        # Simula query con cache
        from intelligent_cache_system import intelligent_cache
        
        # Cache risultato query
        query_result = {"data": [{"id": 1, "name": "Test"}], "count": 1}
        intelligent_cache.set("query:test_hash", query_result, ttl=300, tags=["query_cache"])
        
        # Verifica cache hit
        cached_result = intelligent_cache.get("query:test_hash")
        if cached_result is not None:
            print(f'   🎯 Integrazione cache-query: ✅ Funzionante')
            integration_bonus = 10
        else:
            print(f'   🎯 Integrazione cache-query: ⚠️ Parziale')
            integration_bonus = 5
            
    except Exception as e:
        print(f'   🎯 Integrazione cache-query: ❌ Errore ({e})')
        integration_bonus = 0
    
    # Statistiche finali
    print(f'\n📈 STATISTICHE FINALI FASE 5 (PARZIALE)')
    print('=' * 45)
    
    print(f'📊 Task completati: 2/4')
    print(f'✅ Componenti funzionanti: {integration_score}/{total_tests}')
    print(f'📈 Percentuale successo: {integration_percentage:.1f}%')
    print(f'🎯 Bonus integrazione: +{integration_bonus}%')
    
    final_score = min(100, integration_percentage + integration_bonus)
    
    # Valutazione complessiva
    print(f'\n🏆 VALUTAZIONE PARZIALE FASE 5')
    print('-' * 35)
    
    if final_score >= 90:
        status = "🟢 ECCELLENTE"
        description = "Cache intelligente e query optimizer funzionano perfettamente"
    elif final_score >= 75:
        status = "🟡 BUONO"
        description = "Ottimizzazioni performance implementate correttamente"
    elif final_score >= 50:
        status = "🟠 SUFFICIENTE"
        description = "Funzionalità base implementate, necessari miglioramenti"
    else:
        status = "🔴 INSUFFICIENTE"
        description = "Problemi significativi nell'implementazione"
    
    print(f'Punteggio finale: {final_score:.1f}%')
    print(f'Stato: {status}')
    print(f'Descrizione: {description}')
    
    # Dettagli componenti
    print(f'\n📋 DETTAGLI COMPONENTI:')
    for result in test_results:
        component = result["component"]
        status = "✅ OK" if result["status"] == "success" else "❌ ERRORE"
        print(f'   {component}: {status}')
        
        if result["status"] == "error":
            print(f'      Errore: {result["error"]}')
        elif component == "IntelligentCacheSystem":
            print(f'      Cache hits: {result.get("cache_hits", 0)}')
            print(f'      Speedup: {result.get("speedup", 1.0):.1f}x')
        elif component == "QueryOptimizer":
            print(f'      Query processate: {result.get("total_queries", 0)}')
            print(f'      Suggerimenti: {result.get("suggestions", 0)}')
    
    print(f'\n📝 PROSSIMI TASK:')
    print(f'   🔄 Task 5.3: Performance Profiler e Analyzer')
    print(f'   🎯 Task 5.4: Auto-tuning e Ottimizzazione Dinamica')
    
    return {
        "integration_score": integration_score,
        "total_tests": total_tests,
        "success_percentage": integration_percentage,
        "final_score": final_score,
        "test_results": test_results
    }

if __name__ == "__main__":
    try:
        start_time = time.time()
        results = test_fase5_partial()
        end_time = time.time()
        
        print(f'\n⏱️ Tempo totale test: {end_time - start_time:.2f} secondi')
        print(f'🎉 Test Fase 5 (parziale) completato!')
        
    except Exception as e:
        print(f"❌ Errore generale nel test: {e}")
        import traceback
        traceback.print_exc()
