/**
 * Reports CSS - App Roberto
 * <PERSON>ili per i report e analisi
 * Versione: 1.0.0
 */

/* ===== STILI GENERALI REPORT ===== */
.report-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 40px;
    line-height: 1.6;
}

.report-header {
    border-bottom: 3px solid #007bff;
    padding-bottom: 20px;
    margin-bottom: 30px;
}

.report-title {
    color: #007bff;
    font-size: 2.5em;
    margin: 0;
}

.report-subtitle {
    color: #6c757d;
    font-size: 1.2em;
    margin: 10px 0;
}

.report-section {
    margin: 30px 0;
}

.report-section h2 {
    color: #495057;
    border-left: 4px solid #007bff;
    padding-left: 15px;
}

/* ===== METRICHE ===== */
.metrics {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
    flex-wrap: wrap;
    gap: 1rem;
}

.metric {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    flex: 1;
    min-width: 150px;
}

.metric-value {
    font-size: 2em;
    font-weight: bold;
    color: #007bff;
}

.metric-label {
    color: #6c757d;
    margin-top: 5px;
}

/* ===== RACCOMANDAZIONI ===== */
.recommendations {
    background: #e7f3ff;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

/* ===== FOOTER REPORT ===== */
.report-footer {
    margin-top: 50px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
    color: #6c757d;
    font-size: 0.9em;
}

/* ===== REPORT TECNICO ===== */
.technical-report {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
}

.technical-report .report-container {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.technical-report .report-title {
    color: #28a745;
    font-size: 2.2em;
}

.technical-report .report-header {
    border-bottom: 2px solid #28a745;
}

.technical-report .report-section h2 {
    color: #495057;
    background: #e9ecef;
    padding: 10px;
    border-radius: 5px;
    border-left: none;
}

.analysis-item {
    background: #f8f9fa;
    padding: 15px;
    margin: 10px 0;
    border-left: 3px solid #28a745;
}

/* ===== REPORT COMPLETO ===== */
.comprehensive-report .report-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    border-radius: 15px;
}

.comprehensive-report .report-title {
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.insights {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    padding: 30px;
    border-radius: 15px;
    margin: 30px 0;
    color: #333;
}

.analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.analysis-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    color: #333;
}

.analysis-card h3 {
    color: #495057;
    margin-top: 0;
}

.metric-inline {
    display: inline-block;
    background: #007bff;
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    margin: 5px;
    font-size: 0.9em;
}

.report-footer-dark {
    background: #343a40;
    color: white;
    padding: 30px;
    text-align: center;
}

.toc {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}

/* ===== TEMA SCURO ===== */
[data-theme="dark"] .report-container {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

[data-theme="dark"] .metric {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

[data-theme="dark"] .metric-value {
    color: var(--accent-primary);
}

[data-theme="dark"] .metric-label {
    color: var(--text-secondary);
}

[data-theme="dark"] .recommendations {
    background-color: var(--bg-secondary);
    border-left-color: var(--accent-primary);
    color: var(--text-primary);
}

[data-theme="dark"] .analysis-item {
    background-color: var(--bg-secondary);
    border-left-color: var(--accent-success);
    color: var(--text-primary);
}

[data-theme="dark"] .analysis-card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .toc {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .report-container {
        margin: 20px;
    }
    
    .metrics {
        flex-direction: column;
    }
    
    .analysis-grid {
        grid-template-columns: 1fr;
    }
    
    .report-title {
        font-size: 2em;
    }
    
    .technical-report .report-title {
        font-size: 1.8em;
    }
}
