#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Query Optimizer - Sistema di ottimizzazione query database per app-roberto.
Ottimizza automaticamente le query Supabase per massimizzare le performance.
"""

import logging
import time
import hashlib
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
from collections import defaultdict, deque
import threading

# Import dei moduli esistenti
try:
    from supabase_integration import SupabaseManager
    from intelligent_cache_system import intelligent_cache
except ImportError as e:
    logging.warning(f"Alcuni moduli non disponibili: {e}")

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QueryType(Enum):
    """Tipi di query."""
    SELECT = "select"
    INSERT = "insert"
    UPDATE = "update"
    DELETE = "delete"
    AGGREGATE = "aggregate"

class OptimizationStrategy(Enum):
    """Strategie di ottimizzazione."""
    INDEX_SUGGESTION = "index_suggestion"
    QUERY_REWRITE = "query_rewrite"
    BATCH_OPTIMIZATION = "batch_optimization"
    CACHE_OPTIMIZATION = "cache_optimization"
    PAGINATION_OPTIMIZATION = "pagination_optimization"

@dataclass
class QueryMetrics:
    """Metriche di performance per una query."""
    query_hash: str
    query_type: QueryType
    table_name: str
    execution_time_ms: float
    rows_affected: int
    cache_hit: bool
    timestamp: datetime
    optimization_applied: Optional[str] = None

class QueryPattern:
    """Pattern di query per ottimizzazione."""

    def __init__(self, table_name: str, query_type: QueryType):
        self.table_name = table_name
        self.query_type = query_type
        self.execution_times = deque(maxlen=100)
        self.total_executions = 0
        self.cache_hits = 0
        self.last_execution = None
        self.optimization_suggestions = []

class QueryOptimizer:
    """
    Sistema di ottimizzazione query che:
    - Monitora performance delle query in tempo reale
    - Suggerisce ottimizzazioni automatiche
    - Implementa caching intelligente per query frequenti
    - Ottimizza batch operations
    - Fornisce analytics dettagliate
    """

    def __init__(self, supabase_manager: Optional[Any] = None):
        self.supabase_manager = supabase_manager

        # Configurazione ottimizzazione
        self.SLOW_QUERY_THRESHOLD_MS = 1000  # 1 secondo
        self.CACHE_THRESHOLD_EXECUTIONS = 5  # Cache dopo 5 esecuzioni
        self.BATCH_SIZE_THRESHOLD = 10       # Batch per operazioni > 10 record
        self.OPTIMIZATION_INTERVAL = 300     # 5 minuti

        # Storage metriche e pattern
        self.query_metrics = deque(maxlen=10000)
        self.query_patterns = defaultdict(lambda: defaultdict(QueryPattern))
        self.slow_queries = deque(maxlen=1000)
        self.optimization_history = []

        # Cache query
        self.query_cache = {}
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "invalidations": 0
        }

        # Threading per ottimizzazione automatica
        self.optimization_thread = None
        self.is_optimizing = False
        self.lock = threading.RLock()

        # Statistiche generali
        self.performance_stats = {
            "total_queries": 0,
            "avg_execution_time": 0.0,
            "slow_queries_count": 0,
            "optimizations_applied": 0
        }

        # Avvia ottimizzazione automatica solo se non disabilitata
        import os
        if not os.environ.get('DISABLE_QUERY_OPTIMIZATION'):
            self._start_auto_optimization()
            logger.info("QueryOptimizer inizializzato con ottimizzazione attiva")
        else:
            logger.info("QueryOptimizer inizializzato ma ottimizzazione disabilitata (modalità minimal)")

    def execute_optimized_query(self, table_name: str, query_builder,
                               query_type: QueryType = QueryType.SELECT,
                               cache_ttl: Optional[int] = None) -> Any:
        """
        Esegue una query con ottimizzazioni automatiche.

        Args:
            table_name: Nome della tabella
            query_builder: Query builder di Supabase
            query_type: Tipo di query
            cache_ttl: TTL per cache (se applicabile)

        Returns:
            Risultato della query
        """
        start_time = time.time()

        # Genera hash della query per tracking
        query_hash = self._generate_query_hash(table_name, str(query_builder))

        # Verifica cache per query SELECT
        if query_type == QueryType.SELECT:
            cached_result = self._get_cached_result(query_hash)
            if cached_result is not None:
                self._record_metrics(query_hash, query_type, table_name,
                                   (time.time() - start_time) * 1000, 0, True)
                return cached_result

        # Applica ottimizzazioni pre-esecuzione
        optimized_query = self._apply_pre_execution_optimizations(
            query_builder, table_name, query_type
        )

        try:
            # Esegui query
            result = optimized_query.execute()
            execution_time = (time.time() - start_time) * 1000

            # Registra metriche
            rows_affected = len(result.data) if result.data else 0
            self._record_metrics(query_hash, query_type, table_name,
                               execution_time, rows_affected, False)

            # Cache risultato se appropriato
            if query_type == QueryType.SELECT and self._should_cache_query(
                table_name, query_type, execution_time
            ):
                self._cache_result(query_hash, result, cache_ttl)

            # Applica ottimizzazioni post-esecuzione
            self._apply_post_execution_optimizations(
                table_name, query_type, execution_time
            )

            return result

        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            self._record_metrics(query_hash, query_type, table_name,
                               execution_time, 0, False, str(e))
            raise

    def optimize_batch_operations(self, operations: List[Dict[str, Any]],
                                 table_name: str) -> Dict[str, Any]:
        """
        Ottimizza operazioni batch.

        Args:
            operations: Lista di operazioni da eseguire
            table_name: Nome della tabella

        Returns:
            Risultato delle operazioni batch
        """
        if len(operations) < self.BATCH_SIZE_THRESHOLD:
            # Non vale la pena ottimizzare
            return self._execute_individual_operations(operations, table_name)

        # Raggruppa operazioni per tipo
        grouped_ops = self._group_operations_by_type(operations)

        results = {}
        total_time = 0

        for op_type, ops in grouped_ops.items():
            start_time = time.time()

            if op_type == "insert":
                result = self._batch_insert(ops, table_name)
            elif op_type == "update":
                result = self._batch_update(ops, table_name)
            elif op_type == "delete":
                result = self._batch_delete(ops, table_name)
            else:
                result = self._execute_individual_operations(ops, table_name)

            execution_time = (time.time() - start_time) * 1000
            total_time += execution_time

            results[op_type] = result

            # Registra metriche batch
            self._record_batch_metrics(table_name, op_type, len(ops), execution_time)

        logger.info(f"Batch operations completate in {total_time:.2f}ms per {len(operations)} operazioni")
        return results

    def suggest_optimizations(self, table_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Suggerisce ottimizzazioni basate sui pattern di query.

        Args:
            table_name: Nome tabella specifica (opzionale)

        Returns:
            Lista di suggerimenti di ottimizzazione
        """
        suggestions = []

        with self.lock:
            tables_to_analyze = [table_name] if table_name else self.query_patterns.keys()

            for table in tables_to_analyze:
                table_patterns = self.query_patterns[table]

                for query_type, pattern in table_patterns.items():
                    # Analizza pattern per suggerimenti
                    table_suggestions = self._analyze_pattern_for_suggestions(
                        table, query_type, pattern
                    )
                    suggestions.extend(table_suggestions)

        # Ordina per priorità
        suggestions.sort(key=lambda x: x.get("priority", 0), reverse=True)

        return suggestions

    def get_performance_report(self) -> Dict[str, Any]:
        """Genera report dettagliato delle performance."""
        with self.lock:
            # Calcola statistiche generali
            total_queries = len(self.query_metrics)
            if total_queries == 0:
                return {"message": "Nessuna query registrata"}

            avg_execution_time = sum(m.execution_time_ms for m in self.query_metrics) / total_queries
            slow_queries_count = len([m for m in self.query_metrics
                                    if m.execution_time_ms > self.SLOW_QUERY_THRESHOLD_MS])

            # Statistiche per tabella
            table_stats = defaultdict(lambda: {
                "query_count": 0,
                "avg_execution_time": 0.0,
                "slow_queries": 0,
                "cache_hit_rate": 0.0
            })

            for metric in self.query_metrics:
                stats = table_stats[metric.table_name]
                stats["query_count"] += 1
                stats["avg_execution_time"] += metric.execution_time_ms
                if metric.execution_time_ms > self.SLOW_QUERY_THRESHOLD_MS:
                    stats["slow_queries"] += 1
                if metric.cache_hit:
                    stats["cache_hit_rate"] += 1

            # Finalizza statistiche per tabella
            for table, stats in table_stats.items():
                if stats["query_count"] > 0:
                    stats["avg_execution_time"] /= stats["query_count"]
                    stats["cache_hit_rate"] = (stats["cache_hit_rate"] / stats["query_count"]) * 100

            # Top query lente
            slow_queries = sorted(
                [m for m in self.query_metrics if m.execution_time_ms > self.SLOW_QUERY_THRESHOLD_MS],
                key=lambda x: x.execution_time_ms,
                reverse=True
            )[:10]

            return {
                "summary": {
                    "total_queries": total_queries,
                    "avg_execution_time_ms": avg_execution_time,
                    "slow_queries_count": slow_queries_count,
                    "slow_queries_percentage": (slow_queries_count / total_queries) * 100,
                    "optimizations_applied": self.performance_stats["optimizations_applied"]
                },
                "cache_statistics": {
                    "hits": self.cache_stats["hits"],
                    "misses": self.cache_stats["misses"],
                    "hit_rate_percentage": (self.cache_stats["hits"] /
                                          max(1, self.cache_stats["hits"] + self.cache_stats["misses"])) * 100
                },
                "table_statistics": dict(table_stats),
                "slowest_queries": [
                    {
                        "table": q.table_name,
                        "type": q.query_type.value,
                        "execution_time_ms": q.execution_time_ms,
                        "timestamp": q.timestamp.isoformat()
                    }
                    for q in slow_queries
                ],
                "optimization_suggestions": self.suggest_optimizations()
            }

    def _generate_query_hash(self, table_name: str, query_str: str) -> str:
        """Genera hash univoco per una query."""
        query_signature = f"{table_name}:{query_str}"
        return hashlib.md5(query_signature.encode()).hexdigest()

    def _get_cached_result(self, query_hash: str) -> Any:
        """Recupera risultato dalla cache."""
        cached = intelligent_cache.get(f"query:{query_hash}")
        if cached is not None:
            self.cache_stats["hits"] += 1
            return cached

        self.cache_stats["misses"] += 1
        return None

    def _cache_result(self, query_hash: str, result: Any, ttl: Optional[int] = None):
        """Memorizza risultato in cache."""
        cache_key = f"query:{query_hash}"
        intelligent_cache.set(
            cache_key,
            result,
            ttl=ttl or 300,  # Default 5 minuti
            tags=["query_cache"]
        )

    def _should_cache_query(self, table_name: str, query_type: QueryType,
                           execution_time: float) -> bool:
        """Determina se una query dovrebbe essere cachata."""
        if query_type != QueryType.SELECT:
            return False

        pattern = self.query_patterns[table_name][query_type]

        # Cache se query frequente o lenta
        return (pattern.total_executions >= self.CACHE_THRESHOLD_EXECUTIONS or
                execution_time > self.SLOW_QUERY_THRESHOLD_MS)

    def _record_metrics(self, query_hash: str, query_type: QueryType,
                       table_name: str, execution_time: float,
                       rows_affected: int, cache_hit: bool, error: str = None):
        """Registra metriche di una query."""
        metric = QueryMetrics(
            query_hash=query_hash,
            query_type=query_type,
            table_name=table_name,
            execution_time_ms=execution_time,
            rows_affected=rows_affected,
            cache_hit=cache_hit,
            timestamp=datetime.now()
        )

        with self.lock:
            self.query_metrics.append(metric)

            # Aggiorna pattern
            pattern = self.query_patterns[table_name][query_type]
            pattern.execution_times.append(execution_time)
            pattern.total_executions += 1
            pattern.last_execution = datetime.now()

            if cache_hit:
                pattern.cache_hits += 1

            # Registra query lente
            if execution_time > self.SLOW_QUERY_THRESHOLD_MS:
                self.slow_queries.append(metric)
                self.performance_stats["slow_queries_count"] += 1

            # Aggiorna statistiche generali
            self.performance_stats["total_queries"] += 1
            current_avg = self.performance_stats["avg_execution_time"]
            total_queries = self.performance_stats["total_queries"]
            self.performance_stats["avg_execution_time"] = (
                (current_avg * (total_queries - 1) + execution_time) / total_queries
            )

    def _record_batch_metrics(self, table_name: str, operation_type: str,
                             operation_count: int, execution_time: float):
        """Registra metriche per operazioni batch."""
        logger.info(f"Batch {operation_type} su {table_name}: "
                   f"{operation_count} operazioni in {execution_time:.2f}ms")

        # Registra come ottimizzazione applicata
        self.performance_stats["optimizations_applied"] += 1

    def _apply_pre_execution_optimizations(self, query_builder, table_name: str,
                                         query_type: QueryType):
        """Applica ottimizzazioni prima dell'esecuzione."""
        optimized_query = query_builder

        # Ottimizzazione SELECT con limit automatico
        if query_type == QueryType.SELECT:
            # Aggiungi limit se non presente per evitare query troppo grandi
            query_str = str(query_builder)
            if "limit" not in query_str.lower():
                optimized_query = query_builder.limit(1000)
                logger.debug(f"Aggiunto limit automatico per query su {table_name}")

        return optimized_query

    def _apply_post_execution_optimizations(self, table_name: str,
                                          query_type: QueryType, execution_time: float):
        """Applica ottimizzazioni dopo l'esecuzione."""
        # Invalida cache se operazione di modifica
        if query_type in [QueryType.INSERT, QueryType.UPDATE, QueryType.DELETE]:
            self._invalidate_table_cache(table_name)

        # Suggerisci ottimizzazioni per query lente
        if execution_time > self.SLOW_QUERY_THRESHOLD_MS:
            self._generate_optimization_suggestions(table_name, query_type, execution_time)

    def _invalidate_table_cache(self, table_name: str):
        """Invalida cache per una tabella."""
        intelligent_cache.invalidate_by_tags([f"table:{table_name}"])
        self.cache_stats["invalidations"] += 1
        logger.debug(f"Cache invalidata per tabella: {table_name}")

    def _generate_optimization_suggestions(self, table_name: str,
                                         query_type: QueryType, execution_time: float):
        """Genera suggerimenti di ottimizzazione per query lente."""
        suggestions = []

        if execution_time > self.SLOW_QUERY_THRESHOLD_MS * 2:  # Molto lenta
            suggestions.append({
                "type": "index_suggestion",
                "table": table_name,
                "message": f"Query {query_type.value} molto lenta ({execution_time:.0f}ms). "
                          "Considera l'aggiunta di indici.",
                "priority": 9
            })

        if query_type == QueryType.SELECT:
            pattern = self.query_patterns[table_name][query_type]
            if pattern.total_executions > 10 and pattern.cache_hits / pattern.total_executions < 0.5:
                suggestions.append({
                    "type": "cache_optimization",
                    "table": table_name,
                    "message": "Query SELECT frequente con basso hit rate cache. "
                              "Considera ottimizzazione cache.",
                    "priority": 7
                })

        # Aggiungi alla cronologia
        for suggestion in suggestions:
            self.optimization_history.append({
                **suggestion,
                "timestamp": datetime.now(),
                "execution_time": execution_time
            })

    def _execute_individual_operations(self, operations: List[Dict[str, Any]],
                                     table_name: str) -> List[Any]:
        """Esegue operazioni individuali."""
        results = []
        for op in operations:
            # Implementazione semplificata
            results.append({"success": True, "operation": op})
        return results

    def _group_operations_by_type(self, operations: List[Dict[str, Any]]) -> Dict[str, List]:
        """Raggruppa operazioni per tipo."""
        grouped = defaultdict(list)
        for op in operations:
            op_type = op.get("type", "unknown")
            grouped[op_type].append(op)
        return grouped

    def _batch_insert(self, operations: List[Dict[str, Any]], table_name: str) -> Dict[str, Any]:
        """Esegue insert in batch."""
        try:
            # Estrai dati da inserire
            data_to_insert = [op.get("data", {}) for op in operations]

            # Esegui batch insert
            result = self.supabase_manager.client.table(table_name).insert(data_to_insert).execute()

            return {
                "success": True,
                "inserted_count": len(data_to_insert),
                "result": result
            }
        except Exception as e:
            logger.error(f"Errore batch insert su {table_name}: {e}")
            return {"success": False, "error": str(e)}

    def _batch_update(self, operations: List[Dict[str, Any]], table_name: str) -> Dict[str, Any]:
        """Esegue update in batch."""
        success_count = 0
        errors = []

        for op in operations:
            try:
                data = op.get("data", {})
                condition = op.get("condition", {})

                # Esegui update singolo (Supabase non supporta batch update diretto)
                result = self.supabase_manager.client.table(table_name).update(data).match(condition).execute()
                success_count += 1

            except Exception as e:
                errors.append(str(e))

        return {
            "success": len(errors) == 0,
            "updated_count": success_count,
            "errors": errors
        }

    def _batch_delete(self, operations: List[Dict[str, Any]], table_name: str) -> Dict[str, Any]:
        """Esegue delete in batch."""
        success_count = 0
        errors = []

        for op in operations:
            try:
                condition = op.get("condition", {})

                # Esegui delete singolo
                result = self.supabase_manager.client.table(table_name).delete().match(condition).execute()
                success_count += 1

            except Exception as e:
                errors.append(str(e))

        return {
            "success": len(errors) == 0,
            "deleted_count": success_count,
            "errors": errors
        }

    def _analyze_pattern_for_suggestions(self, table_name: str, query_type: QueryType,
                                       pattern: QueryPattern) -> List[Dict[str, Any]]:
        """Analizza pattern per generare suggerimenti."""
        suggestions = []

        if not pattern.execution_times:
            return suggestions

        avg_time = sum(pattern.execution_times) / len(pattern.execution_times)

        # Suggerimento per query lente
        if avg_time > self.SLOW_QUERY_THRESHOLD_MS:
            suggestions.append({
                "type": "performance_optimization",
                "table": table_name,
                "query_type": query_type.value,
                "message": f"Query {query_type.value} su {table_name} mediamente lenta "
                          f"({avg_time:.0f}ms). Considera ottimizzazioni.",
                "priority": 8,
                "avg_execution_time": avg_time
            })

        # Suggerimento per cache
        if query_type == QueryType.SELECT and pattern.total_executions > 5:
            cache_hit_rate = pattern.cache_hits / pattern.total_executions
            if cache_hit_rate < 0.3:
                suggestions.append({
                    "type": "cache_optimization",
                    "table": table_name,
                    "query_type": query_type.value,
                    "message": f"Basso hit rate cache ({cache_hit_rate:.1%}) per SELECT su {table_name}.",
                    "priority": 6,
                    "cache_hit_rate": cache_hit_rate
                })

        return suggestions

    def _start_auto_optimization(self):
        """Avvia thread per ottimizzazione automatica."""
        if self.optimization_thread and self.optimization_thread.is_alive():
            return

        self.is_optimizing = True
        self.optimization_thread = threading.Thread(
            target=self._optimization_loop,
            daemon=True
        )
        self.optimization_thread.start()
        logger.info("Query optimization thread avviato")

    def _optimization_loop(self):
        """Loop di ottimizzazione automatica."""
        while self.is_optimizing:
            try:
                time.sleep(self.OPTIMIZATION_INTERVAL)
                self._run_automatic_optimizations()
            except Exception as e:
                logger.error(f"Errore nell'ottimizzazione automatica query: {e}")

    def _run_automatic_optimizations(self):
        """Esegue ottimizzazioni automatiche."""
        with self.lock:
            # Pulisci cache vecchia
            self._cleanup_old_cache()

            # Genera suggerimenti automatici
            suggestions = self.suggest_optimizations()

            # Applica ottimizzazioni automatiche a bassa priorità
            auto_applied = 0
            for suggestion in suggestions:
                if suggestion.get("priority", 0) >= 8:  # Solo alta priorità
                    if self._apply_automatic_optimization(suggestion):
                        auto_applied += 1

            if auto_applied > 0:
                logger.info(f"Applicate {auto_applied} ottimizzazioni automatiche")

    def _cleanup_old_cache(self):
        """Pulisce cache vecchia."""
        # Implementazione semplificata - in produzione usare TTL automatico
        pass

    def _apply_automatic_optimization(self, suggestion: Dict[str, Any]) -> bool:
        """Applica ottimizzazione automatica se possibile."""
        # Implementazione semplificata - in produzione implementare ottimizzazioni specifiche
        logger.debug(f"Ottimizzazione automatica suggerita: {suggestion['message']}")
        return False

# Istanza globale
query_optimizer = QueryOptimizer()