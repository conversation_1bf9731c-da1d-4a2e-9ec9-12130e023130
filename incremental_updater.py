#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Incremental Updater - Sistema di aggiornamento incrementale per app-roberto.
Gestisce aggiornamenti incrementali del database per evitare duplicati e
ottimizzare le performance delle operazioni di sincronizzazione.
"""

import logging
import hashlib
import json
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import pandas as pd

# Import dei moduli esistenti
try:
    from supabase_integration import SupabaseManager
    from data_synchronizer import DataSynchronizer, SyncOperation
except ImportError as e:
    logging.warning(f"Alcuni moduli non disponibili: {e}")

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UpdateStrategy(Enum):
    """Strategie di aggiornamento incrementale."""
    TIMESTAMP_BASED = "timestamp_based"
    HASH_BASED = "hash_based"
    VERSION_BASED = "version_based"
    HYBRID = "hybrid"

@dataclass
class UpdateRecord:
    """Record per tracciare aggiornamenti incrementali."""
    table_name: str
    record_id: int
    last_hash: str
    last_updated: datetime
    version: int = 1
    update_count: int = 0

class IncrementalUpdater:
    """
    Sistema di aggiornamento incrementale che:
    - Rileva modifiche basandosi su hash, timestamp o versioni
    - Evita aggiornamenti duplicati
    - Ottimizza le performance con aggiornamenti batch
    - Mantiene cronologia delle modifiche
    - Supporta rollback degli aggiornamenti
    """

    def __init__(self, supabase_manager: Optional[SupabaseManager] = None):
        self.supabase_manager = supabase_manager or SupabaseManager()

        # Configurazione aggiornamenti
        self.UPDATE_STRATEGY = UpdateStrategy.HYBRID
        self.BATCH_SIZE = 100
        self.MAX_UPDATE_HISTORY = 1000
        self.CHANGE_DETECTION_THRESHOLD = 0.1  # 10% di cambiamento per trigger update

        # Cache per tracking aggiornamenti
        self.update_tracking = {}  # table_name:record_id -> UpdateRecord
        self.pending_updates = []
        self.update_history = []

        # Statistiche aggiornamenti
        self.update_stats = {
            "total_updates": 0,
            "incremental_updates": 0,
            "full_updates": 0,
            "skipped_updates": 0,
            "failed_updates": 0,
            "bytes_saved": 0,
            "time_saved_seconds": 0.0
        }

        logger.info("IncrementalUpdater inizializzato")

    def detect_changes(self, table_name: str, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Rileva modifiche nei record per aggiornamento incrementale.

        Args:
            table_name: Nome della tabella
            records: Lista di record da verificare

        Returns:
            Dizionario con modifiche rilevate
        """
        logger.info(f"🔍 Rilevamento modifiche per tabella: {table_name}")

        changes = {
            "new_records": [],
            "modified_records": [],
            "unchanged_records": [],
            "deleted_records": [],
            "total_records": len(records),
            "change_summary": {}
        }

        try:
            # Recupera stato corrente dal database
            current_state = self._get_current_table_state(table_name)
            current_ids = set(current_state.keys())
            new_ids = set(record.get("id") for record in records if record.get("id"))

            # Rileva record eliminati
            deleted_ids = current_ids - new_ids
            changes["deleted_records"] = list(deleted_ids)

            # Analizza ogni record
            for record in records:
                record_id = record.get("id")

                if not record_id:
                    # Nuovo record senza ID
                    changes["new_records"].append(record)
                    continue

                if record_id not in current_state:
                    # Nuovo record con ID
                    changes["new_records"].append(record)
                else:
                    # Record esistente - verifica modifiche
                    current_record = current_state[record_id]
                    if self._has_changed(record, current_record, table_name):
                        changes["modified_records"].append({
                            "current": record,
                            "previous": current_record,
                            "changes": self._get_field_changes(record, current_record)
                        })
                    else:
                        changes["unchanged_records"].append(record)

            # Riassunto modifiche
            changes["change_summary"] = {
                "new_count": len(changes["new_records"]),
                "modified_count": len(changes["modified_records"]),
                "unchanged_count": len(changes["unchanged_records"]),
                "deleted_count": len(changes["deleted_records"]),
                "change_percentage": self._calculate_change_percentage(changes)
            }

            logger.info(f"✅ Rilevamento completato: {changes['change_summary']}")
            return changes

        except Exception as e:
            logger.error(f"❌ Errore nel rilevamento modifiche: {str(e)}")
            return changes

    def apply_incremental_update(self, table_name: str, changes: Dict[str, Any]) -> Dict[str, Any]:
        """
        Applica aggiornamento incrementale basato sulle modifiche rilevate.

        Args:
            table_name: Nome della tabella
            changes: Modifiche rilevate da detect_changes

        Returns:
            Risultato dell'aggiornamento
        """
        logger.info(f"🔄 Applicazione aggiornamento incrementale per: {table_name}")

        update_result = {
            "table_name": table_name,
            "start_time": datetime.now().isoformat(),
            "strategy_used": self.UPDATE_STRATEGY.value,
            "operations": {
                "inserts": {"count": 0, "success": 0, "errors": 0},
                "updates": {"count": 0, "success": 0, "errors": 0},
                "deletes": {"count": 0, "success": 0, "errors": 0}
            },
            "performance": {
                "records_processed": 0,
                "bytes_transferred": 0,
                "time_saved": 0.0
            },
            "errors": []
        }

        try:
            start_time = datetime.now()

            # 1. Inserisci nuovi record
            if changes["new_records"]:
                insert_result = self._batch_insert(table_name, changes["new_records"])
                update_result["operations"]["inserts"] = insert_result

            # 2. Aggiorna record modificati
            if changes["modified_records"]:
                update_result_op = self._batch_update(table_name, changes["modified_records"])
                update_result["operations"]["updates"] = update_result_op

            # 3. Elimina record cancellati
            if changes["deleted_records"]:
                delete_result = self._batch_delete(table_name, changes["deleted_records"])
                update_result["operations"]["deletes"] = delete_result

            # Calcola performance
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            update_result["end_time"] = end_time.isoformat()
            update_result["duration_seconds"] = duration
            update_result["performance"]["records_processed"] = changes["total_records"]

            # Aggiorna statistiche
            self._update_incremental_stats(update_result, changes)

            logger.info(f"✅ Aggiornamento incrementale completato in {duration:.2f}s")
            return update_result

        except Exception as e:
            logger.error(f"❌ Errore nell'aggiornamento incrementale: {str(e)}")
            update_result["error"] = str(e)
            return update_result

    def optimize_update_strategy(self, table_name: str, historical_data: List[Dict[str, Any]]) -> UpdateStrategy:
        """
        Ottimizza la strategia di aggiornamento basandosi sui dati storici.

        Args:
            table_name: Nome della tabella
            historical_data: Dati storici degli aggiornamenti

        Returns:
            Strategia ottimale
        """
        logger.info(f"🎯 Ottimizzazione strategia per tabella: {table_name}")

        try:
            if not historical_data:
                return UpdateStrategy.HYBRID

            # Analizza pattern di aggiornamento
            analysis = {
                "avg_change_frequency": 0.0,
                "avg_record_size": 0.0,
                "change_pattern": "unknown",
                "performance_metrics": {}
            }

            # Calcola frequenza di cambiamento
            total_changes = sum(1 for data in historical_data if data.get("has_changes", False))
            analysis["avg_change_frequency"] = total_changes / len(historical_data)

            # Calcola dimensione media record
            total_size = sum(len(json.dumps(data, default=str)) for data in historical_data)
            analysis["avg_record_size"] = total_size / len(historical_data)

            # Determina strategia ottimale
            if analysis["avg_change_frequency"] > 0.8:
                # Cambiamenti frequenti - usa timestamp
                optimal_strategy = UpdateStrategy.TIMESTAMP_BASED
            elif analysis["avg_record_size"] > 10000:
                # Record grandi - usa hash per efficienza
                optimal_strategy = UpdateStrategy.HASH_BASED
            elif analysis["avg_change_frequency"] < 0.2:
                # Cambiamenti rari - usa versioning
                optimal_strategy = UpdateStrategy.VERSION_BASED
            else:
                # Caso generale - usa hybrid
                optimal_strategy = UpdateStrategy.HYBRID

            logger.info(f"✅ Strategia ottimale per {table_name}: {optimal_strategy.value}")
            return optimal_strategy

        except Exception as e:
            logger.error(f"❌ Errore nell'ottimizzazione strategia: {str(e)}")
            return UpdateStrategy.HYBRID

    def create_update_checkpoint(self, table_name: str) -> str:
        """
        Crea un checkpoint per rollback degli aggiornamenti.

        Args:
            table_name: Nome della tabella

        Returns:
            ID del checkpoint
        """
        try:
            checkpoint_id = f"{table_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Salva stato corrente
            current_state = self._get_current_table_state(table_name)

            checkpoint_data = {
                "checkpoint_id": checkpoint_id,
                "table_name": table_name,
                "created_at": datetime.now().isoformat(),
                "record_count": len(current_state),
                "state_hash": self._calculate_table_hash(current_state),
                "metadata": {
                    "strategy": self.UPDATE_STRATEGY.value,
                    "version": "1.0"
                }
            }

            # Salva checkpoint nel database
            self.supabase_manager.client.table("update_checkpoints").insert(checkpoint_data).execute()

            logger.info(f"✅ Checkpoint creato: {checkpoint_id}")
            return checkpoint_id

        except Exception as e:
            logger.error(f"❌ Errore creazione checkpoint: {str(e)}")
            return ""

    def _get_current_table_state(self, table_name: str) -> Dict[int, Dict[str, Any]]:
        """Recupera lo stato corrente di una tabella dal database."""
        try:
            result = self.supabase_manager.client.table(table_name).select("*").execute()

            state = {}
            for record in result.data or []:
                record_id = record.get("id")
                if record_id:
                    state[record_id] = record

            return state

        except Exception as e:
            logger.error(f"Errore recupero stato tabella {table_name}: {e}")
            return {}

    def _has_changed(self, new_record: Dict[str, Any], old_record: Dict[str, Any], table_name: str) -> bool:
        """Verifica se un record è cambiato."""
        try:
            if self.UPDATE_STRATEGY == UpdateStrategy.HASH_BASED:
                return self._calculate_record_hash(new_record) != self._calculate_record_hash(old_record)

            elif self.UPDATE_STRATEGY == UpdateStrategy.TIMESTAMP_BASED:
                new_updated = new_record.get("updated_at")
                old_updated = old_record.get("updated_at")

                if new_updated and old_updated:
                    return new_updated != old_updated
                return True  # Assume changed if no timestamp

            elif self.UPDATE_STRATEGY == UpdateStrategy.VERSION_BASED:
                new_version = new_record.get("version", 1)
                old_version = old_record.get("version", 1)
                return new_version > old_version

            else:  # HYBRID
                # Combina hash e timestamp
                hash_changed = self._calculate_record_hash(new_record) != self._calculate_record_hash(old_record)

                new_updated = new_record.get("updated_at")
                old_updated = old_record.get("updated_at")
                timestamp_changed = new_updated != old_updated if (new_updated and old_updated) else True

                return hash_changed or timestamp_changed

        except Exception as e:
            logger.error(f"Errore verifica cambiamento record: {e}")
            return True  # Assume changed in case of error

    def _calculate_record_hash(self, record: Dict[str, Any]) -> str:
        """Calcola hash di un record escludendo campi automatici."""
        filtered_record = {k: v for k, v in record.items()
                          if k not in ['id', 'created_at', 'updated_at', 'version']}

        record_str = json.dumps(filtered_record, sort_keys=True, default=str)
        return hashlib.md5(record_str.encode()).hexdigest()

    def _calculate_table_hash(self, table_state: Dict[int, Dict[str, Any]]) -> str:
        """Calcola hash dell'intera tabella."""
        combined_hash = ""
        for record_id in sorted(table_state.keys()):
            record_hash = self._calculate_record_hash(table_state[record_id])
            combined_hash += record_hash

        return hashlib.md5(combined_hash.encode()).hexdigest()

    def _get_field_changes(self, new_record: Dict[str, Any], old_record: Dict[str, Any]) -> Dict[str, Any]:
        """Identifica i campi specifici che sono cambiati."""
        changes = {}

        all_keys = set(new_record.keys()) | set(old_record.keys())

        for key in all_keys:
            old_value = old_record.get(key)
            new_value = new_record.get(key)

            if old_value != new_value:
                changes[key] = {
                    "old": old_value,
                    "new": new_value,
                    "type": "modified" if key in old_record and key in new_record else
                           "added" if key in new_record else "removed"
                }

        return changes

    def _calculate_change_percentage(self, changes: Dict[str, Any]) -> float:
        """Calcola la percentuale di cambiamento."""
        total = changes["total_records"]
        if total == 0:
            return 0.0

        changed = (len(changes["new_records"]) +
                  len(changes["modified_records"]) +
                  len(changes["deleted_records"]))

        return (changed / total) * 100

    def _batch_insert(self, table_name: str, records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Inserisce record in batch."""
        result = {"count": len(records), "success": 0, "errors": 0, "error_details": []}

        try:
            # Processa in batch
            for i in range(0, len(records), self.BATCH_SIZE):
                batch = records[i:i + self.BATCH_SIZE]

                try:
                    insert_result = self.supabase_manager.client.table(table_name).insert(batch).execute()
                    result["success"] += len(batch)

                except Exception as e:
                    result["errors"] += len(batch)
                    result["error_details"].append(f"Batch {i//self.BATCH_SIZE + 1}: {str(e)}")

        except Exception as e:
            result["error_details"].append(f"General error: {str(e)}")

        return result

    def _batch_update(self, table_name: str, modified_records: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Aggiorna record in batch."""
        result = {"count": len(modified_records), "success": 0, "errors": 0, "error_details": []}

        try:
            for modified in modified_records:
                try:
                    current_record = modified["current"]
                    record_id = current_record.get("id")

                    if record_id:
                        # Prepara dati per update
                        update_data = {k: v for k, v in current_record.items() if k != 'id'}
                        update_data['updated_at'] = datetime.now().isoformat()

                        self.supabase_manager.client.table(table_name).update(update_data).eq("id", record_id).execute()
                        result["success"] += 1

                except Exception as e:
                    result["errors"] += 1
                    result["error_details"].append(f"Record {current_record.get('id', 'unknown')}: {str(e)}")

        except Exception as e:
            result["error_details"].append(f"General error: {str(e)}")

        return result

    def _batch_delete(self, table_name: str, record_ids: List[int]) -> Dict[str, Any]:
        """Elimina record in batch."""
        result = {"count": len(record_ids), "success": 0, "errors": 0, "error_details": []}

        try:
            # Processa in batch
            for i in range(0, len(record_ids), self.BATCH_SIZE):
                batch = record_ids[i:i + self.BATCH_SIZE]

                try:
                    self.supabase_manager.client.table(table_name).delete().in_("id", batch).execute()
                    result["success"] += len(batch)

                except Exception as e:
                    result["errors"] += len(batch)
                    result["error_details"].append(f"Batch {i//self.BATCH_SIZE + 1}: {str(e)}")

        except Exception as e:
            result["error_details"].append(f"General error: {str(e)}")

        return result

    def _update_incremental_stats(self, update_result: Dict[str, Any], changes: Dict[str, Any]):
        """Aggiorna le statistiche degli aggiornamenti incrementali."""
        self.update_stats["total_updates"] += 1

        # Calcola se è stato un aggiornamento incrementale efficace
        change_percentage = changes["change_summary"]["change_percentage"]
        if change_percentage < 50:  # Meno del 50% di cambiamenti
            self.update_stats["incremental_updates"] += 1

            # Stima bytes risparmiati
            total_records = changes["total_records"]
            unchanged_records = changes["change_summary"]["unchanged_count"]
            estimated_bytes_saved = unchanged_records * 1000  # Stima 1KB per record
            self.update_stats["bytes_saved"] += estimated_bytes_saved
        else:
            self.update_stats["full_updates"] += 1

        # Aggiorna tempo risparmiato (stima)
        duration = update_result.get("duration_seconds", 0.0)
        estimated_full_update_time = duration * (100 / max(change_percentage, 1))
        time_saved = max(0, estimated_full_update_time - duration)
        self.update_stats["time_saved_seconds"] += time_saved

    def get_update_statistics(self) -> Dict[str, Any]:
        """Restituisce statistiche degli aggiornamenti incrementali."""
        stats = self.update_stats.copy()

        # Calcola percentuali
        total = stats["total_updates"]
        if total > 0:
            stats["incremental_percentage"] = (stats["incremental_updates"] / total) * 100
            stats["efficiency_score"] = (stats["bytes_saved"] / max(total * 10000, 1)) * 100  # Normalizzato
        else:
            stats["incremental_percentage"] = 0.0
            stats["efficiency_score"] = 0.0

        return stats

    def reset_statistics(self):
        """Resetta le statistiche."""
        self.update_stats = {
            "total_updates": 0,
            "incremental_updates": 0,
            "full_updates": 0,
            "skipped_updates": 0,
            "failed_updates": 0,
            "bytes_saved": 0,
            "time_saved_seconds": 0.0
        }
        logger.info("📊 Statistiche aggiornamenti resettate")

# Istanza globale
incremental_updater = IncrementalUpdater()
