@echo off
echo ===================================
echo Avvio del server MCP
echo ===================================
echo.

REM Verifica se l'ambiente virtuale esiste
if not exist venv (
    echo Creazione dell'ambiente virtuale...
    python -m venv venv
    echo Ambiente virtuale creato.
    echo.
)

REM Attiva l'ambiente virtuale
echo Attivazione dell'ambiente virtuale...
call venv\Scripts\activate
echo Ambiente virtuale attivato.
echo.

REM Verifica se le dipendenze sono installate
echo Verifica delle dipendenze...
pip freeze > temp_requirements.txt
findstr /i "fastapi uvicorn" temp_requirements.txt > nul
if %errorlevel% neq 0 (
    echo Installazione delle dipendenze del server MCP...
    cd mcp_server
    pip install -r requirements.txt
    cd ..
    echo Dipendenze installate.
) else (
    echo Le dipendenze sono già installate.
)
del temp_requirements.txt
echo.

REM Avvia il server MCP
echo Avvio del server MCP...
cd mcp_server
python run_server.py
echo.

REM Disattiva l'ambiente virtuale (questo punto viene raggiunto solo quando il server viene chiuso)
cd ..
call venv\Scripts\deactivate
echo Ambiente virtuale disattivato.
echo.

echo ===================================
echo Server MCP terminato
echo ===================================
pause
