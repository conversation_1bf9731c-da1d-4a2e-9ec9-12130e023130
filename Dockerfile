FROM python:3.11-slim

WORKDIR /app

# Copia i file di requisiti
COPY requirements.txt .

# Installa le dipendenze
RUN pip install --no-cache-dir -r requirements.txt

# Copia il resto del codice dell'applicazione
COPY . .

# Crea le directory necessarie
RUN mkdir -p uploads/temp flask_sessions

# Esponi la porta su cui l'app Flask sarà in esecuzione
EXPOSE 5001

# Comando per avviare l'applicazione
CMD ["python", "app.py"]
