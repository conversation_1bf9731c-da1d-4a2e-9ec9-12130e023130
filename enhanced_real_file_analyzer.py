#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced Real File Analyzer con integrazione Supabase e MCP.
Versione avanzata del Real File Analyzer con persistenza cloud e analisi AI.
"""

import os
import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import pandas as pd

# Import dei moduli esistenti
from real_file_analyzer import RealFileAnalyzer
from universal_file_reader import UniversalFileReader

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedRealFileAnalyzer(RealFileAnalyzer):
    """
    Analizzatore di file reali potenziato con:
    - Integrazione Supabase per persistenza
    - Integrazione MCP per analisi AI
    - Cache intelligente
    - Analisi predittiva
    """

    def __init__(self, use_supabase: bool = True, use_mcp: bool = True):
        """
        Inizializza l'analizzatore potenziato.

        Args:
            use_supabase: Se utilizzare Supabase per la persistenza
            use_mcp: Se utilizzare MCP per l'analisi AI
        """
        # Inizializza il parent
        super().__init__()

        self.use_supabase = use_supabase
        self.use_mcp = use_mcp
        self.supabase_manager = None
        self.mcp_client = None

        # Inizializza Supabase se richiesto
        if self.use_supabase:
            try:
                from supabase_integration import supabase_manager
                self.supabase_manager = supabase_manager
                if not self.supabase_manager.is_connected:
                    logger.warning("Supabase non connesso, utilizzo solo analisi locale")
                    self.use_supabase = False
            except ImportError:
                logger.warning("Supabase non disponibile, utilizzo solo analisi locale")
                self.use_supabase = False

        # Inizializza MCP se richiesto
        if self.use_mcp:
            try:
                from mcp_client import MCPClient
                self.mcp_client = MCPClient()
                if not self.mcp_client.is_available:
                    logger.warning("MCP non disponibile, utilizzo solo analisi locale")
                    self.use_mcp = False
            except ImportError:
                logger.warning("MCP Client non disponibile, utilizzo solo analisi locale")
                self.use_mcp = False

        logger.info(f"Enhanced Real File Analyzer inizializzato (Supabase: {self.use_supabase}, MCP: {self.use_mcp})")

    def analyze_file_enhanced(self, file_path: str, save_to_cloud: bool = True) -> Dict[str, Any]:
        """
        Analizza un file con funzionalità avanzate.

        Args:
            file_path: Percorso del file da analizzare
            save_to_cloud: Se salvare i risultati su Supabase

        Returns:
            Dict: Risultati dell'analisi avanzata
        """
        logger.info(f"🔍 Analisi avanzata file: {file_path}")

        # Analisi base usando il parent
        base_result = self.analyze_file(file_path)

        # Aggiungi informazioni avanzate
        enhanced_result = base_result.copy()
        enhanced_result.update({
            "analysis_mode": "enhanced",
            "supabase_enabled": self.use_supabase,
            "mcp_enabled": self.use_mcp,
            "ai_insights": {},
            "cloud_storage": {},
            "performance_metrics": {}
        })

        # Analisi AI con MCP se disponibile
        if self.use_mcp and base_result["success"]:
            ai_insights = self._get_ai_insights(file_path, base_result)
            enhanced_result["ai_insights"] = ai_insights

        # Salvataggio su cloud se richiesto
        if save_to_cloud and self.use_supabase and base_result["success"]:
            cloud_result = self._save_to_cloud(file_path, enhanced_result)
            enhanced_result["cloud_storage"] = cloud_result

        # Metriche di performance
        enhanced_result["performance_metrics"] = self._calculate_performance_metrics(enhanced_result)

        logger.info(f"✅ Analisi avanzata completata: {enhanced_result['detected_type']} (confidenza: {enhanced_result['confidence_score']:.2f})")

        return enhanced_result

    def _get_ai_insights(self, file_path: str, base_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Ottiene insights AI tramite MCP.

        Args:
            file_path: Percorso del file
            base_result: Risultati dell'analisi base

        Returns:
            Dict: Insights AI
        """
        ai_insights = {
            "available": False,
            "insights": [],
            "recommendations": [],
            "quality_score": 0.0,
            "error": None
        }

        if not self.use_mcp or not self.mcp_client:
            ai_insights["error"] = "MCP non disponibile"
            return ai_insights

        try:
            # Prepara i dati per l'analisi AI
            analysis_data = {
                "file_type": base_result["detected_type"],
                "confidence": base_result["confidence_score"],
                "columns": base_result.get("column_names", []),
                "data_quality": base_result.get("data_quality", {}),
                "file_size": base_result.get("file_size", 0),
                "rows": base_result.get("rows", 0)
            }

            # Richiesta di analisi AI al server MCP
            # Nota: Questo richiede un endpoint specifico nel server MCP
            ai_response = self.mcp_client.analyze_file_ai(analysis_data)

            if ai_response and "insights" in ai_response:
                ai_insights.update({
                    "available": True,
                    "insights": ai_response.get("insights", []),
                    "recommendations": ai_response.get("recommendations", []),
                    "quality_score": ai_response.get("quality_score", 0.0)
                })

        except Exception as e:
            logger.warning(f"⚠️ Errore analisi AI: {str(e)}")
            ai_insights["error"] = str(e)

        return ai_insights

    def _save_to_cloud(self, file_path: str, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Salva i risultati dell'analisi su Supabase.

        Args:
            file_path: Percorso del file
            analysis_result: Risultati dell'analisi

        Returns:
            Dict: Risultato del salvataggio cloud
        """
        cloud_result = {
            "saved": False,
            "file_upload_id": None,
            "processed_data_id": None,
            "error": None
        }

        if not self.use_supabase or not self.supabase_manager:
            cloud_result["error"] = "Supabase non disponibile"
            return cloud_result

        try:
            # Prepara informazioni file per Supabase
            file_info = {
                "filename": os.path.basename(file_path),
                "original_filename": os.path.basename(file_path),
                "file_type": analysis_result["detected_type"],
                "file_path": file_path,
                "file_size": analysis_result.get("file_size", 0),
                "status": "analyzed",
                "session_id": f"enhanced_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "mcp_file_id": f"enhanced_mcp_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            }

            # Salva file upload
            file_upload_id = self.supabase_manager.save_file_upload(file_info)
            if file_upload_id:
                cloud_result["file_upload_id"] = file_upload_id

                # Prepara dati elaborati
                processed_data = {
                    "analysis_result": analysis_result,
                    "analysis_timestamp": datetime.now().isoformat(),
                    "analyzer_version": "enhanced_v2.0"
                }

                # Prepara statistiche
                statistics = {
                    "confidence_score": analysis_result["confidence_score"],
                    "detected_type": analysis_result["detected_type"],
                    "columns_count": len(analysis_result.get("column_names", [])),
                    "rows_count": analysis_result.get("rows", 0),
                    "data_quality_score": self._calculate_quality_score(analysis_result)
                }

                # Salva dati elaborati
                processed_data_id = self.supabase_manager.save_processed_data(
                    file_upload_id=file_upload_id,
                    data_type="enhanced_analysis",
                    processed_data=processed_data,
                    statistics=statistics,
                    quality_report=analysis_result.get("data_quality", {})
                )

                if processed_data_id:
                    cloud_result.update({
                        "saved": True,
                        "processed_data_id": processed_data_id
                    })
                else:
                    cloud_result["error"] = "Errore salvataggio dati elaborati"
            else:
                cloud_result["error"] = "Errore salvataggio file upload"

        except Exception as e:
            logger.warning(f"⚠️ Errore salvataggio cloud: {str(e)}")
            cloud_result["error"] = str(e)

        return cloud_result

    def _calculate_quality_score(self, analysis_result: Dict[str, Any]) -> float:
        """
        Calcola un punteggio di qualità per l'analisi.

        Args:
            analysis_result: Risultati dell'analisi

        Returns:
            float: Punteggio di qualità (0.0-1.0)
        """
        score = 0.0

        # Punteggio base dalla confidenza
        confidence = analysis_result.get("confidence_score", 0.0)
        score += confidence * 0.4

        # Punteggio dalla completezza dei dati
        data_quality = analysis_result.get("data_quality", {})
        if data_quality:
            completeness = data_quality.get("completeness_score", 0.0)
            score += completeness * 0.3

        # Punteggio dalla mappatura colonne
        column_mapping = analysis_result.get("column_mapping", {})
        if column_mapping:
            mapped_ratio = len([v for v in column_mapping.values() if v]) / max(len(column_mapping), 1)
            score += mapped_ratio * 0.2

        # Punteggio dalla presenza di raccomandazioni
        recommendations = analysis_result.get("recommendations", [])
        if recommendations:
            score += min(len(recommendations) * 0.02, 0.1)

        return min(score, 1.0)

    def _calculate_performance_metrics(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calcola metriche di performance per l'analisi.

        Args:
            analysis_result: Risultati dell'analisi

        Returns:
            Dict: Metriche di performance
        """
        return {
            "analysis_timestamp": datetime.now().isoformat(),
            "quality_score": self._calculate_quality_score(analysis_result),
            "features_detected": len(analysis_result.get("column_mapping", {})),
            "recommendations_count": len(analysis_result.get("recommendations", [])),
            "ai_insights_available": analysis_result.get("ai_insights", {}).get("available", False),
            "cloud_storage_success": analysis_result.get("cloud_storage", {}).get("saved", False)
        }

    def get_analysis_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Recupera la cronologia delle analisi da Supabase.

        Args:
            limit: Numero massimo di record da recuperare

        Returns:
            List: Lista delle analisi precedenti
        """
        if not self.use_supabase or not self.supabase_manager:
            logger.warning("Supabase non disponibile per recuperare cronologia")
            return []

        try:
            file_uploads = self.supabase_manager.get_file_uploads(limit=limit)
            return file_uploads
        except Exception as e:
            logger.error(f"❌ Errore recupero cronologia: {str(e)}")
            return []

    def compare_analyses(self, file_path1: str, file_path2: str) -> Dict[str, Any]:
        """
        Confronta l'analisi di due file.

        Args:
            file_path1: Primo file da confrontare
            file_path2: Secondo file da confrontare

        Returns:
            Dict: Risultato del confronto
        """
        logger.info(f"🔍 Confronto analisi: {file_path1} vs {file_path2}")

        # Analizza entrambi i file
        analysis1 = self.analyze_file_enhanced(file_path1, save_to_cloud=False)
        analysis2 = self.analyze_file_enhanced(file_path2, save_to_cloud=False)

        # Calcola differenze
        comparison = {
            "file1": {
                "path": file_path1,
                "type": analysis1["detected_type"],
                "confidence": analysis1["confidence_score"]
            },
            "file2": {
                "path": file_path2,
                "type": analysis2["detected_type"],
                "confidence": analysis2["confidence_score"]
            },
            "similarities": [],
            "differences": [],
            "compatibility_score": 0.0
        }

        # Confronta tipi
        if analysis1["detected_type"] == analysis2["detected_type"]:
            comparison["similarities"].append("Stesso tipo di file rilevato")
            compatibility_score = 0.5
        else:
            comparison["differences"].append(f"Tipi diversi: {analysis1['detected_type']} vs {analysis2['detected_type']}")
            compatibility_score = 0.0

        # Confronta colonne
        cols1 = set(analysis1.get("column_names", []))
        cols2 = set(analysis2.get("column_names", []))
        common_cols = cols1.intersection(cols2)

        if common_cols:
            comparison["similarities"].append(f"Colonne comuni: {list(common_cols)}")
            compatibility_score += len(common_cols) / max(len(cols1.union(cols2)), 1) * 0.5

        comparison["compatibility_score"] = min(compatibility_score, 1.0)

        logger.info(f"✅ Confronto completato: compatibilità {comparison['compatibility_score']:.2f}")

        return comparison

# Istanza globale dell'analizzatore potenziato
enhanced_analyzer = EnhancedRealFileAnalyzer()
