#!/usr/bin/env python3
"""
Test processori corretti con Universal File Reader
"""

import sys
sys.path.append('.')

from teamviewer_processor import TeamViewerProcessor
from vehicle_registry_processor import VehicleRegistryProcessor

def test_fixed_processors():
    print("🔧 Test processori corretti...")
    
    # Test TeamViewer Processor
    print("\n🖥️ Test TeamViewer Processor:")
    try:
        processor = TeamViewerProcessor()
        df = processor.process_teamviewer_file('test_raw_file.csv')
        print(f"✅ Elaborazione completata: {df.shape[0]} righe, {df.shape[1]} colonne")
        print(f"📋 Colonne elaborate: {df.columns.tolist()}")
        
        # Test statistiche
        stats = processor.generate_summary_stats(df)
        print(f"📊 Statistiche: {len(stats)} metriche")
        for key, value in stats.items():
            print(f"  - {key}: {value}")
            
    except Exception as e:
        print(f"❌ Errore TeamViewer: {e}")
        import traceback
        traceback.print_exc()
    
    # Test Vehicle Registry Processor
    print("\n🚗 Test Vehicle Registry Processor:")
    try:
        processor = VehicleRegistryProcessor()
        df = processor.process_vehicle_registry_file('test_registro_auto.csv')
        print(f"✅ Elaborazione completata: {df.shape[0]} righe, {df.shape[1]} colonne")
        print(f"📋 Colonne elaborate: {df.columns.tolist()}")
        
        # Test statistiche
        stats = processor.generate_summary_stats(df)
        print(f"📊 Statistiche: {len(stats)} metriche")
        for key, value in stats.items():
            print(f"  - {key}: {value}")
            
    except Exception as e:
        print(f"❌ Errore Vehicle Registry: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fixed_processors()
