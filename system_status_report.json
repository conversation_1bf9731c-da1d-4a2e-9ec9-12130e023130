{"timestamp": "2025-05-22T17:46:24.793051", "overall_status": "error", "components": {"python": {"status": "ok", "version": "3.13.3", "details": "Python runtime attivo"}, "flask": {"status": "ok", "version": "2.2.5", "details": "Flask framework disponibile"}, "fastapi": {"status": "ok", "version": "0.104.1", "details": "FastAPI framework disponibile"}, "pandas": {"status": "ok", "version": "2.2.3", "details": "Pandas per elaborazione dati disponibile"}, "plotly": {"status": "ok", "version": "5.16.1", "details": "Plotly per grafici disponibile"}, "file_app": {"status": "ok", "details": "Applicazione principale Flask presente"}, "file_mcp_server_main": {"status": "ok", "details": "Server MCP presente"}, "file_real_file_analyzer": {"status": "ok", "details": "Analizzatore file reali presente"}, "file_config_manager": {"status": "ok", "details": "Gestore configurazioni presente"}}, "integrations": {"supabase": {"status": "warning", "connected": true, "test_passed": false, "details": "<PERSON><PERSON><PERSON> ma test fallito"}, "mcp": {"status": "error", "error": "'bool' object is not callable"}, "openrouter": {"status": "ok", "available": true, "details": "OpenRouter API disponibile"}, "enhanced_config": {"status": "ok", "details": "Enhanced Config Manager attivo", "system_info": {"config_version": "1.0", "last_updated": "2025-05-22T16:06:18.715311", "local_config_exists": true, "supabase_enabled": true, "supabase_connected": true, "total_config_keys": 7, "employee_count": 1, "supabase_test": false}}}, "performance": {"disk_space": {"total_gb": 237.72, "used_gb": 216.64, "free_gb": 21.08, "usage_percent": 91.1}, "memory": {"info": "psutil non disponibile per monitoraggio memoria"}, "uploads": {"files_count": 0, "total_size_mb": 0.0}}, "recommendations": ["Verificare connessione Supabase e variabili d'ambiente", "Spazio disco quasi esaurito, liberare spazio"], "status_summary": {"ok": 11, "warning": 1, "error": 1, "total": 13}}