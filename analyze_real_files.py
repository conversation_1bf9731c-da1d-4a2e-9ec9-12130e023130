#!/usr/bin/env python3
"""
Analisi dei file reali nella cartella uploads
"""

import pandas as pd
import os
import sys
sys.path.append('.')

from universal_file_reader import universal_reader

def analyze_real_files():
    print("🔍 ANALISI FILE REALI NELLA CARTELLA UPLOADS")
    print("=" * 60)
    
    uploads_dir = "uploads"
    
    # Lista tutti i file nella cartella uploads
    files = [f for f in os.listdir(uploads_dir) if f.endswith(('.csv', '.xlsx', '.xls', '.CSV'))]
    
    for file_name in files:
        if file_name == "Controlli quotidiani.xlsx":
            continue  # Già analizzato
            
        file_path = os.path.join(uploads_dir, file_name)
        print(f"\n📁 FILE: {file_name}")
        print("-" * 40)
        
        try:
            # Usa Universal File Reader
            df, file_info = universal_reader.read_file(file_path)
            
            if file_info['success']:
                print(f"✅ Lettura riuscita:")
                print(f"   - Righe: {file_info['rows']}")
                print(f"   - Colonne: {file_info['columns']}")
                print(f"   - Separatore: '{file_info['separator']}'")
                print(f"   - Encoding: {file_info['encoding']}")
                
                print(f"\n📋 Colonne:")
                for i, col in enumerate(df.columns, 1):
                    print(f"   {i:2d}. {col}")
                
                if not df.empty:
                    print(f"\n📊 Prime 2 righe di dati:")
                    for i, (idx, row) in enumerate(df.head(2).iterrows()):
                        print(f"   Riga {i+1}:")
                        for col in df.columns[:5]:  # Solo prime 5 colonne
                            value = row[col]
                            if pd.isna(value):
                                value = "NaN"
                            elif isinstance(value, str) and len(value) > 50:
                                value = value[:50] + "..."
                            print(f"     {col}: {value}")
                        if len(df.columns) > 5:
                            print(f"     ... e altre {len(df.columns)-5} colonne")
                        print()
            else:
                print(f"❌ Errore lettura: {file_info.get('error', 'Errore sconosciuto')}")
                
        except Exception as e:
            print(f"❌ Errore nell'analisi: {e}")
    
    print("\n" + "=" * 60)
    print("✅ Analisi completata!")

if __name__ == "__main__":
    analyze_real_files()
