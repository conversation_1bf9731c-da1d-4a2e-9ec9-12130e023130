#!/usr/bin/env python3
"""
Test di debug specifico per l'endpoint /api/agents/status
"""

import requests
import json

def test_agents_status_debug():
    """Test debug per endpoint agents status"""
    
    print("🔍 DEBUG ENDPOINT /api/agents/status")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test 1: Verifica connettività
    print("1️⃣ Test connettività base...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   Homepage: {response.status_code} ({'✅' if response.status_code == 200 else '❌'})")
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
        return
    
    # Test 2: Test endpoint che funziona per confronto
    print("\n2️⃣ Test endpoint funzionante /agents/list...")
    try:
        response = requests.get(f"{base_url}/agents/list", timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Agenti trovati: {len(data.get('agents', []))}")
        else:
            print(f"   ❌ Errore: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    # Test 3: Test endpoint problematico /api/agents/status
    print("\n3️⃣ Test endpoint problematico /api/agents/status...")
    try:
        response = requests.get(f"{base_url}/api/agents/status", timeout=5)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Endpoint funziona!")
            try:
                data = response.json()
                print(f"   📄 Risposta: {json.dumps(data, indent=2)}")
            except:
                print("   📄 Risposta non JSON")
        elif response.status_code == 404:
            print("   ❌ Endpoint NON TROVATO (404)")
            try:
                data = response.json()
                print(f"   📄 Errore: {json.dumps(data, indent=2)}")
            except:
                print("   📄 Nessuna risposta JSON")
        elif response.status_code == 503:
            print("   ⚠️ Servizio non disponibile (503)")
            try:
                data = response.json()
                print(f"   📄 Risposta: {json.dumps(data, indent=2)}")
                if 'error' in data:
                    print(f"   🔍 Motivo: {data['error']}")
            except:
                print("   📄 Nessuna risposta JSON")
        else:
            print(f"   ⚠️ Status inaspettato: {response.status_code}")
            try:
                data = response.json()
                print(f"   📄 Risposta: {json.dumps(data, indent=2)}")
            except:
                print("   📄 Nessuna risposta JSON")
                
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    # Test 4: Test altri endpoint API agenti
    print("\n4️⃣ Test altri endpoint API agenti...")
    
    other_endpoints = [
        ("GET", "/api/agents/execute", "Execute agenti (dovrebbe essere POST)"),
        ("GET", "/api/automation/rules", "Regole automazione"),
    ]
    
    for method, path, description in other_endpoints:
        try:
            print(f"   Test {method} {path} ({description})...")
            
            response = requests.get(f"{base_url}{path}", timeout=5)
            print(f"      Status: {response.status_code}")
            
            if response.status_code == 200:
                print("      ✅ OK")
            elif response.status_code == 404:
                print("      ❌ Non trovato")
            elif response.status_code == 405:
                print("      ⚠️ Metodo non consentito (normale per POST)")
            elif response.status_code == 503:
                print("      ⚠️ Servizio non disponibile")
            else:
                print(f"      ⚠️ Status: {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ Errore: {str(e)}")
    
    # Test 5: Verifica lista endpoint disponibili
    print("\n5️⃣ Verifica endpoint disponibili...")
    try:
        response = requests.get(f"{base_url}/api/endpoints", timeout=5)
        if response.status_code == 200:
            data = response.json()
            endpoints = data.get('endpoints', [])
            
            # Cerca endpoint agenti
            agent_endpoints = [ep for ep in endpoints if 'agent' in ep.lower()]
            print(f"   📋 Endpoint agenti trovati: {len(agent_endpoints)}")
            for ep in agent_endpoints:
                print(f"      - {ep}")
                
            # Cerca specificamente /api/agents/status
            status_endpoints = [ep for ep in endpoints if '/api/agents/status' in ep]
            if status_endpoints:
                print(f"   ✅ /api/agents/status trovato in lista: {status_endpoints}")
            else:
                print("   ❌ /api/agents/status NON trovato in lista endpoint")
        else:
            print(f"   ❌ Errore lista endpoint: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    print("\n" + "=" * 60)
    print("📊 ANALISI RISULTATI")
    print("=" * 60)
    print("Se /api/agents/status restituisce 503 con 'Agenti AI non disponibili':")
    print("- Il problema è AI_AGENTS_AVAILABLE = False")
    print("- Errore di importazione ai_agents_framework o intelligent_automation")
    print("- Soluzione: verificare dipendenze e importazioni")
    print()
    print("Se /api/agents/status restituisce 404:")
    print("- La route non è registrata correttamente")
    print("- Possibile conflitto con blueprint")
    print("- Soluzione: verificare registrazione route in app.py")
    
    return True

if __name__ == "__main__":
    test_agents_status_debug()
