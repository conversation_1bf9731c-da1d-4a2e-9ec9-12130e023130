#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FASE 6: Test Stabilità App Roberto
Test di stabilità essenziali per verificare il sistema.
Versione: 1.0.0 - FASE 6 Testing e Stabilizzazione
"""

import sys
import os
from datetime import datetime

def test_imports():
    """Test importazioni essenziali."""
    print("=== TEST IMPORTS ===")
    
    results = []
    
    # Test app
    try:
        from app import app
        print("✅ app: OK")
        results.append(("app", True, "Import successful"))
    except Exception as e:
        print(f"❌ app: {str(e)}")
        results.append(("app", False, str(e)))
    
    # Test health_monitor
    try:
        from health_monitor import health_monitor
        print("✅ health_monitor: OK")
        results.append(("health_monitor", True, "Import successful"))
    except Exception as e:
        print(f"❌ health_monitor: {str(e)}")
        results.append(("health_monitor", False, str(e)))
    
    return results

def test_files():
    """Test file essenziali."""
    print("\n=== TEST FILES ===")
    
    results = []
    
    essential_files = [
        'app.py',
        'health_monitor.py',
        'static/css/style.css',
        'static/css/dark-theme.css',
        'static/js/theme-manager.js',
        'static/js/ui-enhancements.js',
        'templates/base.html',
        'templates/index.html'
    ]
    
    for file_path in essential_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}: OK")
            results.append((file_path, True, "File exists"))
        else:
            print(f"❌ {file_path}: Missing")
            results.append((file_path, False, "File missing"))
    
    return results

def test_app_basic():
    """Test di base dell'app."""
    print("\n=== TEST APP BASIC ===")
    
    results = []
    
    try:
        from app import app
        
        # Test configurazione
        app.config['TESTING'] = True
        print("✅ App configuration: OK")
        results.append(("app_config", True, "Configuration successful"))
        
        # Test client
        with app.test_client() as client:
            # Test home page
            try:
                response = client.get('/')
                if response.status_code == 200:
                    print("✅ Home page: OK")
                    results.append(("home_page", True, f"Status: {response.status_code}"))
                else:
                    print(f"⚠️ Home page: {response.status_code}")
                    results.append(("home_page", False, f"Status: {response.status_code}"))
            except Exception as e:
                print(f"❌ Home page: {str(e)}")
                results.append(("home_page", False, str(e)))
            
            # Test API health
            try:
                response = client.get('/api/health')
                if response.status_code == 200:
                    print("✅ API health: OK")
                    results.append(("api_health", True, f"Status: {response.status_code}"))
                else:
                    print(f"⚠️ API health: {response.status_code}")
                    results.append(("api_health", False, f"Status: {response.status_code}"))
            except Exception as e:
                print(f"❌ API health: {str(e)}")
                results.append(("api_health", False, str(e)))
    
    except Exception as e:
        print(f"❌ App Basic Error: {str(e)}")
        results.append(("app_basic", False, str(e)))
    
    return results

def test_health_monitor_basic():
    """Test di base del health monitor."""
    print("\n=== TEST HEALTH MONITOR BASIC ===")
    
    results = []
    
    try:
        from health_monitor import health_monitor
        
        # Test controllo salute
        try:
            health = health_monitor.check_system_health()
            print(f"✅ System health check: {health.overall_status}")
            results.append(("health_check", True, f"Status: {health.overall_status}"))
        except Exception as e:
            print(f"❌ Health check: {str(e)}")
            results.append(("health_check", False, str(e)))
        
        # Test report
        try:
            report = health_monitor.get_health_report()
            print(f"✅ Health report: {len(report)} keys")
            results.append(("health_report", True, f"Keys: {len(report)}"))
        except Exception as e:
            print(f"❌ Health report: {str(e)}")
            results.append(("health_report", False, str(e)))
    
    except Exception as e:
        print(f"❌ Health Monitor Error: {str(e)}")
        results.append(("health_monitor_basic", False, str(e)))
    
    return results

def main():
    """Funzione principale."""
    print("🚀 TEST STABILITÀ - APP ROBERTO")
    print("=" * 50)
    start_time = datetime.now()
    print(f"Start: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    all_results = []
    
    # Esegui test
    test_functions = [
        ("Imports", test_imports),
        ("Files", test_files),
        ("App Basic", test_app_basic),
        ("Health Monitor Basic", test_health_monitor_basic)
    ]
    
    for category, test_func in test_functions:
        try:
            results = test_func()
            all_results.extend([(category, r[0], r[1], r[2]) for r in results])
        except Exception as e:
            print(f"❌ {category} Error: {str(e)}")
            all_results.append((category, "error", False, str(e)))
    
    # Report finale
    print("\n" + "=" * 50)
    print("📋 STABILITY REPORT")
    print("=" * 50)
    
    total_tests = len(all_results)
    passed = sum(1 for r in all_results if r[2])
    failed = total_tests - passed
    
    duration = (datetime.now() - start_time).total_seconds()
    
    print(f"Duration: {duration:.2f}s")
    print(f"Tests: {total_tests}")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print()
    
    # Raggruppa per categoria
    categories = {}
    for category, test_name, success, details in all_results:
        if category not in categories:
            categories[category] = []
        categories[category].append((test_name, success, details))
    
    # Mostra risultati per categoria
    for category, tests in categories.items():
        category_passed = sum(1 for t in tests if t[1])
        category_total = len(tests)
        
        print(f"📂 {category}: {category_passed}/{category_total}")
        
        for test_name, success, details in tests:
            status_icon = "✅" if success else "❌"
            print(f"   {status_icon} {test_name}: {details}")
        print()
    
    # Valutazione
    success_rate = (passed / total_tests * 100) if total_tests > 0 else 0
    
    if success_rate >= 95:
        grade = "🟢 EXCELLENT"
        stability = "VERY STABLE"
    elif success_rate >= 85:
        grade = "🟡 GOOD"
        stability = "STABLE"
    elif success_rate >= 70:
        grade = "🟠 FAIR"
        stability = "MOSTLY STABLE"
    else:
        grade = "🔴 POOR"
        stability = "UNSTABLE"
    
    print(f"🎯 Grade: {grade} ({success_rate:.1f}%)")
    print(f"🏗️ Stability: {stability}")
    
    # Raccomandazioni
    print("\n💡 Recommendations:")
    if failed == 0:
        print("   ✅ All tests passed - system is stable")
        print("   ✅ Ready for production use")
        print("   ✅ Continue monitoring with health system")
    elif failed <= 2:
        print("   ⚠️ Minor issues detected - review failed tests")
        print("   ✅ System mostly stable")
    else:
        print("   ❌ Multiple issues detected - immediate attention required")
        print("   ❌ Review system configuration")
        print("   ❌ Fix critical components before production")
    
    # Stato finale
    if success_rate >= 85:
        print("\n🎉 SYSTEM STABILITY: ACCEPTABLE")
        print("✅ App Roberto is ready for use!")
        exit_code = 0
    else:
        print("\n⚠️ SYSTEM STABILITY: NEEDS ATTENTION")
        print("❌ Review and fix issues before proceeding")
        exit_code = 1
    
    print(f"\nFASE 6 Testing e Stabilizzazione: {'COMPLETATA' if exit_code == 0 else 'RICHIEDE ATTENZIONE'}")
    
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
