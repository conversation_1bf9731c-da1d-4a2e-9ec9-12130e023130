#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
✅ COHERENCE CHECKER - APP ROBERTO
Sistema di controlli di coerenza post-inserimento dati.

IMPLEMENTA TASK 6 DEL PROMPT:
- Controlli di coerenza Dipendente-Attività-Auto
- Controlli di coerenza Dipendente-Assenze-Attività  
- Controlli di coerenza Permessi-Attività
- Generazione statistiche finali
"""

import os
import sys
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CoherenceChecker:
    """
    Sistema di controlli di coerenza per i dati inseriti.
    Implementa i controlli richiesti nel TASK 6 del prompt.
    """
    
    def __init__(self, db_manager=None):
        """
        Inizializza il checker di coerenza.
        
        Args:
            db_manager: Istanza di AdvancedDatabaseManager
        """
        self.db_manager = db_manager
        
        # Configurazioni controlli
        self.coherence_rules = {
            'vehicle_activity_coherence': {
                'name': 'Coerenza Dipendente-Attività-Auto',
                'description': 'Verifica che dipendenti con auto abbiano attività registrate',
                'critical': True
            },
            'absence_activity_coherence': {
                'name': 'Coerenza Dipendente-Assenze-Attività',
                'description': 'Verifica che dipendenti assenti non abbiano attività',
                'critical': True
            },
            'permission_activity_coherence': {
                'name': 'Coerenza Permessi-Attività',
                'description': 'Verifica coerenza tra permessi e attività registrate',
                'critical': False
            }
        }
        
        logger.info("CoherenceChecker inizializzato")

    def run_all_coherence_checks(self) -> Dict[str, Any]:
        """
        Esegue tutti i controlli di coerenza disponibili.
        
        Returns:
            Dict con risultati di tutti i controlli
        """
        try:
            sys.stdout.write("✅ === AVVIO CONTROLLI DI COERENZA ===\n")
            sys.stdout.flush()
            
            results = {
                'timestamp': datetime.now().isoformat(),
                'checks_performed': [],
                'issues_found': [],
                'statistics': {},
                'overall_coherence_score': 0.0,
                'recommendations': []
            }
            
            # CONTROLLO 1: Coerenza Dipendente-Attività-Auto
            vehicle_check = self.check_vehicle_activity_coherence()
            results['checks_performed'].append(vehicle_check)
            if vehicle_check.get('issues'):
                results['issues_found'].extend(vehicle_check['issues'])
            
            # CONTROLLO 2: Coerenza Dipendente-Assenze-Attività
            absence_check = self.check_absence_activity_coherence()
            results['checks_performed'].append(absence_check)
            if absence_check.get('issues'):
                results['issues_found'].extend(absence_check['issues'])
            
            # CONTROLLO 3: Coerenza Permessi-Attività
            permission_check = self.check_permission_activity_coherence()
            results['checks_performed'].append(permission_check)
            if permission_check.get('issues'):
                results['issues_found'].extend(permission_check['issues'])
            
            # GENERAZIONE STATISTICHE
            statistics = self.generate_final_statistics()
            results['statistics'] = statistics
            
            # CALCOLO SCORE COERENZA
            coherence_score = self.calculate_coherence_score(results)
            results['overall_coherence_score'] = coherence_score
            
            # GENERAZIONE RACCOMANDAZIONI
            recommendations = self.generate_recommendations(results)
            results['recommendations'] = recommendations
            
            sys.stdout.write(f"✅ Controlli completati. Score coerenza: {coherence_score:.1f}%\n")
            sys.stdout.flush()
            
            return results
            
        except Exception as e:
            logger.error(f"Errore controlli coerenza: {str(e)}")
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'checks_performed': [],
                'issues_found': [],
                'overall_coherence_score': 0.0
            }

    def check_vehicle_activity_coherence(self) -> Dict[str, Any]:
        """
        CONTROLLO 1: Verifica coerenza Dipendente-Attività-Auto.
        
        "Voglio verificare se un dipendente che risulta aver preso la macchina 
        in un tal giorno deve avere un'attività registrata in quel giorno, 
        in quelle ore e da quel cliente."
        
        Returns:
            Dict con risultati del controllo
        """
        try:
            sys.stdout.write("🚗 Controllo coerenza Dipendente-Attività-Auto...\n")
            sys.stdout.flush()
            
            if not self.db_manager:
                return {'check_name': 'vehicle_activity_coherence', 'error': 'Database manager non disponibile'}
            
            # Ottieni dati utilizzo veicoli
            vehicle_usage = self.db_manager.get_vehicle_usage_data()
            
            # Ottieni dati attività
            activities = self.db_manager.get_activities_data()
            
            issues = []
            checked_records = 0
            
            for usage in vehicle_usage:
                checked_records += 1
                
                # Estrai informazioni utilizzo veicolo
                employee_id = usage.get('technician_id')
                usage_date = usage.get('date')
                start_time = usage.get('start_time')
                end_time = usage.get('end_time')
                
                if not all([employee_id, usage_date]):
                    continue
                
                # Cerca attività corrispondenti
                matching_activities = [
                    activity for activity in activities
                    if (activity.get('technician_id') == employee_id and
                        activity.get('date') == usage_date)
                ]
                
                if not matching_activities:
                    issues.append({
                        'type': 'missing_activity_for_vehicle_usage',
                        'severity': 'high',
                        'employee_id': employee_id,
                        'date': usage_date,
                        'vehicle_usage': usage,
                        'description': f"Dipendente {employee_id} ha utilizzato veicolo il {usage_date} ma non ha attività registrate"
                    })
                
                # Verifica sovrapposizione orari se disponibili
                elif start_time and end_time:
                    time_overlap_found = False
                    for activity in matching_activities:
                        activity_start = activity.get('start_time')
                        activity_end = activity.get('end_time')
                        
                        if activity_start and activity_end:
                            # Verifica sovrapposizione temporale
                            if self._times_overlap(start_time, end_time, activity_start, activity_end):
                                time_overlap_found = True
                                break
                    
                    if not time_overlap_found:
                        issues.append({
                            'type': 'time_mismatch_vehicle_activity',
                            'severity': 'medium',
                            'employee_id': employee_id,
                            'date': usage_date,
                            'vehicle_usage': usage,
                            'activities': matching_activities,
                            'description': f"Dipendente {employee_id}: orari utilizzo veicolo non coincidono con attività"
                        })
            
            result = {
                'check_name': 'vehicle_activity_coherence',
                'description': 'Coerenza Dipendente-Attività-Auto',
                'records_checked': checked_records,
                'issues_count': len(issues),
                'issues': issues,
                'success': True
            }
            
            sys.stdout.write(f"   ✅ {checked_records} record controllati, {len(issues)} problemi trovati\n")
            sys.stdout.flush()
            
            return result
            
        except Exception as e:
            logger.error(f"Errore controllo veicoli-attività: {str(e)}")
            return {
                'check_name': 'vehicle_activity_coherence',
                'error': str(e),
                'success': False
            }

    def check_absence_activity_coherence(self) -> Dict[str, Any]:
        """
        CONTROLLO 2: Verifica coerenza Dipendente-Assenze-Attività.
        
        "Se un dipendente è assente (per permesso, malattia, ferie) 
        non può avere attività registrate in quel giorno."
        
        Returns:
            Dict con risultati del controllo
        """
        try:
            sys.stdout.write("🏠 Controllo coerenza Dipendente-Assenze-Attività...\n")
            sys.stdout.flush()
            
            if not self.db_manager:
                return {'check_name': 'absence_activity_coherence', 'error': 'Database manager non disponibile'}
            
            # Ottieni dati timbrature/assenze
            timesheets = self.db_manager.get_timesheets_data()
            
            # Ottieni dati attività
            activities = self.db_manager.get_activities_data()
            
            issues = []
            checked_records = 0
            
            # Filtra solo le assenze
            absences = [
                ts for ts in timesheets 
                if ts.get('absence_type') in ['Permesso', 'Malattia', 'Ferie', 'permesso', 'malattia', 'ferie']
            ]
            
            for absence in absences:
                checked_records += 1
                
                employee_id = absence.get('technician_id')
                absence_date = absence.get('date')
                absence_type = absence.get('absence_type')
                
                if not all([employee_id, absence_date]):
                    continue
                
                # Cerca attività nello stesso giorno
                conflicting_activities = [
                    activity for activity in activities
                    if (activity.get('technician_id') == employee_id and
                        activity.get('date') == absence_date)
                ]
                
                if conflicting_activities:
                    issues.append({
                        'type': 'activity_during_absence',
                        'severity': 'high',
                        'employee_id': employee_id,
                        'date': absence_date,
                        'absence_type': absence_type,
                        'conflicting_activities': conflicting_activities,
                        'description': f"Dipendente {employee_id} assente ({absence_type}) il {absence_date} ma ha {len(conflicting_activities)} attività registrate"
                    })
            
            result = {
                'check_name': 'absence_activity_coherence',
                'description': 'Coerenza Dipendente-Assenze-Attività',
                'records_checked': checked_records,
                'issues_count': len(issues),
                'issues': issues,
                'success': True
            }
            
            sys.stdout.write(f"   ✅ {checked_records} assenze controllate, {len(issues)} conflitti trovati\n")
            sys.stdout.flush()
            
            return result
            
        except Exception as e:
            logger.error(f"Errore controllo assenze-attività: {str(e)}")
            return {
                'check_name': 'absence_activity_coherence',
                'error': str(e),
                'success': False
            }

    def check_permission_activity_coherence(self) -> Dict[str, Any]:
        """
        CONTROLLO 3: Verifica coerenza Permessi-Attività.
        
        "Verifica che i permessi siano coerenti con le attività."
        
        Returns:
            Dict con risultati del controllo
        """
        try:
            sys.stdout.write("📝 Controllo coerenza Permessi-Attività...\n")
            sys.stdout.flush()
            
            if not self.db_manager:
                return {'check_name': 'permission_activity_coherence', 'error': 'Database manager non disponibile'}
            
            # Ottieni dati richieste permessi
            requests = self.db_manager.get_requests_data()
            
            # Ottieni dati attività
            activities = self.db_manager.get_activities_data()
            
            issues = []
            checked_records = 0
            
            # Filtra solo i permessi approvati
            approved_permissions = [
                req for req in requests 
                if (req.get('request_type') in ['Permesso', 'permesso'] and
                    req.get('status') in ['Approvato', 'approvato', 'approved'])
            ]
            
            for permission in approved_permissions:
                checked_records += 1
                
                employee_id = permission.get('technician_id')
                permission_date = permission.get('date')
                
                if not all([employee_id, permission_date]):
                    continue
                
                # Cerca attività nello stesso giorno
                conflicting_activities = [
                    activity for activity in activities
                    if (activity.get('technician_id') == employee_id and
                        activity.get('date') == permission_date)
                ]
                
                if conflicting_activities:
                    issues.append({
                        'type': 'activity_during_permission',
                        'severity': 'medium',
                        'employee_id': employee_id,
                        'date': permission_date,
                        'permission': permission,
                        'conflicting_activities': conflicting_activities,
                        'description': f"Dipendente {employee_id} ha permesso approvato il {permission_date} ma ha {len(conflicting_activities)} attività registrate"
                    })
            
            result = {
                'check_name': 'permission_activity_coherence',
                'description': 'Coerenza Permessi-Attività',
                'records_checked': checked_records,
                'issues_count': len(issues),
                'issues': issues,
                'success': True
            }
            
            sys.stdout.write(f"   ✅ {checked_records} permessi controllati, {len(issues)} conflitti trovati\n")
            sys.stdout.flush()
            
            return result
            
        except Exception as e:
            logger.error(f"Errore controllo permessi-attività: {str(e)}")
            return {
                'check_name': 'permission_activity_coherence',
                'error': str(e),
                'success': False
            }

    def generate_final_statistics(self) -> Dict[str, Any]:
        """
        Genera statistiche finali come richiesto nel TASK 6.
        
        Returns:
            Dict con statistiche complete
        """
        try:
            sys.stdout.write("📊 Generazione statistiche finali...\n")
            sys.stdout.flush()
            
            if not self.db_manager:
                return {'error': 'Database manager non disponibile'}
            
            statistics = {
                'costs_effort': self._calculate_costs_effort(),
                'employee_productivity': self._calculate_employee_productivity(),
                'clients_tickets': self._calculate_clients_tickets(),
                'vehicle_usage': self._calculate_vehicle_usage(),
                'generation_timestamp': datetime.now().isoformat()
            }
            
            sys.stdout.write("   ✅ Statistiche generate con successo\n")
            sys.stdout.flush()
            
            return statistics
            
        except Exception as e:
            logger.error(f"Errore generazione statistiche: {str(e)}")
            return {'error': str(e)}

    def _calculate_costs_effort(self) -> Dict[str, Any]:
        """Calcola costi/impegno: Ore lavorate per progetto/cliente/dipendente."""
        try:
            activities = self.db_manager.get_activities_data()
            
            # Calcoli per progetto
            project_hours = {}
            client_hours = {}
            employee_hours = {}
            
            for activity in activities:
                duration = activity.get('duration', 0)
                project_id = activity.get('project_id')
                client_id = activity.get('client_id')
                employee_id = activity.get('technician_id')
                
                if project_id:
                    project_hours[project_id] = project_hours.get(project_id, 0) + duration
                
                if client_id:
                    client_hours[client_id] = client_hours.get(client_id, 0) + duration
                
                if employee_id:
                    employee_hours[employee_id] = employee_hours.get(employee_id, 0) + duration
            
            return {
                'total_hours': sum(employee_hours.values()),
                'hours_by_project': project_hours,
                'hours_by_client': client_hours,
                'hours_by_employee': employee_hours,
                'top_projects': sorted(project_hours.items(), key=lambda x: x[1], reverse=True)[:5],
                'top_clients': sorted(client_hours.items(), key=lambda x: x[1], reverse=True)[:5]
            }
            
        except Exception as e:
            return {'error': str(e)}

    def _calculate_employee_productivity(self) -> Dict[str, Any]:
        """Calcola produttività dipendenti: Attività per dipendente, tempo medio per attività."""
        try:
            activities = self.db_manager.get_activities_data()
            
            employee_stats = {}
            
            for activity in activities:
                employee_id = activity.get('technician_id')
                duration = activity.get('duration', 0)
                
                if not employee_id:
                    continue
                
                if employee_id not in employee_stats:
                    employee_stats[employee_id] = {
                        'total_activities': 0,
                        'total_hours': 0,
                        'activities': []
                    }
                
                employee_stats[employee_id]['total_activities'] += 1
                employee_stats[employee_id]['total_hours'] += duration
                employee_stats[employee_id]['activities'].append(activity)
            
            # Calcola medie
            for employee_id, stats in employee_stats.items():
                if stats['total_activities'] > 0:
                    stats['avg_hours_per_activity'] = stats['total_hours'] / stats['total_activities']
                else:
                    stats['avg_hours_per_activity'] = 0
            
            return {
                'employee_statistics': employee_stats,
                'most_productive': max(employee_stats.items(), key=lambda x: x[1]['total_hours']) if employee_stats else None,
                'most_active': max(employee_stats.items(), key=lambda x: x[1]['total_activities']) if employee_stats else None
            }
            
        except Exception as e:
            return {'error': str(e)}

    def _calculate_clients_tickets(self) -> Dict[str, Any]:
        """Calcola clienti e ticket: Numero di ticket/attività per cliente, andamento nel tempo."""
        try:
            activities = self.db_manager.get_activities_data()
            
            client_stats = {}
            monthly_trends = {}
            
            for activity in activities:
                client_id = activity.get('client_id')
                date = activity.get('date')
                
                if not client_id:
                    continue
                
                # Statistiche per cliente
                if client_id not in client_stats:
                    client_stats[client_id] = {
                        'total_activities': 0,
                        'total_hours': 0,
                        'first_activity': date,
                        'last_activity': date
                    }
                
                client_stats[client_id]['total_activities'] += 1
                client_stats[client_id]['total_hours'] += activity.get('duration', 0)
                
                # Andamento mensile
                if date:
                    month_key = date[:7]  # YYYY-MM
                    if month_key not in monthly_trends:
                        monthly_trends[month_key] = 0
                    monthly_trends[month_key] += 1
            
            return {
                'client_statistics': client_stats,
                'monthly_trends': monthly_trends,
                'top_clients_by_activities': sorted(client_stats.items(), key=lambda x: x[1]['total_activities'], reverse=True)[:5],
                'top_clients_by_hours': sorted(client_stats.items(), key=lambda x: x[1]['total_hours'], reverse=True)[:5]
            }
            
        except Exception as e:
            return {'error': str(e)}

    def _calculate_vehicle_usage(self) -> Dict[str, Any]:
        """Calcola utilizzo auto: Chilometri percorsi, giorni di utilizzo."""
        try:
            vehicle_usage = self.db_manager.get_vehicle_usage_data()
            
            vehicle_stats = {}
            total_km = 0
            total_days = len(set(usage.get('date') for usage in vehicle_usage if usage.get('date')))
            
            for usage in vehicle_usage:
                vehicle_id = usage.get('vehicle_id')
                km = usage.get('kilometers', 0)
                
                if vehicle_id not in vehicle_stats:
                    vehicle_stats[vehicle_id] = {
                        'total_km': 0,
                        'usage_days': 0,
                        'total_usage': 0
                    }
                
                vehicle_stats[vehicle_id]['total_km'] += km
                vehicle_stats[vehicle_id]['usage_days'] += 1
                vehicle_stats[vehicle_id]['total_usage'] += 1
                total_km += km
            
            return {
                'vehicle_statistics': vehicle_stats,
                'total_kilometers': total_km,
                'total_usage_days': total_days,
                'most_used_vehicle': max(vehicle_stats.items(), key=lambda x: x[1]['total_usage']) if vehicle_stats else None,
                'highest_km_vehicle': max(vehicle_stats.items(), key=lambda x: x[1]['total_km']) if vehicle_stats else None
            }
            
        except Exception as e:
            return {'error': str(e)}

    def calculate_coherence_score(self, results: Dict[str, Any]) -> float:
        """Calcola score di coerenza generale."""
        try:
            total_checks = len(results.get('checks_performed', []))
            total_issues = len(results.get('issues_found', []))
            
            if total_checks == 0:
                return 0.0
            
            # Peso per severità
            severity_weights = {'high': 3, 'medium': 2, 'low': 1}
            weighted_issues = sum(
                severity_weights.get(issue.get('severity', 'low'), 1)
                for issue in results.get('issues_found', [])
            )
            
            # Calcola score (100% - penalità per problemi)
            max_possible_issues = total_checks * 10  # Assumiamo max 10 problemi per check
            penalty = min(weighted_issues / max_possible_issues * 100, 100)
            
            return max(100 - penalty, 0)
            
        except Exception as e:
            logger.error(f"Errore calcolo score: {str(e)}")
            return 0.0

    def generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """Genera raccomandazioni basate sui risultati."""
        recommendations = []
        
        issues = results.get('issues_found', [])
        
        # Raccomandazioni basate sui problemi trovati
        vehicle_issues = [i for i in issues if 'vehicle' in i.get('type', '')]
        if vehicle_issues:
            recommendations.append("Verificare e correggere le incongruenze tra utilizzo veicoli e attività registrate")
        
        absence_issues = [i for i in issues if 'absence' in i.get('type', '')]
        if absence_issues:
            recommendations.append("Rivedere le registrazioni di attività durante i giorni di assenza")
        
        permission_issues = [i for i in issues if 'permission' in i.get('type', '')]
        if permission_issues:
            recommendations.append("Verificare la coerenza tra permessi approvati e attività svolte")
        
        # Raccomandazioni generali
        if len(issues) == 0:
            recommendations.append("Ottimo! I dati sono coerenti. Continuare con il monitoraggio regolare")
        elif len(issues) < 5:
            recommendations.append("Pochi problemi rilevati. Correggere le incongruenze minori")
        else:
            recommendations.append("Molti problemi rilevati. Rivedere il processo di inserimento dati")
        
        return recommendations

    def _times_overlap(self, start1: str, end1: str, start2: str, end2: str) -> bool:
        """Verifica se due intervalli temporali si sovrappongono."""
        try:
            # Conversione semplificata - assumiamo formato HH:MM
            def time_to_minutes(time_str):
                if ':' in time_str:
                    hours, minutes = map(int, time_str.split(':'))
                    return hours * 60 + minutes
                return 0
            
            start1_min = time_to_minutes(start1)
            end1_min = time_to_minutes(end1)
            start2_min = time_to_minutes(start2)
            end2_min = time_to_minutes(end2)
            
            return not (end1_min <= start2_min or end2_min <= start1_min)
            
        except Exception:
            return False


def main():
    """Funzione principale per test."""
    checker = CoherenceChecker()
    
    print("✅ COHERENCE CHECKER - TEST")
    print("=" * 50)
    
    # Esegui controlli di coerenza
    results = checker.run_all_coherence_checks()
    
    # Salva risultati
    with open("coherence_check_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"📄 Risultati salvati: coherence_check_results.json")
    print(f"📊 Score coerenza: {results.get('overall_coherence_score', 0):.1f}%")


if __name__ == "__main__":
    main()
