@echo off
echo ===================================
echo Avvio definitivo dell'applicazione app-roberto
echo ===================================

set CLEAN_ENV=clean_env

if not exist %CLEAN_ENV% (
    echo Creazione nuovo ambiente virtuale pulito...
    python -m venv %CLEAN_ENV%

    echo Attivazione ambiente...
    call %CLEAN_ENV%\Scripts\activate

    echo Aggiornamento pip...
    python -m pip install --upgrade pip

    echo Installazione dipendenze base...
    pip install python-dotenv fastapi flask pandas cachelib

    echo Installazione plotly...
    pip install --no-deps plotly==5.16.1
    pip install tenacity packaging

    echo Installazione pywin32 per supporto Excel COM...
    pip install pywin32

    echo Installazione altre dipendenze...
    pip install -r requirements.txt
) else (
    echo Attivazione ambiente esistente...
    call %CLEAN_ENV%\Scripts\activate

    echo Verifica cachelib...
    pip show cachelib > nul 2>&1
    if %errorlevel% neq 0 (
        echo Installazione cachelib...
        pip install cachelib
    )

    echo Verifica pywin32...
    pip show pywin32 > nul 2>&1
    if %errorlevel% neq 0 (
        echo Installazione pywin32 per supporto Excel COM...
        pip install pywin32
    )
)

echo Impostazione variabili d'ambiente...
set OPENROUTER_API_KEY=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
set APP_URL=http://localhost:5001
set MCP_URL=http://localhost:8000

if not exist uploads mkdir uploads

echo Avvio server MCP...
start "MCP Server" cmd /k "call %CLEAN_ENV%\Scripts\activate && cd mcp_server && python run_server.py"

echo Attesa avvio server (5 secondi)...
ping 127.0.0.1 -n 6 > nul

echo Avvio applicazione principale...
start "App Roberto" cmd /k "call %CLEAN_ENV%\Scripts\activate && python app.py"

echo ===================================
echo Applicazione avviata!
echo - Flask: http://localhost:5000
echo - MCP: http://localhost:8000
echo ===================================
echo.
echo NOTA: Il messaggio sulle librerie COM non disponibili
echo e' solo un avviso, non un errore critico.
echo.
pause
