{"agents_dashboard": {"status_code": 200, "success": true, "content_length": 26774}, "agents_list": {"status_code": 200, "success": true, "agents_count": 2, "agents": ["data_cleaning", "export_management"]}, "agents_health": {"status_code": 200, "success": true, "orchestrator_status": "healthy"}, "data_cleaning_caps": {"status_code": 200, "success": true, "capabilities_count": 4, "capabilities": ["missing_value_imputation", "outlier_detection", "data_standardization", "duplicate_resolution"]}, "export_formats": {"status_code": 200, "success": true, "formats_count": 5, "formats": ["xlsx", "csv", "json", "pdf", "xml"], "export_types": ["standard", "filtered", "report", "dashboard"]}, "monitoring_health": {"status_code": 200, "success": true, "overall_status": "healthy", "checks": ["database", "agents", "system"]}, "monitoring_metrics": {"status_code": 200, "success": true, "has_system": true, "has_application": true, "cpu_usage": 36.0, "memory_usage": 78.5}, "monitoring_status": {"status_code": 200, "success": true, "uptime": 127, "alerts_count": 1}, "main_dashboard": {"status_code": 200, "success": true, "content_length": 14294}, "intelligent_dashboard": {"status_code": 200, "success": true, "content_length": 20159}}