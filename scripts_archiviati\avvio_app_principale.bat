@echo off
echo ===================================
echo Avvio applicazione principale con debug
echo ===================================
echo.

set CLEAN_ENV=clean_env

if not exist %CLEAN_ENV% (
    echo ERRORE: L'ambiente virtuale %CLEAN_ENV% non esiste.
    echo Eseguire prima create_clean_env.bat
    pause
    exit /b 1
)

echo Attivazione ambiente virtuale...
call %CLEAN_ENV%\Scripts\activate
if %errorlevel% neq 0 (
    echo ERRORE: Impossibile attivare l'ambiente virtuale.
    pause
    exit /b 1
)
echo Ambiente attivato: %VIRTUAL_ENV%
echo.

echo Impostazione variabili d'ambiente...
set OPENROUTER_API_KEY=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
set APP_URL=http://localhost:5000
set MCP_URL=http://localhost:8000
set PYTHONPATH=%CD%
set FLASK_DEBUG=1

echo Variabili d'ambiente impostate:
echo OPENROUTER_API_KEY: %OPENROUTER_API_KEY%
echo APP_URL: %APP_URL%
echo MCP_URL: %MCP_URL%
echo PYTHONPATH: %PYTHONPATH%
echo.

echo Avvio dell'applicazione principale con debug...
echo NOTA: L'applicazione verrà avviata nella finestra corrente per visualizzare eventuali errori.
echo Premi Ctrl+C per interrompere l'applicazione quando hai finito di verificare.
echo.
echo Comando: python app.py
echo.
python app.py
if %errorlevel% neq 0 (
    echo ERRORE: L'applicazione principale non si è avviata correttamente.
    pause
    exit /b 1
)

echo.
echo ===================================
echo Applicazione avviata correttamente!
echo ===================================
echo.
pause
