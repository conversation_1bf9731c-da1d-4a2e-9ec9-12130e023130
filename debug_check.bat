@echo off
chcp 65001 >nul
echo =====================================
echo CHECK-UP COMPLETO DEL PROGETTO
echo =====================================

echo.
echo 1. Verifica file principali...
if exist app.py (echo ✓ app.py trovato) else (echo ✗ app.py MANCANTE)
if exist mcp_server\main.py (echo ✓ mcp_server\main.py trovato) else (echo ✗ mcp_server\main.py MANCANTE)
if exist static\js\setup-wizard.js (echo ✓ setup-wizard.js trovato) else (echo ✗ setup-wizard.js MANCANTE)
if exist templates\setup_wizard.html (echo ✓ setup_wizard.html trovato) else (echo ✗ setup-wizard.js MANCANTE)

echo.
echo 2. Verifica ambiente virtuale...
if exist clean_env\Scripts\python.exe (echo ✓ Ambiente virtuale OK) else (echo ✗ Ambiente virtuale MANCANTE)

echo.
echo 3. Verifica dimensioni file (per file troncati)...
for %%f in (app.py mcp_server\main.py static\js\setup-wizard.js templates\setup_wizard.html) do (
    if exist "%%f" (
        for %%s in ("%%f") do echo ✓ %%f - %%~zs bytes
    ) else (
        echo ✗ %%f - FILE MANCANTE
    )
)

echo.
echo 4. Test import principali...
call clean_env\Scripts\activate
python -c "import flask; print('✓ Flask OK -', flask.__version__)" 2>nul || echo "✗ Flask ERRORE"
python -c "import pandas; print('✓ Pandas OK -', pandas.__version__)" 2>nul || echo "✗ Pandas ERRORE"
python -c "import fastapi; print('✓ FastAPI OK -', fastapi.__version__)" 2>nul || echo "✗ FastAPI ERRORE"
python -c "import uvicorn; print('✓ Uvicorn OK -', uvicorn.__version__)" 2>nul || echo "✗ Uvicorn ERRORE"

echo.
echo 5. Controllo sintassi file Python...
python -m py_compile app.py 2>nul && echo "✓ app.py sintassi OK" || echo "✗ app.py ERRORI SINTASSI"
python -m py_compile mcp_server\main.py 2>nul && echo "✓ main.py sintassi OK" || echo "✗ main.py ERRORI SINTASSI"

echo.
echo 6. Verifica porte disponibili...
netstat -an | findstr ":5001" >nul && echo "⚠️ Porta 5001 occupata" || echo "✓ Porta 5001 libera"
netstat -an | findstr ":8000" >nul && echo "⚠️ Porta 8000 occupata" || echo "✓ Porta 8000 libera"

echo.
echo 7. Controllo cartelle necessarie...
if exist uploads (echo ✓ Cartella uploads presente) else (echo "⚠️ Cartella uploads mancante - verrà creata")
if exist static (echo ✓ Cartella static presente) else (echo "✗ Cartella static MANCANTE")
if exist templates (echo ✓ Cartella templates presente) else (echo "✗ Cartella templates MANCANTE")
if exist mcp_server (echo ✓ Cartella mcp_server presente) else (echo "✗ Cartella mcp_server MANCANTE")

echo.
echo =====================================
echo CHECK-UP COMPLETATO
echo =====================================
pause