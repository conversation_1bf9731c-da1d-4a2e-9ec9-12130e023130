#!/usr/bin/env python3
"""
Test per verificare l'importazione dei blueprint
"""

import sys
import traceback

def test_blueprint_imports():
    """Test importazione blueprint"""
    
    print("🔍 TEST IMPORTAZIONE BLUEPRINT")
    print("=" * 50)
    
    # Test 1: Import wizard_routes
    print("1️⃣ Test import wizard_routes...")
    try:
        from wizard_routes import wizard_bp
        print("   ✅ wizard_routes importato con successo")
        print(f"   📋 Blueprint name: {wizard_bp.name}")
        print(f"   📋 URL prefix: {wizard_bp.url_prefix}")
        
        # Lista le route del blueprint
        routes = []
        for rule in wizard_bp.url_map.iter_rules():
            routes.append({
                'rule': str(rule),
                'methods': list(rule.methods),
                'endpoint': rule.endpoint
            })
        
        print(f"   📋 Route nel blueprint: {len(routes)}")
        for route in routes:
            print(f"      - {route['rule']} [{', '.join(route['methods'])}] -> {route['endpoint']}")
            
    except Exception as e:
        print(f"   ❌ Errore import wizard_routes: {str(e)}")
        print(f"   📄 Traceback:")
        traceback.print_exc()
    
    # Test 2: Import agent_routes
    print("\n2️⃣ Test import agent_routes...")
    try:
        from agent_routes import agent_bp
        print("   ✅ agent_routes importato con successo")
        print(f"   📋 Blueprint name: {agent_bp.name}")
        print(f"   📋 URL prefix: {agent_bp.url_prefix}")
    except Exception as e:
        print(f"   ❌ Errore import agent_routes: {str(e)}")
        print(f"   📄 Traceback:")
        traceback.print_exc()
    
    # Test 3: Test creazione app Flask minima con blueprint
    print("\n3️⃣ Test registrazione blueprint...")
    try:
        from flask import Flask
        from wizard_routes import wizard_bp
        
        test_app = Flask(__name__)
        test_app.register_blueprint(wizard_bp)
        
        print("   ✅ Blueprint registrato con successo in app di test")
        
        # Lista le route registrate
        routes = []
        for rule in test_app.url_map.iter_rules():
            if '/api/wizard' in str(rule):
                routes.append({
                    'rule': str(rule),
                    'methods': list(rule.methods),
                    'endpoint': rule.endpoint
                })
        
        print(f"   📋 Route wizard registrate: {len(routes)}")
        for route in routes:
            print(f"      - {route['rule']} [{', '.join(route['methods'])}] -> {route['endpoint']}")
            
    except Exception as e:
        print(f"   ❌ Errore registrazione blueprint: {str(e)}")
        print(f"   📄 Traceback:")
        traceback.print_exc()
    
    # Test 4: Test dipendenze wizard_routes
    print("\n4️⃣ Test dipendenze wizard_routes...")
    dependencies = [
        'flask',
        'datetime',
        'json',
        'logging',
        'os',
        'sys'
    ]
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"   ✅ {dep}: OK")
        except Exception as e:
            print(f"   ❌ {dep}: {str(e)}")
    
    print("\n" + "=" * 50)
    print("📊 RISULTATI")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    test_blueprint_imports()
