{
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.linting.flake8Enabled": false,
    "python.linting.pylintArgs": [
        "--disable=C0111,C0103,C0303,C0330,W0611,E1101,R0903,R0913,E0401,W0613,F401,F841"
    ],
    "python.analysis.extraPaths": [
        "${workspaceFolder}",
        "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages",
        "C:\\Program Files\\Python311\\Lib\\site-packages"
    ],
    "python.autoComplete.extraPaths": [
        "${workspaceFolder}",
        "C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages",
        "C:\\Program Files\\Python311\\Lib\\site-packages"
    ],
    "editor.formatOnSave": true,
    "python.formatting.provider": "autopep8",
    "python.formatting.autopep8Args": [
        "--max-line-length=120"
    ],
    "python.analysis.diagnosticSeverityOverrides": {
        "reportMissingImports": "none",
        "reportMissingModuleSource": "none",
        "reportUnusedImport": "none",
        "reportUnusedVariable": "none",
        "reportUnusedFunction": "none",
        "reportUnusedClass": "none",
        "reportOptionalMemberAccess": "none",
        "reportGeneralTypeIssues": "none"
    },
    "python.analysis.typeCheckingMode": "off",
    // DISABILITA COMPLETAMENTE TUTTI I LINTING MARKDOWN
    "markdownlint.config": {
        "MD012": false,
        "MD031": false,
        "MD040": false,
        "MD009": false,
        "MD041": false,
        "MD036": false,
        "MD034": false,
        "default": false
    },
    "markdownlint.ignore": [
        "**/*.md"
    ],
    "markdownlint.run": "onSave",
    // DISABILITA DIAGNOSTICS MARKDOWN
    "markdown.validate.enabled": false,
    "markdown.validate.ignoredLinks": [
        "**"
    ],
    // DISABILITA PROBLEMI GENERALI
    "problems.decorations.enabled": false,
    "problems.showCurrentInStatus": false,
    // ESCLUDE FILE DALLE DIAGNOSTICHE
    "files.exclude": {
        "**/Augment-Memories": true
    },
    // DISABILITA LINTING PER ESTENSIONI SPECIFICHE
    "files.associations": {
        "Augment-Memories": "plaintext"
    }
}