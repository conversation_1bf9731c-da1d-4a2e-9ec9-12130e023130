@echo off
echo 🔧 Setup Database Supabase per app-roberto
echo ==========================================

echo Impostazione variabili d'ambiente...
set SUPABASE_URL=https://zqjllwxqjxjhdkbcawfr.supabase.co
set SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxamxsd3hxanhqaGRrYmNhd2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc4MjE4NzQsImV4cCI6MjA2MzM5Nzg3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8
set OPENROUTER_API_KEY=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
set PYTHONPATH=%CD%

echo Variabili impostate:
echo SUPABASE_URL: %SUPABASE_URL%
echo SUPABASE_KEY: [NASCOSTA]
echo PYTHONPATH: %PYTHONPATH%

echo.
echo Esecuzione setup database...
clean_env\Scripts\python.exe setup_supabase_database.py

echo.
echo Setup completato!
pause
