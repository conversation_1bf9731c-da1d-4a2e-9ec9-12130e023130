@echo off
echo 🔧 Setup Database Supabase per app-roberto
echo ==========================================

echo Impostazione variabili d'ambiente...
set SUPABASE_URL=https://zqjllwxqjxjhdkbcawfr.supabase.co
set SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxamxsd3hxanhqaGRrYmNhd2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc1MDA1NTQsImV4cCI6MjA1MzA3NjU1NH0.WxbX4nvlen-3WixZ7D9flaQwTfLtA5V3PvD0guA5hdo
set SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxamxsd3hxanhqaGRrYmNhd2ZyIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNzUwMDU1NCwiZXhwIjoyMDUzMDc2NTU0fQ.awGNTtc2jlxad8SMFaSVshrAsJi1WsxvCMlBOWxPxrs
set OPENROUTER_API_KEY=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
set PYTHONPATH=%CD%

echo Variabili impostate:
echo SUPABASE_URL: %SUPABASE_URL%
echo SUPABASE_KEY: [NASCOSTA]
echo PYTHONPATH: %PYTHONPATH%

echo.
echo Esecuzione setup database...
clean_env\Scripts\python.exe setup_supabase_database.py

echo.
echo Setup completato!
pause
