#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Analizza il file di permessi per capire perché viene classificato erroneamente come registro_auto.
"""

import pandas as pd
import sys
import os
from enhanced_file_detector import EnhancedFileDetector

def analizza_file_permessi():
    """Analizza il file di permessi e il sistema di riconoscimento."""

    file_path = 'uploads/apprilevazionepresenze-richieste-2025-05-01-2025-05-31_3_1748084961_f08655e3.xlsx'

    print("🔍 ANALISI FILE PERMESSI CLASSIFICATO ERRONEAMENTE")
    print("=" * 60)

    # Verifica esistenza file
    if not os.path.exists(file_path):
        print(f"❌ File non trovato: {file_path}")
        return

    try:
        # Leggi il file
        df = pd.read_excel(file_path)

        print(f"📁 Nome file: {os.path.basename(file_path)}")
        print(f"📊 Dimensioni: {df.shape[0]} righe x {df.shape[1]} colonne")
        print()

        print("📋 COLONNE DEL FILE:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i}. '{col}'")
        print()

        print("🔍 PRIME 3 RIGHE:")
        print(df.head(3).to_string())
        print()

        # Analizza con Enhanced File Detector
        detector = EnhancedFileDetector()
        detected_type, confidence, scores = detector.detect_file_type(df, os.path.basename(file_path))

        print("🤖 RISULTATO RICONOSCIMENTO AUTOMATICO:")
        print(f"   Tipo rilevato: {detected_type}")
        print(f"   Confidenza: {confidence:.3f}")
        print()

        print("📊 PUNTEGGI PER TUTTI I TIPI:")
        for tipo, punteggio in sorted(scores.items(), key=lambda x: x[1], reverse=True):
            print(f"   {tipo}: {punteggio:.3f}")
        print()

        # Analisi manuale
        print("🧠 ANALISI MANUALE:")

        # Verifica nome file
        filename = os.path.basename(file_path).lower()
        if "richieste" in filename:
            print("   ✅ Nome file contiene 'richieste' → DOVREBBE essere 'permessi'")
        if "apprilevazionepresenze" in filename:
            print("   ✅ Nome file contiene 'apprilevazionepresenze' → DOVREBBE essere 'permessi'")

        # Verifica colonne
        columns_lower = [col.lower() for col in df.columns]

        # Pattern permessi
        permessi_indicators = 0
        if any("dipendente" in col or "employee" in col or "nome" in col for col in columns_lower):
            print("   ✅ Trovata colonna dipendente/employee/nome")
            permessi_indicators += 1
        if any("tipo" in col or "type" in col for col in columns_lower):
            print("   ✅ Trovata colonna tipo/type")
            permessi_indicators += 1
        if any("data" in col and ("inizio" in col or "start" in col) for col in columns_lower):
            print("   ✅ Trovata colonna data inizio/start")
            permessi_indicators += 1
        if any("data" in col and ("fine" in col or "end" in col) for col in columns_lower):
            print("   ✅ Trovata colonna data fine/end")
            permessi_indicators += 1
        if any("stato" in col or "status" in col or "approvato" in col for col in columns_lower):
            print("   ✅ Trovata colonna stato/status/approvato")
            permessi_indicators += 1

        # Pattern registro auto
        auto_indicators = 0
        if any("auto" in col or "vehicle" in col or "car" in col for col in columns_lower):
            print("   ⚠️ Trovata colonna auto/vehicle/car")
            auto_indicators += 1
        if any("presa" in col or "pickup" in col for col in columns_lower):
            print("   ⚠️ Trovata colonna presa/pickup")
            auto_indicators += 1
        if any("cliente" in col or "customer" in col for col in columns_lower):
            print("   ⚠️ Trovata colonna cliente/customer")
            auto_indicators += 1

        print()
        print("📈 INDICATORI:")
        print(f"   Permessi: {permessi_indicators}/5 pattern trovati")
        print(f"   Registro Auto: {auto_indicators}/3 pattern trovati")
        print()

        # Conclusione
        if permessi_indicators >= 3 and auto_indicators == 0:
            print("✅ CONCLUSIONE: Questo è CHIARAMENTE un file di PERMESSI")
            print("❌ PROBLEMA: Il sistema lo classifica erroneamente come 'registro_auto'")
            print("🔧 AZIONE: Necessario correggere il sistema di riconoscimento")
        elif permessi_indicators > auto_indicators:
            print("✅ CONCLUSIONE: Questo dovrebbe essere classificato come PERMESSI")
            print("❌ PROBLEMA: Il sistema di riconoscimento ha un errore")
        else:
            print("⚠️ CONCLUSIONE: Classificazione ambigua, necessaria analisi più approfondita")

        return detected_type, confidence, scores, permessi_indicators, auto_indicators

    except Exception as e:
        print(f"❌ Errore nell'analisi: {str(e)}")
        return None

def suggerisci_correzioni():
    """Suggerisce correzioni per il sistema di riconoscimento."""
    print("\n🔧 SUGGERIMENTI PER CORREGGERE IL SISTEMA:")
    print("=" * 50)
    print("1. Aggiungere controllo prioritario sul nome file")
    print("   - Se nome contiene 'richieste' → classificare come 'permessi'")
    print("   - Se nome contiene 'apprilevazionepresenze-richieste' → classificare come 'permessi'")
    print()
    print("2. Migliorare i pattern per 'permessi':")
    print("   - Aumentare peso per pattern specifici dei permessi")
    print("   - Aggiungere pattern per 'richieste', 'ferie', 'ROL'")
    print()
    print("3. Ridurre falsi positivi per 'registro_auto':")
    print("   - Richiedere pattern più specifici per auto/veicoli")
    print("   - Verificare presenza di colonne tipiche del registro auto")
    print()
    print("4. Implementare controllo filename-based come priorità alta")

if __name__ == "__main__":
    risultato = analizza_file_permessi()
    if risultato:
        suggerisci_correzioni()
