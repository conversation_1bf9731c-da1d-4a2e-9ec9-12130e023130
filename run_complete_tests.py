#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FASE 6: Test Runner <PERSON><PERSON><PERSON> App Roberto
<PERSON> di esecuzione test completo con report dettagliati.
Versione: 1.0.0 - FASE 6 Testing e Stabilizzazione
"""

import subprocess
import sys
import time
import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import argparse

class TestRunner:
    """Runner completo per tutti i test."""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.results = {}
        self.total_duration = 0
        
    def run_test_suite(self, test_file: str, markers: Optional[str] = None, 
                      description: str = "") -> Dict[str, Any]:
        """Esegue una suite di test."""
        print(f"\n🧪 ESEGUENDO: {description}")
        print("=" * 60)
        
        # Costruisci comando pytest
        cmd = [sys.executable, "-m", "pytest", test_file, "-v", "--tb=short"]
        
        if markers:
            cmd.extend(["-m", markers])
        
        # Aggiungi opzioni per output dettagliato
        cmd.extend(["--durations=10", "--color=yes"])
        
        start_time = time.time()
        
        try:
            # Esegui test
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minuti timeout
            )
            
            duration = time.time() - start_time
            
            # Analizza output
            stats = self._parse_pytest_output(result.stdout)
            
            test_result = {
                'file': test_file,
                'description': description,
                'return_code': result.returncode,
                'duration': duration,
                'stats': stats,
                'output': result.stdout,
                'errors': result.stderr,
                'success': result.returncode == 0
            }
            
            # Log risultati
            if test_result['success']:
                print(f"✅ {description} - SUCCESSO")
                print(f"   Durata: {duration:.2f}s")
                if stats:
                    print(f"   Test: {stats.get('passed', 0)} passati, "
                          f"{stats.get('failed', 0)} falliti, "
                          f"{stats.get('skipped', 0)} saltati")
            else:
                print(f"❌ {description} - FALLITO")
                print(f"   Durata: {duration:.2f}s")
                print(f"   Return code: {result.returncode}")
                if result.stderr:
                    print(f"   Errori: {result.stderr[:200]}...")
            
            return test_result
            
        except subprocess.TimeoutExpired:
            duration = time.time() - start_time
            print(f"⏰ {description} - TIMEOUT dopo {duration:.2f}s")
            
            return {
                'file': test_file,
                'description': description,
                'return_code': -1,
                'duration': duration,
                'stats': {},
                'output': '',
                'errors': 'Test timeout',
                'success': False
            }
            
        except Exception as e:
            duration = time.time() - start_time
            print(f"💥 {description} - ERRORE: {str(e)}")
            
            return {
                'file': test_file,
                'description': description,
                'return_code': -2,
                'duration': duration,
                'stats': {},
                'output': '',
                'errors': str(e),
                'success': False
            }
    
    def _parse_pytest_output(self, output: str) -> Dict[str, int]:
        """Analizza output pytest per estrarre statistiche."""
        stats = {'passed': 0, 'failed': 0, 'skipped': 0, 'errors': 0}
        
        lines = output.split('\n')
        for line in lines:
            line = line.strip()
            
            # Cerca linea di riepilogo pytest
            if 'passed' in line or 'failed' in line or 'skipped' in line:
                # Esempi: "5 passed", "2 failed, 3 passed", etc.
                parts = line.split()
                for i, part in enumerate(parts):
                    if part.isdigit() and i + 1 < len(parts):
                        count = int(part)
                        status = parts[i + 1].lower()
                        
                        if 'passed' in status:
                            stats['passed'] = count
                        elif 'failed' in status:
                            stats['failed'] = count
                        elif 'skipped' in status:
                            stats['skipped'] = count
                        elif 'error' in status:
                            stats['errors'] = count
        
        return stats
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Esegue tutti i test del sistema."""
        print("🚀 AVVIO TEST COMPLETO APP ROBERTO")
        print("=" * 80)
        print(f"Inizio: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Lista test da eseguire
        test_suites = [
            {
                'file': 'test_app_integration.py',
                'markers': None,
                'description': 'Test Integrazione App (Routes, API, Security)'
            },
            {
                'file': 'test_performance.py',
                'markers': 'performance',
                'description': 'Test Performance (Response Time, Memory, Concurrency)'
            },
            {
                'file': 'health_monitor.py',
                'markers': None,
                'description': 'Test Health Monitor (Standalone)'
            }
        ]
        
        # Esegui ogni suite
        for suite in test_suites:
            result = self.run_test_suite(
                suite['file'],
                suite['markers'],
                suite['description']
            )
            self.results[suite['file']] = result
        
        # Calcola statistiche finali
        self.total_duration = (datetime.now() - self.start_time).total_seconds()
        
        # Genera report finale
        return self.generate_final_report()
    
    def generate_final_report(self) -> Dict[str, Any]:
        """Genera report finale dei test."""
        print("\n" + "=" * 80)
        print("📋 REPORT FINALE TEST")
        print("=" * 80)
        
        # Statistiche generali
        total_suites = len(self.results)
        successful_suites = sum(1 for r in self.results.values() if r['success'])
        
        total_passed = sum(r['stats'].get('passed', 0) for r in self.results.values())
        total_failed = sum(r['stats'].get('failed', 0) for r in self.results.values())
        total_skipped = sum(r['stats'].get('skipped', 0) for r in self.results.values())
        total_errors = sum(r['stats'].get('errors', 0) for r in self.results.values())
        
        success_rate = (successful_suites / total_suites * 100) if total_suites > 0 else 0
        
        print(f"⏱️  Durata totale: {self.total_duration:.2f}s")
        print(f"📊 Suite eseguite: {successful_suites}/{total_suites}")
        print(f"📈 Tasso successo: {success_rate:.1f}%")
        print()
        
        print(f"🧪 Test individuali:")
        print(f"   ✅ Passati: {total_passed}")
        print(f"   ❌ Falliti: {total_failed}")
        print(f"   ⏭️  Saltati: {total_skipped}")
        print(f"   💥 Errori: {total_errors}")
        print()
        
        # Dettagli per suite
        print("📋 Dettagli per suite:")
        for file_name, result in self.results.items():
            status = "✅" if result['success'] else "❌"
            print(f"   {status} {result['description']}")
            print(f"      File: {file_name}")
            print(f"      Durata: {result['duration']:.2f}s")
            
            if result['stats']:
                stats = result['stats']
                print(f"      Test: {stats.get('passed', 0)}✅ "
                      f"{stats.get('failed', 0)}❌ "
                      f"{stats.get('skipped', 0)}⏭️ "
                      f"{stats.get('errors', 0)}💥")
            
            if not result['success'] and result['errors']:
                print(f"      Errore: {result['errors'][:100]}...")
            print()
        
        # Valutazione finale
        if success_rate >= 90:
            grade = "🟢 ECCELLENTE"
        elif success_rate >= 70:
            grade = "🟡 BUONO"
        elif success_rate >= 50:
            grade = "🟠 SUFFICIENTE"
        else:
            grade = "🔴 INSUFFICIENTE"
        
        print(f"🎯 Valutazione: {grade}")
        
        # Raccomandazioni
        print("\n💡 Raccomandazioni:")
        if success_rate < 100:
            print("   - Analizzare i test falliti e correggere i problemi")
            print("   - Verificare la configurazione dell'ambiente")
        
        if total_failed > 0:
            print("   - Priorità alta: correggere test falliti")
        
        if total_errors > 0:
            print("   - Verificare errori di configurazione o dipendenze")
        
        if success_rate >= 90:
            print("   - Sistema stabile e ben testato")
            print("   - Considerare l'aggiunta di test aggiuntivi")
        
        # Crea report JSON
        report = {
            'total_duration': self.total_duration,
            'successful_suites': successful_suites,
            'total_suites': total_suites,
            'total_passed': total_passed,
            'total_failed': total_failed,
            'total_skipped': total_skipped,
            'total_errors': total_errors,
            'success_rate': success_rate,
            'grade': grade,
            'results': self.results
        }
        
        # Salva report
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)
            print(f"\n📄 Report salvato in: {report_file}")
        except Exception as e:
            print(f"\n⚠️ Errore salvataggio report: {str(e)}")
        
        return report

def main():
    """Funzione principale."""
    parser = argparse.ArgumentParser(description='Test Runner Completo App Roberto')
    parser.add_argument('--suite', help='Esegui solo una suite specifica')
    parser.add_argument('--markers', help='Esegui solo test con marker specifico')
    parser.add_argument('--quick', action='store_true', help='Esegui solo test veloci')
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    if args.suite:
        # Esegui solo una suite
        result = runner.run_test_suite(
            args.suite,
            args.markers,
            f"Test Suite: {args.suite}"
        )
        print(f"\nRisultato: {'✅ SUCCESSO' if result['success'] else '❌ FALLITO'}")
    else:
        # Esegui tutti i test
        if args.quick:
            print("🏃 Modalità veloce: saltando test lenti")
        
        report = runner.run_all_tests()
        
        # Exit code basato sui risultati
        if report['success_rate'] >= 70:
            sys.exit(0)
        else:
            sys.exit(1)

if __name__ == "__main__":
    main()
