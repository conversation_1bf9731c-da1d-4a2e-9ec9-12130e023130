#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Data Synchronizer - Sistema di sincronizzazione automatica per app-roberto.
Gestisce la sincronizzazione bidirezionale tra l'applicazione e il database Supabase,
con supporto per aggiornamenti incrementali e rilevamento conflitti.
"""

import asyncio
import logging
import hashlib
import json
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import pandas as pd

# Import dei moduli esistenti
try:
    from supabase_integration import SupabaseManager
    from intelligent_entity_extractor import IntelligentEntityExtractor
    from content_based_file_analyzer import ContentBasedFileAnalyzer
    from multiple_parsing_strategy import MultipleParsingStrategy
except ImportError as e:
    logging.warning(f"Alcuni moduli non disponibili: {e}")

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SyncStatus(Enum):
    """Stati di sincronizzazione."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CONFLICT = "conflict"

class SyncOperation(Enum):
    """Tipi di operazioni di sincronizzazione."""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    MERGE = "merge"

@dataclass
class SyncRecord:
    """Record di sincronizzazione."""
    id: Optional[int] = None
    table_name: str = ""
    record_id: int = 0
    operation: SyncOperation = SyncOperation.CREATE
    data: Dict[str, Any] = None
    data_hash: str = ""
    status: SyncStatus = SyncStatus.PENDING
    created_at: datetime = None
    updated_at: datetime = None
    retry_count: int = 0
    error_message: str = ""

    def __post_init__(self):
        if self.data is None:
            self.data = {}
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
        if not self.data_hash and self.data:
            self.data_hash = self._calculate_hash()

    def _calculate_hash(self) -> str:
        """Calcola hash dei dati per rilevamento modifiche."""
        data_str = json.dumps(self.data, sort_keys=True, default=str)
        return hashlib.md5(data_str.encode()).hexdigest()

class DataSynchronizer:
    """
    Sistema di sincronizzazione automatica che gestisce:
    - Sincronizzazione bidirezionale con Supabase
    - Aggiornamenti incrementali
    - Rilevamento e risoluzione conflitti
    - Retry automatico per operazioni fallite
    - Monitoraggio e logging avanzato
    """

    def __init__(self, supabase_manager: Optional[SupabaseManager] = None):
        self.supabase_manager = supabase_manager or SupabaseManager()

        # Configurazione sincronizzazione
        self.SYNC_INTERVAL_SECONDS = 30  # Intervallo sincronizzazione automatica
        self.MAX_RETRY_ATTEMPTS = 3      # Massimo tentativi di retry
        self.BATCH_SIZE = 50             # Dimensione batch per operazioni
        self.CONFLICT_RESOLUTION_STRATEGY = "latest_wins"  # Strategia risoluzione conflitti

        # Cache locale per ottimizzazione
        self.local_cache = {}
        self.sync_queue = []
        self.is_syncing = False

        # Analizzatori per elaborazione dati
        self.entity_extractor = IntelligentEntityExtractor()
        self.content_analyzer = ContentBasedFileAnalyzer()
        self.parsing_strategy = MultipleParsingStrategy()

        # Statistiche sincronizzazione
        self.sync_stats = {
            "total_syncs": 0,
            "successful_syncs": 0,
            "failed_syncs": 0,
            "conflicts_resolved": 0,
            "last_sync_time": None,
            "average_sync_duration": 0.0
        }

        logger.info("DataSynchronizer inizializzato")

    async def start_auto_sync(self):
        """Avvia la sincronizzazione automatica."""
        logger.info("🔄 Avvio sincronizzazione automatica")

        while True:
            try:
                await self.sync_all_data()
                await asyncio.sleep(self.SYNC_INTERVAL_SECONDS)
            except Exception as e:
                logger.error(f"❌ Errore nella sincronizzazione automatica: {e}")
                await asyncio.sleep(self.SYNC_INTERVAL_SECONDS * 2)  # Attesa maggiore in caso di errore

    async def sync_all_data(self) -> Dict[str, Any]:
        """
        Sincronizza tutti i dati pendenti.

        Returns:
            Risultato della sincronizzazione
        """
        if self.is_syncing:
            logger.warning("⚠️ Sincronizzazione già in corso")
            return {"status": "already_running"}

        self.is_syncing = True
        start_time = datetime.now()

        try:
            logger.info("🔄 Inizio sincronizzazione completa")

            # Risultato sincronizzazione
            sync_result = {
                "start_time": start_time.isoformat(),
                "status": "success",
                "operations": {
                    "file_uploads": {"processed": 0, "errors": 0},
                    "processed_data": {"processed": 0, "errors": 0},
                    "entities": {"processed": 0, "errors": 0},
                    "configurations": {"processed": 0, "errors": 0}
                },
                "conflicts": [],
                "errors": [],
                "duration_seconds": 0.0
            }

            # 1. Sincronizza file uploads
            await self._sync_file_uploads(sync_result)

            # 2. Sincronizza dati elaborati
            await self._sync_processed_data(sync_result)

            # 3. Sincronizza entità estratte
            await self._sync_extracted_entities(sync_result)

            # 4. Sincronizza configurazioni
            await self._sync_configurations(sync_result)

            # 5. Processa coda di sincronizzazione
            await self._process_sync_queue(sync_result)

            # Calcola durata e aggiorna statistiche
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            sync_result["duration_seconds"] = duration
            sync_result["end_time"] = end_time.isoformat()

            self._update_sync_stats(sync_result)

            logger.info(f"✅ Sincronizzazione completata in {duration:.2f}s")
            return sync_result

        except Exception as e:
            logger.error(f"❌ Errore nella sincronizzazione: {str(e)}")
            sync_result["status"] = "error"
            sync_result["error"] = str(e)
            return sync_result

        finally:
            self.is_syncing = False

    async def _sync_file_uploads(self, sync_result: Dict[str, Any]):
        """Sincronizza i file uploads."""
        try:
            logger.info("📁 Sincronizzazione file uploads")

            # Recupera file uploads recenti non sincronizzati
            recent_uploads = self.supabase_manager.get_file_uploads(limit=100)

            for upload in recent_uploads:
                try:
                    # Verifica se necessita sincronizzazione
                    if await self._needs_sync(upload, "file_uploads"):
                        await self._sync_single_record(upload, "file_uploads")
                        sync_result["operations"]["file_uploads"]["processed"] += 1

                except Exception as e:
                    logger.error(f"Errore sync file upload {upload.get('id')}: {e}")
                    sync_result["operations"]["file_uploads"]["errors"] += 1

        except Exception as e:
            logger.error(f"Errore generale sync file uploads: {e}")
            sync_result["errors"].append(f"file_uploads: {str(e)}")

    async def _sync_processed_data(self, sync_result: Dict[str, Any]):
        """Sincronizza i dati elaborati."""
        try:
            logger.info("📊 Sincronizzazione dati elaborati")

            # Recupera dati elaborati recenti
            processed_data = self.supabase_manager.get_processed_data(limit=100)

            for data in processed_data:
                try:
                    # Verifica se necessita sincronizzazione
                    if await self._needs_sync(data, "processed_data"):
                        await self._sync_single_record(data, "processed_data")
                        sync_result["operations"]["processed_data"]["processed"] += 1

                except Exception as e:
                    logger.error(f"Errore sync processed data {data.get('id')}: {e}")
                    sync_result["operations"]["processed_data"]["errors"] += 1

        except Exception as e:
            logger.error(f"Errore generale sync processed data: {e}")
            sync_result["errors"].append(f"processed_data: {str(e)}")

    async def _sync_extracted_entities(self, sync_result: Dict[str, Any]):
        """Sincronizza le entità estratte."""
        try:
            logger.info("🔍 Sincronizzazione entità estratte")

            # Recupera dati recenti per estrazione entità
            recent_data = self.supabase_manager.get_processed_data(limit=50)

            for data in recent_data:
                try:
                    # Estrai entità se non già fatto
                    if not data.get("entities_extracted", False):
                        await self._extract_and_sync_entities(data)
                        sync_result["operations"]["entities"]["processed"] += 1

                except Exception as e:
                    logger.error(f"Errore sync entità per data {data.get('id')}: {e}")
                    sync_result["operations"]["entities"]["errors"] += 1

        except Exception as e:
            logger.error(f"Errore generale sync entità: {e}")
            sync_result["errors"].append(f"entities: {str(e)}")

    async def _sync_configurations(self, sync_result: Dict[str, Any]):
        """Sincronizza le configurazioni di sistema."""
        try:
            logger.info("⚙️ Sincronizzazione configurazioni")

            # Configurazioni da sincronizzare
            configs_to_sync = [
                "sync_interval",
                "max_file_size",
                "auto_entity_extraction",
                "conflict_resolution_strategy"
            ]

            for config_key in configs_to_sync:
                try:
                    # Verifica e sincronizza configurazione
                    await self._sync_configuration(config_key)
                    sync_result["operations"]["configurations"]["processed"] += 1

                except Exception as e:
                    logger.error(f"Errore sync config {config_key}: {e}")
                    sync_result["operations"]["configurations"]["errors"] += 1

        except Exception as e:
            logger.error(f"Errore generale sync configurazioni: {e}")
            sync_result["errors"].append(f"configurations: {str(e)}")

    async def _needs_sync(self, record: Dict[str, Any], table_name: str) -> bool:
        """
        Verifica se un record necessita di sincronizzazione.

        Args:
            record: Record da verificare
            table_name: Nome della tabella

        Returns:
            True se necessita sincronizzazione
        """
        record_id = record.get("id")
        if not record_id:
            return True

        # Verifica cache locale
        cache_key = f"{table_name}:{record_id}"
        cached_hash = self.local_cache.get(cache_key)

        # Calcola hash corrente
        current_hash = self._calculate_record_hash(record)

        # Necessita sync se hash diverso o non in cache
        needs_sync = cached_hash != current_hash

        if needs_sync:
            logger.debug(f"Record {cache_key} necessita sincronizzazione")

        return needs_sync

    async def _sync_single_record(self, record: Dict[str, Any], table_name: str):
        """Sincronizza un singolo record."""
        try:
            record_id = record.get("id")

            # Verifica conflitti
            conflict = await self._detect_conflict(record, table_name)
            if conflict:
                await self._resolve_conflict(conflict, record, table_name)

            # Aggiorna cache locale
            cache_key = f"{table_name}:{record_id}"
            self.local_cache[cache_key] = self._calculate_record_hash(record)

            logger.debug(f"✅ Record {cache_key} sincronizzato")

        except Exception as e:
            logger.error(f"Errore sync record {table_name}:{record.get('id')}: {e}")
            raise

    async def _detect_conflict(self, record: Dict[str, Any], table_name: str) -> Optional[Dict[str, Any]]:
        """
        Rileva conflitti di sincronizzazione.

        Args:
            record: Record da verificare
            table_name: Nome della tabella

        Returns:
            Informazioni sul conflitto se rilevato
        """
        try:
            record_id = record.get("id")
            if not record_id:
                return None

            # Recupera versione dal database
            db_record = await self._get_db_record(record_id, table_name)
            if not db_record:
                return None

            # Confronta timestamp di modifica
            local_updated = record.get("updated_at")
            db_updated = db_record.get("updated_at")

            if local_updated and db_updated:
                local_time = datetime.fromisoformat(str(local_updated).replace('Z', '+00:00'))
                db_time = datetime.fromisoformat(str(db_updated).replace('Z', '+00:00'))

                # Conflitto se entrambi modificati di recente
                time_diff = abs((local_time - db_time).total_seconds())
                if time_diff < 60:  # Conflitto se modifiche entro 1 minuto
                    return {
                        "type": "concurrent_modification",
                        "local_record": record,
                        "db_record": db_record,
                        "time_diff": time_diff
                    }

            return None

        except Exception as e:
            logger.error(f"Errore rilevamento conflitto: {e}")
            return None

    async def _resolve_conflict(self, conflict: Dict[str, Any], record: Dict[str, Any], table_name: str):
        """
        Risolve un conflitto di sincronizzazione.

        Args:
            conflict: Informazioni sul conflitto
            record: Record locale
            table_name: Nome della tabella
        """
        try:
            strategy = self.CONFLICT_RESOLUTION_STRATEGY

            if strategy == "latest_wins":
                # Vince la modifica più recente
                local_time = datetime.fromisoformat(str(record.get("updated_at")).replace('Z', '+00:00'))
                db_time = datetime.fromisoformat(str(conflict["db_record"].get("updated_at")).replace('Z', '+00:00'))

                if local_time > db_time:
                    logger.info(f"🔄 Conflitto risolto: locale vince per {table_name}:{record.get('id')}")
                    # Aggiorna database con versione locale
                    await self._update_db_record(record, table_name)
                else:
                    logger.info(f"🔄 Conflitto risolto: database vince per {table_name}:{record.get('id')}")
                    # Aggiorna cache con versione database
                    cache_key = f"{table_name}:{record.get('id')}"
                    self.local_cache[cache_key] = self._calculate_record_hash(conflict["db_record"])

            elif strategy == "merge":
                # Merge intelligente dei dati
                merged_record = await self._merge_records(record, conflict["db_record"])
                await self._update_db_record(merged_record, table_name)
                logger.info(f"🔄 Conflitto risolto: merge per {table_name}:{record.get('id')}")

            # Aggiorna statistiche
            self.sync_stats["conflicts_resolved"] += 1

        except Exception as e:
            logger.error(f"Errore risoluzione conflitto: {e}")
            raise

    async def _extract_and_sync_entities(self, data_record: Dict[str, Any]):
        """Estrae entità da un record di dati elaborati e le sincronizza."""
        try:
            # Recupera dati elaborati
            processed_data = data_record.get("processed_data", {})
            if not processed_data:
                return

            # Simula DataFrame dai dati elaborati (implementazione semplificata)
            # In un'implementazione reale, ricostruiresti il DataFrame originale
            df = pd.DataFrame(processed_data.get("sample_data", []))
            if df.empty:
                return

            # Estrai entità
            file_type = data_record.get("data_type", "unknown")
            entities_result = self.entity_extractor.extract_entities(df, file_type)

            # Salva entità estratte nel database
            if entities_result.get("entities"):
                await self._save_extracted_entities(data_record["id"], entities_result)

                # Marca come elaborato
                await self._mark_entities_extracted(data_record["id"])

                logger.info(f"✅ Entità estratte e sincronizzate per record {data_record['id']}")

        except Exception as e:
            logger.error(f"Errore estrazione entità per record {data_record.get('id')}: {e}")
            raise

    async def _save_extracted_entities(self, processed_data_id: int, entities_result: Dict[str, Any]):
        """Salva le entità estratte nel database."""
        try:
            # Prepara dati per il salvataggio
            entity_data = {
                "processed_data_id": processed_data_id,
                "entities": entities_result.get("entities", {}),
                "entity_statistics": entities_result.get("entity_statistics", {}),
                "confidence_scores": entities_result.get("confidence_scores", {}),
                "extraction_timestamp": datetime.now().isoformat()
            }

            # Salva nel database (implementazione semplificata)
            # In un'implementazione reale, useresti una tabella dedicata per le entità
            self.supabase_manager.client.table("processed_data").update({
                "extracted_entities": entity_data
            }).eq("id", processed_data_id).execute()

        except Exception as e:
            logger.error(f"Errore salvataggio entità estratte: {e}")
            raise

    async def _mark_entities_extracted(self, processed_data_id: int):
        """Marca un record come avente entità estratte."""
        try:
            self.supabase_manager.client.table("processed_data").update({
                "entities_extracted": True
            }).eq("id", processed_data_id).execute()

        except Exception as e:
            logger.error(f"Errore marcatura entità estratte: {e}")
            raise

    def _calculate_record_hash(self, record: Dict[str, Any]) -> str:
        """Calcola hash di un record per rilevamento modifiche."""
        # Rimuovi campi che cambiano automaticamente
        filtered_record = {k: v for k, v in record.items()
                          if k not in ['updated_at', 'created_at', 'id']}

        record_str = json.dumps(filtered_record, sort_keys=True, default=str)
        return hashlib.md5(record_str.encode()).hexdigest()

    async def _get_db_record(self, record_id: int, table_name: str) -> Optional[Dict[str, Any]]:
        """Recupera un record dal database."""
        try:
            result = self.supabase_manager.client.table(table_name).select("*").eq("id", record_id).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Errore recupero record DB {table_name}:{record_id}: {e}")
            return None

    async def _update_db_record(self, record: Dict[str, Any], table_name: str):
        """Aggiorna un record nel database."""
        try:
            record_id = record.get("id")
            if not record_id:
                return

            # Rimuovi ID dalla update
            update_data = {k: v for k, v in record.items() if k != 'id'}
            update_data['updated_at'] = datetime.now().isoformat()

            self.supabase_manager.client.table(table_name).update(update_data).eq("id", record_id).execute()

        except Exception as e:
            logger.error(f"Errore aggiornamento record DB {table_name}:{record.get('id')}: {e}")
            raise

    async def _merge_records(self, local_record: Dict[str, Any], db_record: Dict[str, Any]) -> Dict[str, Any]:
        """Merge intelligente di due record."""
        try:
            merged = db_record.copy()

            # Merge semplice: prendi valori non nulli dal record locale
            for key, value in local_record.items():
                if value is not None and value != "":
                    merged[key] = value

            # Timestamp di merge
            merged['updated_at'] = datetime.now().isoformat()
            merged['merged_at'] = datetime.now().isoformat()

            return merged

        except Exception as e:
            logger.error(f"Errore merge record: {e}")
            return db_record

    async def _sync_configuration(self, config_key: str):
        """Sincronizza una configurazione specifica."""
        try:
            # Recupera configurazione corrente
            current_value = self.supabase_manager.get_config(config_key)

            # Configurazioni di default per il sincronizzatore
            default_configs = {
                "sync_interval": self.SYNC_INTERVAL_SECONDS,
                "max_file_size": 50,
                "auto_entity_extraction": True,
                "conflict_resolution_strategy": self.CONFLICT_RESOLUTION_STRATEGY
            }

            # Imposta valore di default se non esiste
            if current_value is None and config_key in default_configs:
                self.supabase_manager.set_config(
                    config_key,
                    default_configs[config_key],
                    f"Configurazione automatica per {config_key}"
                )
                logger.info(f"✅ Configurazione {config_key} impostata a valore default")

        except Exception as e:
            logger.error(f"Errore sync configurazione {config_key}: {e}")
            raise

    async def _process_sync_queue(self, sync_result: Dict[str, Any]):
        """Processa la coda di sincronizzazione."""
        try:
            if not self.sync_queue:
                return

            logger.info(f"📋 Processando {len(self.sync_queue)} elementi in coda")

            processed = 0
            errors = 0

            # Processa in batch
            for i in range(0, len(self.sync_queue), self.BATCH_SIZE):
                batch = self.sync_queue[i:i + self.BATCH_SIZE]

                for sync_record in batch:
                    try:
                        await self._process_sync_record(sync_record)
                        processed += 1
                    except Exception as e:
                        logger.error(f"Errore processando sync record: {e}")
                        errors += 1

            # Pulisci coda processata
            self.sync_queue = []

            sync_result["queue_processed"] = processed
            sync_result["queue_errors"] = errors

        except Exception as e:
            logger.error(f"Errore processando coda sync: {e}")
            sync_result["errors"].append(f"sync_queue: {str(e)}")

    async def _process_sync_record(self, sync_record: SyncRecord):
        """Processa un singolo record di sincronizzazione."""
        try:
            if sync_record.operation == SyncOperation.CREATE:
                await self._create_record(sync_record)
            elif sync_record.operation == SyncOperation.UPDATE:
                await self._update_record(sync_record)
            elif sync_record.operation == SyncOperation.DELETE:
                await self._delete_record(sync_record)
            elif sync_record.operation == SyncOperation.MERGE:
                await self._merge_record(sync_record)

            sync_record.status = SyncStatus.COMPLETED

        except Exception as e:
            sync_record.status = SyncStatus.FAILED
            sync_record.error_message = str(e)
            sync_record.retry_count += 1

            # Riprova se non superato limite
            if sync_record.retry_count < self.MAX_RETRY_ATTEMPTS:
                sync_record.status = SyncStatus.PENDING
                self.sync_queue.append(sync_record)

            raise

    async def _create_record(self, sync_record: SyncRecord):
        """Crea un nuovo record."""
        result = self.supabase_manager.client.table(sync_record.table_name).insert(sync_record.data).execute()
        if result.data:
            sync_record.record_id = result.data[0]['id']

    async def _update_record(self, sync_record: SyncRecord):
        """Aggiorna un record esistente."""
        self.supabase_manager.client.table(sync_record.table_name).update(sync_record.data).eq("id", sync_record.record_id).execute()

    async def _delete_record(self, sync_record: SyncRecord):
        """Elimina un record."""
        self.supabase_manager.client.table(sync_record.table_name).delete().eq("id", sync_record.record_id).execute()

    async def _merge_record(self, sync_record: SyncRecord):
        """Merge di un record."""
        # Recupera record esistente
        existing = await self._get_db_record(sync_record.record_id, sync_record.table_name)
        if existing:
            merged = await self._merge_records(sync_record.data, existing)
            await self._update_db_record(merged, sync_record.table_name)

    def _update_sync_stats(self, sync_result: Dict[str, Any]):
        """Aggiorna le statistiche di sincronizzazione."""
        self.sync_stats["total_syncs"] += 1

        if sync_result["status"] == "success":
            self.sync_stats["successful_syncs"] += 1
        else:
            self.sync_stats["failed_syncs"] += 1

        self.sync_stats["last_sync_time"] = datetime.now().isoformat()

        # Calcola durata media
        duration = sync_result.get("duration_seconds", 0.0)
        current_avg = self.sync_stats["average_sync_duration"]
        total_syncs = self.sync_stats["total_syncs"]

        self.sync_stats["average_sync_duration"] = ((current_avg * (total_syncs - 1)) + duration) / total_syncs

    def get_sync_status(self) -> Dict[str, Any]:
        """Restituisce lo stato corrente della sincronizzazione."""
        return {
            "is_syncing": self.is_syncing,
            "queue_size": len(self.sync_queue),
            "cache_size": len(self.local_cache),
            "statistics": self.sync_stats.copy(),
            "configuration": {
                "sync_interval": self.SYNC_INTERVAL_SECONDS,
                "batch_size": self.BATCH_SIZE,
                "max_retries": self.MAX_RETRY_ATTEMPTS,
                "conflict_strategy": self.CONFLICT_RESOLUTION_STRATEGY
            }
        }

    def add_to_sync_queue(self, table_name: str, record_id: int, operation: SyncOperation, data: Dict[str, Any]):
        """Aggiunge un elemento alla coda di sincronizzazione."""
        sync_record = SyncRecord(
            table_name=table_name,
            record_id=record_id,
            operation=operation,
            data=data
        )
        self.sync_queue.append(sync_record)
        logger.debug(f"➕ Aggiunto alla coda sync: {table_name}:{record_id} ({operation.value})")

# Istanza globale
data_synchronizer = DataSynchronizer()