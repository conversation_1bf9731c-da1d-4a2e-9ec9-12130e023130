/**
 * Script per la gestione dei grafici interattivi
 * Versione: 1.0.0
 */

// Stampa la versione nella console per verificare il caricamento
console.log('interactive_charts.js versione 1.0.0 caricato');

// Configurazione globale
const config = {
    chartType: 'bar',
    dataSource: 'processed',
    xColumn: '',
    yColumn: '',
    groupBy: '',
    aggregation: 'sum'
};

// Cache per i dati
const cache = {
    chartData: null,
    availableColumns: [],
    columnTypes: {}
};

// Configurazione globale per Plotly
const plotlyConfig = {
    responsive: true,
    displayModeBar: 'hover',
    locale: 'it',
    scrollZoom: true,
    showTips: true,
    doubleClick: 'reset+autosize',
    modeBarButtonsToAdd: [
        'zoom2d',
        'pan2d',
        'select2d',
        'lasso2d',
        'zoomIn2d',
        'zoomOut2d',
        'autoScale2d',
        'resetScale2d'
    ],
    modeBarButtonsToRemove: ['sendDataToCloud'],
    toImageButtonOptions: {
        format: 'png',
        filename: 'grafico_interattivo',
        height: 600,
        width: 800,
        scale: 2
    }
};

// Elementi DOM
let chartContainer, chartTitle;
let chartTypeSelect, dataSourceSelect, aggregationSelect;
let xColumnSelect, yColumnSelect, groupBySelect;
let updateChartBtn, resetChartBtn;

/**
 * Verifica status connessione Supabase
 */
function checkSupabaseStatus() {
    console.log('🔍 Verificando status Supabase...');

    fetch('/api/supabase/status')
        .then(response => response.json())
        .then(status => {
            console.log('📊 Status Supabase:', status);

            const statusDiv = document.getElementById('supabase-status');
            const statusText = document.getElementById('status-text');
            const badge = document.getElementById('data-source-badge');

            if (status.available && status.test_connection) {
                // Supabase disponibile e connesso
                statusDiv.className = 'alert alert-success mb-3';
                statusText.innerHTML = `
                    <strong>Supabase Connesso</strong> -
                    Attività: ${status.tables.normalized_activities || 0},
                    Tecnici: ${status.tables.master_technicians || 0},
                    Clienti: ${status.tables.master_clients || 0}
                `;
                badge.className = 'badge bg-success ms-2';
                badge.textContent = 'Supabase';

            } else if (status.db_manager && status.connected) {
                // Database manager disponibile ma test connessione fallito
                statusDiv.className = 'alert alert-warning mb-3';
                statusText.innerHTML = `
                    <strong>Supabase Parziale</strong> -
                    Database manager connesso ma test fallito. Usando fallback legacy.
                `;
                badge.className = 'badge bg-warning ms-2';
                badge.textContent = 'Fallback';

            } else {
                // Supabase non disponibile
                statusDiv.className = 'alert alert-secondary mb-3';
                statusText.innerHTML = `
                    <strong>Modalità Legacy</strong> -
                    Supabase non disponibile. Usando dati locali/sessione.
                `;
                badge.className = 'badge bg-secondary ms-2';
                badge.textContent = 'Legacy';
            }

            // Mostra sempre lo status
            statusDiv.classList.remove('d-none');

            // Se c'è un errore, mostralo
            if (status.error) {
                console.warn('⚠️ Errore Supabase:', status.error);
                statusText.innerHTML += `<br><small class="text-muted">Errore: ${status.error}</small>`;
            }

        })
        .catch(error => {
            console.error('❌ Errore verifica status Supabase:', error);

            const statusDiv = document.getElementById('supabase-status');
            const statusText = document.getElementById('status-text');
            const badge = document.getElementById('data-source-badge');

            statusDiv.className = 'alert alert-danger mb-3';
            statusText.innerHTML = `<strong>Errore Connessione</strong> - Impossibile verificare status Supabase`;
            badge.className = 'badge bg-danger ms-2';
            badge.textContent = 'Errore';
            statusDiv.classList.remove('d-none');
        });
}

/**
 * Inizializza la pagina dei grafici interattivi
 */
function initInteractiveCharts() {
    console.log('Inizializzazione pagina grafici interattivi...');

    // Verifica status Supabase
    checkSupabaseStatus();

    // Ottieni riferimenti agli elementi DOM
    chartContainer = document.getElementById('interactive-chart');
    chartTitle = document.getElementById('chart-title');
    chartTypeSelect = document.getElementById('chart-type');
    dataSourceSelect = document.getElementById('data-source');
    aggregationSelect = document.getElementById('aggregation');
    xColumnSelect = document.getElementById('x-column');
    yColumnSelect = document.getElementById('y-column');
    groupBySelect = document.getElementById('group-by');
    updateChartBtn = document.getElementById('update-chart');
    resetChartBtn = document.getElementById('reset-chart');

    // Aggiungi event listeners
    if (chartTypeSelect) {
        chartTypeSelect.addEventListener('change', handleChartTypeChange);
    }

    if (updateChartBtn) {
        updateChartBtn.addEventListener('click', updateChart);
    }

    if (resetChartBtn) {
        resetChartBtn.addEventListener('click', resetChart);
    }

    // Carica il grafico iniziale
    loadInitialChart();
}

/**
 * Carica il grafico iniziale
 */
function loadInitialChart() {
    // Imposta valori predefiniti
    if (xColumnSelect && xColumnSelect.options.length > 1) {
        xColumnSelect.selectedIndex = 1;
        config.xColumn = xColumnSelect.value;
    }

    // Aggiorna il grafico
    updateChart();
}

/**
 * Gestisce il cambio di tipo di grafico
 */
function handleChartTypeChange() {
    const chartType = chartTypeSelect.value;

    // Aggiorna la configurazione
    config.chartType = chartType;

    // Aggiorna l'interfaccia in base al tipo di grafico
    if (chartType === 'pie') {
        // Per i grafici a torta, nascondi il gruppo
        if (groupBySelect) {
            groupBySelect.disabled = true;
            groupBySelect.parentElement.classList.add('opacity-50');
        }
    } else {
        // Per gli altri grafici, abilita il gruppo
        if (groupBySelect) {
            groupBySelect.disabled = false;
            groupBySelect.parentElement.classList.remove('opacity-50');
        }
    }

    if (chartType === 'heatmap' || chartType === 'box') {
        // Per heatmap e box plot, la colonna Y è obbligatoria
        if (yColumnSelect) {
            yColumnSelect.required = true;
            if (yColumnSelect.options.length > 1 && !yColumnSelect.value) {
                yColumnSelect.selectedIndex = 1;
            }
        }
    } else {
        // Per gli altri grafici, la colonna Y è opzionale
        if (yColumnSelect) {
            yColumnSelect.required = false;
        }
    }
}

/**
 * Aggiorna il grafico con i parametri correnti
 */
function updateChart() {
    // Ottieni i valori dai controlli
    config.chartType = chartTypeSelect.value;
    config.dataSource = dataSourceSelect.value;
    config.xColumn = xColumnSelect.value;
    config.yColumn = yColumnSelect.value;
    config.groupBy = groupBySelect.value;
    config.aggregation = aggregationSelect.value;

    // Verifica che sia selezionata almeno la colonna X
    if (!config.xColumn) {
        showError('Seleziona almeno la colonna X');
        return;
    }

    // Verifica che sia selezionata la colonna Y per i grafici che la richiedono
    if ((config.chartType === 'heatmap' || config.chartType === 'box') && !config.yColumn) {
        showError('Per questo tipo di grafico è necessario selezionare anche la colonna Y');
        return;
    }

    // Mostra il loader
    showLoading();

    // Aggiorna il titolo del grafico
    updateChartTitle();

    // Carica i dati per il grafico
    loadChartData();
}

/**
 * Carica i dati per il grafico - VERSIONE MODERNIZZATA
 */
async function loadChartData() {
    try {
        console.log('🔄 Caricamento dati grafico...');

        // Validazione parametri
        if (!config.xColumn) {
            showError('Seleziona una colonna per l\'asse X');
            return;
        }

        // Costruisci parametri con priorità Supabase
        const params = new URLSearchParams({
            type: config.chartType,
            x_column: config.xColumn,
            data_source: config.dataSource || 'supabase', // Default Supabase
            aggregation: config.aggregation
        });

        // Parametri opzionali
        if (config.yColumn) {
            params.append('y_column', config.yColumn);
        }

        if (config.groupBy) {
            params.append('group_by', config.groupBy);
        }

        // Cache busting
        params.append('nocache', Date.now() + Math.random().toString(36).substring(2, 15));

        const requestUrl = `/api/supabase/chart_data?${params.toString()}`;
        console.log('📡 Richiesta API SUPABASE-FIRST:', requestUrl);

        // Richiesta con timeout e retry logic
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000);

        try {
            const response = await fetch(requestUrl, {
                signal: controller.signal,
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                }
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${response.statusText}\n${errorText}`);
            }

        const data = await response.json();
        console.log('📊 Dati SUPABASE ricevuti:', data);

        if (data.error) {
            let errorMessage = data.message || data.error;

            // Se ci sono colonne disponibili, mostrali
            if (data.available_columns && data.available_columns.length > 0) {
                errorMessage += `\n\nColonne disponibili: ${data.available_columns.join(', ')}`;
            }

            if (data.suggestion) {
                errorMessage += `\n\nSuggerimento: ${data.suggestion}`;
            }

            // Informazioni aggiuntive per debug
            if (data.status) {
                console.warn(`⚠️ Status API: ${data.status}`);
            }

            showError(errorMessage);
            return;
        }

        // Verifica che sia effettivamente da Supabase
        if (data.supabase_first) {
            console.log('✅ SUPABASE-FIRST: Dati confermati da Supabase');
            console.log(`📊 Record: ${data.record_count}, Tecnici: ${data.technicians_count}, Clienti: ${data.clients_count}`);
        }

        // Salva i dati nella cache
        cache.chartData = data;

        // Salva le informazioni sulle colonne disponibili
        if (data.available_columns) {
            cache.availableColumns = data.available_columns;
        }

        // Salva le informazioni sui tipi di dati delle colonne
        if (data.column_types) {
            cache.columnTypes = data.column_types;
        }

        // Aggiorna informazioni fonte dati
        updateDataSourceInfo(data);

        // Renderizza il grafico
        renderChart(data);

        } catch (fetchError) {
            clearTimeout(timeoutId);
            throw fetchError;
        }

    } catch (error) {
        console.error('❌ Errore caricamento dati:', error);

        // Gestione errori migliorata
        let errorMessage = 'Errore durante il caricamento dei dati del grafico';
        let errorDetails = '';

        if (error.name === 'AbortError') {
            errorMessage = 'Timeout della richiesta';
            errorDetails = 'La richiesta ha impiegato troppo tempo. Riprova o verifica la connessione.';
        } else if (error.message.includes('Failed to fetch')) {
            errorMessage = 'Errore di connessione';
            errorDetails = 'Impossibile contattare il server. Verifica la connessione di rete.';
        } else if (error.message.includes('HTTP')) {
            errorMessage = 'Errore del server';
            errorDetails = error.message;
        } else {
            errorMessage = 'Errore elaborazione dati';
            errorDetails = error.message;
        }

        showError(`${errorMessage}\n${errorDetails}`);
    }
}

/**
 * Aggiorna le informazioni sulla fonte dati nell'UI
 */
function updateDataSourceInfo(data) {
    try {
        const badge = document.getElementById('data-source-badge');

        // Gestione badge per nuovo sistema Supabase-first
        if (badge) {
            if (data.supabase_first) {
                badge.textContent = `Supabase (${data.data_source})`;
                badge.className = 'badge ms-2 bg-success';
            } else if (data.source_display) {
                badge.textContent = data.source_display;
                badge.className = `badge ms-2 ${
                    data.source === 'supabase' ? 'bg-success' :
                    data.source === 'legacy' ? 'bg-warning' : 'bg-secondary'
                }`;
            } else {
                badge.textContent = 'Dati caricati';
                badge.className = 'badge ms-2 bg-info';
            }
        }

        // Log informazioni dettagliate per Supabase-first
        if (data.supabase_first) {
            console.log(`📊 SUPABASE: ${data.record_count} record da ${data.data_source}`);
            if (data.technicians_count > 0) {
                console.log(`👥 Tecnici riconosciuti: ${data.technicians_count}`);
            }
            if (data.clients_count > 0) {
                console.log(`🏢 Clienti riconosciuti: ${data.clients_count}`);
            }
            if (data.columns_available) {
                console.log(`📋 Colonne disponibili: ${data.columns_available.length}`);
            }
        } else {
            // Log legacy
            if (data.record_count && data.processed_count) {
                console.log(`📊 Elaborazione: ${data.record_count} → ${data.processed_count} record`);
            }
        }

        if (data.generation_info) {
            console.log('🎯 Configurazione grafico:', data.generation_info);
        }
    } catch (error) {
        console.warn('⚠️ Errore aggiornamento info fonte dati:', error);
    }
}

/**
 * Renderizza il grafico - VERSIONE MIGLIORATA
 */
function renderChart(chartData) {
    if (!chartContainer) {
        console.error('❌ Container grafico non trovato');
        return;
    }

    try {
        console.log('🎨 Rendering grafico...');

        // Verifica Plotly
        if (typeof Plotly === 'undefined') {
            console.error('❌ Plotly non caricato');
            showError('Libreria di grafici non caricata. Ricarica la pagina.');
            return;
        }

        // Validazione dati
        if (!chartData || !chartData.data || chartData.data.length === 0) {
            console.warn('⚠️ Nessun dato per il grafico');
            showError('Nessun dato disponibile per il grafico');
            return;
        }

        // Nascondi loader
        hideLoading();

        // Configurazione Plotly migliorata
        const plotlyOptions = {
            ...plotlyConfig,
            responsive: true,
            displayModeBar: 'hover',
            locale: 'it'
        };

        // Rendering con gestione errori robusta
        Plotly.newPlot(
            chartContainer,
            chartData.data,
            chartData.layout,
            plotlyOptions
        ).then(() => {
            console.log('✅ Grafico renderizzato con successo');

            // Aggiorna titolo se disponibile
            if (chartData.generation_info) {
                updateChartTitleFromData(chartData.generation_info);
            }
        }).catch((plotlyError) => {
            console.error('❌ Errore Plotly:', plotlyError);
            showError(`Errore nel rendering del grafico:\n${plotlyError.message || plotlyError}`);
        });

    } catch (error) {
        console.error('❌ Errore critico rendering:', error);
        showError(`Errore critico nel rendering:\n${error.message}`);
    }
}

/**
 * Aggiorna il titolo del grafico dai dati
 */
function updateChartTitleFromData(generationInfo) {
    if (!chartTitle || !generationInfo) return;

    try {
        let title = `Grafico ${getChartTypeName(generationInfo.chart_type)}`;

        if (generationInfo.x_column) {
            title += ` - ${generationInfo.x_column}`;
        }

        if (generationInfo.y_column) {
            title += ` vs ${generationInfo.y_column}`;
        }

        if (generationInfo.group_by) {
            title += ` (Raggruppato per ${generationInfo.group_by})`;
        }

        chartTitle.textContent = title;
        console.log('📝 Titolo aggiornato:', title);
    } catch (error) {
        console.warn('⚠️ Errore aggiornamento titolo:', error);
    }
}

/**
 * Aggiorna il titolo del grafico - VERSIONE LEGACY
 */
function updateChartTitle() {
    if (!chartTitle) return;

    let title = `Grafico ${getChartTypeName(config.chartType)}`;

    if (config.xColumn) {
        title += ` - ${config.xColumn}`;
    }

    if (config.yColumn) {
        title += ` vs ${config.yColumn}`;
    }

    if (config.groupBy) {
        title += ` (Raggruppato per ${config.groupBy})`;
    }

    chartTitle.textContent = title;
}

/**
 * Ottiene il nome del tipo di grafico
 */
function getChartTypeName(chartType) {
    switch (chartType) {
        case 'bar': return 'a Barre';
        case 'line': return 'a Linee';
        case 'scatter': return 'a Dispersione';
        case 'pie': return 'a Torta';
        case 'heatmap': return 'Mappa di Calore';
        case 'box': return 'Box Plot';
        case 'histogram': return 'Istogramma';
        default: return chartType;
    }
}

/**
 * Resetta il grafico
 */
function resetChart() {
    // Resetta i controlli con valori Supabase-first
    if (chartTypeSelect) chartTypeSelect.value = 'bar';
    if (dataSourceSelect) dataSourceSelect.value = 'normalized_activities'; // Default Supabase
    if (aggregationSelect) aggregationSelect.value = 'sum';
    if (xColumnSelect) xColumnSelect.selectedIndex = xColumnSelect.options.length > 1 ? 1 : 0;
    if (yColumnSelect) yColumnSelect.selectedIndex = 0;
    if (groupBySelect) groupBySelect.selectedIndex = 0;

    // Pulisci cache
    cache.chartData = null;
    cache.availableColumns = null;
    cache.columnTypes = null;

    console.log('🔄 Reset grafico - Configurazione Supabase-first');

    // Aggiorna il grafico
    updateChart();
}

/**
 * Mostra un messaggio di errore
 */
function showError(message) {
    if (!chartContainer) return;

    // Nascondi il loader
    hideLoading();

    // Converti i caratteri di nuova linea in <br> per HTML
    const htmlMessage = message.replace(/\n/g, '<br>');

    // Mostra il messaggio di errore
    chartContainer.innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle me-2"></i>
            ${htmlMessage}
        </div>
    `;
}

/**
 * Mostra il loader
 */
function showLoading() {
    if (!chartContainer) return;

    chartContainer.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Caricamento...</span>
            </div>
            <p class="mt-2">Caricamento grafico...</p>
        </div>
    `;
}

/**
 * Nascondi il loader
 */
function hideLoading() {
    if (!chartContainer) return;

    // Rimuovi eventuali elementi di loading
    const loadingElements = chartContainer.querySelectorAll('.spinner-border, .text-center');
    loadingElements.forEach(element => {
        if (element.textContent.includes('Caricamento')) {
            element.remove();
        }
    });
}

/**
 * Verifica stato connessione Supabase
 */
async function checkSupabaseConnection() {
    try {
        const response = await fetch('/api/supabase/chart_data?x_column=test&type=bar', {
            method: 'GET',
            headers: {
                'Accept': 'application/json'
            }
        });

        const connectionBadge = document.getElementById('supabase-connection-badge');

        if (response.ok || response.status === 400) {
            // 400 è OK perché significa che l'endpoint risponde (parametri mancanti)
            if (connectionBadge) {
                connectionBadge.innerHTML = '<i class="fas fa-database me-1"></i>Connesso';
                connectionBadge.className = 'badge bg-success text-white ms-1';
            }
            console.log('✅ Supabase: Connessione verificata');
            return true;
        } else {
            throw new Error(`HTTP ${response.status}`);
        }
    } catch (error) {
        console.warn('⚠️ Supabase: Connessione non disponibile:', error.message);

        const connectionBadge = document.getElementById('supabase-connection-badge');
        if (connectionBadge) {
            connectionBadge.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>Offline';
            connectionBadge.className = 'badge bg-warning text-dark ms-1';
        }
        return false;
    }
}

// Inizializza la pagina quando il documento è pronto
document.addEventListener('DOMContentLoaded', async () => {
    // Verifica connessione Supabase
    await checkSupabaseConnection();

    // Inizializza grafici interattivi
    initInteractiveCharts();
});
