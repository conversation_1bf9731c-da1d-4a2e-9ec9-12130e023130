#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
🔧 MODALITÀ MINIMAL MANAGER - APP ROBERTO
Sistema per gestire l'attivazione/disattivazione della modalità minimal.

FUNZIONALITÀ:
- Toggle modalità minimal on/off
- API per controllo dinamico
- Interfaccia web per gestione
- Riavvio automatico app quando necessario
"""

import os
import sys
import json
import subprocess
import time
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional
from pathlib import Path

class ModalitaMinimalManager:
    """Manager per la modalità minimal dell'app."""
    
    def __init__(self):
        self.config_file = "modalita_minimal_config.json"
        self.app_file = "app.py"
        self.backup_file = "app_backup_original.py"
        
        # Variabili d'ambiente modalità minimal
        self.minimal_env_vars = [
            'DISABLE_PERFORMANCE_MONITORING',
            'DISABLE_AUTO_TUNING', 
            'DISABLE_AGENT_ORCHESTRATOR',
            'DISABLE_WORKFLOW_SCHEDULER',
            'DISABLE_CACHE_OPTIMIZATION',
            'DISABLE_QUERY_OPTIMIZATION'
        ]
        
        # Sistemi controllati dalla modalità minimal
        self.controlled_systems = {
            'DISABLE_PERFORMANCE_MONITORING': {
                'name': 'Performance Profiler',
                'description': 'Monitoraggio performance in tempo reale',
                'impact': 'Analisi CPU/memoria, profiling query, tracking API',
                'critical': False
            },
            'DISABLE_AUTO_TUNING': {
                'name': 'Auto Tuner',
                'description': 'Ottimizzazione automatica parametri sistema',
                'impact': 'Auto-tuning database, ottimizzazione configurazioni',
                'critical': False
            },
            'DISABLE_CACHE_OPTIMIZATION': {
                'name': 'Intelligent Cache System',
                'description': 'Cache intelligente con predizione accessi',
                'impact': 'Cache predittiva, ottimizzazione memoria, pattern recognition',
                'critical': False
            },
            'DISABLE_QUERY_OPTIMIZATION': {
                'name': 'Query Optimizer',
                'description': 'Ottimizzazione automatica query database',
                'impact': 'Analisi query lente, suggerimenti indici, caching risultati',
                'critical': False
            },
            'DISABLE_AGENT_ORCHESTRATOR': {
                'name': 'Agent Orchestrator',
                'description': 'Orchestrazione avanzata agenti AI',
                'impact': 'Bilanciamento carico agenti, scheduling avanzato',
                'critical': False
            },
            'DISABLE_WORKFLOW_SCHEDULER': {
                'name': 'Workflow Scheduler',
                'description': 'Scheduling automatico workflow',
                'impact': 'Automazione workflow complessi, task scheduling',
                'critical': False
            }
        }

    def get_current_status(self) -> Dict[str, Any]:
        """Ottiene lo status attuale della modalità minimal."""
        
        # Controlla variabili d'ambiente
        env_status = {}
        for var in self.minimal_env_vars:
            env_status[var] = os.environ.get(var, '0') == '1'
        
        # Controlla codice app.py
        code_minimal_active = self._check_minimal_in_code()
        
        # Determina stato generale
        env_minimal_count = sum(1 for enabled in env_status.values() if enabled)
        
        if env_minimal_count == len(self.minimal_env_vars) and code_minimal_active:
            overall_status = "FULL_MINIMAL"
        elif env_minimal_count > 0 or code_minimal_active:
            overall_status = "PARTIAL_MINIMAL"
        else:
            overall_status = "FULL_PERFORMANCE"
        
        return {
            'overall_status': overall_status,
            'code_minimal_active': code_minimal_active,
            'environment_variables': env_status,
            'disabled_systems_count': env_minimal_count,
            'total_systems': len(self.minimal_env_vars),
            'systems_detail': self._get_systems_status(env_status),
            'timestamp': datetime.now().isoformat()
        }

    def _check_minimal_in_code(self) -> bool:
        """Controlla se la modalità minimal è attiva nel codice."""
        try:
            with open(self.app_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Cerca le linee di attivazione modalità minimal
            minimal_lines = [
                "os.environ['DISABLE_PERFORMANCE_MONITORING'] = '1'",
                "os.environ['DISABLE_AUTO_TUNING'] = '1'",
                "os.environ['DISABLE_CACHE_OPTIMIZATION'] = '1'"
            ]
            
            active_count = sum(1 for line in minimal_lines if line in content)
            return active_count >= 2  # Se almeno 2 linee sono presenti
            
        except Exception as e:
            print(f"❌ Errore controllo codice: {e}")
            return False

    def _get_systems_status(self, env_status: Dict[str, bool]) -> Dict[str, Dict[str, Any]]:
        """Ottiene status dettagliato dei sistemi."""
        systems_status = {}
        
        for var, enabled in env_status.items():
            if var in self.controlled_systems:
                system_info = self.controlled_systems[var].copy()
                system_info['disabled'] = enabled
                system_info['status'] = 'DISABLED' if enabled else 'ACTIVE'
                systems_status[var] = system_info
        
        return systems_status

    def toggle_minimal_mode(self, enable: bool = None) -> Dict[str, Any]:
        """
        Attiva o disattiva la modalità minimal.
        
        Args:
            enable: True per attivare, False per disattivare, None per toggle
        """
        current_status = self.get_current_status()
        
        if enable is None:
            # Toggle automatico
            enable = current_status['overall_status'] != 'FULL_MINIMAL'
        
        print(f"🔧 {'Attivando' if enable else 'Disattivando'} modalità minimal...")
        
        try:
            # 1. Modifica variabili d'ambiente
            self._set_environment_variables(enable)
            
            # 2. Modifica codice app.py
            self._modify_app_code(enable)
            
            # 3. Salva configurazione
            self._save_config(enable)
            
            new_status = self.get_current_status()
            
            result = {
                'success': True,
                'action': 'enabled' if enable else 'disabled',
                'previous_status': current_status['overall_status'],
                'new_status': new_status['overall_status'],
                'changes_applied': {
                    'environment_variables': enable,
                    'code_modified': True,
                    'config_saved': True
                },
                'restart_required': True,
                'timestamp': datetime.now().isoformat()
            }
            
            print(f"✅ Modalità minimal {'attivata' if enable else 'disattivata'} con successo!")
            print(f"⚠️ RIAVVIO RICHIESTO: Riavvia l'app per applicare le modifiche")
            
            return result
            
        except Exception as e:
            print(f"❌ Errore toggle modalità minimal: {e}")
            return {
                'success': False,
                'error': str(e),
                'action': 'enabled' if enable else 'disabled',
                'restart_required': False,
                'timestamp': datetime.now().isoformat()
            }

    def _set_environment_variables(self, enable: bool):
        """Imposta le variabili d'ambiente."""
        value = '1' if enable else '0'
        
        for var in self.minimal_env_vars:
            os.environ[var] = value
            print(f"   🔧 {var} = {value}")

    def _modify_app_code(self, enable: bool):
        """Modifica il codice app.py per attivare/disattivare modalità minimal."""
        
        # Backup del file originale
        if not os.path.exists(self.backup_file):
            with open(self.app_file, 'r', encoding='utf-8') as f:
                content = f.read()
            with open(self.backup_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"   💾 Backup creato: {self.backup_file}")
        
        # Leggi file attuale
        with open(self.app_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Trova e modifica le linee della modalità minimal
        modified_lines = []
        in_minimal_section = False
        
        for line in lines:
            if "# MODALITÀ MINIMAL - DISABILITA MONITORAGGIO" in line:
                in_minimal_section = True
                modified_lines.append(line)
                continue
            
            if in_minimal_section and "print(\"✅ MODALITÀ MINIMAL:" in line:
                in_minimal_section = False
                modified_lines.append(line)
                continue
            
            if in_minimal_section and "os.environ[" in line and "DISABLE_" in line:
                if enable:
                    # Attiva modalità minimal (rimuovi commento se presente)
                    clean_line = line.lstrip('# ')
                    modified_lines.append(clean_line)
                else:
                    # Disattiva modalità minimal (commenta la linea)
                    if not line.strip().startswith('#'):
                        modified_lines.append(f"# {line}")
                    else:
                        modified_lines.append(line)
            else:
                modified_lines.append(line)
        
        # Scrivi file modificato
        with open(self.app_file, 'w', encoding='utf-8') as f:
            f.writelines(modified_lines)
        
        print(f"   📝 Codice app.py modificato")

    def _save_config(self, enable: bool):
        """Salva la configurazione attuale."""
        config = {
            'minimal_mode_enabled': enable,
            'last_modified': datetime.now().isoformat(),
            'environment_variables': {var: enable for var in self.minimal_env_vars},
            'systems_status': self._get_systems_status({var: enable for var in self.minimal_env_vars})
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"   💾 Configurazione salvata: {self.config_file}")

    def restore_original_code(self) -> bool:
        """Ripristina il codice originale dal backup."""
        try:
            if os.path.exists(self.backup_file):
                with open(self.backup_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                with open(self.app_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ Codice originale ripristinato da {self.backup_file}")
                return True
            else:
                print(f"❌ File backup non trovato: {self.backup_file}")
                return False
        except Exception as e:
            print(f"❌ Errore ripristino: {e}")
            return False

    def generate_status_report(self) -> str:
        """Genera un report dettagliato dello status."""
        status = self.get_current_status()
        
        report = f"""# 🔧 REPORT MODALITÀ MINIMAL - APP ROBERTO

## 📊 STATUS GENERALE
- **Modalità attuale**: {status['overall_status']}
- **Codice minimal attivo**: {'✅ Sì' if status['code_minimal_active'] else '❌ No'}
- **Sistemi disabilitati**: {status['disabled_systems_count']}/{status['total_systems']}
- **Timestamp**: {status['timestamp']}

## 🎯 SISTEMI CONTROLLATI

"""
        
        for var, system in status['systems_detail'].items():
            status_icon = "❌" if system['disabled'] else "✅"
            report += f"### {status_icon} {system['name']}\n"
            report += f"- **Status**: {system['status']}\n"
            report += f"- **Descrizione**: {system['description']}\n"
            report += f"- **Impatto**: {system['impact']}\n"
            report += f"- **Critico**: {'Sì' if system['critical'] else 'No'}\n\n"
        
        report += f"""## 🔧 AZIONI DISPONIBILI

### ✅ Attivare Modalità Minimal
```python
manager = ModalitaMinimalManager()
result = manager.toggle_minimal_mode(enable=True)
```

### ❌ Disattivare Modalità Minimal  
```python
manager = ModalitaMinimalManager()
result = manager.toggle_minimal_mode(enable=False)
```

### 🔄 Toggle Automatico
```python
manager = ModalitaMinimalManager()
result = manager.toggle_minimal_mode()  # Cambia stato attuale
```

### 🔙 Ripristino Originale
```python
manager = ModalitaMinimalManager()
manager.restore_original_code()
```

## ⚠️ IMPORTANTE

**RIAVVIO RICHIESTO**: Dopo ogni modifica, riavvia l'app per applicare le modifiche:
```bash
python app.py
```

---
*Report generato automaticamente da ModalitaMinimalManager*
"""
        
        return report

    def create_web_interface_code(self) -> str:
        """Genera codice per interfaccia web di gestione."""
        return '''
<!-- INTERFACCIA WEB MODALITÀ MINIMAL -->
<div class="minimal-mode-manager">
    <h3>🔧 Gestione Modalità Minimal</h3>
    
    <div class="status-panel">
        <div id="minimal-status">Caricamento status...</div>
    </div>
    
    <div class="controls-panel">
        <button onclick="toggleMinimalMode(true)" class="btn btn-warning">
            ❌ Attiva Modalità Minimal
        </button>
        <button onclick="toggleMinimalMode(false)" class="btn btn-success">
            ✅ Disattiva Modalità Minimal
        </button>
        <button onclick="toggleMinimalMode()" class="btn btn-info">
            🔄 Toggle Automatico
        </button>
    </div>
    
    <div class="systems-panel">
        <h4>Sistemi Controllati</h4>
        <div id="systems-list">Caricamento sistemi...</div>
    </div>
</div>

<script>
async function loadMinimalStatus() {
    try {
        const response = await fetch('/api/minimal-mode/status');
        const data = await response.json();
        
        document.getElementById('minimal-status').innerHTML = `
            <strong>Status:</strong> ${data.overall_status}<br>
            <strong>Sistemi disabilitati:</strong> ${data.disabled_systems_count}/${data.total_systems}
        `;
        
        // Aggiorna lista sistemi
        const systemsList = document.getElementById('systems-list');
        systemsList.innerHTML = '';
        
        for (const [var, system] of Object.entries(data.systems_detail)) {
            const statusIcon = system.disabled ? '❌' : '✅';
            systemsList.innerHTML += `
                <div class="system-item">
                    ${statusIcon} <strong>${system.name}</strong>: ${system.status}
                    <br><small>${system.description}</small>
                </div>
            `;
        }
        
    } catch (error) {
        console.error('Errore caricamento status:', error);
    }
}

async function toggleMinimalMode(enable = null) {
    try {
        const response = await fetch('/api/minimal-mode/toggle', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({enable: enable})
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert(`Modalità minimal ${result.action}! Riavvia l'app per applicare le modifiche.`);
            loadMinimalStatus();
        } else {
            alert(`Errore: ${result.error}`);
        }
        
    } catch (error) {
        console.error('Errore toggle modalità minimal:', error);
        alert('Errore durante il toggle della modalità minimal');
    }
}

// Carica status all'avvio
document.addEventListener('DOMContentLoaded', loadMinimalStatus);
</script>
'''


def main():
    """Funzione principale per test."""
    manager = ModalitaMinimalManager()
    
    print("🔧 MODALITÀ MINIMAL MANAGER")
    print("=" * 40)
    
    # Mostra status attuale
    status = manager.get_current_status()
    print(f"📊 Status attuale: {status['overall_status']}")
    print(f"🔧 Sistemi disabilitati: {status['disabled_systems_count']}/{status['total_systems']}")
    
    # Genera report
    report = manager.generate_status_report()
    with open("MODALITA_MINIMAL_STATUS.md", "w", encoding="utf-8") as f:
        f.write(report)
    print(f"📄 Report salvato: MODALITA_MINIMAL_STATUS.md")
    
    # Menu interattivo
    while True:
        print("\n🎯 AZIONI DISPONIBILI:")
        print("1. 📊 Mostra status dettagliato")
        print("2. ❌ Attiva modalità minimal")
        print("3. ✅ Disattiva modalità minimal")
        print("4. 🔄 Toggle automatico")
        print("5. 🔙 Ripristina codice originale")
        print("6. 🚪 Esci")
        
        choice = input("\nScegli un'azione (1-6): ").strip()
        
        if choice == "1":
            status = manager.get_current_status()
            print(f"\n📊 STATUS DETTAGLIATO:")
            print(f"   Overall: {status['overall_status']}")
            print(f"   Codice minimal: {status['code_minimal_active']}")
            print(f"   Sistemi disabilitati: {status['disabled_systems_count']}")
            
        elif choice == "2":
            result = manager.toggle_minimal_mode(enable=True)
            print(f"✅ Risultato: {result}")
            
        elif choice == "3":
            result = manager.toggle_minimal_mode(enable=False)
            print(f"✅ Risultato: {result}")
            
        elif choice == "4":
            result = manager.toggle_minimal_mode()
            print(f"✅ Risultato: {result}")
            
        elif choice == "5":
            success = manager.restore_original_code()
            print(f"✅ Ripristino: {'Successo' if success else 'Fallito'}")
            
        elif choice == "6":
            print("👋 Arrivederci!")
            break
            
        else:
            print("❌ Scelta non valida")


if __name__ == "__main__":
    main()
