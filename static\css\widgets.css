/**
 * <PERSON><PERSON> per i widget della dashboard
 * Versione: 1.0.0
 */

/* Layout a griglia per i widget */
.widget-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

/* Widget container */
.widget {
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.widget:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* Widget header */
.widget-header {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.widget-title {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
}

.widget-actions {
    display: flex;
    gap: 0.5rem;
}

.widget-action {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.widget-action:hover {
    color: #343a40;
    background-color: #f8f9fa;
}

/* Widget body */
.widget-body {
    padding: 1rem;
    flex: 1;
    overflow: auto;
}

/* Widget footer */
.widget-footer {
    padding: 0.75rem 1rem;
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa;
    border-radius: 0 0 0.5rem 0.5rem;
    font-size: 0.875rem;
    color: #6c757d;
}

/* Widget sizes */
.widget-sm {
    grid-column: span 1;
}

.widget-md {
    grid-column: span 2;
}

.widget-lg {
    grid-column: span 3;
}

.widget-xl {
    grid-column: span 4;
}

/* Widget types */
.widget-chart .widget-body {
    min-height: 300px;
    position: relative;
}

.widget-table .widget-body {
    padding: 0;
}

.widget-table table {
    margin-bottom: 0;
}

.widget-kpi {
    text-align: center;
}

.widget-kpi .widget-body {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.widget-kpi-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.widget-kpi-label {
    font-size: 1rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.widget-kpi-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    width: 4rem;
    height: 4rem;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
}

/* Widget colors */
.widget-primary .widget-kpi-value {
    color: #007bff;
}

.widget-primary .widget-kpi-icon {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.widget-success .widget-kpi-value {
    color: #28a745;
}

.widget-success .widget-kpi-icon {
    color: #28a745;
    background-color: rgba(40, 167, 69, 0.1);
}

.widget-info .widget-kpi-value {
    color: #17a2b8;
}

.widget-info .widget-kpi-icon {
    color: #17a2b8;
    background-color: rgba(23, 162, 184, 0.1);
}

.widget-warning .widget-kpi-value {
    color: #ffc107;
}

.widget-warning .widget-kpi-icon {
    color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
}

.widget-danger .widget-kpi-value {
    color: #dc3545;
}

.widget-danger .widget-kpi-icon {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}

/* Widget progress */
.widget-progress {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}

.widget-progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
}

.widget-progress-bar {
    height: 0.5rem;
    border-radius: 0.25rem;
    background-color: #e9ecef;
    overflow: hidden;
}

.widget-progress-value {
    height: 100%;
    border-radius: 0.25rem;
    transition: width 0.6s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .widget-grid {
        grid-template-columns: 1fr;
    }
    
    .widget-sm, .widget-md, .widget-lg, .widget-xl {
        grid-column: span 1;
    }
}

/* Animation for loading state */
.widget-loading {
    position: relative;
}

.widget-loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    border-radius: 0.5rem;
}

.widget-loading::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2rem;
    height: 2rem;
    border: 0.25rem solid #e9ecef;
    border-top-color: #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 11;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}
