-- ===============================================
-- SCHEMA ESTESO SUPABASE PER SISTEMA INTELLIGENTE
-- Fase 2: Integrazione Database Avanzata
-- ===============================================

-- Abilita le estensioni necessarie
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- Per ricerca fuzzy
CREATE EXTENSION IF NOT EXISTS "unaccent"; -- Per normalizzazione testo

-- ===============================================
-- TABELLE ENTITÀ MASTER
-- ===============================================

-- Tabella master per tecnici/dipendenti
CREATE TABLE IF NOT EXISTS master_technicians (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    normalized_name VARCHAR(255) NOT NULL UNIQUE,
    original_names TEXT[], -- Array di varianti del nome
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    email VARCHAR(255),
    phone VARCHAR(50),
    department VARCHAR(100),
    role VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    confidence_score DECIMAL(3,2) DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Tabella master per clienti/aziende
CREATE TABLE IF NOT EXISTS master_clients (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    normalized_name VARCHAR(255) NOT NULL UNIQUE,
    original_names TEXT[], -- Array di varianti del nome
    company_type VARCHAR(50), -- SRL, SPA, etc.
    vat_number VARCHAR(50),
    address TEXT,
    city VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'IT',
    contact_person VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    confidence_score DECIMAL(3,2) DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Tabella master per progetti
CREATE TABLE IF NOT EXISTS master_projects (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    project_code VARCHAR(100) NOT NULL UNIQUE,
    project_name VARCHAR(255) NOT NULL,
    client_id INTEGER REFERENCES master_clients(id),
    manager_id INTEGER REFERENCES master_technicians(id),
    status VARCHAR(50) DEFAULT 'active',
    priority VARCHAR(20) DEFAULT 'medium',
    start_date DATE,
    end_date DATE,
    estimated_hours DECIMAL(8,2),
    budget DECIMAL(12,2),
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    confidence_score DECIMAL(3,2) DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Tabella master per veicoli
CREATE TABLE IF NOT EXISTS master_vehicles (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    vehicle_name VARCHAR(100) NOT NULL,
    license_plate VARCHAR(20),
    vehicle_type VARCHAR(50),
    brand VARCHAR(50),
    model VARCHAR(50),
    year INTEGER,
    fuel_type VARCHAR(30),
    is_active BOOLEAN DEFAULT true,
    confidence_score DECIMAL(3,2) DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- ===============================================
-- TABELLE DATI NORMALIZZATI
-- ===============================================

-- Tabella per dati TeamViewer normalizzati
CREATE TABLE IF NOT EXISTS normalized_teamviewer (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    file_upload_id INTEGER REFERENCES file_uploads(id) ON DELETE CASCADE,
    technician_id INTEGER REFERENCES master_technicians(id),
    client_id INTEGER REFERENCES master_clients(id),
    session_start TIMESTAMP,
    session_end TIMESTAMP,
    duration_minutes INTEGER,
    session_type VARCHAR(100),
    computer_name VARCHAR(255),
    session_id VARCHAR(100),
    billing_rate DECIMAL(8,2),
    total_cost DECIMAL(10,2),
    notes TEXT,
    quality_score DECIMAL(3,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Tabella per dati attività normalizzati
CREATE TABLE IF NOT EXISTS normalized_activities (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    file_upload_id INTEGER REFERENCES file_uploads(id) ON DELETE CASCADE,
    technician_id INTEGER REFERENCES master_technicians(id),
    client_id INTEGER REFERENCES master_clients(id),
    project_id INTEGER REFERENCES master_projects(id),
    activity_date DATE,
    start_time TIME,
    end_time TIME,
    duration_hours DECIMAL(5,2),
    activity_type VARCHAR(100),
    description TEXT,
    ticket_number VARCHAR(100),
    billing_rate DECIMAL(8,2),
    total_cost DECIMAL(10,2),
    status VARCHAR(50) DEFAULT 'completed',
    quality_score DECIMAL(3,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Tabella per dati registro auto normalizzati
CREATE TABLE IF NOT EXISTS normalized_vehicle_usage (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    file_upload_id INTEGER REFERENCES file_uploads(id) ON DELETE CASCADE,
    technician_id INTEGER REFERENCES master_technicians(id),
    vehicle_id INTEGER REFERENCES master_vehicles(id),
    client_id INTEGER REFERENCES master_clients(id),
    usage_date DATE,
    pickup_time TIMESTAMP,
    return_time TIMESTAMP,
    duration_hours DECIMAL(5,2),
    kilometers INTEGER,
    fuel_cost DECIMAL(8,2),
    destination VARCHAR(255),
    purpose TEXT,
    notes TEXT,
    quality_score DECIMAL(3,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Tabella per dati calendario normalizzati
CREATE TABLE IF NOT EXISTS normalized_calendar (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    file_upload_id INTEGER REFERENCES file_uploads(id) ON DELETE CASCADE,
    technician_id INTEGER REFERENCES master_technicians(id),
    client_id INTEGER REFERENCES master_clients(id),
    project_id INTEGER REFERENCES master_projects(id),
    event_date DATE,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration_hours DECIMAL(5,2),
    event_title VARCHAR(255),
    event_type VARCHAR(100),
    location VARCHAR(255),
    attendees TEXT[],
    description TEXT,
    status VARCHAR(50) DEFAULT 'scheduled',
    quality_score DECIMAL(3,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Tabella per dati timbrature normalizzati
CREATE TABLE IF NOT EXISTS normalized_timesheets (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    file_upload_id INTEGER REFERENCES file_uploads(id) ON DELETE CASCADE,
    technician_id INTEGER REFERENCES master_technicians(id),
    timesheet_date DATE,
    clock_in TIME,
    clock_out TIME,
    break_duration_minutes INTEGER DEFAULT 0,
    total_hours DECIMAL(5,2),
    overtime_hours DECIMAL(5,2) DEFAULT 0,
    timesheet_type VARCHAR(50) DEFAULT 'regular',
    notes TEXT,
    quality_score DECIMAL(3,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- ===============================================
-- TABELLE SISTEMA INTELLIGENTE
-- ===============================================

-- Tabella per log estrazione entità
CREATE TABLE IF NOT EXISTS entity_extraction_log (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    file_upload_id INTEGER REFERENCES file_uploads(id) ON DELETE CASCADE,
    extraction_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    file_type VARCHAR(50),
    entities_extracted JSONB,
    column_mapping JSONB,
    confidence_scores JSONB,
    statistics JSONB,
    recommendations TEXT[],
    processing_time_ms INTEGER,
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Tabella per log standardizzazione
CREATE TABLE IF NOT EXISTS standardization_log (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    file_upload_id INTEGER REFERENCES file_uploads(id) ON DELETE CASCADE,
    standardization_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    operations_applied JSONB,
    quality_metrics JSONB,
    changes_summary JSONB,
    processing_time_ms INTEGER,
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Tabella per configurazioni sistema intelligente
CREATE TABLE IF NOT EXISTS intelligent_system_config (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    config_category VARCHAR(100) NOT NULL,
    config_key VARCHAR(100) NOT NULL,
    config_value JSONB,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    UNIQUE(config_category, config_key)
);

-- ===============================================
-- INDICI PER PERFORMANCE
-- ===============================================

-- Indici per ricerca fuzzy sui nomi
CREATE INDEX IF NOT EXISTS idx_master_technicians_name_trgm ON master_technicians USING gin (normalized_name gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_master_clients_name_trgm ON master_clients USING gin (normalized_name gin_trgm_ops);

-- Indici per ricerca su array di nomi originali
CREATE INDEX IF NOT EXISTS idx_master_technicians_original_names ON master_technicians USING gin (original_names);
CREATE INDEX IF NOT EXISTS idx_master_clients_original_names ON master_clients USING gin (original_names);

-- Indici per date e timestamp
CREATE INDEX IF NOT EXISTS idx_normalized_teamviewer_date ON normalized_teamviewer (session_start);
CREATE INDEX IF NOT EXISTS idx_normalized_activities_date ON normalized_activities (activity_date);
CREATE INDEX IF NOT EXISTS idx_normalized_vehicle_usage_date ON normalized_vehicle_usage (usage_date);
CREATE INDEX IF NOT EXISTS idx_normalized_calendar_date ON normalized_calendar (event_date);
CREATE INDEX IF NOT EXISTS idx_normalized_timesheets_date ON normalized_timesheets (timesheet_date);

-- Indici per foreign keys
CREATE INDEX IF NOT EXISTS idx_normalized_teamviewer_technician ON normalized_teamviewer (technician_id);
CREATE INDEX IF NOT EXISTS idx_normalized_teamviewer_client ON normalized_teamviewer (client_id);
CREATE INDEX IF NOT EXISTS idx_normalized_activities_technician ON normalized_activities (technician_id);
CREATE INDEX IF NOT EXISTS idx_normalized_activities_client ON normalized_activities (client_id);
CREATE INDEX IF NOT EXISTS idx_normalized_activities_project ON normalized_activities (project_id);

-- Indici per quality score e confidence
CREATE INDEX IF NOT EXISTS idx_master_technicians_confidence ON master_technicians (confidence_score);
CREATE INDEX IF NOT EXISTS idx_master_clients_confidence ON master_clients (confidence_score);
CREATE INDEX IF NOT EXISTS idx_normalized_teamviewer_quality ON normalized_teamviewer (quality_score);
CREATE INDEX IF NOT EXISTS idx_normalized_activities_quality ON normalized_activities (quality_score);

-- ===============================================
-- TRIGGER E FUNZIONI
-- ===============================================

-- Funzione per aggiornare updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger per aggiornamento automatico updated_at
CREATE TRIGGER update_master_technicians_updated_at BEFORE UPDATE ON master_technicians
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_master_clients_updated_at BEFORE UPDATE ON master_clients
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_master_projects_updated_at BEFORE UPDATE ON master_projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_master_vehicles_updated_at BEFORE UPDATE ON master_vehicles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_intelligent_system_config_updated_at BEFORE UPDATE ON intelligent_system_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Funzione per normalizzazione automatica nomi
CREATE OR REPLACE FUNCTION normalize_name(input_name TEXT)
RETURNS TEXT AS $$
BEGIN
    -- Rimuove accenti, converte in minuscolo, rimuove spazi extra
    RETURN TRIM(LOWER(UNACCENT(REGEXP_REPLACE(input_name, '\s+', ' ', 'g'))));
END;
$$ LANGUAGE plpgsql;

-- Funzione per calcolo confidence score basato su frequenza
CREATE OR REPLACE FUNCTION calculate_confidence_score(entity_frequency INTEGER, total_occurrences INTEGER)
RETURNS DECIMAL(3,2) AS $$
BEGIN
    IF total_occurrences = 0 THEN
        RETURN 0.5;
    END IF;

    -- Calcola confidence basato su frequenza relativa
    RETURN LEAST(1.0, GREATEST(0.1, (entity_frequency::DECIMAL / total_occurrences::DECIMAL) * 2));
END;
$$ LANGUAGE plpgsql;

-- ===============================================
-- ROW LEVEL SECURITY (RLS)
-- ===============================================

-- Abilita RLS su tutte le nuove tabelle
ALTER TABLE master_technicians ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE master_vehicles ENABLE ROW LEVEL SECURITY;
ALTER TABLE normalized_teamviewer ENABLE ROW LEVEL SECURITY;
ALTER TABLE normalized_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE normalized_vehicle_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE normalized_calendar ENABLE ROW LEVEL SECURITY;
ALTER TABLE normalized_timesheets ENABLE ROW LEVEL SECURITY;
ALTER TABLE entity_extraction_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE standardization_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE intelligent_system_config ENABLE ROW LEVEL SECURITY;

-- Policy per master_technicians
CREATE POLICY "Users can view own technicians" ON master_technicians
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can insert own technicians" ON master_technicians
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own technicians" ON master_technicians
    FOR UPDATE USING (auth.uid() = user_id);

-- Policy per master_clients
CREATE POLICY "Users can view own clients" ON master_clients
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can insert own clients" ON master_clients
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own clients" ON master_clients
    FOR UPDATE USING (auth.uid() = user_id);

-- Policy per master_projects
CREATE POLICY "Users can view own projects" ON master_projects
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can insert own projects" ON master_projects
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own projects" ON master_projects
    FOR UPDATE USING (auth.uid() = user_id);

-- Policy per master_vehicles
CREATE POLICY "Users can view own vehicles" ON master_vehicles
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can insert own vehicles" ON master_vehicles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own vehicles" ON master_vehicles
    FOR UPDATE USING (auth.uid() = user_id);

-- Policy per dati normalizzati (esempio per teamviewer, simili per altri)
CREATE POLICY "Users can view own normalized teamviewer" ON normalized_teamviewer
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own normalized teamviewer" ON normalized_teamviewer
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy per log sistema
CREATE POLICY "Users can view own extraction logs" ON entity_extraction_log
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own extraction logs" ON entity_extraction_log
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view own standardization logs" ON standardization_log
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own standardization logs" ON standardization_log
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- ===============================================
-- VISTE PER ANALISI
-- ===============================================

-- Vista per analisi produttività tecnici
CREATE OR REPLACE VIEW technician_productivity AS
SELECT
    t.normalized_name as technician_name,
    COUNT(DISTINCT a.id) as total_activities,
    SUM(a.duration_hours) as total_hours,
    AVG(a.duration_hours) as avg_activity_duration,
    COUNT(DISTINCT a.client_id) as unique_clients,
    COUNT(DISTINCT a.project_id) as unique_projects,
    AVG(a.quality_score) as avg_quality_score,
    DATE_TRUNC('month', a.activity_date) as month_year
FROM master_technicians t
LEFT JOIN normalized_activities a ON t.id = a.technician_id
WHERE t.is_active = true
GROUP BY t.id, t.normalized_name, DATE_TRUNC('month', a.activity_date);

-- Vista per analisi clienti
CREATE OR REPLACE VIEW client_analysis AS
SELECT
    c.normalized_name as client_name,
    COUNT(DISTINCT a.id) as total_activities,
    SUM(a.duration_hours) as total_hours,
    SUM(a.total_cost) as total_revenue,
    COUNT(DISTINCT a.technician_id) as technicians_involved,
    COUNT(DISTINCT a.project_id) as active_projects,
    AVG(a.quality_score) as avg_quality_score,
    MAX(a.activity_date) as last_activity_date
FROM master_clients c
LEFT JOIN normalized_activities a ON c.id = a.client_id
WHERE c.is_active = true
GROUP BY c.id, c.normalized_name;

-- Vista per analisi progetti
CREATE OR REPLACE VIEW project_analysis AS
SELECT
    p.project_code,
    p.project_name,
    c.normalized_name as client_name,
    m.normalized_name as manager_name,
    COUNT(DISTINCT a.id) as total_activities,
    SUM(a.duration_hours) as actual_hours,
    p.estimated_hours,
    (SUM(a.duration_hours) / NULLIF(p.estimated_hours, 0)) * 100 as completion_percentage,
    SUM(a.total_cost) as actual_cost,
    p.budget,
    (SUM(a.total_cost) / NULLIF(p.budget, 0)) * 100 as budget_usage_percentage,
    AVG(a.quality_score) as avg_quality_score,
    p.status
FROM master_projects p
LEFT JOIN master_clients c ON p.client_id = c.id
LEFT JOIN master_technicians m ON p.manager_id = m.id
LEFT JOIN normalized_activities a ON p.id = a.project_id
GROUP BY p.id, p.project_code, p.project_name, c.normalized_name, m.normalized_name, p.estimated_hours, p.budget, p.status;

-- ===============================================
-- CONFIGURAZIONI INIZIALI
-- ===============================================

-- Inserisci configurazioni di default per sistema intelligente
INSERT INTO intelligent_system_config (config_category, config_key, config_value, description) VALUES
('file_detection', 'confidence_threshold', '0.7', 'Soglia minima di confidenza per riconoscimento file'),
('file_detection', 'fuzzy_threshold', '0.8', 'Soglia per fuzzy matching colonne'),
('entity_extraction', 'min_confidence', '0.6', 'Confidenza minima per estrazione entità'),
('entity_extraction', 'auto_learning', 'true', 'Abilita apprendimento automatico entità'),
('standardization', 'auto_standardize', 'true', 'Abilita standardizzazione automatica'),
('standardization', 'similarity_threshold', '0.85', 'Soglia similarità per deduplicazione'),
('quality_control', 'min_quality_score', '0.7', 'Punteggio qualità minimo accettabile'),
('performance', 'batch_size', '1000', 'Dimensione batch per elaborazioni massive'),
('alerts', 'enable_notifications', 'true', 'Abilita notifiche automatiche'),
('backup', 'auto_backup_days', '7', 'Giorni per backup automatico')
ON CONFLICT (config_category, config_key) DO NOTHING;

-- ===============================================
-- COMMENTI DOCUMENTAZIONE
-- ===============================================

COMMENT ON TABLE master_technicians IS 'Tabella master per tecnici/dipendenti con normalizzazione nomi';
COMMENT ON TABLE master_clients IS 'Tabella master per clienti/aziende con normalizzazione nomi';
COMMENT ON TABLE master_projects IS 'Tabella master per progetti con codici standardizzati';
COMMENT ON TABLE master_vehicles IS 'Tabella master per veicoli aziendali';
COMMENT ON TABLE normalized_teamviewer IS 'Dati TeamViewer normalizzati e collegati alle entità master';
COMMENT ON TABLE normalized_activities IS 'Dati attività normalizzati e collegati alle entità master';
COMMENT ON TABLE normalized_vehicle_usage IS 'Dati utilizzo veicoli normalizzati';
COMMENT ON TABLE normalized_calendar IS 'Dati calendario normalizzati';
COMMENT ON TABLE normalized_timesheets IS 'Dati timbrature normalizzati';
COMMENT ON TABLE entity_extraction_log IS 'Log delle operazioni di estrazione entità';
COMMENT ON TABLE standardization_log IS 'Log delle operazioni di standardizzazione';
COMMENT ON TABLE intelligent_system_config IS 'Configurazioni per il sistema intelligente';

COMMENT ON VIEW technician_productivity IS 'Vista per analisi produttività tecnici per mese';
COMMENT ON VIEW client_analysis IS 'Vista per analisi performance e valore clienti';
COMMENT ON VIEW project_analysis IS 'Vista per analisi avanzamento e costi progetti';

-- ===============================================
-- FINE SCHEMA ESTESO
-- ===============================================
