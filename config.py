#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os

class Config:
    """Configurazione base dell'applicazione"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'bait_service_app_secret_key'
    UPLOAD_FOLDER = os.path.join(os.getcwd(), 'uploads')
    ALLOWED_EXTENSIONS = {'csv', 'xlsx', 'xls'}
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max

    # Assicurarsi che la cartella uploads esista
    os.makedirs(UPLOAD_FOLDER, exist_ok=True)

    # Configurazione per il parsing delle date
    DATE_FORMAT = '%d/%m/%Y'
    DATETIME_FORMAT = '%d/%m/%Y %H:%M'

    # Configurazione per la visualizzazione
    ITEMS_PER_PAGE = 20

    # Mappatura dei tipi di file
    FILE_TYPES = {
        'teamviewer_bait': {
            'description': 'Sessioni di assistenza remota',
            'icon': 'headset'
        },
        'teamviewer_gruppo': {
            'description': 'Dati di gruppo TeamViewer',
            'icon': 'users'
        },
        'calendario': {
            'description': 'Appuntamenti e pianificazione',
            'icon': 'calendar'
        },
        'controlli': {
            'description': 'Controlli quotidiani',
            'icon': 'clipboard-check'
        },
        'permessi': {
            'description': 'Permessi',
            'icon': 'calendar-check'
        },
        'timbrature': {
            'description': 'Timbrature',
            'icon': 'clock'
        },
        'registro_auto': {
            'description': 'Registro utilizzo auto aziendali',
            'icon': 'car'
        },
        'progetti': {
            'description': 'Esportazione progetti',
            'icon': 'project-diagram'
        }
    }

    # Configurazione per i grafici
    CHART_COLORS = [
        '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
        '#5a5c69', '#6610f2', '#6f42c1', '#fd7e14', '#20c9a6'
    ]

class DevelopmentConfig(Config):
    """Configurazione per l'ambiente di sviluppo"""
    DEBUG = True
    TESTING = False

class TestingConfig(Config):
    """Configurazione per l'ambiente di test"""
    DEBUG = False
    TESTING = True
    WTF_CSRF_ENABLED = False

class ProductionConfig(Config):
    """Configurazione per l'ambiente di produzione"""
    DEBUG = False
    TESTING = False

    # In produzione, usa una chiave segreta più sicura
    SECRET_KEY = os.environ.get('SECRET_KEY') or os.urandom(24)

# Configurazione predefinita
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

# Configurazione attiva
active_config = config[os.environ.get('FLASK_ENV', 'default')]
