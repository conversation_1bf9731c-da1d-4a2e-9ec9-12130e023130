#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sistema di Monitoring per Produzione - Fase 7.
Monitoraggio avanzato per il sistema di riconoscimento intelligente.
"""

import sys
import os
import psutil
import time
import json
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
from flask import Blueprint, jsonify, request
import logging

# Aggiungi il percorso del progetto
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Blueprint per monitoring
monitoring_bp = Blueprint('monitoring', __name__, url_prefix='/monitoring')

class ProductionMonitoring:
    """Sistema di monitoring per produzione."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or self._load_default_config()
        self.metrics_history = []
        self.alerts_history = []
        self.start_time = datetime.now()

        logger.info("Sistema di Monitoring inizializzato")

    def _load_default_config(self) -> Dict[str, Any]:
        """Carica configurazione default monitoring."""
        return {
            'health_check_interval': 60,
            'metrics_retention_hours': 24,
            'alert_thresholds': {
                'cpu_usage': 80,
                'memory_usage': 85,
                'disk_usage': 90,
                'response_time': 5000,
                'error_rate': 5
            },
            'alerting_enabled': True,
            'metrics_enabled': True
        }

    async def collect_system_metrics(self) -> Dict[str, Any]:
        """Raccoglie metriche sistema."""
        try:
            # Metriche sistema
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('.')

            # Metriche processo
            process = psutil.Process(os.getpid())
            process_memory = process.memory_info()

            # Metriche rete (se disponibili)
            try:
                network = psutil.net_io_counters()
                network_metrics = {
                    'bytes_sent': network.bytes_sent,
                    'bytes_recv': network.bytes_recv,
                    'packets_sent': network.packets_sent,
                    'packets_recv': network.packets_recv
                }
            except:
                network_metrics = {}

            metrics = {
                'timestamp': datetime.now().isoformat(),
                'system': {
                    'cpu_usage_percent': cpu_percent,
                    'memory_total_gb': round(memory.total / (1024**3), 2),
                    'memory_used_gb': round(memory.used / (1024**3), 2),
                    'memory_usage_percent': memory.percent,
                    'disk_total_gb': round(disk.total / (1024**3), 2),
                    'disk_used_gb': round(disk.used / (1024**3), 2),
                    'disk_usage_percent': round((disk.used / disk.total) * 100, 2),
                    'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else [0, 0, 0]
                },
                'process': {
                    'memory_rss_mb': round(process_memory.rss / (1024**2), 2),
                    'memory_vms_mb': round(process_memory.vms / (1024**2), 2),
                    'cpu_percent': process.cpu_percent(),
                    'num_threads': process.num_threads(),
                    'open_files': len(process.open_files()),
                    'connections': len(process.connections())
                },
                'network': network_metrics
            }

            # Aggiungi metriche applicazione
            app_metrics = await self._collect_app_metrics()
            metrics['application'] = app_metrics

            # Salva nella cronologia
            self.metrics_history.append(metrics)

            # Mantieni solo le ultime N ore
            cutoff_time = datetime.now() - timedelta(hours=self.config['metrics_retention_hours'])
            self.metrics_history = [
                m for m in self.metrics_history
                if datetime.fromisoformat(m['timestamp']) > cutoff_time
            ]

            return metrics

        except Exception as e:
            logger.error(f"Errore raccolta metriche: {str(e)}")
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}

    async def _collect_app_metrics(self) -> Dict[str, Any]:
        """Raccoglie metriche specifiche dell'applicazione."""
        app_metrics = {
            'uptime_seconds': int((datetime.now() - self.start_time).total_seconds()),
            'database_status': 'unknown',
            'agents_status': 'unknown',
            'llm_status': 'unknown',
            'cache_status': 'unknown'
        }

        try:
            # Verifica database
            from supabase_integration import SupabaseManager
            supabase_manager = SupabaseManager()
            app_metrics['database_status'] = 'connected' if supabase_manager.is_connected else 'disconnected'

        except Exception as e:
            app_metrics['database_status'] = f'error: {str(e)}'

        try:
            # Verifica agenti
            from advanced_agent_framework import get_advanced_orchestrator
            orchestrator = get_advanced_orchestrator()

            agents_health = await orchestrator.health_check()
            app_metrics['agents_status'] = agents_health['orchestrator_status']
            app_metrics['agents_count'] = agents_health['agents_count']
            app_metrics['queue_size'] = agents_health['queue_size']
            app_metrics['running_tasks'] = agents_health['running_tasks']
            app_metrics['completed_tasks'] = agents_health['completed_tasks']

        except Exception as e:
            app_metrics['agents_status'] = f'error: {str(e)}'

        try:
            # Verifica LLM
            from enhanced_llm_assistant import EnhancedLLMAssistant
            llm_assistant = EnhancedLLMAssistant()

            if llm_assistant.client:
                llm_health = await llm_assistant.health_check()
                app_metrics['llm_status'] = 'connected' if llm_health.get('llm_connection') else 'disconnected'
            else:
                app_metrics['llm_status'] = 'not_configured'

        except Exception as e:
            app_metrics['llm_status'] = f'error: {str(e)}'

        return app_metrics

    def check_alerts(self, metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Verifica condizioni di alert."""
        alerts = []
        thresholds = self.config['alert_thresholds']

        if 'system' in metrics:
            system = metrics['system']

            # Alert CPU
            if system['cpu_usage_percent'] > thresholds['cpu_usage']:
                alerts.append({
                    'type': 'cpu_high',
                    'severity': 'warning',
                    'message': f"CPU usage alto: {system['cpu_usage_percent']:.1f}%",
                    'value': system['cpu_usage_percent'],
                    'threshold': thresholds['cpu_usage'],
                    'timestamp': metrics['timestamp']
                })

            # Alert Memory
            if system['memory_usage_percent'] > thresholds['memory_usage']:
                alerts.append({
                    'type': 'memory_high',
                    'severity': 'warning',
                    'message': f"Memoria alta: {system['memory_usage_percent']:.1f}%",
                    'value': system['memory_usage_percent'],
                    'threshold': thresholds['memory_usage'],
                    'timestamp': metrics['timestamp']
                })

            # Alert Disk
            if system['disk_usage_percent'] > thresholds['disk_usage']:
                alerts.append({
                    'type': 'disk_high',
                    'severity': 'critical',
                    'message': f"Spazio disco basso: {system['disk_usage_percent']:.1f}%",
                    'value': system['disk_usage_percent'],
                    'threshold': thresholds['disk_usage'],
                    'timestamp': metrics['timestamp']
                })

        # Alert applicazione
        if 'application' in metrics:
            app = metrics['application']

            # Alert database
            if app['database_status'] != 'connected':
                alerts.append({
                    'type': 'database_disconnected',
                    'severity': 'critical',
                    'message': f"Database disconnesso: {app['database_status']}",
                    'timestamp': metrics['timestamp']
                })

            # Alert agenti
            if app['agents_status'] != 'healthy':
                alerts.append({
                    'type': 'agents_unhealthy',
                    'severity': 'warning',
                    'message': f"Agenti non healthy: {app['agents_status']}",
                    'timestamp': metrics['timestamp']
                })

        # Salva alert nella cronologia
        for alert in alerts:
            self.alerts_history.append(alert)

        # Mantieni solo gli ultimi alert
        cutoff_time = datetime.now() - timedelta(hours=24)
        self.alerts_history = [
            a for a in self.alerts_history
            if datetime.fromisoformat(a['timestamp']) > cutoff_time
        ]

        return alerts

    async def health_check(self) -> Dict[str, Any]:
        """Esegue health check completo."""
        health_status = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'healthy',
            'checks': {}
        }

        try:
            # Check database
            from supabase_integration import SupabaseManager
            supabase_manager = SupabaseManager()
            health_status['checks']['database'] = {
                'status': 'healthy' if supabase_manager.is_connected else 'unhealthy',
                'connected': supabase_manager.is_connected
            }

        except Exception as e:
            health_status['checks']['database'] = {
                'status': 'error',
                'error': str(e)
            }

        try:
            # Check agenti
            from advanced_agent_framework import get_advanced_orchestrator
            orchestrator = get_advanced_orchestrator()

            agents_health = await orchestrator.health_check()
            health_status['checks']['agents'] = {
                'status': 'healthy' if agents_health['orchestrator_status'] == 'healthy' else 'unhealthy',
                'details': agents_health
            }

        except Exception as e:
            health_status['checks']['agents'] = {
                'status': 'error',
                'error': str(e)
            }

        try:
            # Check sistema
            metrics = await self.collect_system_metrics()

            system_healthy = (
                metrics.get('system', {}).get('cpu_usage_percent', 0) < 90 and
                metrics.get('system', {}).get('memory_usage_percent', 0) < 90 and
                metrics.get('system', {}).get('disk_usage_percent', 0) < 95
            )

            health_status['checks']['system'] = {
                'status': 'healthy' if system_healthy else 'unhealthy',
                'metrics': metrics.get('system', {})
            }

        except Exception as e:
            health_status['checks']['system'] = {
                'status': 'error',
                'error': str(e)
            }

        # Determina status generale
        all_checks = [check.get('status') for check in health_status['checks'].values()]
        if 'error' in all_checks:
            health_status['overall_status'] = 'error'
        elif 'unhealthy' in all_checks:
            health_status['overall_status'] = 'unhealthy'
        else:
            health_status['overall_status'] = 'healthy'

        return health_status

    def get_metrics_summary(self, hours: int = 1) -> Dict[str, Any]:
        """Ottiene riassunto metriche per periodo."""
        cutoff_time = datetime.now() - timedelta(hours=hours)

        recent_metrics = [
            m for m in self.metrics_history
            if datetime.fromisoformat(m['timestamp']) > cutoff_time
        ]

        if not recent_metrics:
            return {'error': 'Nessuna metrica disponibile per il periodo'}

        # Calcola statistiche
        cpu_values = [m.get('system', {}).get('cpu_usage_percent', 0) for m in recent_metrics]
        memory_values = [m.get('system', {}).get('memory_usage_percent', 0) for m in recent_metrics]

        summary = {
            'period_hours': hours,
            'metrics_count': len(recent_metrics),
            'cpu': {
                'avg': round(sum(cpu_values) / len(cpu_values), 2) if cpu_values else 0,
                'min': min(cpu_values) if cpu_values else 0,
                'max': max(cpu_values) if cpu_values else 0
            },
            'memory': {
                'avg': round(sum(memory_values) / len(memory_values), 2) if memory_values else 0,
                'min': min(memory_values) if memory_values else 0,
                'max': max(memory_values) if memory_values else 0
            },
            'alerts_count': len([
                a for a in self.alerts_history
                if datetime.fromisoformat(a['timestamp']) > cutoff_time
            ])
        }

        return summary

# Istanza globale monitoring
production_monitoring = ProductionMonitoring()

# API Endpoints per monitoring

@monitoring_bp.route('/health', methods=['GET'])
def health_endpoint():
    """Endpoint health check."""
    try:
        import asyncio
        health_status = asyncio.run(production_monitoring.health_check())

        status_code = 200
        if health_status['overall_status'] == 'unhealthy':
            status_code = 503
        elif health_status['overall_status'] == 'error':
            status_code = 500

        return jsonify(health_status), status_code

    except Exception as e:
        return jsonify({
            'overall_status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@monitoring_bp.route('/metrics', methods=['GET'])
def metrics_endpoint():
    """Endpoint metriche."""
    try:
        import asyncio
        metrics = asyncio.run(production_monitoring.collect_system_metrics())
        return jsonify(metrics)

    except Exception as e:
        return jsonify({
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@monitoring_bp.route('/metrics/summary', methods=['GET'])
def metrics_summary_endpoint():
    """Endpoint riassunto metriche."""
    try:
        hours = request.args.get('hours', 1, type=int)
        summary = production_monitoring.get_metrics_summary(hours)
        return jsonify(summary)

    except Exception as e:
        return jsonify({
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@monitoring_bp.route('/alerts', methods=['GET'])
def alerts_endpoint():
    """Endpoint alert."""
    try:
        hours = request.args.get('hours', 24, type=int)
        cutoff_time = datetime.now() - timedelta(hours=hours)

        recent_alerts = [
            a for a in production_monitoring.alerts_history
            if datetime.fromisoformat(a['timestamp']) > cutoff_time
        ]

        return jsonify({
            'alerts': recent_alerts,
            'count': len(recent_alerts),
            'period_hours': hours
        })

    except Exception as e:
        return jsonify({
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@monitoring_bp.route('/status', methods=['GET'])
def status_endpoint():
    """Endpoint status generale."""
    try:
        import asyncio
        # Raccoglie metriche correnti
        metrics = asyncio.run(production_monitoring.collect_system_metrics())

        # Verifica alert
        alerts = production_monitoring.check_alerts(metrics)

        # Health check
        health = asyncio.run(production_monitoring.health_check())

        status = {
            'timestamp': datetime.now().isoformat(),
            'uptime_seconds': int((datetime.now() - production_monitoring.start_time).total_seconds()),
            'health': health,
            'current_metrics': metrics,
            'active_alerts': alerts,
            'alerts_count': len(alerts)
        }

        return jsonify(status)

    except Exception as e:
        return jsonify({
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

def init_production_monitoring(app):
    """Inizializza monitoring per app Flask."""
    app.register_blueprint(monitoring_bp)

    # Avvia monitoring in background (Flask 3.x compatible)
    with app.app_context():
        logger.info("Monitoring produzione inizializzato")

    return production_monitoring
