/* Dashboard CSS */
.chart-container {
    height: 300px;
}

.heatmap-container {
    height: 400px;
}

.h-100 {
    height: 100%;
}

.d-flex {
    display: flex;
}

.justify-content-center {
    justify-content: center;
}

.align-items-center {
    align-items: center;
}

/* <PERSON>ili per i KPI */
.border-left-primary {
    border-left: 4px solid #4e73df;
}

.border-left-success {
    border-left: 4px solid #1cc88a;
}

.border-left-info {
    border-left: 4px solid #36b9cc;
}

.border-left-warning {
    border-left: 4px solid #f6c23e;
}

.border-left-danger {
    border-left: 4px solid #e74a3b;
}

.border-left-secondary {
    border-left: 4px solid #858796;
}

.border-left-dark {
    border-left: 4px solid #5a5c69;
}

.text-xs {
    font-size: 0.7rem;
}

.text-gray-800 {
    color: #5a5c69;
}

/* ===== DASHBOARD INTELLIGENTE ===== */
.analysis-card {
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.analysis-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.discrepancy-item {
    border-left: 4px solid;
    padding: 10px 15px;
    margin-bottom: 10px;
    border-radius: 0 5px 5px 0;
}

.discrepancy-critical {
    border-left-color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}

.discrepancy-warning {
    border-left-color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
}

.discrepancy-info {
    border-left-color: #0dcaf0;
    background-color: rgba(13, 202, 240, 0.1);
}

.analysis-summary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.metric-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    color: #495057;
}

.metric-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 5px;
}

.analysis-tabs .nav-link {
    border-radius: 20px 20px 0 0;
    margin-right: 5px;
}

.analysis-tabs .nav-link.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.loading-spinner {
    display: none;
    text-align: center;
    padding: 20px;
}

.chart-container {
    height: 300px;
    margin-bottom: 20px;
}

.text-gray-300 {
    color: #dddfeb;
}

/* ===== TEMA SCURO DASHBOARD ===== */
[data-theme="dark"] .metric-card {
    background-color: var(--card-bg);
    color: var(--text-primary);
}

[data-theme="dark"] .metric-value {
    color: var(--text-primary);
}

[data-theme="dark"] .metric-label {
    color: var(--text-secondary);
}

[data-theme="dark"] .analysis-card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .analysis-card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

[data-theme="dark"] .discrepancy-critical {
    background-color: rgba(255, 107, 107, 0.2);
}

[data-theme="dark"] .discrepancy-warning {
    background-color: rgba(255, 212, 59, 0.2);
}

[data-theme="dark"] .discrepancy-info {
    background-color: rgba(34, 211, 238, 0.2);
}

.font-weight-bold {
    font-weight: 700;
}

.progress {
    height: 10px;
}

.progress-bar {
    transition: width 1s ease;
    width: 0;
}

.progress-bar.bg-info {
    width: calc(attr(data-width) * 1%);
}

.no-gutters {
    margin-right: 0;
    margin-left: 0;
}

.no-gutters > .col,
.no-gutters > [class*="col-"] {
    padding-right: 0;
    padding-left: 0;
}





