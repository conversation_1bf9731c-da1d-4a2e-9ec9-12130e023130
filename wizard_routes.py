#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Route per il Setup Wizard - MODULO SEPARATO.
Questo modulo definisce le route API per il setup wizard seguendo il pattern Blueprint
già utilizzato nel progetto (agent_routes.py, auth_routes.py).
"""

import os
import sys
import json
import logging
from datetime import datetime
from flask import Blueprint, request, jsonify, session, make_response

# Configurazione del logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Crea un blueprint per le route del wizard
wizard_bp = Blueprint('wizard', __name__, url_prefix='/api/wizard')

@wizard_bp.route('/complete', methods=['POST', 'OPTIONS'])
def complete_wizard():
    """
    Endpoint per completare il setup wizard.
    
    Gestisce il completamento del wizard di configurazione iniziale,
    salvando i dati in sessione e restituendo una risposta standardizzata.
    
    Returns:
        JSON response con formato standardizzato
    """
    try:
        # Gestione CORS per richieste preflight
        if request.method == 'OPTIONS':
            response = make_response()
            response.headers.add("Access-Control-Allow-Origin", "*")
            response.headers.add('Access-Control-Allow-Headers', "*")
            response.headers.add('Access-Control-Allow-Methods', "*")
            return response
        
        # Log dell'operazione
        sys.stdout.write("=== WIZARD COMPLETE - BLUEPRINT MODULARIZZATO ===\n")
        sys.stdout.flush()
        
        # Ottieni dati dalla richiesta
        data = request.get_json() or {}
        files = data.get('files', [])
        employees = data.get('employees', [])
        vehicles = data.get('vehicles', [])
        configuration = data.get('configuration', {})
        
        # Validazione base dei dati
        if not isinstance(files, list):
            files = []
        if not isinstance(employees, list):
            employees = []
        if not isinstance(vehicles, list):
            vehicles = []
        if not isinstance(configuration, dict):
            configuration = {}
        
        # Salva configurazione wizard in sessione
        session['wizard_completed'] = True
        session['wizard_config'] = {
            'files_count': len(files),
            'employees_count': len(employees),
            'vehicles_count': len(vehicles),
            'configuration': configuration,
            'completed_at': datetime.now().isoformat(),
            'version': '1.0.0',
            'module': 'wizard_blueprint'
        }
        
        # Salva dipendenti se presenti
        if employees:
            session['employees'] = employees
            sys.stdout.write(f"✅ Salvati {len(employees)} dipendenti in sessione\n")
            sys.stdout.flush()
        
        # Salva veicoli se presenti
        if vehicles:
            session['vehicles'] = vehicles
            sys.stdout.write(f"✅ Salvati {len(vehicles)} veicoli in sessione\n")
            sys.stdout.flush()
        
        # Salva informazioni file se presenti
        if files:
            session['wizard_files'] = files
            sys.stdout.write(f"✅ Salvate informazioni di {len(files)} file in sessione\n")
            sys.stdout.flush()
        
        # Log completamento
        sys.stdout.write(f"🎉 Setup wizard completato con successo!\n")
        sys.stdout.write(f"📁 File: {len(files)}, 👥 Dipendenti: {len(employees)}, 🚗 Veicoli: {len(vehicles)}\n")
        sys.stdout.flush()
        
        # Risposta di successo standardizzata
        response_data = {
            'success': True,
            'message': 'Setup completato con successo!',
            'data': {
                'redirect': '/dashboard',
                'wizard_completed': True,
                'files_processed': len(files),
                'employees_configured': len(employees),
                'vehicles_configured': len(vehicles),
                'timestamp': datetime.now().isoformat()
            }
        }
        
        response = make_response(jsonify(response_data))
        response.headers.add("Access-Control-Allow-Origin", "*")
        response.headers.add("Access-Control-Allow-Headers", "Content-Type")
        response.headers.add("Access-Control-Allow-Methods", "POST, OPTIONS")
        
        return response
        
    except Exception as e:
        # Log dell'errore
        sys.stdout.write(f"❌ Errore wizard blueprint: {str(e)}\n")
        sys.stdout.flush()
        
        # Fallback: non bloccare l'utente anche in caso di errore
        session['wizard_completed'] = True
        session['wizard_config'] = {
            'completed_at': datetime.now().isoformat(),
            'error_recovery': True,
            'error_message': str(e),
            'module': 'wizard_blueprint'
        }
        
        # Risposta di successo anche in caso di errore (fallback graceful)
        response_data = {
            'success': True,
            'message': 'Setup completato con modalità di recupero errori',
            'data': {
                'redirect': '/dashboard',
                'wizard_completed': True,
                'error_recovery': True,
                'timestamp': datetime.now().isoformat()
            }
        }
        
        response = make_response(jsonify(response_data))
        response.headers.add("Access-Control-Allow-Origin", "*")
        response.headers.add("Access-Control-Allow-Headers", "Content-Type")
        response.headers.add("Access-Control-Allow-Methods", "POST, OPTIONS")
        
        return response

@wizard_bp.route('/status', methods=['GET'])
def wizard_status():
    """
    Endpoint per verificare lo stato del wizard.
    
    Returns:
        JSON response con lo stato del wizard
    """
    try:
        wizard_completed = session.get('wizard_completed', False)
        wizard_config = session.get('wizard_config', {})
        
        response_data = {
            'success': True,
            'data': {
                'wizard_completed': wizard_completed,
                'config': wizard_config,
                'timestamp': datetime.now().isoformat()
            }
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@wizard_bp.route('/reset', methods=['POST'])
def reset_wizard():
    """
    Endpoint per resettare lo stato del wizard.
    
    Returns:
        JSON response con conferma del reset
    """
    try:
        # Rimuovi dati wizard dalla sessione
        session.pop('wizard_completed', None)
        session.pop('wizard_config', None)
        session.pop('employees', None)
        session.pop('vehicles', None)
        session.pop('wizard_files', None)
        
        sys.stdout.write("🔄 Wizard reset completato\n")
        sys.stdout.flush()
        
        response_data = {
            'success': True,
            'message': 'Wizard reset completato',
            'data': {
                'wizard_completed': False,
                'timestamp': datetime.now().isoformat()
            }
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

# Endpoint di test per verificare la connettività del blueprint
@wizard_bp.route('/test', methods=['GET', 'POST', 'OPTIONS'])
def test_wizard_blueprint():
    """
    Endpoint di test per verificare che il blueprint wizard funzioni correttamente.
    
    Returns:
        JSON response con informazioni di test
    """
    if request.method == 'OPTIONS':
        response = make_response()
        response.headers.add("Access-Control-Allow-Origin", "*")
        response.headers.add('Access-Control-Allow-Headers', "*")
        response.headers.add('Access-Control-Allow-Methods', "*")
        return response
    
    sys.stdout.write("=== WIZARD BLUEPRINT TEST ===\n")
    sys.stdout.write(f"🔍 Metodo: {request.method}\n")
    sys.stdout.flush()
    
    response_data = {
        'success': True,
        'message': 'Wizard blueprint funziona correttamente!',
        'data': {
            'method': request.method,
            'blueprint': 'wizard_bp',
            'prefix': '/api/wizard',
            'timestamp': datetime.now().isoformat()
        }
    }
    
    return jsonify(response_data)
