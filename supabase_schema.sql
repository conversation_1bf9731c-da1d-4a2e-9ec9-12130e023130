-- Schema del database Supabase per app-roberto
-- Eseguire questo script nel SQL Editor di Supabase

-- A<PERSON>ita le estensioni necessarie
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON><PERSON> per i file caricati
CREATE TABLE IF NOT EXISTS file_uploads (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_path TEXT NOT NULL,
    upload_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    session_id VARCHAR(100),
    mcp_file_id VARCHAR(100),
    file_size BIGINT DEFAULT 0,
    status VARCHAR(20) DEFAULT 'uploaded',
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON> per i dati elaborati
CREATE TABLE IF NOT EXISTS processed_data (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    file_upload_id INTEGER REFERENCES file_uploads(id) ON DELETE CASCADE,
    data_type VARCHAR(50) NOT NULL,
    processed_data JSONB,
    statistics JSONB,
    quality_report JSONB,
    processing_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabella per le sessioni utente
CREATE TABLE IF NOT EXISTS user_sessions (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    session_id VARCHAR(100) UNIQUE NOT NULL,
    user_data JSONB,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabella per le configurazioni di sistema
CREATE TABLE IF NOT EXISTS system_config (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value JSONB,
    description TEXT,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabella per i costi dipendenti
CREATE TABLE IF NOT EXISTS employee_costs (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    employee_name VARCHAR(255) NOT NULL,
    hourly_rate DECIMAL(10,2) NOT NULL,
    vat_included BOOLEAN DEFAULT true,
    vat_rate DECIMAL(5,2) DEFAULT 22.0,
    notes TEXT,
    is_active BOOLEAN DEFAULT true,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(employee_name, user_id)
);

-- Tabella per i log di sistema
CREATE TABLE IF NOT EXISTS system_logs (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    log_level VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    component VARCHAR(100),
    details JSONB,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabella per le analisi AI
CREATE TABLE IF NOT EXISTS ai_analyses (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    file_upload_id INTEGER REFERENCES file_uploads(id) ON DELETE CASCADE,
    analysis_type VARCHAR(50) NOT NULL,
    ai_insights JSONB,
    recommendations JSONB,
    quality_score DECIMAL(3,2),
    model_used VARCHAR(100),
    processing_time_ms INTEGER,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indici per migliorare le performance
CREATE INDEX IF NOT EXISTS idx_file_uploads_user_id ON file_uploads(user_id);
CREATE INDEX IF NOT EXISTS idx_file_uploads_created_at ON file_uploads(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_file_uploads_file_type ON file_uploads(file_type);
CREATE INDEX IF NOT EXISTS idx_processed_data_file_upload_id ON processed_data(file_upload_id);
CREATE INDEX IF NOT EXISTS idx_processed_data_user_id ON processed_data(user_id);
CREATE INDEX IF NOT EXISTS idx_system_config_config_key ON system_config(config_key);
CREATE INDEX IF NOT EXISTS idx_employee_costs_user_id ON employee_costs(user_id);
CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ai_analyses_file_upload_id ON ai_analyses(file_upload_id);

-- Trigger per aggiornare updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Applica il trigger alle tabelle che hanno updated_at
CREATE TRIGGER update_file_uploads_updated_at BEFORE UPDATE ON file_uploads 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_config_updated_at BEFORE UPDATE ON system_config 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_employee_costs_updated_at BEFORE UPDATE ON employee_costs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies
-- Abilita RLS su tutte le tabelle
ALTER TABLE file_uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE processed_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE employee_costs ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_analyses ENABLE ROW LEVEL SECURITY;

-- Policy per file_uploads: gli utenti possono vedere solo i propri file
CREATE POLICY "Users can view own file uploads" ON file_uploads
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own file uploads" ON file_uploads
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own file uploads" ON file_uploads
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own file uploads" ON file_uploads
    FOR DELETE USING (auth.uid() = user_id);

-- Policy per processed_data: gli utenti possono vedere solo i propri dati elaborati
CREATE POLICY "Users can view own processed data" ON processed_data
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own processed data" ON processed_data
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy per user_sessions: gli utenti possono gestire solo le proprie sessioni
CREATE POLICY "Users can manage own sessions" ON user_sessions
    FOR ALL USING (auth.uid() = user_id);

-- Policy per system_config: gli utenti possono gestire solo le proprie configurazioni
CREATE POLICY "Users can manage own config" ON system_config
    FOR ALL USING (auth.uid() = user_id);

-- Policy per employee_costs: gli utenti possono gestire solo i propri costi dipendenti
CREATE POLICY "Users can manage own employee costs" ON employee_costs
    FOR ALL USING (auth.uid() = user_id);

-- Policy per system_logs: gli utenti possono vedere solo i propri log
CREATE POLICY "Users can view own logs" ON system_logs
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "System can insert logs" ON system_logs
    FOR INSERT WITH CHECK (true);

-- Policy per ai_analyses: gli utenti possono vedere solo le proprie analisi AI
CREATE POLICY "Users can view own AI analyses" ON ai_analyses
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own AI analyses" ON ai_analyses
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Funzione per ottenere statistiche utente
CREATE OR REPLACE FUNCTION get_user_statistics(user_uuid UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_files', COUNT(DISTINCT fu.id),
        'total_processed', COUNT(DISTINCT pd.id),
        'total_ai_analyses', COUNT(DISTINCT ai.id),
        'total_employees', COUNT(DISTINCT ec.id),
        'last_upload', MAX(fu.created_at),
        'storage_used_mb', COALESCE(SUM(fu.file_size), 0) / 1024 / 1024
    ) INTO result
    FROM file_uploads fu
    LEFT JOIN processed_data pd ON fu.id = pd.file_upload_id
    LEFT JOIN ai_analyses ai ON fu.id = ai.file_upload_id
    LEFT JOIN employee_costs ec ON ec.user_id = user_uuid
    WHERE fu.user_id = user_uuid;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Funzione per pulire dati vecchi (da eseguire periodicamente)
CREATE OR REPLACE FUNCTION cleanup_old_data(days_to_keep INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Elimina file uploads più vecchi di X giorni
    DELETE FROM file_uploads 
    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 day' * days_to_keep;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Elimina log più vecchi di X giorni
    DELETE FROM system_logs 
    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '1 day' * days_to_keep;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Inserisci alcune configurazioni di default
INSERT INTO system_config (config_key, config_value, description, user_id) 
VALUES 
    ('app_version', '"2.0.0"', 'Versione dell''applicazione', NULL),
    ('max_file_size_mb', '50', 'Dimensione massima file in MB', NULL),
    ('supported_formats', '["xlsx", "xls", "csv"]', 'Formati file supportati', NULL),
    ('default_vat_rate', '22.0', 'Aliquota IVA di default', NULL)
ON CONFLICT (config_key) DO NOTHING;

-- Commenti per documentazione
COMMENT ON TABLE file_uploads IS 'Tabella per tracciare tutti i file caricati dagli utenti';
COMMENT ON TABLE processed_data IS 'Tabella per i dati elaborati dal sistema MCP';
COMMENT ON TABLE user_sessions IS 'Tabella per gestire le sessioni utente';
COMMENT ON TABLE system_config IS 'Tabella per le configurazioni di sistema';
COMMENT ON TABLE employee_costs IS 'Tabella per i costi orari dei dipendenti';
COMMENT ON TABLE system_logs IS 'Tabella per i log di sistema';
COMMENT ON TABLE ai_analyses IS 'Tabella per le analisi AI generate dal sistema';

-- Visualizzazioni utili
CREATE OR REPLACE VIEW user_file_summary AS
SELECT 
    fu.user_id,
    COUNT(*) as total_files,
    COUNT(DISTINCT fu.file_type) as unique_file_types,
    SUM(fu.file_size) as total_size_bytes,
    MAX(fu.created_at) as last_upload,
    MIN(fu.created_at) as first_upload
FROM file_uploads fu
GROUP BY fu.user_id;

CREATE OR REPLACE VIEW recent_activity AS
SELECT 
    'file_upload' as activity_type,
    fu.user_id,
    fu.filename as description,
    fu.created_at as timestamp
FROM file_uploads fu
WHERE fu.created_at > CURRENT_TIMESTAMP - INTERVAL '7 days'
UNION ALL
SELECT 
    'ai_analysis' as activity_type,
    ai.user_id,
    ai.analysis_type as description,
    ai.created_at as timestamp
FROM ai_analyses ai
WHERE ai.created_at > CURRENT_TIMESTAMP - INTERVAL '7 days'
ORDER BY timestamp DESC;

-- Grant permissions per l'utente anonimo (per testing)
GRANT USAGE ON SCHEMA public TO anon;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO anon;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon;
