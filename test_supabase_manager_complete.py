#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test completo del SupabaseManager migliorato per app-roberto
"""

from supabase_integration import SupabaseManager
import json
from datetime import datetime

def test_supabase_manager_complete():
    """Test completo di tutte le funzionalità del SupabaseManager"""
    print('🧪 Test Completo SupabaseManager')
    print('=' * 50)

    # Usa le chiavi aggiornate direttamente
    url = "https://zqjllwxqjxjhdkbcawfr.supabase.co"
    key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxamxsd3hxanhqaGRrYmNhd2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc1MDA1NTQsImV4cCI6MjA1MzA3NjU1NH0.WxbX4nvlen-3WixZ7D9flaQwTfLtA5V3PvD0guA5hdo"

    manager = SupabaseManager(url=url, key=key)
    
    if not manager.is_connected:
        print('❌ Connessione fallita')
        return False

    print('✅ Connessione stabilita')
    
    # Test 1: Gestione costi dipendenti
    print('\n📊 Test 1: Gestione Costi Dipendenti')
    employee_data = {
        "employee_name": "<PERSON>",
        "hourly_rate": 25.50,
        "vat_included": True,
        "vat_rate": 22.0,
        "notes": "Tecnico senior",
        "is_active": True
    }
    
    employee_id = manager.save_employee_cost(employee_data)
    if employee_id:
        print(f'  ✅ Dipendente salvato con ID: {employee_id}')
        
        # Test recupero dipendenti
        employees = manager.get_employee_costs()
        print(f'  📋 Dipendenti attivi: {len(employees)}')
        
        # Test recupero specifico
        mario = manager.get_employee_cost_by_name("Mario Rossi")
        if mario:
            print(f'  👤 Mario Rossi trovato: {mario["hourly_rate"]}€/h')
        else:
            print('  ❌ Mario Rossi non trovato')
    else:
        print('  ❌ Errore nel salvare dipendente')

    # Test 2: Gestione configurazioni
    print('\n⚙️ Test 2: Gestione Configurazioni')
    
    # Salva configurazione
    config_saved = manager.set_config("test_config", {"value": 123, "enabled": True}, "Configurazione di test")
    if config_saved:
        print('  ✅ Configurazione salvata')
        
        # Recupera configurazione
        config_value = manager.get_config("test_config")
        print(f'  📖 Configurazione recuperata: {config_value}')
        
        # Test valore default
        default_value = manager.get_config("config_inesistente", "valore_default")
        print(f'  🔧 Valore default: {default_value}')
    else:
        print('  ❌ Errore nel salvare configurazione')

    # Test 3: Logging di sistema
    print('\n📝 Test 3: Logging di Sistema')
    
    log_saved = manager.log_system_event(
        level="INFO",
        message="Test del sistema di logging",
        component="test_manager",
        details={"test_id": 123, "timestamp": datetime.now().isoformat()}
    )
    
    if log_saved:
        print('  ✅ Log salvato con successo')
    else:
        print('  ❌ Errore nel salvare log')

    # Test 4: File upload e dati elaborati
    print('\n📁 Test 4: File Upload e Dati Elaborati')
    
    file_info = {
        "filename": "test_file.xlsx",
        "original_filename": "test_file.xlsx",
        "file_type": "attivita",
        "file_path": "/uploads/test_file.xlsx",
        "file_size": 1024,
        "session_id": "test_session_123"
    }
    
    file_id = manager.save_file_upload(file_info)
    if file_id:
        print(f'  ✅ File upload salvato con ID: {file_id}')
        
        # Salva dati elaborati
        processed_data = {
            "rows": 50,
            "columns": 8,
            "processing_time": 1.5,
            "detected_employees": ["Mario Rossi", "Luigi Verdi"]
        }
        
        statistics = {
            "total_hours": 120.5,
            "unique_employees": 2,
            "date_range": "2024-01-01 to 2024-01-31"
        }
        
        processed_id = manager.save_processed_data(
            file_upload_id=file_id,
            data_type="attivita",
            processed_data=processed_data,
            statistics=statistics
        )
        
        if processed_id:
            print(f'  ✅ Dati elaborati salvati con ID: {processed_id}')
            
            # Test analisi AI
            ai_id = manager.save_ai_analysis(
                file_upload_id=file_id,
                analysis_type="productivity_analysis",
                ai_insights={"efficiency_score": 0.85, "recommendations": ["Ottimizzare orari"]},
                quality_score=0.92,
                model_used="gpt-4",
                processing_time_ms=2500
            )
            
            if ai_id:
                print(f'  🤖 Analisi AI salvata con ID: {ai_id}')
            else:
                print('  ❌ Errore nel salvare analisi AI')
        else:
            print('  ❌ Errore nel salvare dati elaborati')
    else:
        print('  ❌ Errore nel salvare file upload')

    # Test 5: Analisi incrociata
    print('\n🔍 Test 5: Analisi Incrociata')
    
    cross_data = manager.get_cross_analysis_data(data_types=["attivita"])
    print(f'  📊 Dati per analisi incrociata: {len(cross_data)} tipi')
    
    productivity_stats = manager.get_employee_productivity_stats()
    print(f'  📈 Statistiche produttività: {productivity_stats}')

    print('\n🎉 Test completo terminato!')
    return True

if __name__ == "__main__":
    test_supabase_manager_complete()
