@echo off
chcp 65001 >nul
title App Roberto - <PERSON><PERSON><PERSON> con MCP Watchdog

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    APP ROBERTO - AVVIO COMPLETO              ║
echo ║                     Con MCP Watchdog Automatico              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📍 Directory di lavoro: %CD%
echo.

REM Verifica se siamo nella directory corretta
if not exist "app.py" (
    echo ❌ ERRORE: File app.py non trovato nella directory corrente
    echo 💡 Assicurati di essere nella directory del progetto app-roberto
    pause
    exit /b 1
)

echo ⚙️ Configurazione ambiente...
echo.

REM Attiva l'ambiente virtuale se esiste
if exist "venv\Scripts\activate.bat" (
    echo 🔄 Attivazione ambiente virtuale...
    call venv\Scripts\activate.bat
    echo ✅ Ambiente virtuale attivato
) else (
    echo ⚠️ Ambiente virtuale non trovato, uso Python di sistema
)

echo.
echo 🌐 Impostazione variabili d'ambiente...
set OPENROUTER_API_KEY=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
set APP_URL=http://localhost:5000
set MCP_URL=http://localhost:8000
set FLASK_DEBUG=1
set FLASK_APP=app.py

echo ✅ Variabili d'ambiente configurate
echo.

echo 🐕 Avvio MCP Watchdog (monitoraggio automatico)...
echo 💡 Il watchdog manterrà il server MCP sempre attivo
start "MCP Watchdog" python mcp_watchdog.py

echo.
echo ⏳ Attesa avvio server MCP (10 secondi)...
timeout /t 10 /nobreak >nul

echo.
echo 🚀 Avvio applicazione principale Flask...
echo.
echo ═══════════════════════════════════════════════════════════════
echo  🌐 Applicazione disponibile su: http://localhost:5000
echo  🔧 Server MCP monitorato su: http://localhost:8000
echo  🐕 Watchdog: Riavvio automatico se MCP si ferma
echo ═══════════════════════════════════════════════════════════════
echo.
echo 💡 Per fermare tutto: Chiudi questa finestra e la finestra del Watchdog
echo.

REM Avvia l'app Flask
python app.py

echo.
echo 🛑 Applicazione terminata
pause
