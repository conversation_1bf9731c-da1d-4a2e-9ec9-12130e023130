#!/usr/bin/env python3
"""
Test specifico per l'API endpoints
"""

import requests
import json

def test_endpoints_api():
    """Test API endpoints"""
    
    print("🔍 TEST API ENDPOINTS")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test 1: Test diretto endpoint
    print("1️⃣ Test diretto /api/endpoints...")
    try:
        response = requests.get(f"{base_url}/api/endpoints", timeout=5)
        print(f"   Status: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"   📄 Risposta JSON: {json.dumps(data, indent=2)}")
            except:
                print(f"   📄 Risposta non JSON: {response.text[:200]}...")
        else:
            print(f"   📄 Contenuto: {response.text[:200]}...")
            
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    # Test 2: Test altri endpoint base
    print("\n2️⃣ Test endpoint base...")
    
    base_endpoints = [
        "/",
        "/dashboard", 
        "/setup-wizard",
        "/agents",
        "/chat"
    ]
    
    for endpoint in base_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            status = "✅" if response.status_code == 200 else "❌"
            print(f"   {status} {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {endpoint}: Errore - {str(e)}")
    
    # Test 3: Test endpoint API noti
    print("\n3️⃣ Test endpoint API noti...")
    
    api_endpoints = [
        "/api/health",
        "/api/get-processed-data",
        "/api/wizard/status",
        "/api/wizard/complete"
    ]
    
    for endpoint in api_endpoints:
        try:
            if endpoint in ["/api/get-processed-data", "/api/wizard/complete"]:
                response = requests.post(
                    f"{base_url}{endpoint}",
                    json={"test": True},
                    headers={"Content-Type": "application/json"},
                    timeout=5
                )
            else:
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
                
            status = "✅" if response.status_code in [200, 400, 404] else "❌"
            print(f"   {status} {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {endpoint}: Errore - {str(e)}")
    
    return True

if __name__ == "__main__":
    test_endpoints_api()
