#!/usr/bin/env python3
"""
Test finale completo dell'endpoint wizard con dati realistici
"""

import requests
import json

def test_wizard_final():
    """Test finale completo dell'endpoint wizard"""
    
    print("🎯 TEST FINALE ENDPOINT WIZARD COMPLETE")
    print("=" * 60)
    
    # Dati realistici per il test
    test_data = {
        "files": [
            {"name": "interventi_gennaio.xlsx", "size": 15420, "type": "excel"},
            {"name": "dipendenti.csv", "size": 2340, "type": "csv"}
        ],
        "employees": [
            {"name": "<PERSON>", "hourly_rate": 30.0, "role": "Tecnico Senior"},
            {"name": "<PERSON>", "hourly_rate": 25.0, "role": "Tecnico"},
            {"name": "<PERSON>", "hourly_rate": 35.0, "role": "Supervisore"}
        ],
        "vehicles": [
            {"name": "Fiat Ducato", "fuel_consumption": 8.5, "daily_cost": 45.0},
            {"name": "Iveco Daily", "fuel_consumption": 9.2, "daily_cost": 50.0}
        ],
        "configuration": {
            "company_name": "Test Company",
            "business_type": "assistenza_tecnica",
            "setup_version": "1.0.0"
        }
    }
    
    print("📋 DATI DI TEST:")
    print(f"   📁 File: {len(test_data['files'])}")
    print(f"   👥 Dipendenti: {len(test_data['employees'])}")
    print(f"   🚗 Veicoli: {len(test_data['vehicles'])}")
    print(f"   ⚙️ Configurazioni: {len(test_data['configuration'])}")
    print()
    
    # Test 1: OPTIONS (CORS)
    print("1️⃣ Test CORS (OPTIONS)...")
    try:
        response = requests.options("http://127.0.0.1:5000/api/wizard/complete", timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ CORS configurato correttamente")
            cors_headers = {
                'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
            }
            print(f"   Headers CORS: {cors_headers}")
        else:
            print(f"   ❌ CORS non configurato: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore CORS: {str(e)}")
    
    print()
    
    # Test 2: POST con dati completi
    print("2️⃣ Test POST con dati completi...")
    try:
        response = requests.post(
            "http://127.0.0.1:5000/api/wizard/complete",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"   Status: {response.status_code}")
        print(f"   Content-Type: {response.headers.get('content-type', 'N/A')}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("   ✅ RISPOSTA JSON VALIDA")
                print(f"   Success: {result.get('success', 'N/A')}")
                print(f"   Message: {result.get('message', 'N/A')}")
                
                if 'data' in result:
                    data = result['data']
                    print(f"   Redirect: {data.get('redirect', 'N/A')}")
                    print(f"   Wizard Completed: {data.get('wizard_completed', 'N/A')}")
                    print(f"   Files Processed: {data.get('files_processed', 'N/A')}")
                    print(f"   Employees Configured: {data.get('employees_configured', 'N/A')}")
                    print(f"   Vehicles Configured: {data.get('vehicles_configured', 'N/A')}")
                    print(f"   Supabase Integration: {data.get('supabase_integration', 'N/A')}")
                    
                    if 'supabase_details' in data:
                        supabase_details = data['supabase_details']
                        print(f"   Supabase Details:")
                        print(f"      Onboarding: {supabase_details.get('onboarding', 'N/A')}")
                        print(f"      Employees: {supabase_details.get('employees', 'N/A')}")
                        print(f"      Vehicles: {supabase_details.get('vehicles', 'N/A')}")
                    
                    print(f"   User ID: {data.get('user_id', 'N/A')}")
                
                # Verifica che la risposta sia corretta per il frontend
                if result.get('success') and 'data' in result and result['data'].get('redirect') == '/dashboard':
                    print("   🎉 ENDPOINT WIZARD FUNZIONA PERFETTAMENTE!")
                    return True
                else:
                    print("   ⚠️ Risposta non ottimale per il frontend")
                    
            except json.JSONDecodeError:
                print(f"   ❌ RISPOSTA NON JSON: {response.text[:200]}...")
                
        else:
            print(f"   ❌ ERRORE HTTP: {response.status_code}")
            print(f"   Contenuto: {response.text[:200]}...")
            
    except requests.exceptions.Timeout:
        print("   ❌ TIMEOUT - L'endpoint impiega troppo tempo")
    except requests.exceptions.ConnectionError:
        print("   ❌ CONNECTION ERROR - Applicazione non raggiungibile")
    except Exception as e:
        print(f"   ❌ ERRORE GENERICO: {str(e)}")
    
    print()
    
    # Test 3: POST con dati minimi
    print("3️⃣ Test POST con dati minimi...")
    minimal_data = {
        "files": [],
        "employees": [],
        "vehicles": [],
        "configuration": {}
    }
    
    try:
        response = requests.post(
            "http://127.0.0.1:5000/api/wizard/complete",
            json=minimal_data,
            headers={'Content-Type': 'application/json'},
            timeout=15
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and 'data' in result:
                print("   ✅ Gestione dati minimi OK")
            else:
                print("   ⚠️ Problema con dati minimi")
        else:
            print(f"   ❌ Errore con dati minimi: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore test dati minimi: {str(e)}")
    
    print()
    
    # Test 4: Verifica headers CORS nella risposta POST
    print("4️⃣ Test headers CORS nella risposta POST...")
    try:
        response = requests.post(
            "http://127.0.0.1:5000/api/wizard/complete",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=15
        )
        
        cors_origin = response.headers.get('Access-Control-Allow-Origin')
        if cors_origin == '*':
            print("   ✅ Headers CORS presenti nella risposta POST")
        else:
            print(f"   ⚠️ Headers CORS mancanti o incorretti: {cors_origin}")
            
    except Exception as e:
        print(f"   ❌ Errore test CORS POST: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🎯 RISULTATO FINALE:")
    print("   Se tutti i test sono passati, l'endpoint wizard è completamente funzionante")
    print("   Il pulsante 'Vai alla Dashboard' dovrebbe funzionare al 100%")
    
    return False

if __name__ == "__main__":
    success = test_wizard_final()
    if success:
        print("\n🎉 SUCCESSO: Endpoint wizard completamente funzionante!")
    else:
        print("\n❌ FALLIMENTO: Endpoint wizard ha ancora problemi")
