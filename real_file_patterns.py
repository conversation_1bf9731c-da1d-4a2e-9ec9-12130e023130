#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configurazione pattern per il riconoscimento dei file reali del sistema.
Basato sull'analisi dei file nella cartella uploads/.
"""

import re
from typing import Dict, List, Any

# Pattern per riconoscere i file reali basati sui nomi e strutture effettive
REAL_FILE_PATTERNS = {
    "attivita": {
        "name_patterns": [
            r"export_\d+_[a-f0-9]+\.xlsx$",
            r"export_\d+\.xlsx$"
        ],
        "required_columns": [
            ["Id Ticket", "id ticket", "ticket id"],
            ["Iniziata il", "iniziata", "started", "start"],
            ["Azienda", "cliente", "customer", "client", "company"]
        ],
        "optional_columns": [
            ["Contratto", "contract"],
            ["Descrizione", "description", "summary"],
            ["Durata", "duration", "time"],
            ["Linea di Contratto", "contract line"]
        ],
        "frequency": "multiple_daily",
        "priority": "CRITICA",
        "description": "File esportazione attività e ticket dal gestionale",
        "typical_size": "300-800 righe",
        "update_pattern": "Più volte al giorno"
    },

    "timbrature": {
        "name_patterns": [
            r"apprilevazionepresenze-timbrature-.*\.xlsx$",
            r".*timbrature.*\.xlsx$"
        ],
        "required_columns": [
            ["dipendente nome", "nome", "first name"],
            ["dipendente cognome", "cognome", "last name"],
            ["ora inizio", "start time", "inizio"],
            ["ora fine", "end time", "fine"],
            ["ore", "hours", "duration"]
        ],
        "optional_columns": [
            ["cliente nome", "customer", "client"],
            ["descrizione attività", "activity", "description"],
            ["pausa", "break", "pause"],
            ["stato timbratura", "status"]
        ],
        "frequency": "weekly",
        "priority": "ALTA",
        "description": "File timbrature dettagliate da sistema presenze",
        "typical_size": "100-200 righe",
        "update_pattern": "Settimanale"
    },

    "teamviewer": {
        "name_patterns": [
            r"connectionreport_\d+_[a-f0-9]+\.csv$",
            r".*connectionreport.*\.csv$"
        ],
        "required_columns": [
            ["Utente", "user", "technician"],
            ["Computer", "pc", "client", "machine"],
            ["Inizio", "start", "begin"],
            ["Fine", "end", "stop"],
            ["Tipo di sessione", "session type", "type"]
        ],
        "optional_columns": [
            ["Durata", "duration", "time"],
            ["Gruppo", "group", "team"],
            ["ID", "session id", "code"],
            ["Note", "notes", "comments"]
        ],
        "frequency": "daily",
        "priority": "ALTA",
        "description": "Report connessioni TeamViewer per assistenze remote",
        "typical_size": "20-100 righe",
        "update_pattern": "Giornaliero"
    },

    "calendario": {
        "name_patterns": [
            r"tmp-\d+_[a-f0-9]+\.csv$",
            r".*calendar.*\.csv$",
            r".*calendario.*\.csv$"
        ],
        "required_columns": [
            ["SUMMARY", "summary", "title", "titolo"],
            ["DTSTART", "start", "inizio", "data inizio"],
            ["DTEND", "end", "fine", "data fine"]
        ],
        "optional_columns": [
            ["ATTENDEE", "attendees", "partecipanti"],
            ["NOTES", "notes", "description"],
            ["ORGANIZER", "organizer", "responsabile"]
        ],
        "frequency": "daily",
        "priority": "MEDIA",
        "description": "Esportazione calendario appuntamenti e trasferte",
        "typical_size": "50-100 righe",
        "update_pattern": "Giornaliero"
    },

    "registro_auto": {
        "name_patterns": [
            r"auto_\d+_[a-f0-9]+\.CSV$",
            r"auto_\d+_[a-f0-9]+\.csv$",
            r".*auto.*\.csv$",
            r".*veicoli.*\.csv$"
        ],
        "required_columns": [
            ["Dipendente", "employee", "driver"],
            ["Data", "date", "giorno"],
            ["Auto", "vehicle", "car", "veicolo"],
            ["Presa Data e Ora", "pickup", "start"]
        ],
        "optional_columns": [
            ["Riconsegna Data e Ora", "return", "end"],
            ["Cliente", "customer", "client"],
            ["Ore", "hours", "duration"],
            ["Note", "notes", "comments"]
        ],
        "frequency": "daily",
        "priority": "MEDIA",
        "description": "Registro utilizzo veicoli aziendali",
        "typical_size": "10-50 righe",
        "update_pattern": "Giornaliero"
    },

    "permessi": {
        "name_patterns": [
            r"apprilevazionepresenze-richieste-.*\.xlsx$",
            r".*richieste.*\.xlsx$",
            r".*permessi.*\.xlsx$"
        ],
        "required_columns": [
            ["Dipendente", "employee", "nome"],
            ["Tipo", "type", "leave type"],
            ["Data inizio", "start date", "from"],
            ["Data fine", "end date", "to"],
            ["Stato", "status", "approved"]
        ],
        "optional_columns": [
            ["Note", "notes", "reason", "motivo"],
            ["Ore", "hours", "duration"],
            ["Data della richiesta", "request date"]
        ],
        "frequency": "weekly",
        "priority": "BASSA",
        "description": "Richieste permessi, ferie e ROL",
        "typical_size": "10-30 righe",
        "update_pattern": "Settimanale"
    },

    "progetti": {
        "name_patterns": [
            r"export_\d+_.*progetti.*\.xlsx$",
            r".*progetti.*\.xlsx$",
            r".*project.*\.xlsx$"
        ],
        "required_columns": [
            ["Codice Progetto", "project code", "code"],
            ["Stato", "status", "state"],
            ["Azienda Assegnataria", "customer", "client"],
            ["Nome", "name", "title", "project name"]
        ],
        "optional_columns": [
            ["Capo Progetto", "project manager", "pm"],
            ["Creato il", "created", "start date"],
            ["Aggiornato il", "updated", "last update"],
            ["Priorità", "priority"]
        ],
        "frequency": "weekly",
        "priority": "BASSA",
        "description": "Esportazione progetti in corso",
        "typical_size": "20-50 righe",
        "update_pattern": "Settimanale"
    }
}

# Configurazione per la logica di rilevamento
DETECTION_CONFIG = {
    "min_confidence_threshold": 0.6,  # Abbassata per i file reali
    "required_columns_weight": 2.0,
    "optional_columns_weight": 1.0,
    "filename_pattern_weight": 1.5,
    "column_fuzzy_threshold": 0.7,  # Più permissivo per fuzzy matching
    "max_file_age_days": 30  # File più vecchi di 30 giorni vengono ignorati
}

# Mapping per standardizzazione nomi colonne
COLUMN_STANDARDIZATION = {
    "attivita": {
        "Id Ticket": ["id ticket", "ticket id", "numero ticket"],
        "Iniziata il": ["iniziata", "started", "start", "data inizio"],
        "Conclusa il": ["conclusa", "completed", "end", "data fine"],
        "Azienda": ["cliente", "customer", "client", "company"],
        "Creato da": ["dipendente", "employee", "technician", "tecnico"],
        "Descrizione": ["description", "summary", "oggetto"],
        "Durata": ["duration", "time", "tempo", "ore"]
    },
    "timbrature": {
        "dipendente_nome": ["dipendente nome", "nome", "first name"],
        "dipendente_cognome": ["dipendente cognome", "cognome", "last name"],
        "cliente": ["cliente nome", "customer", "client"],
        "ora_inizio": ["ora inizio", "start time", "inizio"],
        "ora_fine": ["ora fine", "end time", "fine"],
        "ore_lavorate": ["ore", "hours", "duration", "ore lavorate"],
        "descrizione": ["descrizione attività", "activity", "description"]
    },
    "teamviewer": {
        "utente": ["utente", "user", "technician", "assegnatario"],
        "computer": ["computer", "pc", "client", "machine", "nome"],
        "inizio": ["inizio", "start", "begin"],
        "fine": ["fine", "end", "stop"],
        "durata": ["durata", "duration", "time"],
        "tipo_sessione": ["tipo di sessione", "session type", "type"],
        "gruppo": ["gruppo", "group", "team"]
    },
    "calendario": {
        "titolo": ["summary", "title", "titolo", "oggetto"],
        "data_inizio": ["dtstart", "start", "inizio", "data inizio"],
        "data_fine": ["dtend", "end", "fine", "data fine"],
        "luogo": ["location", "luogo", "where", "dove"],
        "partecipanti": ["attendee", "attendees", "partecipanti"],
        "note": ["notes", "description", "descrizione"]
    },
    "registro_auto": {
        "dipendente": ["dipendente", "employee", "driver", "conducente"],
        "data": ["data", "date", "giorno"],
        "veicolo": ["auto", "vehicle", "car", "veicolo"],
        "presa": ["presa data e ora", "pickup", "start", "ritiro"],
        "riconsegna": ["riconsegna data e ora", "return", "end", "consegna"],
        "cliente": ["cliente", "customer", "client"],
        "ore": ["ore", "hours", "duration", "tempo"]
    },
    "permessi": {
        "dipendente": ["dipendente", "employee", "nome"],
        "tipo": ["tipo", "type", "leave type"],
        "data_inizio": ["data inizio", "start date", "from", "da"],
        "data_fine": ["data fine", "end date", "to", "a"],
        "stato": ["stato", "status", "approved"],
        "note": ["note", "notes", "reason", "motivo"]
    },
    "progetti": {
        "codice": ["codice progetto", "project code", "code"],
        "nome": ["nome", "name", "title", "project name"],
        "stato": ["stato", "status", "state"],
        "cliente": ["azienda assegnataria", "customer", "client"],
        "responsabile": ["capo progetto", "project manager", "pm"],
        "data_creazione": ["creato il", "created", "start date"],
        "data_aggiornamento": ["aggiornato il", "updated", "last update"]
    }
}

# Configurazione per la validazione dei dati
DATA_VALIDATION_RULES = {
    "attivita": {
        "required_fields": ["Id Ticket", "Iniziata il", "Azienda"],
        "date_fields": ["Iniziata il", "Conclusa il"],
        "numeric_fields": ["Id Ticket"],
        "text_fields": ["Azienda", "Descrizione", "Creato da"]
    },
    "timbrature": {
        "required_fields": ["dipendente_nome", "ora_inizio", "cliente"],
        "date_fields": ["ora_inizio", "ora_fine"],
        "numeric_fields": ["ore_lavorate"],
        "text_fields": ["dipendente_nome", "dipendente_cognome", "cliente"]
    },
    "teamviewer": {
        "required_fields": ["utente", "computer", "inizio"],
        "date_fields": ["inizio", "fine"],
        "text_fields": ["utente", "computer", "tipo_sessione", "gruppo"]
    },
    "calendario": {
        "required_fields": ["titolo", "data_inizio"],
        "date_fields": ["data_inizio", "data_fine"],
        "text_fields": ["titolo", "luogo", "partecipanti"]
    },
    "registro_auto": {
        "required_fields": ["dipendente", "data", "veicolo"],
        "date_fields": ["data", "presa", "riconsegna"],
        "text_fields": ["dipendente", "veicolo", "cliente"]
    },
    "permessi": {
        "required_fields": ["dipendente", "tipo", "data_inizio"],
        "date_fields": ["data_inizio", "data_fine"],
        "text_fields": ["dipendente", "tipo", "stato", "note"]
    },
    "progetti": {
        "required_fields": ["codice", "nome", "stato"],
        "date_fields": ["data_creazione", "data_aggiornamento"],
        "text_fields": ["codice", "nome", "stato", "cliente", "responsabile"]
    }
}

def get_file_type_by_pattern(filename: str) -> str:
    """
    Determina il tipo di file basandosi sui pattern del nome file.

    Args:
        filename: Nome del file da analizzare

    Returns:
        Tipo di file rilevato o "unknown"
    """
    filename_lower = filename.lower()

    for file_type, config in REAL_FILE_PATTERNS.items():
        for pattern in config["name_patterns"]:
            if re.search(pattern.lower(), filename_lower):
                return file_type

    return "unknown"

def get_priority_order() -> List[str]:
    """
    Restituisce l'ordine di priorità per l'elaborazione dei file.

    Returns:
        Lista dei tipi di file ordinati per priorità
    """
    priority_map = {"CRITICA": 1, "ALTA": 2, "MEDIA": 3, "BASSA": 4}

    file_types = list(REAL_FILE_PATTERNS.keys())
    file_types.sort(key=lambda x: priority_map.get(REAL_FILE_PATTERNS[x]["priority"], 5))

    return file_types

def get_expected_columns(file_type: str) -> Dict[str, List[str]]:
    """
    Restituisce le colonne attese per un tipo di file.

    Args:
        file_type: Tipo di file

    Returns:
        Dizionario con colonne required e optional
    """
    if file_type not in REAL_FILE_PATTERNS:
        return {"required": [], "optional": []}

    config = REAL_FILE_PATTERNS[file_type]
    return {
        "required": config.get("required_columns", []),
        "optional": config.get("optional_columns", [])
    }
