#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Auto Tuner - Sistema di auto-tuning e ottimizzazione dinamica per app-roberto.
Ottimizza automaticamente le configurazioni del sistema basandosi sui pattern di utilizzo.
"""

import time
import threading
import logging
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict, deque
import statistics

# Import dei moduli esistenti
try:
    from intelligent_cache_system import intelligent_cache, CacheStrategy
    from query_optimizer import query_optimizer
    from performance_profiler import performance_profiler
    from supabase_integration import SupabaseManager
except ImportError as e:
    logging.warning(f"Alcuni moduli non disponibili: {e}")

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TuningStrategy(Enum):
    """Strategie di tuning."""
    CONSERVATIVE = "conservative"  # Cambiamenti graduali
    BALANCED = "balanced"         # Bilanciato tra performance e stabilità
    AGGRESSIVE = "aggressive"     # Ottimizzazione massima

class OptimizationTarget(Enum):
    """Obiettivi di ottimizzazione."""
    RESPONSE_TIME = "response_time"
    THROUGHPUT = "throughput"
    MEMORY_USAGE = "memory_usage"
    CACHE_HIT_RATE = "cache_hit_rate"
    ERROR_RATE = "error_rate"

@dataclass
class TuningParameter:
    """Parametro di tuning."""
    name: str
    current_value: Any
    min_value: Any
    max_value: Any
    step_size: Any
    target_metric: str
    last_changed: datetime
    change_count: int = 0
    performance_impact: float = 0.0

@dataclass
class OptimizationResult:
    """Risultato di un'ottimizzazione."""
    parameter_name: str
    old_value: Any
    new_value: Any
    metric_before: float
    metric_after: float
    improvement_percent: float
    timestamp: datetime
    strategy_used: TuningStrategy

class AutoTuner:
    """
    Sistema di auto-tuning che:
    - Monitora metriche di performance in tempo reale
    - Ottimizza automaticamente parametri di configurazione
    - Applica machine learning per predire ottimizzazioni
    - Mantiene stabilità del sistema durante il tuning
    - Fornisce rollback automatico per cambiamenti negativi
    """

    def __init__(self, supabase_manager: Optional[Any] = None):
        self.supabase_manager = supabase_manager

        # Configurazione auto-tuning
        self.TUNING_STRATEGY = TuningStrategy.BALANCED
        self.TUNING_INTERVAL = 300  # 5 minuti
        self.STABILITY_PERIOD = 600  # 10 minuti di stabilità prima di nuovi cambiamenti
        self.ROLLBACK_THRESHOLD = -5.0  # Rollback se performance peggiora del 5%
        self.MAX_CHANGES_PER_HOUR = 3

        # Parametri di tuning
        self.tuning_parameters = self._initialize_tuning_parameters()
        self.optimization_history = deque(maxlen=1000)
        self.metric_history = defaultdict(lambda: deque(maxlen=100))

        # Stato tuning
        self.tuning_active = False
        self.tuning_thread = None
        self.last_optimization = datetime.now()
        self.changes_this_hour = 0
        self.lock = threading.RLock()

        # Metriche baseline
        self.baseline_metrics = {}
        self.current_metrics = {}

        # Avvia auto-tuning solo se non disabilitato
        import os
        if not os.environ.get('DISABLE_AUTO_TUNING'):
            self.start_auto_tuning()
            logger.info("AutoTuner inizializzato con tuning attivo")
        else:
            logger.info("AutoTuner inizializzato ma tuning disabilitato (modalità minimal)")

    def _initialize_tuning_parameters(self) -> Dict[str, TuningParameter]:
        """Inizializza parametri di tuning."""
        return {
            # Cache parameters
            "cache_memory_size": TuningParameter(
                name="cache_memory_size",
                current_value=1000,
                min_value=500,
                max_value=5000,
                step_size=100,
                target_metric="cache_hit_rate",
                last_changed=datetime.now()
            ),
            "cache_ttl_default": TuningParameter(
                name="cache_ttl_default",
                current_value=3600,
                min_value=300,
                max_value=7200,
                step_size=300,
                target_metric="cache_hit_rate",
                last_changed=datetime.now()
            ),

            # Query optimizer parameters
            "slow_query_threshold": TuningParameter(
                name="slow_query_threshold",
                current_value=1000,
                min_value=500,
                max_value=5000,
                step_size=250,
                target_metric="avg_query_time",
                last_changed=datetime.now()
            ),
            "batch_size_threshold": TuningParameter(
                name="batch_size_threshold",
                current_value=10,
                min_value=5,
                max_value=50,
                step_size=5,
                target_metric="throughput",
                last_changed=datetime.now()
            ),

            # Performance profiler parameters
            "profiler_snapshot_interval": TuningParameter(
                name="profiler_snapshot_interval",
                current_value=5,
                min_value=1,
                max_value=30,
                step_size=1,
                target_metric="cpu_usage",
                last_changed=datetime.now()
            )
        }

    def start_auto_tuning(self):
        """Avvia auto-tuning automatico."""
        if self.tuning_active:
            return

        self.tuning_active = True
        self.tuning_thread = threading.Thread(
            target=self._tuning_loop,
            daemon=True
        )
        self.tuning_thread.start()
        logger.info("🎯 Auto-tuning avviato")

    def stop_auto_tuning(self):
        """Ferma auto-tuning."""
        self.tuning_active = False
        if self.tuning_thread:
            self.tuning_thread.join(timeout=5)
        logger.info("⏹️ Auto-tuning fermato")

    def _tuning_loop(self):
        """Loop principale di auto-tuning."""
        logger.info("🔄 Loop auto-tuning avviato")

        # Stabilisci baseline iniziale
        self._establish_baseline()

        while self.tuning_active:
            try:
                # Raccogli metriche correnti
                self._collect_current_metrics()

                # Verifica se è tempo di ottimizzare
                if self._should_optimize():
                    self._perform_optimization()

                # Verifica se serve rollback
                self._check_for_rollback()

                # Reset contatore orario
                self._reset_hourly_counter()

                time.sleep(self.TUNING_INTERVAL)

            except Exception as e:
                logger.error(f"Errore nel loop auto-tuning: {e}")
                time.sleep(self.TUNING_INTERVAL)

        logger.info("🔄 Loop auto-tuning terminato")

    def _establish_baseline(self):
        """Stabilisce metriche baseline."""
        logger.info("📊 Stabilendo baseline metriche...")

        # Raccogli metriche per 3 cicli
        baseline_samples = []
        for _ in range(3):
            metrics = self._collect_metrics_snapshot()
            if metrics:
                baseline_samples.append(metrics)
            time.sleep(60)  # Attendi 1 minuto tra campioni

        if baseline_samples:
            # Calcola media per baseline
            self.baseline_metrics = {}
            for key in baseline_samples[0].keys():
                values = [sample[key] for sample in baseline_samples if key in sample]
                if values:
                    self.baseline_metrics[key] = statistics.mean(values)

            logger.info(f"✅ Baseline stabilita: {len(self.baseline_metrics)} metriche")
        else:
            logger.warning("⚠️ Impossibile stabilire baseline")

    def _collect_current_metrics(self):
        """Raccoglie metriche correnti."""
        metrics = self._collect_metrics_snapshot()
        if metrics:
            self.current_metrics = metrics

            # Aggiungi alla cronologia
            for key, value in metrics.items():
                self.metric_history[key].append({
                    "value": value,
                    "timestamp": datetime.now()
                })

    def _collect_metrics_snapshot(self) -> Dict[str, float]:
        """Raccoglie snapshot delle metriche."""
        metrics = {}

        try:
            # Metriche cache
            cache_stats = intelligent_cache.get_cache_statistics()
            metrics["cache_hit_rate"] = cache_stats["performance"]["hit_rate_percentage"]
            metrics["cache_memory_usage"] = cache_stats["cache_levels"]["memory_entries"]

        except Exception as e:
            logger.debug(f"Errore metriche cache: {e}")

        try:
            # Metriche query optimizer
            query_report = query_optimizer.get_performance_report()
            metrics["avg_query_time"] = query_report["summary"]["avg_execution_time_ms"]
            metrics["slow_query_percentage"] = query_report["summary"]["slow_queries_percentage"]

        except Exception as e:
            logger.debug(f"Errore metriche query: {e}")

        try:
            # Metriche performance profiler
            perf_report = performance_profiler.get_performance_report()
            metrics["cpu_usage"] = perf_report["system_performance"]["avg_cpu_percent"]
            metrics["memory_usage"] = perf_report["system_performance"]["avg_memory_percent"]
            metrics["slow_function_percentage"] = perf_report["function_performance"]["slow_call_percentage"]

        except Exception as e:
            logger.debug(f"Errore metriche profiler: {e}")

        return metrics

    def _should_optimize(self) -> bool:
        """Determina se è il momento di ottimizzare."""
        now = datetime.now()

        # Verifica limiti temporali
        time_since_last = (now - self.last_optimization).total_seconds()
        if time_since_last < self.STABILITY_PERIOD:
            return False

        # Verifica limite cambiamenti orari
        if self.changes_this_hour >= self.MAX_CHANGES_PER_HOUR:
            return False

        # Verifica se ci sono metriche da ottimizzare
        return self._identify_optimization_opportunities()

    def _identify_optimization_opportunities(self) -> bool:
        """Identifica opportunità di ottimizzazione."""
        if not self.baseline_metrics or not self.current_metrics:
            return False

        # Cerca metriche che sono peggiorate significativamente
        for metric, current_value in self.current_metrics.items():
            baseline_value = self.baseline_metrics.get(metric)
            if baseline_value is None:
                continue

            # Calcola degradazione percentuale
            if baseline_value > 0:
                degradation = ((baseline_value - current_value) / baseline_value) * 100

                # Se degradazione > 10%, considera ottimizzazione
                if degradation > 10:
                    logger.info(f"🎯 Opportunità ottimizzazione rilevata: {metric} "
                               f"degradato del {degradation:.1f}%")
                    return True

        return False

    def _perform_optimization(self):
        """Esegue ottimizzazione automatica."""
        logger.info("🔧 Eseguendo ottimizzazione automatica...")

        # Identifica parametro da ottimizzare
        parameter_to_optimize = self._select_optimization_parameter()
        if not parameter_to_optimize:
            return

        # Calcola nuovo valore
        new_value = self._calculate_optimal_value(parameter_to_optimize)
        if new_value is None:
            return

        # Applica ottimizzazione
        result = self._apply_optimization(parameter_to_optimize, new_value)
        if result:
            self.optimization_history.append(result)
            self.last_optimization = datetime.now()
            self.changes_this_hour += 1

            logger.info(f"✅ Ottimizzazione applicata: {parameter_to_optimize.name} "
                       f"{parameter_to_optimize.current_value} → {new_value}")

    def _select_optimization_parameter(self) -> Optional[TuningParameter]:
        """Seleziona parametro da ottimizzare."""
        candidates = []

        for param in self.tuning_parameters.values():
            # Verifica se il parametro può essere modificato
            time_since_change = (datetime.now() - param.last_changed).total_seconds()
            if time_since_change < self.STABILITY_PERIOD:
                continue

            # Verifica se la metrica target è degradata
            target_metric = param.target_metric
            if target_metric in self.current_metrics and target_metric in self.baseline_metrics:
                current = self.current_metrics[target_metric]
                baseline = self.baseline_metrics[target_metric]

                if baseline > 0:
                    degradation = ((baseline - current) / baseline) * 100
                    if degradation > 5:  # Soglia 5% per considerare ottimizzazione
                        candidates.append((param, degradation))

        if not candidates:
            return None

        # Seleziona parametro con maggiore degradazione
        candidates.sort(key=lambda x: x[1], reverse=True)
        return candidates[0][0]

    def _calculate_optimal_value(self, parameter: TuningParameter) -> Any:
        """Calcola valore ottimale per un parametro."""
        current_value = parameter.current_value

        # Determina direzione del cambiamento basandosi sulla strategia
        if self.TUNING_STRATEGY == TuningStrategy.CONSERVATIVE:
            step_multiplier = 0.5
        elif self.TUNING_STRATEGY == TuningStrategy.BALANCED:
            step_multiplier = 1.0
        else:  # AGGRESSIVE
            step_multiplier = 2.0

        step = parameter.step_size * step_multiplier

        # Determina direzione basandosi sul tipo di metrica
        if parameter.target_metric in ["cache_hit_rate", "throughput"]:
            # Metriche che vogliamo aumentare
            new_value = current_value + step
        else:
            # Metriche che vogliamo diminuire (tempi, utilizzo)
            new_value = current_value - step

        # Applica limiti
        new_value = max(parameter.min_value, min(parameter.max_value, new_value))

        # Verifica che sia diverso dal valore corrente
        if new_value == current_value:
            return None

        return new_value

    def _apply_optimization(self, parameter: TuningParameter, new_value: Any) -> Optional[OptimizationResult]:
        """Applica ottimizzazione a un parametro."""
        old_value = parameter.current_value
        metric_before = self.current_metrics.get(parameter.target_metric, 0.0)

        try:
            # Applica il nuovo valore
            success = self._set_parameter_value(parameter.name, new_value)
            if not success:
                return None

            # Aggiorna parametro
            parameter.current_value = new_value
            parameter.last_changed = datetime.now()
            parameter.change_count += 1

            # Attendi stabilizzazione
            time.sleep(30)

            # Misura impatto
            new_metrics = self._collect_metrics_snapshot()
            metric_after = new_metrics.get(parameter.target_metric, 0.0)

            # Calcola miglioramento
            if metric_before > 0:
                improvement = ((metric_after - metric_before) / metric_before) * 100
            else:
                improvement = 0.0

            parameter.performance_impact = improvement

            return OptimizationResult(
                parameter_name=parameter.name,
                old_value=old_value,
                new_value=new_value,
                metric_before=metric_before,
                metric_after=metric_after,
                improvement_percent=improvement,
                timestamp=datetime.now(),
                strategy_used=self.TUNING_STRATEGY
            )

        except Exception as e:
            logger.error(f"Errore applicando ottimizzazione: {e}")
            # Rollback
            self._set_parameter_value(parameter.name, old_value)
            parameter.current_value = old_value
            return None

    def _set_parameter_value(self, parameter_name: str, value: Any) -> bool:
        """Imposta valore di un parametro nel sistema."""
        try:
            if parameter_name == "cache_memory_size":
                intelligent_cache.MEMORY_CACHE_SIZE = value
                return True
            elif parameter_name == "cache_ttl_default":
                intelligent_cache.DEFAULT_TTL_SECONDS = value
                return True
            elif parameter_name == "slow_query_threshold":
                query_optimizer.SLOW_QUERY_THRESHOLD_MS = value
                return True
            elif parameter_name == "batch_size_threshold":
                query_optimizer.BATCH_SIZE_THRESHOLD = value
                return True
            elif parameter_name == "profiler_snapshot_interval":
                performance_profiler.SNAPSHOT_INTERVAL = value
                return True
            else:
                logger.warning(f"Parametro sconosciuto: {parameter_name}")
                return False

        except Exception as e:
            logger.error(f"Errore impostando {parameter_name}: {e}")
            return False

    def _check_for_rollback(self):
        """Verifica se serve rollback di ottimizzazioni recenti."""
        if not self.optimization_history:
            return

        # Verifica ultime ottimizzazioni (ultimi 30 minuti)
        cutoff_time = datetime.now() - timedelta(minutes=30)
        recent_optimizations = [
            opt for opt in self.optimization_history
            if opt.timestamp >= cutoff_time
        ]

        for optimization in recent_optimizations:
            if optimization.improvement_percent < self.ROLLBACK_THRESHOLD:
                logger.warning(f"🔄 Rollback necessario per {optimization.parameter_name}: "
                              f"peggioramento del {abs(optimization.improvement_percent):.1f}%")
                self._perform_rollback(optimization)

    def _perform_rollback(self, optimization: OptimizationResult):
        """Esegue rollback di un'ottimizzazione."""
        parameter = self.tuning_parameters.get(optimization.parameter_name)
        if not parameter:
            return

        # Ripristina valore precedente
        success = self._set_parameter_value(optimization.parameter_name, optimization.old_value)
        if success:
            parameter.current_value = optimization.old_value
            parameter.last_changed = datetime.now()

            logger.info(f"✅ Rollback completato: {optimization.parameter_name} "
                       f"{optimization.new_value} → {optimization.old_value}")
        else:
            logger.error(f"❌ Fallimento rollback per {optimization.parameter_name}")

    def _reset_hourly_counter(self):
        """Reset contatore cambiamenti orari."""
        now = datetime.now()
        if hasattr(self, '_last_hour_reset'):
            if (now - self._last_hour_reset).total_seconds() >= 3600:
                self.changes_this_hour = 0
                self._last_hour_reset = now
        else:
            self._last_hour_reset = now

    def get_tuning_status(self) -> Dict[str, Any]:
        """Ottiene stato corrente del tuning."""
        return {
            "tuning_active": self.tuning_active,
            "strategy": self.TUNING_STRATEGY.value,
            "last_optimization": self.last_optimization.isoformat(),
            "changes_this_hour": self.changes_this_hour,
            "parameters": {
                name: {
                    "current_value": param.current_value,
                    "target_metric": param.target_metric,
                    "last_changed": param.last_changed.isoformat(),
                    "change_count": param.change_count,
                    "performance_impact": param.performance_impact
                }
                for name, param in self.tuning_parameters.items()
            },
            "recent_optimizations": [
                {
                    "parameter": opt.parameter_name,
                    "old_value": opt.old_value,
                    "new_value": opt.new_value,
                    "improvement_percent": opt.improvement_percent,
                    "timestamp": opt.timestamp.isoformat()
                }
                for opt in list(self.optimization_history)[-10:]  # Ultimi 10
            ]
        }

    def get_optimization_report(self) -> Dict[str, Any]:
        """Genera report delle ottimizzazioni."""
        if not self.optimization_history:
            return {"message": "Nessuna ottimizzazione eseguita"}

        # Calcola statistiche
        improvements = [opt.improvement_percent for opt in self.optimization_history]
        successful_optimizations = [opt for opt in self.optimization_history if opt.improvement_percent > 0]

        # Raggruppa per parametro
        by_parameter = defaultdict(list)
        for opt in self.optimization_history:
            by_parameter[opt.parameter_name].append(opt)

        return {
            "summary": {
                "total_optimizations": len(self.optimization_history),
                "successful_optimizations": len(successful_optimizations),
                "success_rate": len(successful_optimizations) / len(self.optimization_history) * 100,
                "avg_improvement": statistics.mean(improvements) if improvements else 0.0,
                "best_improvement": max(improvements) if improvements else 0.0,
                "worst_improvement": min(improvements) if improvements else 0.0
            },
            "by_parameter": {
                param_name: {
                    "optimization_count": len(opts),
                    "avg_improvement": statistics.mean([o.improvement_percent for o in opts]),
                    "last_optimization": max(opts, key=lambda x: x.timestamp).timestamp.isoformat()
                }
                for param_name, opts in by_parameter.items()
            },
            "current_metrics": self.current_metrics,
            "baseline_metrics": self.baseline_metrics
        }

    def force_optimization(self, parameter_name: str = None) -> Dict[str, Any]:
        """Forza ottimizzazione di un parametro specifico."""
        if parameter_name and parameter_name not in self.tuning_parameters:
            return {"error": f"Parametro {parameter_name} non trovato"}

        if parameter_name:
            parameter = self.tuning_parameters[parameter_name]
            new_value = self._calculate_optimal_value(parameter)
            if new_value is not None:
                result = self._apply_optimization(parameter, new_value)
                if result:
                    return {"success": True, "optimization": asdict(result)}
                else:
                    return {"error": "Ottimizzazione fallita"}
            else:
                return {"error": "Impossibile calcolare valore ottimale"}
        else:
            # Ottimizza tutti i parametri possibili
            self._perform_optimization()
            return {"success": True, "message": "Ottimizzazione automatica eseguita"}

# Istanza globale
auto_tuner = AutoTuner()
