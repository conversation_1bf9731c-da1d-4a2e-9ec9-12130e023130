#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Processore per i file di registro auto aziendali.
Gestisce l'elaborazione e la standardizzazione dei dati di utilizzo veicoli.
Versione semplificata per MCP Server senza dipendenze esterne.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import re
import logging

logger = logging.getLogger(__name__)

class VehicleRegistryProcessor:
    """
    Processore specializzato per i file di registro auto aziendali.
    Gestisce calcolo ore, validazione dati e standardizzazione.
    """

    def __init__(self):
        # Mappatura specifica per i campi del registro auto
        self.vehicle_fields = {
            'Dipendente': 'dipendente',
            'Data': 'data',
            'Auto': 'veicolo',
            'Presa Data e Ora': 'presa_datetime',
            'Riconsegna Data e Ora': 'riconsegna_datetime',
            'Cliente': 'cliente',
            'Ore': 'ore_utilizzo',
            'Note': 'note'
        }

        # Lista dei veicoli aziendali conosciuti
        self.known_vehicles = [
            'Punto', 'Peugeot', 'Fiesta', 'Fiat Punto',
            'Peugeot 208', 'Ford Fiesta', 'Auto 1', 'Auto 2'
        ]

        # Pattern per riconoscere messaggi di errore
        self.error_patterns = {
            'manca_presa': r'manca.*data.*ora.*presa',
            'manca_riconsegna': r'manca.*data.*ora.*riconsegna',
            'manca_entrambi': r'manca.*data.*ora'
        }

    def process_vehicle_registry_file(self, file_path):
        """
        Elabora un file di registro auto e restituisce un DataFrame standardizzato
        """
        try:
            # Leggi il file direttamente
            if file_path.endswith('.csv'):
                try:
                    df = pd.read_csv(file_path, sep=';', encoding='utf-8-sig')
                except:
                    df = pd.read_csv(file_path, sep=',', encoding='utf-8-sig')
            elif file_path.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(file_path)
            else:
                raise ValueError(f"Formato file non supportato: {file_path}")

            logger.info(f"🚗 File Registro Auto letto: {len(df)} righe, {len(df.columns)} colonne")
            logger.info(f"📋 Colonne rilevate: {df.columns.tolist()}")

            # Standardizza i nomi delle colonne
            df = self._standardize_vehicle_columns(df)

            # Pulisci e valida i dati
            df = self._clean_vehicle_data(df)

            # Elabora le date e gli orari
            df = self._process_vehicle_datetime(df)

            # Calcola le ore di utilizzo
            df = self._calculate_usage_hours(df)

            # Valida e standardizza i veicoli
            df = self._standardize_vehicles(df)

            # Gestisci dipendenti multipli
            df = self._process_multiple_employees(df)

            # Calcola metriche aggiuntive
            df = self._calculate_additional_metrics(df)

            return df

        except Exception as e:
            logger.error(f"Errore nell'elaborazione del file registro auto: {str(e)}")
            raise

    def _standardize_vehicle_columns(self, df):
        """Standardizza i nomi delle colonne specifiche del registro auto"""
        renamed_columns = {}

        for col in df.columns:
            if col in self.vehicle_fields:
                renamed_columns[col] = self.vehicle_fields[col]

        if renamed_columns:
            return df.rename(columns=renamed_columns)

        return df

    def _clean_vehicle_data(self, df):
        """Pulisce i dati del registro auto"""
        df_clean = df.copy()

        # Rimuovi righe completamente vuote
        df_clean = df_clean.dropna(how='all')

        # Rimuovi righe dove mancano i dati essenziali
        essential_columns = ['dipendente', 'data', 'veicolo']
        for col in essential_columns:
            if col in df_clean.columns:
                df_clean = df_clean[df_clean[col].notna()]
                df_clean = df_clean[df_clean[col] != '']

        return df_clean

    def _process_vehicle_datetime(self, df):
        """Elabora le colonne di data e ora del registro auto"""
        df_processed = df.copy()

        # Elabora la data
        if 'data' in df_processed.columns:
            df_processed['data'] = df_processed['data'].apply(self._parse_italian_date)

        # Elabora presa e riconsegna
        datetime_columns = ['presa_datetime', 'riconsegna_datetime']

        for col in datetime_columns:
            if col in df_processed.columns:
                df_processed[col] = df_processed[col].apply(self._parse_datetime_with_errors)

        return df_processed

    def _parse_italian_date(self, date_str):
        """Parse delle date in formato italiano"""
        if pd.isna(date_str) or date_str == '':
            return None

        date_str = str(date_str).strip()

        # Formati comuni italiani
        formats = [
            '%d/%m/%Y',
            '%d-%m-%Y',
            '%d.%m.%Y',
            '%d/%m/%y',
            '%d-%m-%y',
            '%d.%m.%y',
            '%Y-%m-%d',
            '%Y/%m/%d'
        ]

        for fmt in formats:
            try:
                return pd.to_datetime(date_str, format=fmt)
            except:
                continue

        # Prova parsing automatico
        try:
            return pd.to_datetime(date_str, dayfirst=True)
        except:
            return None

    def _parse_datetime_with_errors(self, value):
        """Parse datetime gestendo messaggi di errore"""
        if pd.isna(value) or value == '':
            return None

        value_str = str(value).strip()

        # Controlla se è un messaggio di errore
        for pattern in self.error_patterns.values():
            if re.search(pattern, value_str, re.IGNORECASE):
                return None

        # Prova a parsare la data
        return self._parse_italian_date(value_str)

    def _calculate_usage_hours(self, df):
        """Calcola le ore di utilizzo basandosi su presa e riconsegna"""
        df_calc = df.copy()

        if 'presa_datetime' in df_calc.columns and 'riconsegna_datetime' in df_calc.columns:
            # Calcola ore solo dove abbiamo entrambe le date
            mask = (df_calc['presa_datetime'].notna()) & (df_calc['riconsegna_datetime'].notna())

            if mask.any():
                duration = df_calc.loc[mask, 'riconsegna_datetime'] - df_calc.loc[mask, 'presa_datetime']
                df_calc.loc[mask, 'ore_calcolate'] = duration.dt.total_seconds() / 3600

                # Arrotonda a 2 decimali
                df_calc['ore_calcolate'] = df_calc['ore_calcolate'].round(2)

        # Gestisci la colonna ore esistente
        if 'ore_utilizzo' in df_calc.columns:
            df_calc['ore_originali'] = df_calc['ore_utilizzo'].apply(self._parse_hours)

            # Usa ore calcolate se disponibili, altrimenti usa quelle originali
            if 'ore_calcolate' in df_calc.columns:
                df_calc['ore_finali'] = df_calc['ore_calcolate'].fillna(df_calc['ore_originali'])
            else:
                df_calc['ore_finali'] = df_calc['ore_originali']

        return df_calc

    def _parse_hours(self, value):
        """Parse delle ore gestendo diversi formati"""
        if pd.isna(value) or value == '':
            return None

        value_str = str(value).strip()

        # Controlla se è un messaggio di errore
        for pattern in self.error_patterns.values():
            if re.search(pattern, value_str, re.IGNORECASE):
                return None

        # Prova a convertire in float (sostituisci virgola con punto)
        try:
            return float(value_str.replace(',', '.'))
        except ValueError:
            return None

    def _standardize_vehicles(self, df):
        """Standardizza i nomi dei veicoli"""
        if 'veicolo' not in df.columns:
            return df

        df_std = df.copy()

        # Mappa veicoli simili
        vehicle_mapping = {
            'punto': 'Fiat Punto',
            'fiat punto': 'Fiat Punto',
            'peugeot': 'Peugeot 208',
            'peugeot 208': 'Peugeot 208',
            'fiesta': 'Ford Fiesta',
            'ford fiesta': 'Ford Fiesta'
        }

        df_std['veicolo_std'] = df_std['veicolo'].apply(
            lambda x: vehicle_mapping.get(str(x).lower(), str(x)) if pd.notna(x) else x
        )

        return df_std

    def _process_multiple_employees(self, df):
        """Gestisce dipendenti multipli (es. 'Matteo Signo/Franco')"""
        if 'dipendente' not in df.columns:
            return df

        df_proc = df.copy()

        # Identifica righe con dipendenti multipli
        df_proc['dipendenti_multipli'] = df_proc['dipendente'].str.contains('/', na=False)

        # Estrai dipendente principale
        df_proc['dipendente_principale'] = df_proc['dipendente'].apply(
            lambda x: str(x).split('/')[0].strip() if pd.notna(x) and '/' in str(x) else str(x)
        )

        # Conta numero dipendenti
        df_proc['numero_dipendenti'] = df_proc['dipendente'].apply(
            lambda x: len(str(x).split('/')) if pd.notna(x) and '/' in str(x) else 1
        )

        return df_proc

    def _calculate_additional_metrics(self, df):
        """Calcola metriche aggiuntive per l'analisi"""
        df_metrics = df.copy()

        # Aggiungi informazioni temporali
        if 'data' in df_metrics.columns:
            df_metrics['giorno_settimana'] = df_metrics['data'].dt.day_name()
            df_metrics['mese'] = df_metrics['data'].dt.month
            df_metrics['anno'] = df_metrics['data'].dt.year
            df_metrics['settimana_anno'] = df_metrics['data'].dt.isocalendar().week

        # Calcola durata in categorie
        if 'ore_finali' in df_metrics.columns:
            df_metrics['categoria_durata'] = df_metrics['ore_finali'].apply(self._categorize_duration)

        # Identifica utilizzi anomali
        df_metrics['utilizzo_anomalo'] = self._identify_anomalous_usage(df_metrics)

        return df_metrics

    def _categorize_duration(self, hours):
        """Categorizza la durata dell'utilizzo"""
        if pd.isna(hours):
            return 'Non disponibile'
        elif hours < 1:
            return 'Breve (< 1h)'
        elif hours < 4:
            return 'Medio (1-4h)'
        elif hours < 8:
            return 'Lungo (4-8h)'
        else:
            return 'Molto lungo (> 8h)'

    def _identify_anomalous_usage(self, df):
        """Identifica utilizzi anomali"""
        anomalies = []

        for idx, row in df.iterrows():
            anomaly_flags = []

            # Ore mancanti
            if pd.isna(row.get('ore_finali')):
                anomaly_flags.append('ore_mancanti')

            # Durata eccessiva (> 24 ore)
            if pd.notna(row.get('ore_finali')) and row['ore_finali'] > 24:
                anomaly_flags.append('durata_eccessiva')

            # Date mancanti
            if pd.isna(row.get('presa_datetime')) and pd.isna(row.get('riconsegna_datetime')):
                anomaly_flags.append('date_mancanti')

            # Cliente mancante
            if pd.isna(row.get('cliente')) or row.get('cliente', '') == '':
                anomaly_flags.append('cliente_mancante')

            anomalies.append(anomaly_flags if anomaly_flags else None)

        return anomalies

    def generate_summary_stats(self, df):
        """Genera statistiche riassuntive per il registro auto"""
        stats = {}

        if df.empty:
            return stats

        # Statistiche generali
        stats['total_records'] = len(df)
        stats['unique_employees'] = df['dipendente'].nunique() if 'dipendente' in df.columns else 0
        stats['unique_vehicles'] = df['veicolo'].nunique() if 'veicolo' in df.columns else 0
        stats['unique_clients'] = df['cliente'].nunique() if 'cliente' in df.columns else 0

        # Statistiche ore
        if 'ore_finali' in df.columns:
            valid_hours = df['ore_finali'].dropna()
            if not valid_hours.empty:
                stats['total_hours'] = valid_hours.sum()
                stats['avg_hours'] = valid_hours.mean()
                stats['max_hours'] = valid_hours.max()
                stats['min_hours'] = valid_hours.min()

        # Veicoli più utilizzati
        if 'veicolo' in df.columns:
            vehicle_usage = df['veicolo'].value_counts().head(5)
            stats['top_vehicles'] = vehicle_usage.to_dict()

        # Dipendenti più attivi
        if 'dipendente' in df.columns:
            employee_usage = df['dipendente'].value_counts().head(5)
            stats['top_employees'] = employee_usage.to_dict()

        # Clienti più serviti
        if 'cliente' in df.columns:
            client_usage = df['cliente'].value_counts().head(5)
            stats['top_clients'] = client_usage.to_dict()

        return stats
