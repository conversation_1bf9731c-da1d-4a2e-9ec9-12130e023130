#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FASE 7: Script per eseguire tutti i test di Testing e Debugging Avanzato
"""

import subprocess
import sys
import time
import os
from datetime import datetime
from typing import Dict, List, Any

class TestRunner:
    """Runner per test della Fase 7."""

    def __init__(self):
        self.results = {}
        self.start_time = None
        self.end_time = None

    def run_test_suite(self, test_file: str, markers: str = None, description: str = "") -> Dict[str, Any]:
        """Esegue una suite di test."""
        print(f"\n🧪 ESEGUENDO: {description}")
        print("=" * 60)

        # Gestione speciale per test funzionali
        if test_file == "test_fase6_agents.py":
            cmd = ["python", test_file]
        else:
            # Costruisci comando pytest
            cmd = ["python", "-m", "pytest", test_file, "-v"]

            if markers:
                cmd.extend(["-m", markers])

            # Aggiungi opzioni per output dettagliato
            cmd.extend(["--tb=short", "--durations=10"])

        start_time = time.time()

        try:
            # Esegui test
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='replace',
                timeout=600  # 10 minuti timeout
            )

            end_time = time.time()
            duration = end_time - start_time

            # Analizza output
            output_lines = result.stdout.split('\n')
            error_lines = result.stderr.split('\n')

            # Estrai statistiche
            stats = self._extract_test_stats(output_lines)

            test_result = {
                "file": test_file,
                "description": description,
                "return_code": result.returncode,
                "duration": duration,
                "stats": stats,
                "output": result.stdout,
                "errors": result.stderr,
                "success": result.returncode == 0
            }

            # Stampa risultato
            if test_result["success"]:
                print(f"✅ SUCCESSO: {description}")
                print(f"   Durata: {duration:.2f}s")
                if stats:
                    print(f"   Test: {stats.get('passed', 0)} passati, {stats.get('failed', 0)} falliti, {stats.get('skipped', 0)} saltati")
            else:
                print(f"❌ FALLIMENTO: {description}")
                print(f"   Durata: {duration:.2f}s")
                print(f"   Codice uscita: {result.returncode}")
                if error_lines:
                    print(f"   Errori: {len([l for l in error_lines if l.strip()])}")

            return test_result

        except subprocess.TimeoutExpired:
            print(f"⏰ TIMEOUT: {description} (>10 minuti)")
            return {
                "file": test_file,
                "description": description,
                "return_code": -1,
                "duration": 600,
                "stats": {},
                "output": "",
                "errors": "Test timeout dopo 10 minuti",
                "success": False
            }

        except Exception as e:
            print(f"💥 ERRORE: {description} - {e}")
            return {
                "file": test_file,
                "description": description,
                "return_code": -2,
                "duration": 0,
                "stats": {},
                "output": "",
                "errors": str(e),
                "success": False
            }

    def _extract_test_stats(self, output_lines: List[str]) -> Dict[str, int]:
        """Estrae statistiche dai test."""
        stats = {"passed": 0, "failed": 0, "skipped": 0, "errors": 0}

        for line in output_lines:
            line = line.strip()

            # Cerca linea di summary pytest
            if "passed" in line or "failed" in line or "skipped" in line:
                # Esempi: "5 passed, 2 failed, 1 skipped"
                parts = line.split()
                for i, part in enumerate(parts):
                    if part.isdigit() and i + 1 < len(parts):
                        count = int(part)
                        status = parts[i + 1].rstrip(',')

                        if status in stats:
                            stats[status] = count

        return stats

    def run_all_tests(self) -> Dict[str, Any]:
        """Esegue tutti i test della Fase 7."""
        print("🚀 INIZIO FASE 7: TESTING E DEBUGGING AVANZATO")
        print("=" * 80)
        print(f"📅 Data/Ora: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🐍 Python: {sys.version}")
        print(f"📁 Directory: {os.getcwd()}")

        self.start_time = time.time()

        # Lista test da eseguire
        test_suites = [
            {
                "file": "test_suite_complete.py",
                "markers": None,
                "description": "Task 7.1 - Test Suite Completa (Integrazione Sistema)"
            },
            {
                "file": "performance_testing.py",
                "markers": "performance",
                "description": "Task 7.2 - Performance Testing (Cache, Agenti, Sistema)"
            },
            {
                "file": "security_testing.py",
                "markers": "security",
                "description": "Task 7.3 - Security Testing (Validazione, Protezione, Controlli)"
            },
            {
                "file": "test_fase6_agents.py",
                "markers": None,
                "description": "Test Agenti AI (Verifica Fase 6) - Funzionale"
            }
        ]

        # Esegui ogni suite
        for i, suite in enumerate(test_suites, 1):
            print(f"\n📋 SUITE {i}/{len(test_suites)}")

            result = self.run_test_suite(
                suite["file"],
                suite["markers"],
                suite["description"]
            )

            self.results[suite["file"]] = result

        self.end_time = time.time()

        # Genera report finale
        return self._generate_final_report()

    def _generate_final_report(self) -> Dict[str, Any]:
        """Genera report finale."""
        total_duration = self.end_time - self.start_time

        # Calcola statistiche aggregate
        total_passed = 0
        total_failed = 0
        total_skipped = 0
        total_errors = 0
        successful_suites = 0

        for result in self.results.values():
            if result["success"]:
                successful_suites += 1

            stats = result.get("stats", {})
            total_passed += stats.get("passed", 0)
            total_failed += stats.get("failed", 0)
            total_skipped += stats.get("skipped", 0)
            total_errors += stats.get("errors", 0)

        # Stampa report finale
        print("\n" + "=" * 80)
        print("📊 REPORT FINALE FASE 7")
        print("=" * 80)

        print(f"\n⏱️  DURATA TOTALE: {total_duration:.2f} secondi ({total_duration/60:.1f} minuti)")

        print(f"\n📋 SUITE DI TEST:")
        for file, result in self.results.items():
            status = "✅ SUCCESSO" if result["success"] else "❌ FALLIMENTO"
            duration = result["duration"]
            print(f"   {status} - {file} ({duration:.2f}s)")

        print(f"\n📈 STATISTICHE AGGREGATE:")
        print(f"   Suite completate: {successful_suites}/{len(self.results)}")
        print(f"   Test passati: {total_passed}")
        print(f"   Test falliti: {total_failed}")
        print(f"   Test saltati: {total_skipped}")
        print(f"   Errori: {total_errors}")

        total_tests = total_passed + total_failed + total_skipped
        if total_tests > 0:
            success_rate = (total_passed / total_tests) * 100
            print(f"   Tasso successo: {success_rate:.1f}%")

        # Valutazione finale
        print(f"\n🏆 VALUTAZIONE FASE 7:")

        if successful_suites == len(self.results) and total_failed == 0:
            grade = "🟢 ECCELLENTE"
            description = "Tutti i test passati con successo"
        elif successful_suites >= len(self.results) * 0.8 and total_failed <= total_tests * 0.1:
            grade = "🟡 BUONO"
            description = "Maggior parte dei test passati"
        elif successful_suites >= len(self.results) * 0.6:
            grade = "🟠 SUFFICIENTE"
            description = "Test base passati, necessari miglioramenti"
        else:
            grade = "🔴 INSUFFICIENTE"
            description = "Problemi significativi rilevati"

        print(f"   Voto: {grade}")
        print(f"   Descrizione: {description}")

        # Raccomandazioni
        print(f"\n💡 RACCOMANDAZIONI:")
        if total_failed > 0:
            print(f"   - Investigare e correggere {total_failed} test falliti")
        if total_errors > 0:
            print(f"   - Risolvere {total_errors} errori di sistema")
        if successful_suites < len(self.results):
            print(f"   - Verificare configurazione per suite fallite")
        if total_failed == 0 and total_errors == 0:
            print(f"   - Sistema stabile, pronto per produzione")

        print(f"\n📝 PROSSIMI PASSI:")
        print(f"   - Commit e push risultati Fase 7")
        print(f"   - Documentazione test coverage")
        print(f"   - Setup CI/CD pipeline")
        print(f"   - Preparazione deployment produzione")

        return {
            "total_duration": total_duration,
            "successful_suites": successful_suites,
            "total_suites": len(self.results),
            "total_passed": total_passed,
            "total_failed": total_failed,
            "total_skipped": total_skipped,
            "total_errors": total_errors,
            "success_rate": (total_passed / max(total_tests, 1)) * 100,
            "grade": grade,
            "results": self.results
        }

def main():
    """Funzione principale."""
    print("🧪 FASE 7: TESTING E DEBUGGING AVANZATO")
    print("Inizializzazione test runner...")

    # Verifica che i file di test esistano
    test_files = [
        "test_suite_complete.py",
        "performance_testing.py",
        "security_testing.py",
        "test_fase6_agents.py"
    ]

    missing_files = []
    for file in test_files:
        if not os.path.exists(file):
            missing_files.append(file)

    if missing_files:
        print(f"❌ File di test mancanti: {missing_files}")
        print("Assicurati che tutti i file di test siano presenti nella directory corrente.")
        return 1

    # Esegui test
    runner = TestRunner()
    final_report = runner.run_all_tests()

    # Salva report
    try:
        import json
        with open("fase7_test_report.json", "w", encoding="utf-8") as f:
            json.dump(final_report, f, indent=2, ensure_ascii=False)
        print(f"\n💾 Report salvato in: fase7_test_report.json")
    except Exception as e:
        print(f"⚠️ Impossibile salvare report: {e}")

    # Ritorna codice di uscita
    if final_report["total_failed"] == 0 and final_report["successful_suites"] == final_report["total_suites"]:
        return 0
    else:
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
