#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Cross-Analysis Engine per analisi incrociate automatiche dei dati.
Identifica discrepanze, pattern anomali e genera report di coerenza.
"""

import logging
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta, date, time
from dataclasses import dataclass
import json
from collections import defaultdict

# Configurazione logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class Discrepancy:
    """Rappresenta una discrepanza trovata nell'analisi."""
    id: str
    type: str  # 'missing_activity', 'time_overlap', 'data_inconsistency', etc.
    severity: str  # 'low', 'medium', 'high', 'critical'
    description: str
    affected_entities: List[str]
    suggested_action: str
    data_details: Dict[str, Any]
    confidence: float

@dataclass
class AnalysisResult:
    """Risultato di un'analisi incrociata."""
    analysis_type: str
    timestamp: datetime
    total_records_analyzed: int
    discrepancies_found: List[Discrepancy]
    summary_stats: Dict[str, Any]
    recommendations: List[str]
    processing_time_ms: int

class CrossAnalysisEngine:
    """
    Motore per analisi incrociate automatiche dei dati.

    Funzionalità:
    - Controlli coerenza ore tecnici vs calendario
    - Verifica presenza attività per sessioni remote
    - Controllo duplicati e sovrapposizioni
    - Analisi produttività e costi
    - Sistema di alerting per anomalie
    """

    def __init__(self, db_manager=None):
        """
        Inizializza il motore di analisi incrociata.

        Args:
            db_manager: Istanza di AdvancedDatabaseManager
        """
        self.db_manager = db_manager

        # Configurazioni analisi
        self.config = {
            'time_tolerance_minutes': 15,  # Tolleranza per sovrapposizioni temporali
            'min_session_duration_minutes': 5,  # Durata minima sessione valida
            'max_daily_hours': 12,  # Ore massime giornaliere per tecnico
            'productivity_threshold': 0.7,  # Soglia produttività minima
            'cost_variance_threshold': 0.2  # Soglia varianza costi
        }

        # Contatori per ID univoci
        self._discrepancy_counter = 0

        logger.info("CrossAnalysisEngine inizializzato")

    def run_comprehensive_analysis(self, date_from: Optional[str] = None,
                                 date_to: Optional[str] = None) -> Dict[str, AnalysisResult]:
        """
        Esegue analisi completa su tutti i tipi di controlli.

        Args:
            date_from: Data inizio analisi (YYYY-MM-DD)
            date_to: Data fine analisi (YYYY-MM-DD)

        Returns:
            Dizionario con risultati di tutte le analisi
        """
        start_time = datetime.now()

        try:
            logger.info(f"🔍 Avvio analisi completa: {date_from} - {date_to}")

            results = {}

            # 1. Analisi coerenza temporale
            logger.info("⏰ Analisi coerenza temporale")
            results['time_consistency'] = self.analyze_time_consistency(date_from, date_to)

            # 2. Analisi attività vs sessioni remote
            logger.info("🖥️ Analisi attività vs sessioni remote")
            results['activity_remote_correlation'] = self.analyze_activity_remote_correlation(date_from, date_to)

            # 3. Analisi duplicati e sovrapposizioni
            logger.info("🔄 Analisi duplicati e sovrapposizioni")
            results['duplicates_overlaps'] = self.analyze_duplicates_and_overlaps(date_from, date_to)

            # 4. Analisi produttività
            logger.info("📊 Analisi produttività")
            results['productivity_analysis'] = self.analyze_productivity(date_from, date_to)

            # 5. Analisi costi e fatturazione
            logger.info("💰 Analisi costi e fatturazione")
            results['cost_analysis'] = self.analyze_costs_and_billing(date_from, date_to)

            # 6. Analisi qualità dati
            logger.info("🔍 Analisi qualità dati")
            results['data_quality'] = self.analyze_data_quality(date_from, date_to)

            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            # Genera summary globale
            results['global_summary'] = self._generate_global_summary(results, processing_time)

            logger.info(f"✅ Analisi completa terminata in {processing_time:.0f}ms")
            return results

        except Exception as e:
            logger.error(f"❌ Errore analisi completa: {str(e)}")
            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            return {
                'error': AnalysisResult(
                    analysis_type='comprehensive_error',
                    timestamp=datetime.now(),
                    total_records_analyzed=0,
                    discrepancies_found=[],
                    summary_stats={'error': str(e)},
                    recommendations=[f"Verificare configurazione sistema: {str(e)}"],
                    processing_time_ms=int(processing_time)
                )
            }

    def analyze_time_consistency(self, date_from: Optional[str] = None,
                               date_to: Optional[str] = None) -> AnalysisResult:
        """
        Analizza coerenza temporale tra attività, timbrature e calendario.

        Controlli:
        - Ore lavorate vs ore registrate in attività
        - Sovrapposizioni temporali impossibili
        - Orari di lavoro fuori norma
        """
        start_time = datetime.now()
        discrepancies = []
        total_records = 0

        try:
            if not self.db_manager or not self.db_manager.is_connected:
                raise Exception("Database manager non disponibile")

            # Recupera dati per analisi
            analysis_data = self.db_manager.get_cross_analysis_data(date_from, date_to)

            activities = analysis_data.get('activities', [])
            teamviewer = analysis_data.get('teamviewer', [])
            total_records = len(activities) + len(teamviewer)

            # Raggruppa per tecnico e data
            technician_daily_data = self._group_by_technician_and_date(activities, teamviewer)

            for tech_name, daily_data in technician_daily_data.items():
                for date_str, day_data in daily_data.items():
                    # Controllo 1: Ore totali giornaliere eccessive
                    total_hours = self._calculate_total_daily_hours(day_data)
                    if total_hours > self.config['max_daily_hours']:
                        discrepancies.append(self._create_discrepancy(
                            'excessive_daily_hours',
                            'high',
                            f"Tecnico {tech_name} ha {total_hours:.1f} ore registrate il {date_str} (limite: {self.config['max_daily_hours']})",
                            [tech_name],
                            "Verificare registrazioni orarie e possibili duplicati",
                            {'technician': tech_name, 'date': date_str, 'total_hours': total_hours},
                            0.9
                        ))

                    # Controllo 2: Sovrapposizioni temporali
                    overlaps = self._find_time_overlaps(day_data)
                    for overlap in overlaps:
                        discrepancies.append(self._create_discrepancy(
                            'time_overlap',
                            'medium',
                            f"Sovrapposizione temporale per {tech_name} il {date_str}: {overlap['description']}",
                            [tech_name],
                            "Verificare e correggere orari sovrapposti",
                            overlap,
                            0.8
                        ))

                    # Controllo 3: Sessioni troppo brevi o troppo lunghe
                    duration_issues = self._check_session_durations(day_data)
                    for issue in duration_issues:
                        discrepancies.append(self._create_discrepancy(
                            'unusual_duration',
                            'low',
                            f"Durata inusuale per {tech_name} il {date_str}: {issue['description']}",
                            [tech_name],
                            "Verificare accuratezza registrazione durata",
                            issue,
                            0.6
                        ))

            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            return AnalysisResult(
                analysis_type='time_consistency',
                timestamp=datetime.now(),
                total_records_analyzed=total_records,
                discrepancies_found=discrepancies,
                summary_stats={
                    'technicians_analyzed': len(technician_daily_data),
                    'days_analyzed': sum(len(daily_data) for daily_data in technician_daily_data.values()),
                    'excessive_hours_cases': len([d for d in discrepancies if d.type == 'excessive_daily_hours']),
                    'time_overlaps': len([d for d in discrepancies if d.type == 'time_overlap']),
                    'duration_issues': len([d for d in discrepancies if d.type == 'unusual_duration'])
                },
                recommendations=self._generate_time_consistency_recommendations(discrepancies),
                processing_time_ms=int(processing_time)
            )

        except Exception as e:
            logger.error(f"Errore analisi coerenza temporale: {str(e)}")
            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            return AnalysisResult(
                analysis_type='time_consistency',
                timestamp=datetime.now(),
                total_records_analyzed=total_records,
                discrepancies_found=[],
                summary_stats={'error': str(e)},
                recommendations=[f"Errore analisi: {str(e)}"],
                processing_time_ms=int(processing_time)
            )

    def analyze_activity_remote_correlation(self, date_from: Optional[str] = None,
                                          date_to: Optional[str] = None) -> AnalysisResult:
        """
        Analizza correlazione tra attività registrate e sessioni remote.

        Controlli:
        - Sessioni TeamViewer senza attività corrispondenti
        - Attività remote senza sessioni TeamViewer
        - Discrepanze nei clienti tra attività e sessioni
        """
        start_time = datetime.now()
        discrepancies = []
        total_records = 0

        try:
            if not self.db_manager or not self.db_manager.is_connected:
                raise Exception("Database manager non disponibile")

            analysis_data = self.db_manager.get_cross_analysis_data(date_from, date_to)

            activities = analysis_data.get('activities', [])
            teamviewer = analysis_data.get('teamviewer', [])
            total_records = len(activities) + len(teamviewer)

            # Raggruppa per tecnico, data e cliente
            activity_sessions = self._group_activities_by_tech_date_client(activities)
            tv_sessions = self._group_teamviewer_by_tech_date_client(teamviewer)

            # Controllo 1: Sessioni TeamViewer senza attività
            for key, tv_session in tv_sessions.items():
                tech, date_str, client = key
                if key not in activity_sessions:
                    discrepancies.append(self._create_discrepancy(
                        'teamviewer_without_activity',
                        'medium',
                        f"Sessione TeamViewer per {tech} presso {client} il {date_str} senza attività registrata",
                        [tech, client],
                        "Registrare attività corrispondente o verificare classificazione sessione",
                        {'technician': tech, 'client': client, 'date': date_str, 'session_duration': tv_session.get('duration_minutes', 0)},
                        0.7
                    ))

            # Controllo 2: Attività remote senza sessioni TeamViewer
            for key, activity in activity_sessions.items():
                tech, date_str, client = key
                if self._is_remote_activity(activity) and key not in tv_sessions:
                    discrepancies.append(self._create_discrepancy(
                        'remote_activity_without_teamviewer',
                        'medium',
                        f"Attività remota per {tech} presso {client} il {date_str} senza sessione TeamViewer",
                        [tech, client],
                        "Verificare se attività è effettivamente remota o registrare sessione TeamViewer",
                        {'technician': tech, 'client': client, 'date': date_str, 'activity_type': activity.get('activity_type', 'N/A')},
                        0.6
                    ))

            # Controllo 3: Discrepanze durata tra attività e sessioni
            for key in set(activity_sessions.keys()) & set(tv_sessions.keys()):
                activity = activity_sessions[key]
                tv_session = tv_sessions[key]

                activity_duration = activity.get('duration_hours', 0) * 60  # Converti in minuti
                tv_duration = tv_session.get('duration_minutes', 0)

                if abs(activity_duration - tv_duration) > self.config['time_tolerance_minutes']:
                    tech, date_str, client = key
                    discrepancies.append(self._create_discrepancy(
                        'duration_mismatch',
                        'low',
                        f"Discrepanza durata per {tech} presso {client} il {date_str}: attività {activity_duration:.0f}min vs TeamViewer {tv_duration:.0f}min",
                        [tech, client],
                        "Verificare e allineare durate registrate",
                        {'technician': tech, 'client': client, 'date': date_str, 'activity_duration': activity_duration, 'tv_duration': tv_duration},
                        0.5
                    ))

            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            return AnalysisResult(
                analysis_type='activity_remote_correlation',
                timestamp=datetime.now(),
                total_records_analyzed=total_records,
                discrepancies_found=discrepancies,
                summary_stats={
                    'total_activities': len(activities),
                    'total_teamviewer_sessions': len(teamviewer),
                    'matched_sessions': len(set(activity_sessions.keys()) & set(tv_sessions.keys())),
                    'unmatched_teamviewer': len([d for d in discrepancies if d.type == 'teamviewer_without_activity']),
                    'unmatched_activities': len([d for d in discrepancies if d.type == 'remote_activity_without_teamviewer']),
                    'duration_mismatches': len([d for d in discrepancies if d.type == 'duration_mismatch'])
                },
                recommendations=self._generate_activity_remote_recommendations(discrepancies),
                processing_time_ms=int(processing_time)
            )

        except Exception as e:
            logger.error(f"Errore analisi correlazione attività-remote: {str(e)}")
            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            return AnalysisResult(
                analysis_type='activity_remote_correlation',
                timestamp=datetime.now(),
                total_records_analyzed=total_records,
                discrepancies_found=[],
                summary_stats={'error': str(e)},
                recommendations=[f"Errore analisi: {str(e)}"],
                processing_time_ms=int(processing_time)
            )

    def analyze_duplicates_and_overlaps(self, date_from: Optional[str] = None,
                                      date_to: Optional[str] = None) -> AnalysisResult:
        """
        Analizza duplicati e sovrapposizioni nei dati.
        """
        start_time = datetime.now()
        discrepancies = []
        total_records = 0

        try:
            if not self.db_manager or not self.db_manager.is_connected:
                raise Exception("Database manager non disponibile")

            analysis_data = self.db_manager.get_cross_analysis_data(date_from, date_to)

            activities = analysis_data.get('activities', [])
            teamviewer = analysis_data.get('teamviewer', [])
            total_records = len(activities) + len(teamviewer)

            # Controllo duplicati attività
            activity_duplicates = self._find_duplicate_activities(activities)
            for duplicate_group in activity_duplicates:
                discrepancies.append(self._create_discrepancy(
                    'duplicate_activities',
                    'medium',
                    f"Trovate {len(duplicate_group)} attività duplicate",
                    [act.get('technician_name', 'N/A') for act in duplicate_group],
                    "Rimuovere duplicati mantenendo il record più accurato",
                    {'duplicate_count': len(duplicate_group), 'activities': duplicate_group},
                    0.8
                ))

            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            return AnalysisResult(
                analysis_type='duplicates_overlaps',
                timestamp=datetime.now(),
                total_records_analyzed=total_records,
                discrepancies_found=discrepancies,
                summary_stats={
                    'duplicate_activity_groups': len(activity_duplicates),
                    'total_duplicate_records': sum(len(group) for group in activity_duplicates)
                },
                recommendations=self._generate_duplicates_recommendations(discrepancies),
                processing_time_ms=int(processing_time)
            )

        except Exception as e:
            logger.error(f"Errore analisi duplicati: {str(e)}")
            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            return AnalysisResult(
                analysis_type='duplicates_overlaps',
                timestamp=datetime.now(),
                total_records_analyzed=total_records,
                discrepancies_found=[],
                summary_stats={'error': str(e)},
                recommendations=[f"Errore analisi: {str(e)}"],
                processing_time_ms=int(processing_time)
            )

    def analyze_productivity(self, date_from: Optional[str] = None,
                           date_to: Optional[str] = None) -> AnalysisResult:
        """Analizza produttività tecnici."""
        start_time = datetime.now()
        discrepancies = []
        total_records = 0

        try:
            if not self.db_manager or not self.db_manager.is_connected:
                raise Exception("Database manager non disponibile")

            analysis_data = self.db_manager.get_cross_analysis_data(date_from, date_to)
            activities = analysis_data.get('activities', [])
            total_records = len(activities)

            # Calcola metriche produttività per tecnico
            productivity_metrics = self._calculate_productivity_metrics(activities)

            for tech_name, metrics in productivity_metrics.items():
                if metrics['productivity_score'] < self.config['productivity_threshold']:
                    discrepancies.append(self._create_discrepancy(
                        'low_productivity',
                        'medium',
                        f"Produttività bassa per {tech_name}: {metrics['productivity_score']:.2f}",
                        [tech_name],
                        "Analizzare cause e fornire supporto",
                        metrics,
                        0.7
                    ))

            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            return AnalysisResult(
                analysis_type='productivity_analysis',
                timestamp=datetime.now(),
                total_records_analyzed=total_records,
                discrepancies_found=discrepancies,
                summary_stats={
                    'technicians_analyzed': len(productivity_metrics),
                    'low_productivity_cases': len([d for d in discrepancies if d.type == 'low_productivity'])
                },
                recommendations=self._generate_productivity_recommendations(discrepancies, productivity_metrics),
                processing_time_ms=int(processing_time)
            )

        except Exception as e:
            logger.error(f"Errore analisi produttività: {str(e)}")
            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            return AnalysisResult(
                analysis_type='productivity_analysis',
                timestamp=datetime.now(),
                total_records_analyzed=total_records,
                discrepancies_found=[],
                summary_stats={'error': str(e)},
                recommendations=[f"Errore analisi: {str(e)}"],
                processing_time_ms=int(processing_time)
            )

    def analyze_costs_and_billing(self, date_from: Optional[str] = None,
                                date_to: Optional[str] = None) -> AnalysisResult:
        """Analizza costi e fatturazione."""
        start_time = datetime.now()
        discrepancies = []
        total_records = 0

        try:
            if not self.db_manager or not self.db_manager.is_connected:
                raise Exception("Database manager non disponibile")

            analysis_data = self.db_manager.get_cross_analysis_data(date_from, date_to)
            activities = analysis_data.get('activities', [])
            total_records = len(activities)

            # Controllo attività senza costi
            for activity in activities:
                if not activity.get('total_cost') or activity.get('total_cost', 0) <= 0:
                    tech_name = activity.get('master_technicians', {}).get('normalized_name', 'N/A')
                    client_name = activity.get('master_clients', {}).get('normalized_name', 'N/A')

                    discrepancies.append(self._create_discrepancy(
                        'missing_cost',
                        'medium',
                        f"Attività senza costo per {tech_name} presso {client_name}",
                        [tech_name, client_name],
                        "Verificare e aggiornare tariffe/costi",
                        {'activity_id': activity.get('id'), 'duration_hours': activity.get('duration_hours', 0)},
                        0.8
                    ))

            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            return AnalysisResult(
                analysis_type='cost_analysis',
                timestamp=datetime.now(),
                total_records_analyzed=total_records,
                discrepancies_found=discrepancies,
                summary_stats={
                    'activities_without_cost': len([d for d in discrepancies if d.type == 'missing_cost'])
                },
                recommendations=self._generate_cost_recommendations(discrepancies),
                processing_time_ms=int(processing_time)
            )

        except Exception as e:
            logger.error(f"Errore analisi costi: {str(e)}")
            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            return AnalysisResult(
                analysis_type='cost_analysis',
                timestamp=datetime.now(),
                total_records_analyzed=total_records,
                discrepancies_found=[],
                summary_stats={'error': str(e)},
                recommendations=[f"Errore analisi: {str(e)}"],
                processing_time_ms=int(processing_time)
            )

    def analyze_data_quality(self, date_from: Optional[str] = None,
                           date_to: Optional[str] = None) -> AnalysisResult:
        """Analizza qualità generale dei dati."""
        start_time = datetime.now()
        discrepancies = []
        total_records = 0

        try:
            if not self.db_manager or not self.db_manager.is_connected:
                raise Exception("Database manager non disponibile")

            # Usa il report qualità del database manager
            quality_report = self.db_manager.get_quality_report(date_from, date_to)

            if 'error' in quality_report:
                raise Exception(quality_report['error'])

            # Analizza metriche qualità
            quality_metrics = quality_report.get('quality_metrics', {})

            for table, metrics in quality_metrics.items():
                avg_quality = metrics.get('avg_quality_score', 0)
                low_quality_count = metrics.get('low_quality_count', 0)

                if avg_quality < 0.7:
                    discrepancies.append(self._create_discrepancy(
                        'low_data_quality',
                        'high',
                        f"Qualità dati bassa per tabella {table}: {avg_quality:.2f}",
                        [],
                        "Verificare e migliorare qualità dati",
                        {'table': table, 'avg_quality': avg_quality, 'low_quality_count': low_quality_count},
                        0.9
                    ))

            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            return AnalysisResult(
                analysis_type='data_quality',
                timestamp=datetime.now(),
                total_records_analyzed=total_records,
                discrepancies_found=discrepancies,
                summary_stats=quality_report.get('quality_metrics', {}),
                recommendations=quality_report.get('recommendations', []),
                processing_time_ms=int(processing_time)
            )

        except Exception as e:
            logger.error(f"Errore analisi qualità dati: {str(e)}")
            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            return AnalysisResult(
                analysis_type='data_quality',
                timestamp=datetime.now(),
                total_records_analyzed=total_records,
                discrepancies_found=[],
                summary_stats={'error': str(e)},
                recommendations=[f"Errore analisi: {str(e)}"],
                processing_time_ms=int(processing_time)
            )

    # ===============================================
    # METODI HELPER
    # ===============================================

    def _create_discrepancy(self, disc_type: str, severity: str, description: str,
                          affected_entities: List[str], suggested_action: str,
                          data_details: Dict[str, Any], confidence: float) -> Discrepancy:
        """Crea una nuova discrepanza."""
        self._discrepancy_counter += 1
        return Discrepancy(
            id=f"DISC_{self._discrepancy_counter:04d}",
            type=disc_type,
            severity=severity,
            description=description,
            affected_entities=affected_entities,
            suggested_action=suggested_action,
            data_details=data_details,
            confidence=confidence
        )

    def _group_by_technician_and_date(self, activities: List[Dict], teamviewer: List[Dict]) -> Dict[str, Dict[str, List[Dict]]]:
        """Raggruppa dati per tecnico e data."""
        grouped = defaultdict(lambda: defaultdict(list))

        # Raggruppa attività
        for activity in activities:
            tech_name = activity.get('master_technicians', {}).get('normalized_name', 'Unknown')
            activity_date = activity.get('activity_date', '')
            if activity_date:
                grouped[tech_name][activity_date].append({
                    'type': 'activity',
                    'data': activity,
                    'start_time': activity.get('start_time'),
                    'end_time': activity.get('end_time'),
                    'duration_hours': activity.get('duration_hours', 0)
                })

        # Raggruppa TeamViewer
        for session in teamviewer:
            tech_name = session.get('master_technicians', {}).get('normalized_name', 'Unknown')
            session_start = session.get('session_start', '')
            if session_start:
                session_date = session_start.split('T')[0] if 'T' in session_start else session_start.split(' ')[0]
                grouped[tech_name][session_date].append({
                    'type': 'teamviewer',
                    'data': session,
                    'start_time': session.get('session_start'),
                    'end_time': session.get('session_end'),
                    'duration_minutes': session.get('duration_minutes', 0)
                })

        return dict(grouped)

    def _calculate_total_daily_hours(self, day_data: List[Dict]) -> float:
        """Calcola ore totali giornaliere."""
        total_hours = 0

        for item in day_data:
            if item['type'] == 'activity':
                total_hours += item.get('duration_hours', 0)
            elif item['type'] == 'teamviewer':
                total_hours += (item.get('duration_minutes', 0) / 60)

        return total_hours

    def _find_time_overlaps(self, day_data: List[Dict]) -> List[Dict]:
        """Trova sovrapposizioni temporali."""
        overlaps = []

        # Ordina per ora di inizio
        sorted_data = sorted([item for item in day_data if item.get('start_time')],
                           key=lambda x: x['start_time'])

        for i in range(len(sorted_data) - 1):
            current = sorted_data[i]
            next_item = sorted_data[i + 1]

            current_end = current.get('end_time')
            next_start = next_item.get('start_time')

            if current_end and next_start and current_end > next_start:
                overlaps.append({
                    'description': f"Sovrapposizione tra {current['type']} e {next_item['type']}",
                    'current_item': current,
                    'next_item': next_item,
                    'overlap_duration': 'da calcolare'
                })

        return overlaps

    def _check_session_durations(self, day_data: List[Dict]) -> List[Dict]:
        """Controlla durate sessioni inusuali."""
        issues = []

        for item in day_data:
            duration = 0
            if item['type'] == 'activity':
                duration = item.get('duration_hours', 0) * 60  # Converti in minuti
            elif item['type'] == 'teamviewer':
                duration = item.get('duration_minutes', 0)

            if duration < self.config['min_session_duration_minutes']:
                issues.append({
                    'description': f"Sessione troppo breve: {duration} minuti",
                    'item': item,
                    'duration': duration
                })
            elif duration > 480:  # 8 ore
                issues.append({
                    'description': f"Sessione troppo lunga: {duration} minuti",
                    'item': item,
                    'duration': duration
                })

        return issues

    def _group_activities_by_tech_date_client(self, activities: List[Dict]) -> Dict[Tuple[str, str, str], Dict]:
        """Raggruppa attività per tecnico, data e cliente."""
        grouped = {}

        for activity in activities:
            tech_name = activity.get('master_technicians', {}).get('normalized_name', 'Unknown')
            client_name = activity.get('master_clients', {}).get('normalized_name', 'Unknown')
            activity_date = activity.get('activity_date', '')

            key = (tech_name, activity_date, client_name)
            grouped[key] = activity

        return grouped

    def _group_teamviewer_by_tech_date_client(self, teamviewer: List[Dict]) -> Dict[Tuple[str, str, str], Dict]:
        """Raggruppa sessioni TeamViewer per tecnico, data e cliente."""
        grouped = {}

        for session in teamviewer:
            tech_name = session.get('master_technicians', {}).get('normalized_name', 'Unknown')
            client_name = session.get('master_clients', {}).get('normalized_name', 'Unknown')
            session_start = session.get('session_start', '')

            if session_start:
                session_date = session_start.split('T')[0] if 'T' in session_start else session_start.split(' ')[0]
                key = (tech_name, session_date, client_name)
                grouped[key] = session

        return grouped

    def _is_remote_activity(self, activity: Dict) -> bool:
        """Determina se un'attività è remota."""
        activity_type = activity.get('activity_type', '').lower()
        description = activity.get('description', '').lower()

        remote_keywords = ['remoto', 'remote', 'teamviewer', 'rdp', 'vpn', 'online']

        return any(keyword in activity_type or keyword in description for keyword in remote_keywords)

    def _find_duplicate_activities(self, activities: List[Dict]) -> List[List[Dict]]:
        """Trova gruppi di attività duplicate."""
        duplicates = []
        processed = set()

        for i, activity in enumerate(activities):
            if i in processed:
                continue

            duplicate_group = [activity]

            for j, other_activity in enumerate(activities[i+1:], i+1):
                if j in processed:
                    continue

                if self._are_activities_duplicate(activity, other_activity):
                    duplicate_group.append(other_activity)
                    processed.add(j)

            if len(duplicate_group) > 1:
                duplicates.append(duplicate_group)
                processed.add(i)

        return duplicates

    def _are_activities_duplicate(self, activity1: Dict, activity2: Dict) -> bool:
        """Verifica se due attività sono duplicate."""
        # Confronta campi chiave
        fields_to_compare = ['activity_date', 'technician_id', 'client_id', 'duration_hours']

        for field in fields_to_compare:
            if activity1.get(field) != activity2.get(field):
                return False

        # Confronta descrizioni (similarità)
        desc1 = activity1.get('description', '').lower()
        desc2 = activity2.get('description', '').lower()

        if desc1 and desc2:
            from difflib import SequenceMatcher
            similarity = SequenceMatcher(None, desc1, desc2).ratio()
            return similarity > 0.9

        return True

    def _calculate_productivity_metrics(self, activities: List[Dict]) -> Dict[str, Dict]:
        """Calcola metriche produttività per tecnico."""
        metrics = {}

        # Raggruppa per tecnico
        tech_activities = defaultdict(list)
        for activity in activities:
            tech_name = activity.get('master_technicians', {}).get('normalized_name', 'Unknown')
            tech_activities[tech_name].append(activity)

        for tech_name, tech_acts in tech_activities.items():
            total_hours = sum(act.get('duration_hours', 0) for act in tech_acts)
            total_activities = len(tech_acts)
            unique_clients = len(set(act.get('client_id') for act in tech_acts if act.get('client_id')))

            # Calcola score produttività (semplificato)
            productivity_score = min(1.0, total_hours / (total_activities * 2)) if total_activities > 0 else 0

            metrics[tech_name] = {
                'total_hours': total_hours,
                'total_activities': total_activities,
                'unique_clients': unique_clients,
                'avg_hours_per_activity': total_hours / total_activities if total_activities > 0 else 0,
                'productivity_score': productivity_score
            }

        return metrics

    def _generate_global_summary(self, results: Dict[str, AnalysisResult], processing_time: float) -> AnalysisResult:
        """Genera summary globale di tutte le analisi."""
        all_discrepancies = []
        total_records = 0

        for analysis_type, result in results.items():
            if hasattr(result, 'discrepancies_found'):
                all_discrepancies.extend(result.discrepancies_found)
                total_records += result.total_records_analyzed

        # Raggruppa per severità
        severity_counts = defaultdict(int)
        for disc in all_discrepancies:
            severity_counts[disc.severity] += 1

        # Genera raccomandazioni globali
        global_recommendations = []
        if severity_counts['critical'] > 0:
            global_recommendations.append(f"🚨 {severity_counts['critical']} problemi critici richiedono attenzione immediata")
        if severity_counts['high'] > 0:
            global_recommendations.append(f"⚠️ {severity_counts['high']} problemi ad alta priorità da risolvere")
        if severity_counts['medium'] > 0:
            global_recommendations.append(f"📋 {severity_counts['medium']} problemi medi da pianificare")
        if severity_counts['low'] > 0:
            global_recommendations.append(f"💡 {severity_counts['low']} miglioramenti suggeriti")

        if not all_discrepancies:
            global_recommendations.append("✅ Nessuna discrepanza critica trovata - sistema in buono stato")

        return AnalysisResult(
            analysis_type='global_summary',
            timestamp=datetime.now(),
            total_records_analyzed=total_records,
            discrepancies_found=all_discrepancies,
            summary_stats={
                'total_analyses': len(results),
                'total_discrepancies': len(all_discrepancies),
                'severity_breakdown': dict(severity_counts),
                'processing_time_ms': processing_time
            },
            recommendations=global_recommendations,
            processing_time_ms=int(processing_time)
        )

    def _generate_time_consistency_recommendations(self, discrepancies: List[Discrepancy]) -> List[str]:
        """Genera raccomandazioni per coerenza temporale."""
        recommendations = []

        excessive_hours = len([d for d in discrepancies if d.type == 'excessive_daily_hours'])
        overlaps = len([d for d in discrepancies if d.type == 'time_overlap'])

        if excessive_hours > 0:
            recommendations.append(f"Verificare {excessive_hours} casi di ore eccessive giornaliere")
        if overlaps > 0:
            recommendations.append(f"Risolvere {overlaps} sovrapposizioni temporali")

        if not discrepancies:
            recommendations.append("✅ Coerenza temporale ottimale")

        return recommendations

    def _generate_activity_remote_recommendations(self, discrepancies: List[Discrepancy]) -> List[str]:
        """Genera raccomandazioni per correlazione attività-remote."""
        recommendations = []

        unmatched_tv = len([d for d in discrepancies if d.type == 'teamviewer_without_activity'])
        unmatched_act = len([d for d in discrepancies if d.type == 'remote_activity_without_teamviewer'])

        if unmatched_tv > 0:
            recommendations.append(f"Registrare attività per {unmatched_tv} sessioni TeamViewer")
        if unmatched_act > 0:
            recommendations.append(f"Verificare {unmatched_act} attività remote senza sessioni")

        if not discrepancies:
            recommendations.append("✅ Correlazione attività-remote ottimale")

        return recommendations

    def _generate_duplicates_recommendations(self, discrepancies: List[Discrepancy]) -> List[str]:
        """Genera raccomandazioni per duplicati."""
        recommendations = []

        if discrepancies:
            recommendations.append(f"Rimuovere {len(discrepancies)} gruppi di record duplicati")
            recommendations.append("Implementare controlli per prevenire duplicati futuri")
        else:
            recommendations.append("✅ Nessun duplicato trovato")

        return recommendations

    def _generate_productivity_recommendations(self, discrepancies: List[Discrepancy], metrics: Dict) -> List[str]:
        """Genera raccomandazioni per produttività."""
        recommendations = []

        low_productivity = len([d for d in discrepancies if d.type == 'low_productivity'])

        if low_productivity > 0:
            recommendations.append(f"Supportare {low_productivity} tecnici con produttività bassa")
            recommendations.append("Analizzare cause e fornire formazione mirata")
        else:
            recommendations.append("✅ Produttività generale soddisfacente")

        return recommendations

    def _generate_cost_recommendations(self, discrepancies: List[Discrepancy]) -> List[str]:
        """Genera raccomandazioni per costi."""
        recommendations = []

        missing_costs = len([d for d in discrepancies if d.type == 'missing_cost'])

        if missing_costs > 0:
            recommendations.append(f"Aggiornare costi per {missing_costs} attività")
            recommendations.append("Verificare configurazione tariffe")
        else:
            recommendations.append("✅ Costi e fatturazione corretti")

        return recommendations