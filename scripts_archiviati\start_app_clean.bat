@echo off
echo ===================================
echo Avvio dell'applicazione con l'ambiente pulito
echo ===================================
echo.

REM Attiva l'ambiente pulito
echo Attivazione dell'ambiente pulito...
call clean_env\Scripts\activate
echo Ambiente attivato.
echo.

REM Verifica se cachelib è installato
echo Verifica delle dipendenze necessarie...
pip show cachelib > nul 2>&1
if %errorlevel% neq 0 (
    echo Installazione di cachelib (necessario per Flask-Session)...
    pip install cachelib
    echo Cachelib installato.
    echo.
) else (
    echo Cachelib già installato.
    echo.
)

REM Imposta le variabili d'ambiente
set OPENROUTER_API_KEY=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
set APP_URL=http://localhost:5000
set MCP_URL=http://localhost:8000

echo Variabili d'ambiente impostate:
echo OPENROUTER_API_KEY: %OPENROUTER_API_KEY%
echo APP_URL: %APP_URL%
echo MCP_URL: %MCP_URL%
echo.

REM Avvia il server MCP in una nuova finestra
echo Avvio del server MCP...
start "MCP Server" cmd /k "call clean_env\Scripts\activate && cd mcp_server && python run_server.py"
echo Server MCP avviato in una nuova finestra.
echo.

REM Attendi 5 secondi per dare tempo al server MCP di avviarsi
echo Attesa di 5 secondi per l'avvio del server MCP...
ping 127.0.0.1 -n 6 > nul
echo.

REM Avvia l'applicazione principale
echo Avvio dell'applicazione principale...
start "App Roberto" cmd /k "call clean_env\Scripts\activate && python app.py"
echo Applicazione principale avviata in una nuova finestra.
echo.

echo ===================================
echo Applicazione avviata con successo!
echo - Flask: http://localhost:5000
echo - MCP: http://localhost:8000
echo ===================================
echo.
echo Premi un tasto per chiudere questa finestra...
pause > nul
