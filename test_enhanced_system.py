#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test del sistema di riconoscimento intelligente potenziato.
Testa il nuovo sistema con i file reali nella cartella test_file_grezzi.
"""

import pandas as pd
import sys
import os
from pathlib import Path

# Aggiungi il percorso del progetto
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_file_detector import EnhancedFileDetector
from intelligent_entity_extractor import IntelligentEntityExtractor
from data_standardizer import DataStandardizer
from universal_file_reader import UniversalFileReader

def test_enhanced_system():
    """
    Testa il sistema potenziato con i file di test reali.
    """
    print("🧪 TEST SISTEMA DI RICONOSCIMENTO INTELLIGENTE POTENZIATO")
    print("=" * 60)

    # Inizializza componenti
    file_detector = EnhancedFileDetector()
    entity_extractor = IntelligentEntityExtractor()
    data_standardizer = DataStandardizer()
    universal_reader = UniversalFileReader()

    # Cartella dei file di test
    test_folder = Path("test_file_grezzi")

    if not test_folder.exists():
        print("❌ Cartella test_file_grezzi non trovata!")
        return

    # Lista file di test
    test_files = list(test_folder.glob("*"))
    print(f"📁 Trovati {len(test_files)} file di test")
    print()

    results = []

    for file_path in test_files:
        if file_path.is_file():
            print(f"🔍 Analizzando: {file_path.name}")
            print("-" * 40)

            try:
                # 1. Lettura file
                df, file_info = universal_reader.read_file(str(file_path))

                if not file_info["success"]:
                    print(f"❌ Errore lettura: {file_info.get('error', 'Sconosciuto')}")
                    continue

                print(f"✅ File letto: {len(df)} righe, {len(df.columns)} colonne")
                print(f"📋 Colonne: {df.columns.tolist()}")

                # 2. Rilevamento tipo file
                detected_type, confidence, type_scores = file_detector.detect_file_type(df)
                print(f"🎯 Tipo rilevato: {detected_type} (confidenza: {confidence:.3f})")

                # Mostra tutti i punteggi
                print("📊 Punteggi per tipo:")
                for file_type, score in sorted(type_scores.items(), key=lambda x: x[1], reverse=True):
                    print(f"   {file_type}: {score:.3f}")

                # 3. Estrazione entità
                print("\n🔬 Estrazione entità...")
                extraction_result = entity_extractor.extract_entities(df, detected_type)

                entities = extraction_result.get("entities", {})
                print(f"📈 Entità estratte:")
                for entity_type, entity_list in entities.items():
                    if entity_list:
                        print(f"   {entity_type}: {len(entity_list)} entità")
                        # Mostra le prime 3 entità
                        for i, entity in enumerate(entity_list[:3]):
                            print(f"     - {entity['value']} (conf: {entity['confidence']:.2f})")
                        if len(entity_list) > 3:
                            print(f"     ... e altre {len(entity_list) - 3}")

                # 4. Mappatura colonne
                column_mapping = extraction_result.get("column_mapping", {})
                if column_mapping:
                    print(f"\n🗺️ Mappatura colonne:")
                    for col, entity_types in column_mapping.items():
                        print(f"   {col} -> {entity_types}")

                # 5. Standardizzazione (se ci sono entità mappate)
                if column_mapping:
                    print("\n🔧 Standardizzazione dati...")
                    # Crea mappatura semplificata per standardizer
                    simple_mapping = {}
                    for col, entity_types in column_mapping.items():
                        if entity_types:
                            simple_mapping[col] = entity_types[0]  # Prendi il primo tipo

                    standardization_result = data_standardizer.standardize_data(
                        df, detected_type, simple_mapping
                    )

                    total_changes = standardization_result.get("quality_metrics", {}).get("total_changes", 0)
                    print(f"✨ Standardizzazioni applicate: {total_changes}")

                    if total_changes > 0:
                        changes_by_operation = standardization_result.get("quality_metrics", {}).get("changes_by_operation", {})
                        print("📋 Operazioni:")
                        for operation, count in changes_by_operation.items():
                            print(f"   {operation}: {count}")

                # Salva risultati
                result = {
                    "file_name": file_path.name,
                    "detected_type": detected_type,
                    "confidence": confidence,
                    "entities_count": sum(len(entities.get(et, [])) for et in entities),
                    "columns_mapped": len(column_mapping),
                    "standardizations": standardization_result.get("quality_metrics", {}).get("total_changes", 0) if column_mapping else 0
                }
                results.append(result)

                print("✅ Analisi completata")

            except Exception as e:
                print(f"❌ Errore durante l'analisi: {str(e)}")
                import traceback
                traceback.print_exc()

            print("\n" + "=" * 60 + "\n")

    # Riepilogo finale
    print("📊 RIEPILOGO RISULTATI")
    print("=" * 60)

    if results:
        print(f"📁 File analizzati: {len(results)}")

        # Statistiche per tipo
        type_counts = {}
        for result in results:
            file_type = result["detected_type"]
            type_counts[file_type] = type_counts.get(file_type, 0) + 1

        print("\n🎯 Tipi rilevati:")
        for file_type, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"   {file_type}: {count} file")

        # Statistiche confidenza
        confidences = [r["confidence"] for r in results]
        avg_confidence = sum(confidences) / len(confidences)
        print(f"\n📈 Confidenza media: {avg_confidence:.3f}")

        high_confidence = len([c for c in confidences if c >= 0.7])
        print(f"🎯 File con alta confidenza (≥0.7): {high_confidence}/{len(results)}")

        # Statistiche entità
        total_entities = sum(r["entities_count"] for r in results)
        print(f"\n🔬 Entità totali estratte: {total_entities}")

        # Statistiche standardizzazione
        total_standardizations = sum(r["standardizations"] for r in results)
        print(f"🔧 Standardizzazioni totali: {total_standardizations}")

        print("\n📋 Dettaglio per file:")
        for result in results:
            print(f"   {result['file_name']}: {result['detected_type']} "
                  f"(conf: {result['confidence']:.2f}, "
                  f"entità: {result['entities_count']}, "
                  f"std: {result['standardizations']})")

    else:
        print("❌ Nessun file analizzato con successo")

if __name__ == "__main__":
    test_enhanced_system()
