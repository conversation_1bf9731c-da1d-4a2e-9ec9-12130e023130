#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test del ContentBasedFileAnalyzer per app-roberto
"""

import pandas as pd
import numpy as np
from content_based_file_analyzer import ContentBasedFileAnalyzer
import json

def create_test_data():
    """Crea dati di test per diversi tipi di file."""
    
    # Dati di test per attività
    activity_data = pd.DataFrame({
        'Ticket ID': ['TK-12345', 'TK-12346', 'TK-12347', 'TK-12348', 'TK-12349'],
        'Titolo Attività': ['Installazione software', 'Riparazione PC', 'Configurazione rete', 'Backup dati', 'Aggiornamento sistema'],
        'Tecnico Assegnato': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
        'Cliente': ['Azienda ABC', 'Studio XYZ', 'Azienda ABC', 'Uff<PERSON>o 123', 'Studio XYZ'],
        'Data Inizio': ['15/01/2024', '16/01/2024', '17/01/2024', '18/01/2024', '19/01/2024'],
        '<PERSON> Fine': ['15/01/2024', '16/01/2024', '17/01/2024', '18/01/2024', '19/01/2024'],
        'Durata': ['2:30', '1:45', '3:15', '0:45', '2:00'],
        'Stato': ['Completato', 'In corso', 'Completato', 'Completato', 'Aperto']
    })
    
    # Dati di test per timbrature
    timesheet_data = pd.DataFrame({
        'Dipendente': ['MR001', 'LV002', 'AB003', 'MR001', 'LV002'],
        'Data': ['20/01/2024', '20/01/2024', '20/01/2024', '21/01/2024', '21/01/2024'],
        'Ora Entrata': ['08:30', '09:00', '08:45', '08:30', '09:15'],
        'Ora Uscita': ['17:30', '18:00', '17:45', '17:30', '18:15'],
        'Pausa Pranzo': ['12:30-13:30', '13:00-14:00', '12:45-13:45', '12:30-13:30', '13:00-14:00'],
        'Ore Lavorate': ['8:00', '8:00', '8:00', '8:00', '8:00']
    })
    
    # Dati di test per TeamViewer
    teamviewer_data = pd.DataFrame({
        'Computer ID': ['123456789', '987654321', '456789123', '789123456', '321654987'],
        'Nome Computer': ['PC-UFFICIO-01', 'LAPTOP-MARIO', 'PC-RECEPTION', 'SERVER-01', 'PC-CONTABILITA'],
        'Utente': ['mario.rossi', 'luigi.verdi', 'anna.bianchi', 'admin', 'contabile'],
        'IP Address': ['************', '************', '************', '***********', '************'],
        'Durata Sessione': ['01:30:45', '00:45:30', '02:15:20', '00:30:15', '01:45:00'],
        'Data Connessione': ['22/01/2024 10:30', '22/01/2024 14:15', '22/01/2024 16:00', '23/01/2024 09:00', '23/01/2024 11:30']
    })
    
    # Dati di test per calendario
    calendar_data = pd.DataFrame({
        'Titolo Evento': ['Riunione settimanale', 'Presentazione progetto', 'Formazione team', 'Call cliente', 'Review codice'],
        'Data': ['25/01/2024', '26/01/2024', '27/01/2024', '28/01/2024', '29/01/2024'],
        'Ora Inizio': ['09:00', '14:30', '10:00', '15:00', '11:00'],
        'Ora Fine': ['10:00', '16:00', '12:00', '16:00', '12:00'],
        'Partecipanti': ['Mario, Luigi, Anna', 'Tutto il team', 'Sviluppatori', 'Mario, Cliente ABC', 'Luigi, Anna'],
        'Luogo': ['Sala Riunioni A', 'Sala Conferenze', 'Ufficio', 'Online', 'Sala Riunioni B']
    })
    
    # Dati di test per registro auto
    vehicle_data = pd.DataFrame({
        'Targa': ['AB123CD', 'EF456GH', 'IJ789KL', 'MN012OP', 'QR345ST'],
        'Conducente': ['Mario Rossi', 'Luigi Verdi', 'Anna Bianchi', 'Mario Rossi', 'Luigi Verdi'],
        'Data': ['30/01/2024', '30/01/2024', '31/01/2024', '31/01/2024', '01/02/2024'],
        'Km Iniziali': ['15000', '22000', '8500', '15050', '22100'],
        'Km Finali': ['15080', '22150', '8620', '15120', '22250'],
        'Destinazione': ['Cliente ABC', 'Fornitore XYZ', 'Ufficio Postale', 'Cliente DEF', 'Banca'],
        'Carburante': ['€ 25.50', '€ 35.00', '€ 18.75', '€ 22.00', '€ 40.00']
    })
    
    return {
        'attivita': activity_data,
        'timbrature': timesheet_data,
        'teamviewer': teamviewer_data,
        'calendario': calendar_data,
        'registro_auto': vehicle_data
    }

def test_content_based_analyzer():
    """Test completo del ContentBasedFileAnalyzer."""
    print('🧪 Test ContentBasedFileAnalyzer')
    print('=' * 50)
    
    # Inizializza l'analizzatore
    analyzer = ContentBasedFileAnalyzer()
    
    # Crea dati di test
    test_datasets = create_test_data()
    
    results = {}
    
    for expected_type, df in test_datasets.items():
        print(f'\n📊 Test per tipo: {expected_type}')
        print(f'   Righe: {len(df)}, Colonne: {len(df.columns)}')
        
        # Esegui analisi
        result = analyzer.analyze_content(df, f"test_{expected_type}.xlsx")
        
        # Mostra risultati
        detected_type = result.get('detected_type', 'unknown')
        confidence = result.get('confidence_score', 0.0)
        
        print(f'   🎯 Tipo rilevato: {detected_type}')
        print(f'   📈 Confidenza: {confidence:.3f}')
        
        # Verifica correttezza
        is_correct = detected_type == expected_type
        status = '✅ CORRETTO' if is_correct else '❌ ERRATO'
        print(f'   {status}')
        
        # Mostra punteggi per tutti i tipi
        type_scores = result.get('type_scores', {})
        print(f'   📊 Punteggi:')
        for file_type, score in sorted(type_scores.items(), key=lambda x: x[1], reverse=True):
            print(f'      {file_type}: {score:.3f}')
        
        # Mostra entità estratte
        entities = result.get('entity_extraction', {})
        if entities:
            print(f'   🔍 Entità estratte:')
            for entity_type, entity_list in entities.items():
                if isinstance(entity_list, list) and entity_list:
                    print(f'      {entity_type}: {len(entity_list)} elementi')
        
        # Mostra raccomandazioni
        recommendations = result.get('recommendations', [])
        if recommendations:
            print(f'   💡 Raccomandazioni: {len(recommendations)}')
        
        results[expected_type] = {
            'detected': detected_type,
            'confidence': confidence,
            'correct': is_correct,
            'scores': type_scores
        }
    
    # Statistiche finali
    print(f'\n📈 STATISTICHE FINALI')
    print('=' * 30)
    
    total_tests = len(results)
    correct_detections = sum(1 for r in results.values() if r['correct'])
    accuracy = (correct_detections / total_tests) * 100
    
    print(f'Test totali: {total_tests}')
    print(f'Rilevamenti corretti: {correct_detections}')
    print(f'Accuratezza: {accuracy:.1f}%')
    
    # Confidenza media
    avg_confidence = sum(r['confidence'] for r in results.values()) / total_tests
    print(f'Confidenza media: {avg_confidence:.3f}')
    
    # Dettagli per tipo
    print(f'\n📋 DETTAGLI PER TIPO:')
    for expected_type, result in results.items():
        status = '✅' if result['correct'] else '❌'
        print(f'{status} {expected_type}: {result["detected"]} ({result["confidence"]:.3f})')
    
    return results

if __name__ == "__main__":
    test_content_based_analyzer()
