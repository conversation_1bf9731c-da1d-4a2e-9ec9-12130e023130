#!/usr/bin/env python3
"""
App Flask minimalista per testare solo la route problematica
"""

from flask import Flask, request, jsonify
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Crea app Flask
app = Flask(__name__)
app.secret_key = 'test_secret_key'

@app.route('/')
def home():
    """Homepage di test"""
    return jsonify({
        'status': 'OK',
        'message': 'App Flask minimalista funzionante',
        'routes': [
            'GET /',
            'GET /api/health',
            'POST /api/get-processed-data'
        ]
    })

@app.route('/api/health')
def health():
    """Endpoint di health check"""
    return jsonify({
        'status': 'healthy',
        'message': 'App funzionante'
    })

@app.route('/api/get-processed-data', methods=['POST'])
def api_get_processed_data():
    """
    Versione semplificata dell'endpoint problematico.
    Testa solo la registrazione della route.
    """
    try:
        logger.info("=== API GET-PROCESSED-DATA CHIAMATA ===")
        
        data = request.get_json() or {}
        filename = data.get('filename')
        
        if not filename:
            return jsonify({
                'success': False,
                'error': 'Nome file richiesto'
            }), 400
        
        # Risposta di test
        return jsonify({
            'success': True,
            'message': f'Route funzionante! File richiesto: {filename}',
            'data': [],
            'test': True
        })
        
    except Exception as e:
        logger.error(f"Errore API get-processed-data: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/test-routes')
def test_routes():
    """Endpoint per listare tutte le route registrate"""
    routes = []
    for rule in app.url_map.iter_rules():
        routes.append({
            'endpoint': rule.endpoint,
            'methods': list(rule.methods),
            'rule': str(rule)
        })
    
    return jsonify({
        'success': True,
        'routes': routes,
        'total': len(routes)
    })

if __name__ == '__main__':
    print("🚀 Avvio app Flask minimalista...")
    print("📍 Endpoint disponibili:")
    print("   GET  / - Homepage")
    print("   GET  /api/health - Health check")
    print("   POST /api/get-processed-data - Endpoint da testare")
    print("   GET  /api/test-routes - Lista route registrate")
    print()
    
    # Avvia in modalità debug
    app.run(host='127.0.0.1', port=5000, debug=True)
