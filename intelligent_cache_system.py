#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Intelligent Cache System - Sistema di caching intelligente multi-livello per app-roberto.
Implementa caching predittivo, invalidazione intelligente e ottimizzazione automatica.
"""

import asyncio
import logging
import hashlib
import json
import pickle
import time
import threading
from typing import Dict, List, Any, Optional, Tuple, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
from collections import defaultdict, OrderedDict
import weakref

# Import dei moduli esistenti
try:
    from performance import Cache as BasicCache, global_cache
    from supabase_integration import SupabaseManager
except ImportError as e:
    logging.warning(f"Alcuni moduli non disponibili: {e}")

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CacheLevel(Enum):
    """Livelli di cache."""
    MEMORY = "memory"
    REDIS = "redis"
    DATABASE = "database"
    DISK = "disk"

class CacheStrategy(Enum):
    """Strategie di caching."""
    LRU = "lru"              # Least Recently Used
    LFU = "lfu"              # Least Frequently Used
    TTL = "ttl"              # Time To Live
    ADAPTIVE = "adaptive"     # Adattiva basata su pattern
    PREDICTIVE = "predictive" # Predittiva con ML

class InvalidationStrategy(Enum):
    """Strategie di invalidazione."""
    TIME_BASED = "time_based"
    EVENT_BASED = "event_based"
    DEPENDENCY_BASED = "dependency_based"
    SMART = "smart"

@dataclass
class CacheEntry:
    """Entry della cache con metadati avanzati."""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int
    ttl_seconds: Optional[int]
    size_bytes: int
    tags: List[str]
    dependencies: List[str]
    hit_rate: float = 0.0
    prediction_score: float = 0.0

    def __post_init__(self):
        if self.last_accessed is None:
            self.last_accessed = self.created_at

    @property
    def is_expired(self) -> bool:
        """Verifica se l'entry è scaduta."""
        if self.ttl_seconds is None:
            return False
        return (datetime.now() - self.created_at).total_seconds() > self.ttl_seconds

    @property
    def age_seconds(self) -> float:
        """Età dell'entry in secondi."""
        return (datetime.now() - self.created_at).total_seconds()

class IntelligentCacheSystem:
    """
    Sistema di caching intelligente multi-livello che:
    - Gestisce cache su più livelli (memoria, Redis, database, disco)
    - Implementa strategie di caching adattive e predittive
    - Ottimizza automaticamente basandosi sui pattern di accesso
    - Supporta invalidazione intelligente e gestione dipendenze
    - Monitora performance e fornisce analytics
    """

    def __init__(self, supabase_manager: Optional[Any] = None):
        self.supabase_manager = supabase_manager

        # Configurazione cache
        self.MEMORY_CACHE_SIZE = 1000
        self.DEFAULT_TTL_SECONDS = 3600  # 1 ora
        self.PREDICTION_THRESHOLD = 0.7
        self.AUTO_OPTIMIZATION_INTERVAL = 300  # 5 minuti

        # Cache multi-livello
        self.memory_cache = OrderedDict()  # L1 Cache
        self.redis_cache = None            # L2 Cache (se disponibile)
        self.disk_cache = {}               # L3 Cache

        # Metadati e analytics
        self.cache_metadata = {}           # Metadati per ogni entry
        self.access_patterns = defaultdict(list)
        self.hit_rates = defaultdict(float)
        self.performance_metrics = {
            "total_requests": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "evictions": 0,
            "invalidations": 0
        }

        # Configurazione strategie
        self.cache_strategy = CacheStrategy.ADAPTIVE
        self.invalidation_strategy = InvalidationStrategy.SMART

        # Pattern prediction
        self.access_predictor = AccessPredictor()
        self.dependency_tracker = DependencyTracker()

        # Threading
        self.lock = threading.RLock()
        self.optimization_thread = None
        self.is_optimizing = False

        # Inizializza Redis se disponibile
        self._init_redis_cache()

        # Avvia ottimizzazione automatica solo se non disabilitata
        import os
        if not os.environ.get('DISABLE_CACHE_OPTIMIZATION'):
            self._start_auto_optimization()
            logger.info("IntelligentCacheSystem inizializzato con ottimizzazione attiva")
        else:
            logger.info("IntelligentCacheSystem inizializzato ma ottimizzazione disabilitata (modalità minimal)")

    def get(self, key: str, default: Any = None) -> Any:
        """
        Recupera un valore dalla cache multi-livello.

        Args:
            key: Chiave del valore
            default: Valore di default se non trovato

        Returns:
            Valore dalla cache o default
        """
        with self.lock:
            self.performance_metrics["total_requests"] += 1

            # Registra accesso per pattern analysis
            self._record_access(key)

            # L1: Memory Cache
            if key in self.memory_cache:
                entry = self.cache_metadata.get(key)
                if entry and not entry.is_expired:
                    # Aggiorna statistiche accesso
                    entry.last_accessed = datetime.now()
                    entry.access_count += 1

                    # Sposta in cima (LRU)
                    value = self.memory_cache.pop(key)
                    self.memory_cache[key] = value

                    self.performance_metrics["cache_hits"] += 1
                    self._update_hit_rate(key, True)

                    logger.debug(f"Cache hit L1: {key}")
                    return value
                else:
                    # Entry scaduta, rimuovi
                    self._remove_from_memory(key)

            # L2: Redis Cache
            if self.redis_cache:
                redis_value = self._get_from_redis(key)
                if redis_value is not None:
                    # Promuovi a L1
                    self._set_in_memory(key, redis_value)

                    self.performance_metrics["cache_hits"] += 1
                    self._update_hit_rate(key, True)

                    logger.debug(f"Cache hit L2: {key}")
                    return redis_value

            # L3: Disk Cache
            disk_value = self._get_from_disk(key)
            if disk_value is not None:
                # Promuovi a L1 e L2
                self._set_in_memory(key, disk_value)
                if self.redis_cache:
                    self._set_in_redis(key, disk_value)

                self.performance_metrics["cache_hits"] += 1
                self._update_hit_rate(key, True)

                logger.debug(f"Cache hit L3: {key}")
                return disk_value

            # Cache miss
            self.performance_metrics["cache_misses"] += 1
            self._update_hit_rate(key, False)

            logger.debug(f"Cache miss: {key}")
            return default

    def set(self, key: str, value: Any, ttl: Optional[int] = None,
           tags: List[str] = None, dependencies: List[str] = None) -> None:
        """
        Imposta un valore nella cache multi-livello.

        Args:
            key: Chiave del valore
            value: Valore da memorizzare
            ttl: Tempo di vita in secondi
            tags: Tag per categorizzazione
            dependencies: Dipendenze per invalidazione
        """
        with self.lock:
            if ttl is None:
                ttl = self.DEFAULT_TTL_SECONDS

            # Calcola dimensione
            size_bytes = self._calculate_size(value)

            # Crea entry con metadati
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                access_count=1,
                ttl_seconds=ttl,
                size_bytes=size_bytes,
                tags=tags or [],
                dependencies=dependencies or []
            )

            # Determina livelli di cache basandosi sulla strategia
            cache_levels = self._determine_cache_levels(entry)

            # Imposta nei livelli appropriati
            if CacheLevel.MEMORY in cache_levels:
                self._set_in_memory(key, value, entry)

            if CacheLevel.REDIS in cache_levels and self.redis_cache:
                self._set_in_redis(key, value, ttl)

            if CacheLevel.DISK in cache_levels:
                self._set_in_disk(key, value, ttl)

            # Registra dipendenze
            if dependencies:
                self.dependency_tracker.add_dependencies(key, dependencies)

            logger.debug(f"Cache set: {key} in levels {[level.value for level in cache_levels]}")

    def invalidate(self, key: str) -> None:
        """Invalida una chiave dalla cache."""
        with self.lock:
            self._remove_from_memory(key)

            if self.redis_cache:
                self._remove_from_redis(key)

            self._remove_from_disk(key)

            # Invalida dipendenze
            dependent_keys = self.dependency_tracker.get_dependents(key)
            for dep_key in dependent_keys:
                self.invalidate(dep_key)

            self.performance_metrics["invalidations"] += 1
            logger.debug(f"Cache invalidated: {key}")

    def invalidate_by_tags(self, tags: List[str]) -> int:
        """
        Invalida tutte le entry con i tag specificati.

        Args:
            tags: Lista di tag

        Returns:
            Numero di entry invalidate
        """
        invalidated_count = 0

        with self.lock:
            keys_to_invalidate = []

            for key, entry in self.cache_metadata.items():
                if any(tag in entry.tags for tag in tags):
                    keys_to_invalidate.append(key)

            for key in keys_to_invalidate:
                self.invalidate(key)
                invalidated_count += 1

        logger.info(f"Invalidated {invalidated_count} entries by tags: {tags}")
        return invalidated_count

    def _init_redis_cache(self):
        """Inizializza la cache Redis se disponibile."""
        try:
            import redis
            self.redis_cache = redis.Redis(
                host='localhost',
                port=6379,
                db=0,
                decode_responses=False,
                socket_timeout=1,
                socket_connect_timeout=1
            )
            # Test connessione
            self.redis_cache.ping()
            logger.info("Redis cache inizializzata")
        except Exception as e:
            # Redis non disponibile - usa solo cache in memoria
            self.redis_cache = None

    def _set_in_memory(self, key: str, value: Any, entry: Optional[CacheEntry] = None):
        """Imposta valore nella cache in memoria."""
        # Gestione LRU: rimuovi elementi se cache piena
        if len(self.memory_cache) >= self.MEMORY_CACHE_SIZE:
            self._evict_from_memory()

        self.memory_cache[key] = value

        if entry:
            self.cache_metadata[key] = entry
        elif key not in self.cache_metadata:
            # Crea entry di base se non fornita
            self.cache_metadata[key] = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                access_count=1,
                ttl_seconds=self.DEFAULT_TTL_SECONDS,
                size_bytes=self._calculate_size(value),
                tags=[],
                dependencies=[]
            )

    def _remove_from_memory(self, key: str):
        """Rimuove valore dalla cache in memoria."""
        if key in self.memory_cache:
            del self.memory_cache[key]
        if key in self.cache_metadata:
            del self.cache_metadata[key]

    def _evict_from_memory(self):
        """Rimuove elementi dalla cache in memoria secondo la strategia."""
        if not self.memory_cache:
            return

        if self.cache_strategy == CacheStrategy.LRU:
            # Rimuovi il meno recentemente usato
            key_to_remove = next(iter(self.memory_cache))
        elif self.cache_strategy == CacheStrategy.LFU:
            # Rimuovi il meno frequentemente usato
            key_to_remove = min(
                self.cache_metadata.keys(),
                key=lambda k: self.cache_metadata[k].access_count
            )
        elif self.cache_strategy == CacheStrategy.TTL:
            # Rimuovi il più vecchio
            key_to_remove = min(
                self.cache_metadata.keys(),
                key=lambda k: self.cache_metadata[k].created_at
            )
        else:  # ADAPTIVE o PREDICTIVE
            # Usa score combinato
            key_to_remove = min(
                self.cache_metadata.keys(),
                key=lambda k: self._calculate_eviction_score(k)
            )

        self._remove_from_memory(key_to_remove)
        self.performance_metrics["evictions"] += 1

    def _calculate_eviction_score(self, key: str) -> float:
        """Calcola score per eviction (più basso = più probabile rimozione)."""
        entry = self.cache_metadata.get(key)
        if not entry:
            return 0.0

        # Fattori per il calcolo
        age_factor = entry.age_seconds / 3600  # Normalizza per ore
        access_factor = 1.0 / max(entry.access_count, 1)
        hit_rate_factor = 1.0 - self.hit_rates.get(key, 0.0)
        prediction_factor = 1.0 - entry.prediction_score

        # Score combinato (più basso = rimuovi prima)
        score = (age_factor * 0.3 +
                access_factor * 0.3 +
                hit_rate_factor * 0.2 +
                prediction_factor * 0.2)

        return score

    def _get_from_redis(self, key: str) -> Any:
        """Recupera valore da Redis."""
        if not self.redis_cache:
            return None

        try:
            data = self.redis_cache.get(f"cache:{key}")
            if data:
                return pickle.loads(data)
        except Exception as e:
            logger.warning(f"Errore lettura Redis per {key}: {e}")

        return None

    def _set_in_redis(self, key: str, value: Any, ttl: Optional[int] = None):
        """Imposta valore in Redis."""
        if not self.redis_cache:
            return

        try:
            data = pickle.dumps(value)
            redis_key = f"cache:{key}"

            if ttl:
                self.redis_cache.setex(redis_key, ttl, data)
            else:
                self.redis_cache.set(redis_key, data)
        except Exception as e:
            logger.warning(f"Errore scrittura Redis per {key}: {e}")

    def _remove_from_redis(self, key: str):
        """Rimuove valore da Redis."""
        if not self.redis_cache:
            return

        try:
            self.redis_cache.delete(f"cache:{key}")
        except Exception as e:
            logger.warning(f"Errore rimozione Redis per {key}: {e}")

    def _get_from_disk(self, key: str) -> Any:
        """Recupera valore dalla cache su disco."""
        # Implementazione semplificata - in produzione usare un sistema più robusto
        return self.disk_cache.get(key)

    def _set_in_disk(self, key: str, value: Any, ttl: Optional[int] = None):
        """Imposta valore nella cache su disco."""
        # Implementazione semplificata
        self.disk_cache[key] = {
            'value': value,
            'expires_at': datetime.now() + timedelta(seconds=ttl) if ttl else None
        }

    def _remove_from_disk(self, key: str):
        """Rimuove valore dalla cache su disco."""
        if key in self.disk_cache:
            del self.disk_cache[key]

    def _determine_cache_levels(self, entry: CacheEntry) -> List[CacheLevel]:
        """Determina in quali livelli di cache memorizzare l'entry."""
        levels = [CacheLevel.MEMORY]  # Sempre in memoria

        # Logica per determinare livelli aggiuntivi
        if entry.size_bytes > 1024 * 1024:  # > 1MB
            levels.append(CacheLevel.DISK)

        if entry.ttl_seconds and entry.ttl_seconds > 3600:  # > 1 ora
            if self.redis_cache:
                levels.append(CacheLevel.REDIS)

        # Predizione: se alta probabilità di riaccesso, usa più livelli
        if entry.prediction_score > self.PREDICTION_THRESHOLD:
            if CacheLevel.REDIS not in levels and self.redis_cache:
                levels.append(CacheLevel.REDIS)

        return levels

    def _calculate_size(self, value: Any) -> int:
        """Calcola dimensione approssimativa di un valore."""
        try:
            return len(pickle.dumps(value))
        except:
            # Fallback per oggetti non serializzabili
            return len(str(value).encode('utf-8'))

    def _record_access(self, key: str):
        """Registra accesso per pattern analysis."""
        now = datetime.now()
        self.access_patterns[key].append(now)

        # Mantieni solo ultimi 100 accessi per chiave
        if len(self.access_patterns[key]) > 100:
            self.access_patterns[key] = self.access_patterns[key][-100:]

        # Aggiorna predizione
        self.access_predictor.update_pattern(key, now)

    def _update_hit_rate(self, key: str, hit: bool):
        """Aggiorna hit rate per una chiave."""
        current_rate = self.hit_rates.get(key, 0.0)
        # Media mobile con peso 0.1 per nuovi valori
        new_rate = current_rate * 0.9 + (1.0 if hit else 0.0) * 0.1
        self.hit_rates[key] = new_rate

    def _start_auto_optimization(self):
        """Avvia thread per ottimizzazione automatica."""
        if self.optimization_thread and self.optimization_thread.is_alive():
            return

        self.is_optimizing = True
        self.optimization_thread = threading.Thread(
            target=self._optimization_loop,
            daemon=True
        )
        self.optimization_thread.start()
        logger.info("Auto-optimization thread avviato")

    def _optimization_loop(self):
        """Loop di ottimizzazione automatica."""
        while self.is_optimizing:
            try:
                time.sleep(self.AUTO_OPTIMIZATION_INTERVAL)
                self._optimize_cache()
            except Exception as e:
                logger.error(f"Errore nell'ottimizzazione automatica: {e}")

    def _optimize_cache(self):
        """Ottimizza la cache basandosi sui pattern di accesso."""
        with self.lock:
            # Pulisci entry scadute
            self._cleanup_expired_entries()

            # Ottimizza strategia di caching
            self._optimize_strategy()

            # Aggiorna predizioni
            self._update_predictions()

            logger.debug("Ottimizzazione cache completata")

    def _cleanup_expired_entries(self):
        """Rimuove entry scadute da tutti i livelli."""
        expired_keys = []

        for key, entry in self.cache_metadata.items():
            if entry.is_expired:
                expired_keys.append(key)

        for key in expired_keys:
            self.invalidate(key)

        if expired_keys:
            logger.debug(f"Rimosse {len(expired_keys)} entry scadute")

    def _optimize_strategy(self):
        """Ottimizza la strategia di caching basandosi sulle performance."""
        total_requests = self.performance_metrics["total_requests"]
        if total_requests < 100:  # Non abbastanza dati
            return

        hit_rate = self.performance_metrics["cache_hits"] / total_requests

        # Cambia strategia se hit rate basso
        if hit_rate < 0.5:
            if self.cache_strategy == CacheStrategy.LRU:
                self.cache_strategy = CacheStrategy.LFU
            elif self.cache_strategy == CacheStrategy.LFU:
                self.cache_strategy = CacheStrategy.ADAPTIVE
            else:
                self.cache_strategy = CacheStrategy.PREDICTIVE

            logger.info(f"Strategia cache cambiata a: {self.cache_strategy.value}")

    def _update_predictions(self):
        """Aggiorna predizioni per tutte le entry."""
        for key in self.cache_metadata.keys():
            prediction_score = self.access_predictor.predict_access_probability(key)
            self.cache_metadata[key].prediction_score = prediction_score

    def get_cache_statistics(self) -> Dict[str, Any]:
        """Restituisce statistiche dettagliate della cache."""
        total_requests = self.performance_metrics["total_requests"]
        hit_rate = (self.performance_metrics["cache_hits"] / total_requests * 100) if total_requests > 0 else 0.0

        return {
            "performance": {
                "total_requests": total_requests,
                "cache_hits": self.performance_metrics["cache_hits"],
                "cache_misses": self.performance_metrics["cache_misses"],
                "hit_rate_percentage": hit_rate,
                "evictions": self.performance_metrics["evictions"],
                "invalidations": self.performance_metrics["invalidations"]
            },
            "cache_levels": {
                "memory_entries": len(self.memory_cache),
                "memory_max_size": self.MEMORY_CACHE_SIZE,
                "redis_available": self.redis_cache is not None,
                "disk_entries": len(self.disk_cache)
            },
            "configuration": {
                "cache_strategy": self.cache_strategy.value,
                "invalidation_strategy": self.invalidation_strategy.value,
                "default_ttl": self.DEFAULT_TTL_SECONDS,
                "prediction_threshold": self.PREDICTION_THRESHOLD
            },
            "top_accessed_keys": self._get_top_accessed_keys(10)
        }

    def _get_top_accessed_keys(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Restituisce le chiavi più accessate."""
        sorted_keys = sorted(
            self.cache_metadata.items(),
            key=lambda x: x[1].access_count,
            reverse=True
        )

        return [
            {
                "key": key,
                "access_count": entry.access_count,
                "hit_rate": self.hit_rates.get(key, 0.0),
                "age_seconds": entry.age_seconds,
                "prediction_score": entry.prediction_score
            }
            for key, entry in sorted_keys[:limit]
        ]

class AccessPredictor:
    """Predittore di accessi basato su pattern temporali."""

    def __init__(self):
        self.access_patterns = defaultdict(list)
        self.pattern_weights = {
            'frequency': 0.4,
            'recency': 0.3,
            'periodicity': 0.2,
            'trend': 0.1
        }

    def update_pattern(self, key: str, access_time: datetime):
        """Aggiorna pattern di accesso per una chiave."""
        self.access_patterns[key].append(access_time)

        # Mantieni solo ultimi 50 accessi
        if len(self.access_patterns[key]) > 50:
            self.access_patterns[key] = self.access_patterns[key][-50:]

    def predict_access_probability(self, key: str) -> float:
        """Predice la probabilità di accesso futuro per una chiave."""
        accesses = self.access_patterns.get(key, [])
        if len(accesses) < 2:
            return 0.5  # Probabilità neutra per chiavi con pochi dati

        now = datetime.now()

        # Calcola fattori di predizione
        frequency_score = self._calculate_frequency_score(accesses)
        recency_score = self._calculate_recency_score(accesses, now)
        periodicity_score = self._calculate_periodicity_score(accesses)
        trend_score = self._calculate_trend_score(accesses, now)

        # Combina i punteggi
        prediction = (
            frequency_score * self.pattern_weights['frequency'] +
            recency_score * self.pattern_weights['recency'] +
            periodicity_score * self.pattern_weights['periodicity'] +
            trend_score * self.pattern_weights['trend']
        )

        return max(0.0, min(1.0, prediction))

    def _calculate_frequency_score(self, accesses: List[datetime]) -> float:
        """Calcola score basato sulla frequenza di accesso."""
        if len(accesses) < 2:
            return 0.5

        # Normalizza per numero di accessi (max 1.0 per 20+ accessi)
        return min(1.0, len(accesses) / 20.0)

    def _calculate_recency_score(self, accesses: List[datetime], now: datetime) -> float:
        """Calcola score basato sulla recenza dell'ultimo accesso."""
        if not accesses:
            return 0.0

        last_access = max(accesses)
        hours_since_last = (now - last_access).total_seconds() / 3600

        # Score decresce esponenzialmente con il tempo
        return max(0.0, 1.0 - (hours_since_last / 24.0))

    def _calculate_periodicity_score(self, accesses: List[datetime]) -> float:
        """Calcola score basato sulla periodicità degli accessi."""
        if len(accesses) < 3:
            return 0.5

        # Calcola intervalli tra accessi
        intervals = []
        for i in range(1, len(accesses)):
            interval = (accesses[i] - accesses[i-1]).total_seconds()
            intervals.append(interval)

        if not intervals:
            return 0.5

        # Calcola varianza degli intervalli (bassa varianza = alta periodicità)
        mean_interval = sum(intervals) / len(intervals)
        variance = sum((x - mean_interval) ** 2 for x in intervals) / len(intervals)

        # Normalizza varianza (score alto per bassa varianza)
        normalized_variance = min(1.0, variance / (mean_interval ** 2))
        return 1.0 - normalized_variance

    def _calculate_trend_score(self, accesses: List[datetime], now: datetime) -> float:
        """Calcola score basato sul trend di accesso."""
        if len(accesses) < 4:
            return 0.5

        # Divide accessi in due metà e confronta frequenze
        mid_point = len(accesses) // 2
        first_half = accesses[:mid_point]
        second_half = accesses[mid_point:]

        # Calcola frequenza per ora in ogni metà
        first_duration = (first_half[-1] - first_half[0]).total_seconds() / 3600
        second_duration = (second_half[-1] - second_half[0]).total_seconds() / 3600

        if first_duration <= 0 or second_duration <= 0:
            return 0.5

        first_freq = len(first_half) / first_duration
        second_freq = len(second_half) / second_duration

        # Score alto se frequenza sta aumentando
        if first_freq == 0:
            return 1.0 if second_freq > 0 else 0.5

        trend_ratio = second_freq / first_freq
        return min(1.0, max(0.0, trend_ratio))

class DependencyTracker:
    """Tracker per gestire dipendenze tra chiavi di cache."""

    def __init__(self):
        self.dependencies = defaultdict(set)  # key -> set of dependent keys
        self.dependents = defaultdict(set)    # key -> set of keys that depend on it

    def add_dependencies(self, key: str, dependencies: List[str]):
        """Aggiunge dipendenze per una chiave."""
        for dep in dependencies:
            self.dependencies[key].add(dep)
            self.dependents[dep].add(key)

    def remove_dependencies(self, key: str):
        """Rimuove tutte le dipendenze di una chiave."""
        # Rimuovi dalle dipendenze
        for dep in self.dependencies[key]:
            self.dependents[dep].discard(key)

        # Rimuovi dai dipendenti
        for dependent in self.dependents[key]:
            self.dependencies[dependent].discard(key)

        # Pulisci
        del self.dependencies[key]
        del self.dependents[key]

    def get_dependents(self, key: str) -> List[str]:
        """Restituisce tutte le chiavi che dipendono da questa chiave."""
        return list(self.dependents.get(key, set()))

    def get_dependencies(self, key: str) -> List[str]:
        """Restituisce tutte le dipendenze di una chiave."""
        return list(self.dependencies.get(key, set()))

# Istanza globale del sistema di cache intelligente
intelligent_cache = IntelligentCacheSystem()

# Decoratore per cache intelligente
def smart_cache(ttl: Optional[int] = None, tags: List[str] = None,
               dependencies: List[str] = None, key_func: Optional[Callable] = None):
    """
    Decoratore per cache intelligente.

    Args:
        ttl: Tempo di vita in secondi
        tags: Tag per categorizzazione
        dependencies: Dipendenze per invalidazione
        key_func: Funzione per generare chiave personalizzata
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Genera chiave
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__module__}.{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"

            # Prova a ottenere dalla cache
            result = intelligent_cache.get(cache_key)
            if result is not None:
                return result

            # Calcola risultato
            result = func(*args, **kwargs)

            # Memorizza in cache
            intelligent_cache.set(
                cache_key,
                result,
                ttl=ttl,
                tags=tags or [],
                dependencies=dependencies or []
            )

            return result
        return wrapper
    return decorator
