/**
 * Employee Costs CSS - App Roberto
 * Stili per la gestione costi dipendenti
 * Versione: 1.0.0
 */

/* ===== CARD COSTI ===== */
.cost-card {
    transition: transform 0.2s;
}

.cost-card:hover {
    transform: translateY(-2px);
}

/* ===== BADGE IVA ===== */
.vat-badge {
    font-size: 0.8em;
}

/* ===== SEZIONI FORM ===== */
.employee-form {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.settings-section {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.cost-analysis {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
}

.export-section {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #333;
}

/* ===== FORM CONTROLS ===== */
.employee-form .form-control,
.employee-form .form-select {
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #333;
}

.employee-form .form-control:focus,
.employee-form .form-select:focus {
    background-color: white;
    border-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

/* ===== TABELLE ===== */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* ===== ALERT PERSONALIZZATI ===== */
.alert-custom {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* ===== BOTTONI ===== */
.btn-gradient {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* ===== UTILITY CLASSES ===== */
.wizard-hidden {
    display: none !important;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .employee-form,
    .settings-section,
    .cost-analysis,
    .export-section {
        margin-bottom: 1rem;
    }

    .cost-card {
        margin-bottom: 1rem;
    }
}

/* ===== TEMA SCURO ===== */
[data-theme="dark"] .cost-card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .employee-form .form-control,
[data-theme="dark"] .employee-form .form-select {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-primary);
}

[data-theme="dark"] .table-hover tbody tr:hover {
    background-color: var(--table-hover);
}
