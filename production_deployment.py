#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sistema di Deployment per Produzione - Fase 7.
Configurazione e deployment del sistema di riconoscimento intelligente.
"""

import sys
import os
import json
import subprocess
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductionDeployment:
    """Sistema di deployment per produzione."""

    def __init__(self, config_file: str = "production_config.json"):
        self.config_file = config_file
        self.config = self._load_config()
        self.deployment_log = []

        logger.info("Sistema di Deployment inizializzato")

    def _load_config(self) -> Dict[str, Any]:
        """Carica configurazione deployment."""
        default_config = {
            "environment": "production",
            "app_name": "sistema-riconoscimento-intelligente",
            "version": "1.0.0",
            "python_version": "3.9+",
            "database": {
                "type": "supabase",
                "backup_enabled": True,
                "connection_pool_size": 20
            },
            "server": {
                "host": "0.0.0.0",
                "port": 5001,
                "workers": 4,
                "timeout": 300
            },
            "security": {
                "https_enabled": True,
                "ssl_cert_path": "/etc/ssl/certs/app.crt",
                "ssl_key_path": "/etc/ssl/private/app.key",
                "secret_key_rotation": True
            },
            "monitoring": {
                "health_check_enabled": True,
                "metrics_enabled": True,
                "logging_level": "INFO",
                "log_retention_days": 30
            },
            "performance": {
                "caching_enabled": True,
                "compression_enabled": True,
                "static_files_cdn": False
            },
            "agents": {
                "max_concurrent_tasks": 10,
                "task_timeout": 600,
                "auto_restart": True
            }
        }

        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                logger.warning(f"Errore caricamento config: {str(e)}, uso default")

        return default_config

    def run_deployment(self) -> Dict[str, Any]:
        """Esegue deployment completo."""
        logger.info("🚀 Avvio deployment produzione")

        deployment_steps = [
            ("Verifica Prerequisiti", self._check_prerequisites),
            ("Preparazione Ambiente", self._prepare_environment),
            ("Configurazione Database", self._configure_database),
            ("Setup Sicurezza", self._setup_security),
            ("Configurazione Server", self._configure_server),
            ("Setup Monitoring", self._setup_monitoring),
            ("Deployment Agenti", self._deploy_agents),
            ("Test Produzione", self._run_production_tests),
            ("Backup Sistema", self._create_system_backup),
            ("Avvio Servizi", self._start_services)
        ]

        results = {}

        for step_name, step_function in deployment_steps:
            logger.info(f"📋 Esecuzione: {step_name}")

            try:
                step_result = step_function()
                results[step_name] = {
                    'success': True,
                    'result': step_result,
                    'timestamp': datetime.now().isoformat()
                }

                self.deployment_log.append({
                    'step': step_name,
                    'status': 'success',
                    'timestamp': datetime.now().isoformat(),
                    'details': step_result
                })

                logger.info(f"✅ {step_name} completato")

            except Exception as e:
                error_msg = str(e)
                logger.error(f"❌ {step_name} fallito: {error_msg}")

                results[step_name] = {
                    'success': False,
                    'error': error_msg,
                    'timestamp': datetime.now().isoformat()
                }

                self.deployment_log.append({
                    'step': step_name,
                    'status': 'failed',
                    'timestamp': datetime.now().isoformat(),
                    'error': error_msg
                })

                # Interrompi deployment se step critico fallisce
                if step_name in ["Verifica Prerequisiti", "Configurazione Database"]:
                    logger.error("🛑 Step critico fallito, interrompo deployment")
                    break

        # Genera report finale
        self._generate_deployment_report(results)

        return results

    def _check_prerequisites(self) -> Dict[str, Any]:
        """Verifica prerequisiti sistema."""
        checks = {}

        # Verifica Python
        try:
            python_version = sys.version_info
            checks['python_version'] = {
                'current': f"{python_version.major}.{python_version.minor}.{python_version.micro}",
                'required': self.config['python_version'],
                'ok': python_version >= (3, 9)
            }
        except Exception as e:
            checks['python_version'] = {'error': str(e), 'ok': False}

        # Verifica dipendenze critiche
        critical_packages = [
            'flask', 'pandas', 'supabase', 'openai', 'plotly', 'jinja2'
        ]

        checks['packages'] = {}
        for package in critical_packages:
            try:
                __import__(package)
                checks['packages'][package] = {'installed': True, 'ok': True}
            except ImportError:
                checks['packages'][package] = {'installed': False, 'ok': False}

        # Verifica spazio disco
        try:
            disk_usage = shutil.disk_usage('.')
            free_gb = disk_usage.free / (1024**3)
            checks['disk_space'] = {
                'free_gb': round(free_gb, 2),
                'required_gb': 5.0,
                'ok': free_gb >= 5.0
            }
        except Exception as e:
            checks['disk_space'] = {'error': str(e), 'ok': False}

        # Verifica variabili ambiente
        required_env_vars = ['SUPABASE_URL', 'SUPABASE_KEY']
        checks['environment_variables'] = {}

        for var in required_env_vars:
            value = os.getenv(var)
            checks['environment_variables'][var] = {
                'set': bool(value),
                'ok': bool(value)
            }

        return checks

    def _prepare_environment(self) -> Dict[str, Any]:
        """Prepara ambiente produzione."""
        preparations = {}

        # Crea directory necessarie
        directories = [
            'logs',
            'backups',
            'uploads',
            'exports',
            'temp',
            'static/cache',
            'intelligent_output'
        ]

        preparations['directories'] = {}
        for directory in directories:
            try:
                Path(directory).mkdir(parents=True, exist_ok=True)
                preparations['directories'][directory] = {'created': True, 'ok': True}
            except Exception as e:
                preparations['directories'][directory] = {'error': str(e), 'ok': False}

        # Configura logging produzione
        try:
            log_config = {
                'version': 1,
                'disable_existing_loggers': False,
                'formatters': {
                    'standard': {
                        'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
                    },
                },
                'handlers': {
                    'default': {
                        'level': self.config['monitoring']['logging_level'],
                        'formatter': 'standard',
                        'class': 'logging.StreamHandler',
                    },
                    'file': {
                        'level': 'INFO',
                        'formatter': 'standard',
                        'class': 'logging.FileHandler',
                        'filename': 'logs/app.log',
                        'mode': 'a',
                    },
                }
            }

            with open('logging_config.json', 'w') as f:
                json.dump(log_config, f, indent=2)

            preparations['logging'] = {'configured': True, 'ok': True}

        except Exception as e:
            preparations['logging'] = {'error': str(e), 'ok': False}

        return preparations

    def _configure_database(self) -> Dict[str, Any]:
        """Configura database per produzione."""
        db_config = {}

        # Verifica connessione Supabase
        try:
            from supabase_integration import SupabaseManager
            supabase_manager = SupabaseManager()

            db_config['connection'] = {
                'connected': supabase_manager.is_connected,
                'url_configured': bool(supabase_manager.url),
                'key_configured': bool(supabase_manager.key),
                'ok': supabase_manager.is_connected
            }

        except Exception as e:
            db_config['connection'] = {'error': str(e), 'ok': False}

        # Configura connection pooling
        try:
            pool_config = {
                'pool_size': self.config['database']['connection_pool_size'],
                'max_overflow': 10,
                'pool_timeout': 30,
                'pool_recycle': 3600
            }

            db_config['connection_pool'] = {
                'configured': True,
                'config': pool_config,
                'ok': True
            }

        except Exception as e:
            db_config['connection_pool'] = {'error': str(e), 'ok': False}

        return db_config

    def _setup_security(self) -> Dict[str, Any]:
        """Configura sicurezza produzione."""
        security_config = {}

        # Genera secret key sicura
        try:
            import secrets
            secret_key = secrets.token_urlsafe(32)

            # Salva in file sicuro
            with open('.env.production', 'w') as f:
                f.write(f"SECRET_KEY={secret_key}\n")
                f.write(f"FLASK_ENV=production\n")
                f.write(f"FLASK_DEBUG=False\n")

            # Imposta permessi file
            os.chmod('.env.production', 0o600)

            security_config['secret_key'] = {'generated': True, 'ok': True}

        except Exception as e:
            security_config['secret_key'] = {'error': str(e), 'ok': False}

        # Configura HTTPS (se abilitato)
        if self.config['security']['https_enabled']:
            try:
                ssl_config = {
                    'cert_path': self.config['security']['ssl_cert_path'],
                    'key_path': self.config['security']['ssl_key_path'],
                    'cert_exists': os.path.exists(self.config['security']['ssl_cert_path']),
                    'key_exists': os.path.exists(self.config['security']['ssl_key_path'])
                }

                security_config['https'] = {
                    'configured': True,
                    'config': ssl_config,
                    'ok': ssl_config['cert_exists'] and ssl_config['key_exists']
                }

            except Exception as e:
                security_config['https'] = {'error': str(e), 'ok': False}

        return security_config

    def _configure_server(self) -> Dict[str, Any]:
        """Configura server produzione."""
        server_config = {}

        # Crea configurazione Gunicorn
        try:
            gunicorn_config = f"""
bind = "{self.config['server']['host']}:{self.config['server']['port']}"
workers = {self.config['server']['workers']}
worker_class = "sync"
worker_connections = 1000
timeout = {self.config['server']['timeout']}
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
accesslog = "logs/access.log"
errorlog = "logs/error.log"
loglevel = "info"
"""

            with open('gunicorn.conf.py', 'w') as f:
                f.write(gunicorn_config)

            server_config['gunicorn'] = {'configured': True, 'ok': True}

        except Exception as e:
            server_config['gunicorn'] = {'error': str(e), 'ok': False}

        # Crea script di avvio
        try:
            startup_script = f"""#!/bin/bash
# Script di avvio produzione
export FLASK_ENV=production
export FLASK_DEBUG=False

# Carica variabili ambiente
if [ -f .env.production ]; then
    source .env.production
fi

# Avvia server
gunicorn --config gunicorn.conf.py app:app
"""

            with open('start_production.sh', 'w') as f:
                f.write(startup_script)

            os.chmod('start_production.sh', 0o755)

            server_config['startup_script'] = {'created': True, 'ok': True}

        except Exception as e:
            server_config['startup_script'] = {'error': str(e), 'ok': False}

        return server_config

    def _setup_monitoring(self) -> Dict[str, Any]:
        """Configura monitoring produzione."""
        monitoring_config = {}

        # Configura health check endpoint
        try:
            health_check_config = {
                'endpoint': '/health',
                'checks': [
                    'database_connection',
                    'agents_status',
                    'disk_space',
                    'memory_usage'
                ],
                'timeout': 30
            }

            monitoring_config['health_check'] = {
                'configured': True,
                'config': health_check_config,
                'ok': True
            }

        except Exception as e:
            monitoring_config['health_check'] = {'error': str(e), 'ok': False}

        # Configura metriche
        if self.config['monitoring']['metrics_enabled']:
            try:
                metrics_config = {
                    'endpoint': '/metrics',
                    'format': 'prometheus',
                    'metrics': [
                        'request_count',
                        'request_duration',
                        'agent_tasks_total',
                        'database_connections'
                    ]
                }

                monitoring_config['metrics'] = {
                    'configured': True,
                    'config': metrics_config,
                    'ok': True
                }

            except Exception as e:
                monitoring_config['metrics'] = {'error': str(e), 'ok': False}

        return monitoring_config

    def _deploy_agents(self) -> Dict[str, Any]:
        """Deploya agenti per produzione."""
        agents_config = {}

        try:
            from advanced_agent_framework import get_advanced_orchestrator
            orchestrator = get_advanced_orchestrator()

            # Verifica agenti (senza await per ora)
            agents_config['agents_count'] = len(orchestrator.agents)
            agents_config['orchestrator_ready'] = True
            agents_config['ok'] = True

        except Exception as e:
            agents_config['error'] = str(e)
            agents_config['ok'] = False

        return agents_config

    def _run_production_tests(self) -> Dict[str, Any]:
        """Esegue test per produzione."""
        test_results = {}

        # Test connessioni
        try:
            # Test database
            from supabase_integration import SupabaseManager
            supabase_manager = SupabaseManager()
            test_results['database'] = {'connected': supabase_manager.is_connected}

            # Test agenti
            from advanced_agent_framework import get_advanced_orchestrator
            orchestrator = get_advanced_orchestrator()
            test_results['agents'] = {'count': len(orchestrator.agents)}

            test_results['ok'] = True

        except Exception as e:
            test_results['error'] = str(e)
            test_results['ok'] = False

        return test_results

    def _create_system_backup(self) -> Dict[str, Any]:
        """Crea backup sistema."""
        backup_config = {}

        if self.config['database']['backup_enabled']:
            try:
                backup_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_dir = f"backups/backup_{backup_timestamp}"

                Path(backup_dir).mkdir(parents=True, exist_ok=True)

                # Backup configurazioni
                config_files = [
                    'production_config.json',
                    '.env.production',
                    'gunicorn.conf.py',
                    'logging_config.json'
                ]

                for config_file in config_files:
                    if os.path.exists(config_file):
                        shutil.copy2(config_file, backup_dir)

                backup_config['backup_created'] = True
                backup_config['backup_path'] = backup_dir
                backup_config['ok'] = True

            except Exception as e:
                backup_config['error'] = str(e)
                backup_config['ok'] = False
        else:
            backup_config['skipped'] = True
            backup_config['ok'] = True

        return backup_config

    def _start_services(self) -> Dict[str, Any]:
        """Avvia servizi produzione."""
        services_config = {}

        try:
            # Verifica che tutto sia pronto
            services_config['ready_for_start'] = True
            services_config['startup_command'] = './start_production.sh'
            services_config['ok'] = True

            logger.info("🚀 Sistema pronto per avvio produzione")
            logger.info("   Comando avvio: ./start_production.sh")

        except Exception as e:
            services_config['error'] = str(e)
            services_config['ok'] = False

        return services_config

    def _generate_deployment_report(self, results: Dict[str, Any]):
        """Genera report deployment."""
        report_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"deployment_report_{report_timestamp}.json"

        report = {
            'deployment_info': {
                'timestamp': datetime.now().isoformat(),
                'environment': self.config['environment'],
                'app_name': self.config['app_name'],
                'version': self.config['version']
            },
            'configuration': self.config,
            'deployment_steps': results,
            'deployment_log': self.deployment_log,
            'summary': {
                'total_steps': len(results),
                'successful_steps': len([r for r in results.values() if r.get('success', False)]),
                'failed_steps': len([r for r in results.values() if not r.get('success', True)]),
                'deployment_success': all(r.get('success', False) for r in results.values())
            }
        }

        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, default=str)

            logger.info(f"📄 Report deployment salvato: {report_file}")

        except Exception as e:
            logger.error(f"Errore salvataggio report: {str(e)}")

async def run_production_deployment():
    """Esegue deployment produzione."""
    deployment = ProductionDeployment()
    results = deployment.run_deployment()
    return results

if __name__ == "__main__":
    import asyncio
    asyncio.run(run_production_deployment())
