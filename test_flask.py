#!/usr/bin/env python
# -*- coding: utf-8 -*-

# Test di importazione Flask
try:
    import flask
    print("Flask importato con successo!")
    print(f"Versione Flask: {flask.__version__}")
except ImportError as e:
    print(f"Errore nell'importazione di Flask: {e}")

# Test di importazione flask_session
try:
    from flask_session import Session
    print("Flask-Session importato con successo!")
except ImportError as e:
    print(f"Errore nell'importazione di Flask-Session: {e}")

# Test di importazione pandas
try:
    import pandas as pd
    print("Pandas importato con successo!")
    print(f"Versione Pandas: {pd.__version__}")
except ImportError as e:
    print(f"Errore nell'importazione di Pandas: {e}")

# Test di importazione numpy
try:
    import numpy as np
    print("NumPy importato con successo!")
    print(f"Versione NumPy: {np.__version__}")
except ImportError as e:
    print(f"Errore nell'importazione di NumPy: {e}")

# Test di importazione plotly
try:
    import plotly
    print("Plotly importato con successo!")
    print(f"Versione Plotly: {plotly.__version__}")
except ImportError as e:
    print(f"Errore nell'importazione di Plotly: {e}")

# Test di importazione werkzeug
try:
    import werkzeug
    print("Werkzeug importato con successo!")
    print(f"Versione Werkzeug: {werkzeug.__version__}")
except ImportError as e:
    print(f"Errore nell'importazione di Werkzeug: {e}")

print("\nTest completato!")
