#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Debug del server MCP per capire perché elabora 0 eventi.
"""

import requests
import json
import os

def debug_mcp_processing():
    """
    Debug dettagliato dell'elaborazione MCP.
    """
    print("🔍 DEBUG SERVER MCP - ELABORAZIONE CALENDARIO")
    print("=" * 60)
    
    # File di test
    file_path = "uploads/tmp-174791856851810_1747992045_94627023.csv"
    
    if not os.path.exists(file_path):
        print(f"❌ File non trovato: {file_path}")
        return
    
    print(f"📁 File di test: {file_path}")
    
    try:
        # 1. Test upload
        print("\n🔄 STEP 1: Upload file al server MCP")
        file_id = "debug_test_1747992200"
        
        with open(file_path, 'rb') as f:
            files = {'file': f}
            data = {
                'file_id': file_id,
                'file_type': 'calendario'
            }
            
            response = requests.post(
                "http://localhost:8000/upload-file/",
                files=files,
                data=data,
                timeout=30
            )
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            upload_result = response.json()
            print(f"✅ Upload riuscito: {json.dumps(upload_result, indent=2)}")
        else:
            print(f"❌ Upload fallito: {response.text}")
            return
        
        # 2. Test process
        print("\n🔄 STEP 2: Elaborazione file")
        process_data = {
            'file_id': file_id,
            'file_type': 'calendario',
            'options': {}
        }
        
        response = requests.post(
            "http://localhost:8000/process-file/",
            json=process_data,
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            process_result = response.json()
            print(f"✅ Elaborazione riuscita:")
            print(json.dumps(process_result, indent=2))
        else:
            print(f"❌ Elaborazione fallita: {response.text}")
            return
        
        # 3. Test summary
        print("\n🔄 STEP 3: Riepilogo file")
        response = requests.get(
            f"http://localhost:8000/file-summary/{file_id}",
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            summary_result = response.json()
            print(f"✅ Riepilogo ottenuto:")
            print(json.dumps(summary_result, indent=2))
        else:
            print(f"❌ Riepilogo fallito: {response.text}")
        
        # 4. Analisi dettagliata
        print("\n📊 ANALISI DETTAGLIATA:")
        if 'statistics' in process_result:
            stats = process_result['statistics']
            print(f"- Righe totali: {stats.get('total_rows', 'N/A')}")
            print(f"- Colonne totali: {stats.get('total_columns', 'N/A')}")
            print(f"- Eventi totali: {stats.get('total_events', 'N/A')}")
            print(f"- Partecipanti unici: {stats.get('unique_attendees', 'N/A')}")
            
            if 'duration' in stats:
                duration = stats['duration']
                print(f"- Durata totale: {duration.get('total_hours', 'N/A')} ore")
                print(f"- Durata media: {duration.get('average_hours', 'N/A')} ore")
        
        # 5. Verifica mappatura colonne
        print("\n🗂️ VERIFICA MAPPATURA COLONNE:")
        if 'applied_mapping' in process_result:
            mapping = process_result['applied_mapping']
            print("Mappatura applicata:")
            for orig, std in mapping.items():
                print(f"  {orig} → {std}")
        
        if 'missing_columns' in process_result:
            missing = process_result['missing_columns']
            if missing:
                print("Colonne mancanti:")
                for col in missing:
                    print(f"  - {col}")
            else:
                print("✅ Nessuna colonna mancante")
        
    except Exception as e:
        print(f"❌ Errore durante il debug: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_mcp_processing()
