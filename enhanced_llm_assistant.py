#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced LLM Assistant per Sistema di Riconoscimento Intelligente.
Integrazione potenziata con OpenRouter per analisi avanzate e generazione report.
"""

import os
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from dataclasses import dataclass
import openai
from openai import AsyncOpenAI

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class LLMAnalysisRequest:
    """Richiesta di analisi LLM."""
    analysis_type: str
    data: Dict[str, Any]
    context: Optional[Dict[str, Any]] = None
    model_preference: Optional[str] = None
    temperature: float = 0.3
    max_tokens: int = 2000

@dataclass
class LLMAnalysisResponse:
    """Risposta analisi LLM."""
    analysis_type: str
    result: str
    confidence: float
    suggestions: List[str]
    metadata: Dict[str, Any]
    processing_time_ms: int

class EnhancedLLMAssistant:
    """
    Assistant LLM potenziato per il sistema di riconoscimento intelligente.
    """
    
    def __init__(self, openrouter_api_key: Optional[str] = None):
        """
        Inizializza l'Enhanced LLM Assistant.
        
        Args:
            openrouter_api_key: Chiave API OpenRouter (opzionale, usa variabile ambiente)
        """
        self.api_key = openrouter_api_key or os.getenv('OPENROUTER_API_KEY')
        if not self.api_key:
            logger.warning("OPENROUTER_API_KEY non trovata. Alcune funzionalità potrebbero non essere disponibili.")
        
        # Configurazione client OpenAI per OpenRouter
        self.client = AsyncOpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=self.api_key,
        ) if self.api_key else None
        
        # Configurazione modelli
        self.models = {
            'analysis': 'anthropic/claude-3.5-sonnet',
            'narrative': 'openai/gpt-4o',
            'technical': 'meta-llama/llama-3.1-70b-instruct',
            'fast': 'openai/gpt-4o-mini',
            'free': 'meta-llama/llama-3.2-3b-instruct:free'
        }
        
        # Prompt templates specializzati
        self.prompt_templates = self._initialize_prompt_templates()
        
        logger.info("Enhanced LLM Assistant inizializzato")
    
    def _initialize_prompt_templates(self) -> Dict[str, str]:
        """Inizializza i template di prompt specializzati."""
        return {
            'entity_resolution': """
Analizza i seguenti dati per risolvere ambiguità nelle entità:

DATI: {data}
CONTESTO: {context}

Compiti:
1. Identifica entità duplicate o simili
2. Suggerisci normalizzazioni
3. Proponi mapping corretti
4. Evidenzia potenziali errori

Rispondi in formato JSON con:
- entities_found: lista entità identificate
- duplicates: gruppi di duplicati
- suggestions: azioni suggerite
- confidence: score 0-1
""",
            
            'anomaly_analysis': """
Analizza i seguenti pattern per identificare anomalie:

DATI ANALISI: {data}
DISCREPANZE TROVATE: {discrepancies}
CONTESTO BUSINESS: {context}

Compiti:
1. Analizza pattern anomali
2. Identifica cause probabili
3. Suggerisci correzioni
4. Valuta impatto business

Rispondi in formato JSON con:
- anomalies: lista anomalie identificate
- root_causes: cause probabili
- impact_assessment: valutazione impatto
- recommendations: raccomandazioni specifiche
""",
            
            'narrative_report': """
Genera un report narrativo professionale basato sui seguenti dati:

RISULTATI ANALISI: {analysis_results}
PERIODO: {period}
CONTESTO AZIENDALE: {context}

Crea un report che includa:
1. Executive Summary
2. Analisi dettagliata per area
3. Trend identificati
4. Raccomandazioni strategiche
5. Piano d'azione

Stile: professionale, chiaro, orientato al business.
Lunghezza: 800-1200 parole.
""",
            
            'data_quality_assessment': """
Valuta la qualità dei seguenti dati:

DATI: {data}
METRICHE QUALITÀ: {quality_metrics}
STANDARD AZIENDALI: {standards}

Analizza:
1. Completezza dati
2. Accuratezza
3. Consistenza
4. Tempestività
5. Validità

Fornisci:
- quality_score: punteggio 0-100
- issues: problemi identificati
- improvements: miglioramenti suggeriti
- priority: priorità interventi
""",
            
            'configuration_optimization': """
Ottimizza le configurazioni del sistema basandoti sui seguenti dati:

CONFIGURAZIONI ATTUALI: {current_config}
PERFORMANCE METRICS: {performance}
OBIETTIVI: {objectives}

Analizza e suggerisci:
1. Parametri da ottimizzare
2. Valori raccomandati
3. Impatto previsto
4. Rischi potenziali

Formato risposta JSON con:
- optimizations: lista ottimizzazioni
- expected_impact: impatto previsto
- implementation_steps: passi implementazione
- monitoring_metrics: metriche da monitorare
"""
        }
    
    async def analyze_entity_resolution(self, entities_data: Dict[str, Any], 
                                      context: Optional[Dict[str, Any]] = None) -> LLMAnalysisResponse:
        """
        Analizza e risolve ambiguità nelle entità.
        
        Args:
            entities_data: Dati delle entità da analizzare
            context: Contesto aggiuntivo per l'analisi
            
        Returns:
            LLMAnalysisResponse con risultati analisi
        """
        start_time = datetime.now()
        
        try:
            prompt = self.prompt_templates['entity_resolution'].format(
                data=json.dumps(entities_data, indent=2),
                context=json.dumps(context or {}, indent=2)
            )
            
            response = await self._call_llm(
                prompt=prompt,
                model=self.models['analysis'],
                temperature=0.2
            )
            
            # Parse JSON response
            try:
                result_data = json.loads(response)
                suggestions = result_data.get('suggestions', [])
                confidence = result_data.get('confidence', 0.8)
            except json.JSONDecodeError:
                suggestions = ["Analisi completata - vedere risultato testuale"]
                confidence = 0.7
            
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            return LLMAnalysisResponse(
                analysis_type='entity_resolution',
                result=response,
                confidence=confidence,
                suggestions=suggestions,
                metadata={
                    'entities_count': len(entities_data),
                    'model_used': self.models['analysis']
                },
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            logger.error(f"Errore analisi entity resolution: {str(e)}")
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            return LLMAnalysisResponse(
                analysis_type='entity_resolution',
                result=f"Errore durante l'analisi: {str(e)}",
                confidence=0.0,
                suggestions=["Verificare configurazione LLM"],
                metadata={'error': str(e)},
                processing_time_ms=processing_time
            )
    
    async def analyze_anomalies(self, analysis_data: Dict[str, Any], 
                              discrepancies: List[Dict[str, Any]],
                              context: Optional[Dict[str, Any]] = None) -> LLMAnalysisResponse:
        """
        Analizza pattern anomali e suggerisce correzioni.
        
        Args:
            analysis_data: Dati dell'analisi
            discrepancies: Lista delle discrepanze trovate
            context: Contesto business
            
        Returns:
            LLMAnalysisResponse con analisi anomalie
        """
        start_time = datetime.now()
        
        try:
            prompt = self.prompt_templates['anomaly_analysis'].format(
                data=json.dumps(analysis_data, indent=2),
                discrepancies=json.dumps(discrepancies, indent=2),
                context=json.dumps(context or {}, indent=2)
            )
            
            response = await self._call_llm(
                prompt=prompt,
                model=self.models['analysis'],
                temperature=0.3
            )
            
            # Parse response
            try:
                result_data = json.loads(response)
                suggestions = result_data.get('recommendations', [])
                confidence = 0.85
            except json.JSONDecodeError:
                suggestions = ["Analisi anomalie completata"]
                confidence = 0.7
            
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            return LLMAnalysisResponse(
                analysis_type='anomaly_analysis',
                result=response,
                confidence=confidence,
                suggestions=suggestions,
                metadata={
                    'discrepancies_count': len(discrepancies),
                    'model_used': self.models['analysis']
                },
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            logger.error(f"Errore analisi anomalie: {str(e)}")
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            return LLMAnalysisResponse(
                analysis_type='anomaly_analysis',
                result=f"Errore durante l'analisi: {str(e)}",
                confidence=0.0,
                suggestions=["Verificare configurazione LLM"],
                metadata={'error': str(e)},
                processing_time_ms=processing_time
            )
    
    async def generate_narrative_report(self, analysis_results: Dict[str, Any],
                                      period: str,
                                      context: Optional[Dict[str, Any]] = None) -> LLMAnalysisResponse:
        """
        Genera report narrativo professionale.
        
        Args:
            analysis_results: Risultati delle analisi
            period: Periodo di riferimento
            context: Contesto aziendale
            
        Returns:
            LLMAnalysisResponse con report narrativo
        """
        start_time = datetime.now()
        
        try:
            prompt = self.prompt_templates['narrative_report'].format(
                analysis_results=json.dumps(analysis_results, indent=2),
                period=period,
                context=json.dumps(context or {}, indent=2)
            )
            
            response = await self._call_llm(
                prompt=prompt,
                model=self.models['narrative'],
                temperature=0.4,
                max_tokens=3000
            )
            
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            return LLMAnalysisResponse(
                analysis_type='narrative_report',
                result=response,
                confidence=0.9,
                suggestions=["Report generato con successo"],
                metadata={
                    'period': period,
                    'model_used': self.models['narrative'],
                    'word_count': len(response.split())
                },
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            logger.error(f"Errore generazione report: {str(e)}")
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            return LLMAnalysisResponse(
                analysis_type='narrative_report',
                result=f"Errore durante la generazione: {str(e)}",
                confidence=0.0,
                suggestions=["Verificare configurazione LLM"],
                metadata={'error': str(e)},
                processing_time_ms=processing_time
            )
    
    async def assess_data_quality(self, data: Dict[str, Any],
                                quality_metrics: Dict[str, Any],
                                standards: Optional[Dict[str, Any]] = None) -> LLMAnalysisResponse:
        """
        Valuta la qualità dei dati.
        
        Args:
            data: Dati da valutare
            quality_metrics: Metriche di qualità
            standards: Standard aziendali
            
        Returns:
            LLMAnalysisResponse con valutazione qualità
        """
        start_time = datetime.now()
        
        try:
            prompt = self.prompt_templates['data_quality_assessment'].format(
                data=json.dumps(data, indent=2),
                quality_metrics=json.dumps(quality_metrics, indent=2),
                standards=json.dumps(standards or {}, indent=2)
            )
            
            response = await self._call_llm(
                prompt=prompt,
                model=self.models['technical'],
                temperature=0.2
            )
            
            # Parse response per estrarre quality score
            try:
                result_data = json.loads(response)
                quality_score = result_data.get('quality_score', 75)
                suggestions = result_data.get('improvements', [])
                confidence = 0.85
            except json.JSONDecodeError:
                quality_score = 75
                suggestions = ["Valutazione qualità completata"]
                confidence = 0.7
            
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            return LLMAnalysisResponse(
                analysis_type='data_quality_assessment',
                result=response,
                confidence=confidence,
                suggestions=suggestions,
                metadata={
                    'quality_score': quality_score,
                    'model_used': self.models['technical']
                },
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            logger.error(f"Errore valutazione qualità: {str(e)}")
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            return LLMAnalysisResponse(
                analysis_type='data_quality_assessment',
                result=f"Errore durante la valutazione: {str(e)}",
                confidence=0.0,
                suggestions=["Verificare configurazione LLM"],
                metadata={'error': str(e)},
                processing_time_ms=processing_time
            )
    
    async def optimize_configuration(self, current_config: Dict[str, Any],
                                   performance_metrics: Dict[str, Any],
                                   objectives: Optional[Dict[str, Any]] = None) -> LLMAnalysisResponse:
        """
        Ottimizza configurazioni del sistema.
        
        Args:
            current_config: Configurazioni attuali
            performance_metrics: Metriche di performance
            objectives: Obiettivi di ottimizzazione
            
        Returns:
            LLMAnalysisResponse con ottimizzazioni suggerite
        """
        start_time = datetime.now()
        
        try:
            prompt = self.prompt_templates['configuration_optimization'].format(
                current_config=json.dumps(current_config, indent=2),
                performance=json.dumps(performance_metrics, indent=2),
                objectives=json.dumps(objectives or {}, indent=2)
            )
            
            response = await self._call_llm(
                prompt=prompt,
                model=self.models['technical'],
                temperature=0.3
            )
            
            # Parse response
            try:
                result_data = json.loads(response)
                suggestions = result_data.get('optimizations', [])
                confidence = 0.8
            except json.JSONDecodeError:
                suggestions = ["Ottimizzazioni suggerite nel testo"]
                confidence = 0.7
            
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            return LLMAnalysisResponse(
                analysis_type='configuration_optimization',
                result=response,
                confidence=confidence,
                suggestions=suggestions,
                metadata={
                    'config_params': len(current_config),
                    'model_used': self.models['technical']
                },
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            logger.error(f"Errore ottimizzazione configurazione: {str(e)}")
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            return LLMAnalysisResponse(
                analysis_type='configuration_optimization',
                result=f"Errore durante l'ottimizzazione: {str(e)}",
                confidence=0.0,
                suggestions=["Verificare configurazione LLM"],
                metadata={'error': str(e)},
                processing_time_ms=processing_time
            )
    
    async def _call_llm(self, prompt: str, model: str, temperature: float = 0.3, 
                       max_tokens: int = 2000) -> str:
        """
        Effettua chiamata al modello LLM.
        
        Args:
            prompt: Prompt per il modello
            model: Nome del modello da utilizzare
            temperature: Temperatura per la generazione
            max_tokens: Numero massimo di token
            
        Returns:
            Risposta del modello
        """
        if not self.client:
            raise Exception("Client LLM non configurato - verificare OPENROUTER_API_KEY")
        
        try:
            response = await self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": "Sei un esperto analista di dati aziendali. Fornisci analisi precise, actionable e professionali."},
                    {"role": "user", "content": prompt}
                ],
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"Errore chiamata LLM: {str(e)}")
            raise
    
    def get_available_models(self) -> Dict[str, str]:
        """Restituisce i modelli disponibili."""
        return self.models.copy()
    
    def get_prompt_templates(self) -> List[str]:
        """Restituisce i template di prompt disponibili."""
        return list(self.prompt_templates.keys())
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Verifica lo stato di salute dell'assistant.
        
        Returns:
            Dizionario con stato di salute
        """
        status = {
            'api_key_configured': bool(self.api_key),
            'client_initialized': bool(self.client),
            'models_available': len(self.models),
            'templates_loaded': len(self.prompt_templates),
            'timestamp': datetime.now().isoformat()
        }
        
        if self.client:
            try:
                # Test rapido con modello free
                test_response = await self._call_llm(
                    prompt="Rispondi solo 'OK' se funzioni correttamente.",
                    model=self.models['free'],
                    max_tokens=10
                )
                status['llm_connection'] = 'OK' in test_response
            except Exception as e:
                status['llm_connection'] = False
                status['connection_error'] = str(e)
        else:
            status['llm_connection'] = False
        
        return status
