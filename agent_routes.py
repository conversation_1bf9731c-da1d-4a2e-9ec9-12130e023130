#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Route per gli agenti AI - FASE 6 AGGIORNATA.
Questo modulo definisce le route API per interagire con il nuovo sistema di agenti AI.
"""

import os
import sys
import json
import time
from flask import Blueprint, request, jsonify, session

# Import nuovo sistema agenti Fase 6
from agent_system import agent_orchestrator, AgentType, AgentTask
from data_cleaning_agent import data_cleaning_agent
from business_analysis_agent import business_analysis_agent
from workflow_automation_agent import workflow_automation_agent
from recommendation_agent import recommendation_agent

# Crea un blueprint per le route degli agenti
agent_bp = Blueprint('agent', __name__, url_prefix='/agents')

# Registra gli agenti nel nuovo orchestratore
try:
    agent_orchestrator.register_agent(data_cleaning_agent)
    agent_orchestrator.register_agent(business_analysis_agent)
    agent_orchestrator.register_agent(workflow_automation_agent)
    agent_orchestrator.register_agent(recommendation_agent)
    print("✅ Agenti Fase 6 registrati con successo")
except Exception as e:
    print(f"⚠️ Errore registrazione agenti: {e}")


@agent_bp.route('/list', methods=['GET'])
def list_agents():
    """
    Elenca tutti gli agenti disponibili.

    Returns:
        Risposta JSON con la lista degli agenti
    """
    # Usa il nuovo sistema agenti Fase 6
    status = agent_orchestrator.get_system_status()

    agents = [
        {
            'name': 'data_cleaning',
            'type': 'data_cleaning',
            'description': 'Agente per pulizia e validazione dati',
            'status': 'available'
        },
        {
            'name': 'business_analysis',
            'type': 'business_analysis',
            'description': 'Agente per analisi business e KPI',
            'status': 'available'
        },
        {
            'name': 'workflow_automation',
            'type': 'workflow_automation',
            'description': 'Agente per automazione workflow',
            'status': 'available'
        },
        {
            'name': 'recommendation',
            'type': 'recommendation',
            'description': 'Agente per raccomandazioni intelligenti',
            'status': 'available'
        }
    ]

    return jsonify({
        'agents': agents,
        'system_status': status
    })


@agent_bp.route('/run/<agent_name>', methods=['POST'])
def run_agent(agent_name):
    """
    Avvia l'esecuzione di un agente.

    Args:
        agent_name: Nome dell'agente da eseguire

    Returns:
        Risposta JSON con l'ID dell'esecuzione dell'agente
    """
    # Ottieni i parametri dalla richiesta
    params = request.json or {}

    # Verifica se c'è un file elaborato in sessione
    file_id = session.get('mcp_file_id')
    if not file_id and 'file_id' not in params:
        return jsonify({
            'error': 'Nessun file specificato',
            'message': 'È necessario specificare un file_id o avere un file elaborato in sessione'
        }), 400

    # Usa il file_id dalla sessione se non specificato nei parametri
    if 'file_id' not in params and file_id:
        params['file_id'] = file_id

    try:
        # Mappa nomi agenti ai tipi
        agent_type_map = {
            'data_cleaning': AgentType.DATA_CLEANING,
            'business_analysis': AgentType.BUSINESS_ANALYSIS,
            'workflow_automation': AgentType.WORKFLOW_AUTOMATION,
            'recommendation': AgentType.RECOMMENDATION
        }

        if agent_name not in agent_type_map:
            return jsonify({
                'error': 'Agente non trovato',
                'message': f'Agente {agent_name} non disponibile'
            }), 404

        # Crea task per l'agente
        task = AgentTask(
            task_id=f"web_task_{agent_name}_{int(time.time())}",
            agent_type=agent_type_map[agent_name],
            input_data=params,
            priority=5,
            timeout_seconds=300
        )

        # Sottometti task all'orchestratore
        task_id = agent_orchestrator.submit_task(task)

        return jsonify({
            'agent_id': task_id,
            'status': 'submitted',
            'message': f'Agente {agent_name} avviato con successo'
        })

    except Exception as e:
        sys.stdout.write(f"Errore nell'esecuzione dell'agente {agent_name}: {str(e)}\n")
        sys.stdout.flush()

        return jsonify({
            'error': 'Errore interno',
            'message': f'Si è verificato un errore durante l\'esecuzione dell\'agente: {str(e)}'
        }), 500


@agent_bp.route('/status/<agent_id>', methods=['GET'])
def get_agent_status(agent_id):
    """
    Ottiene lo stato di un agente.

    Args:
        agent_id: ID dell'agente

    Returns:
        Risposta JSON con lo stato dell'agente
    """
    # Usa il nuovo sistema agenti Fase 6
    task_status = agent_orchestrator.get_task_status(agent_id)

    if not task_status:
        return jsonify({
            'error': 'Task non trovato',
            'message': f'Nessun task trovato con ID {agent_id}'
        }), 404

    return jsonify({
        'agent_id': agent_id,
        'status': task_status["status"],
        'progress': task_status.get("progress", 0),
        'start_time': task_status.get("start_time"),
        'details': task_status
    })


@agent_bp.route('/result/<agent_id>', methods=['GET'])
def get_agent_result(agent_id):
    """
    Ottiene il risultato di un agente completato.

    Args:
        agent_id: ID dell'agente

    Returns:
        Risposta JSON con il risultato dell'agente
    """
    # Usa il nuovo sistema agenti Fase 6
    task_status = agent_orchestrator.get_task_status(agent_id)

    if not task_status:
        return jsonify({
            'error': 'Task non trovato',
            'message': f'Nessun task trovato con ID {agent_id}'
        }), 404

    if task_status['status'] not in ['completed', 'error']:
        return jsonify({
            'error': 'Task non completato',
            'message': f'Il task è ancora in esecuzione. Stato attuale: {task_status["status"]}'
        }), 400

    # Ottieni risultato del task
    result = agent_orchestrator.get_task_result(agent_id)

    return jsonify({
        'agent_id': agent_id,
        'status': task_status['status'],
        'result': result.result if result else None,
        'error': result.error if result and hasattr(result, 'error') else None
    })


@agent_bp.route('/dashboard', methods=['GET'])
def agent_dashboard():
    """
    Renderizza la dashboard degli agenti - FASE 6 AGGIORNATA.

    Returns:
        Template HTML della dashboard degli agenti
    """
    from flask import render_template

    # Usa il nuovo sistema agenti Fase 6
    system_status = agent_orchestrator.get_system_status()

    # Lista agenti disponibili
    agents = [
        {'name': 'data_cleaning', 'description': 'Pulizia e validazione dati'},
        {'name': 'business_analysis', 'description': 'Analisi business e KPI'},
        {'name': 'workflow_automation', 'description': 'Automazione workflow'},
        {'name': 'recommendation', 'description': 'Raccomandazioni intelligenti'}
    ]

    # Per ora, simuliamo agenti in esecuzione e completati
    # In un sistema reale, questi dati verrebbero dal database o cache
    running_agents = []
    completed_agents = []

    return render_template(
        'agent_dashboard.html',
        agents=agents,
        running_agents=running_agents,
        completed_agents=completed_agents,
        system_status=system_status
    )
