<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Errori Console - App Roberto</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
        #console-output { background: #f5f5f5; padding: 10px; height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🔍 Test Errori Console - App Roberto</h1>
    
    <div class="test-section">
        <h2>📋 Test Automatici</h2>
        <button onclick="testSetupWizard()">Test Setup Wizard</button>
        <button onclick="testInteractiveCharts()">Test Grafici Interattivi</button>
        <button onclick="testDashboard()">Test Dashboard</button>
        <button onclick="testChatAI()">Test Chat AI</button>
        <button onclick="clearConsole()">P<PERSON><PERSON>ci Console</button>
    </div>
    
    <div class="test-section">
        <h2>📊 Output Console</h2>
        <div id="console-output"></div>
    </div>
    
    <div class="test-section">
        <h2>🎯 Test API Endpoints</h2>
        <div id="api-results"></div>
    </div>

    <script>
        // Intercetta console.log, console.error, console.warn
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'success');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warning');
        };
        
        function clearConsole() {
            consoleOutput.innerHTML = '';
        }
        
        async function testAPI(endpoint, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data && method !== 'GET') {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(`http://127.0.0.1:5001${endpoint}`, options);
                const result = await response.json();
                
                console.log(`✅ ${endpoint}: ${response.status} OK`);
                return { success: true, status: response.status, data: result };
            } catch (error) {
                console.error(`❌ ${endpoint}: ${error.message}`);
                return { success: false, error: error.message };
            }
        }
        
        async function testSetupWizard() {
            console.log('🧪 Testing Setup Wizard...');
            
            // Test API wizard
            await testAPI('/api/wizard/status');
            await testAPI('/api/wizard/test');
            
            // Test caricamento pagina
            try {
                const response = await fetch('http://127.0.0.1:5001/setup-wizard');
                if (response.ok) {
                    console.log('✅ Setup Wizard page loads successfully');
                } else {
                    console.error(`❌ Setup Wizard page error: ${response.status}`);
                }
            } catch (error) {
                console.error(`❌ Setup Wizard page error: ${error.message}`);
            }
        }
        
        async function testInteractiveCharts() {
            console.log('📊 Testing Interactive Charts...');
            
            // Test API chart data
            await testAPI('/api/chart_data');
            await testAPI('/api/data');
            await testAPI('/api/processed_data');
            
            // Test caricamento pagina
            try {
                const response = await fetch('http://127.0.0.1:5001/interactive-charts');
                if (response.ok) {
                    console.log('✅ Interactive Charts page loads successfully');
                } else {
                    console.error(`❌ Interactive Charts page error: ${response.status}`);
                }
            } catch (error) {
                console.error(`❌ Interactive Charts page error: ${error.message}`);
            }
        }
        
        async function testDashboard() {
            console.log('📈 Testing Dashboard...');
            
            // Test API dashboard
            await testAPI('/api/dashboard_data');
            await testAPI('/api/database/status');
            
            // Test caricamento pagina
            try {
                const response = await fetch('http://127.0.0.1:5001/dashboard');
                if (response.ok) {
                    console.log('✅ Dashboard page loads successfully');
                } else {
                    console.error(`❌ Dashboard page error: ${response.status}`);
                }
            } catch (error) {
                console.error(`❌ Dashboard page error: ${error.message}`);
            }
        }
        
        async function testChatAI() {
            console.log('💬 Testing Chat AI...');
            
            // Test API chat
            await testAPI('/api/models');
            
            // Test caricamento pagina
            try {
                const response = await fetch('http://127.0.0.1:5001/chat');
                if (response.ok) {
                    console.log('✅ Chat AI page loads successfully');
                } else {
                    console.error(`❌ Chat AI page error: ${response.status}`);
                }
            } catch (error) {
                console.error(`❌ Chat AI page error: ${error.message}`);
            }
        }
        
        // Test automatico all'avvio
        window.addEventListener('load', function() {
            console.log('🚀 Test Errori Console - App Roberto');
            console.log('📋 Pronto per i test. Clicca sui pulsanti per testare le sezioni.');
        });
    </script>
</body>
</html>
