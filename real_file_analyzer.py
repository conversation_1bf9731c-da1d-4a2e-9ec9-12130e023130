#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Real File Analyzer - Analizzatore per i file reali del sistema.
Implementa la logica di riconoscimento e analisi dei file basata sui pattern reali.
"""

import os
import re
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime
import logging
from difflib import SequenceMatcher

from real_file_patterns import (
    REAL_FILE_PATTERNS, 
    DETECTION_CONFIG, 
    COLUMN_STANDARDIZATION,
    DATA_VALIDATION_RULES,
    get_file_type_by_pattern,
    get_priority_order
)
from universal_file_reader import universal_reader

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealFileAnalyzer:
    """
    Analizzatore avanzato per i file reali del sistema.
    Riconosce automaticamente il tipo di file e ne analizza la struttura.
    """
    
    def __init__(self):
        self.patterns = REAL_FILE_PATTERNS
        self.config = DETECTION_CONFIG
        self.standardization = COLUMN_STANDARDIZATION
        self.validation_rules = DATA_VALIDATION_RULES
    
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """
        Analizza un file e determina il tipo e la struttura.
        
        Args:
            file_path: Percorso del file da analizzare
            
        Returns:
            Dizionario con risultati dell'analisi
        """
        logger.info(f"🔍 Analisi file: {file_path}")
        
        # Informazioni base del file
        filename = os.path.basename(file_path)
        file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        file_age = self._get_file_age(file_path)
        
        result = {
            "file_path": file_path,
            "filename": filename,
            "file_size": file_size,
            "file_age_days": file_age,
            "analysis_timestamp": datetime.now().isoformat(),
            "success": False,
            "error": None,
            "detected_type": "unknown",
            "confidence_score": 0.0,
            "type_scores": {},
            "column_mapping": {},
            "data_quality": {},
            "recommendations": []
        }
        
        try:
            # Verifica età del file
            if file_age > self.config["max_file_age_days"]:
                result["error"] = f"File troppo vecchio ({file_age} giorni)"
                result["recommendations"].append("Verificare se il file è ancora valido")
                return result
            
            # Lettura del file
            df, file_info = universal_reader.read_file(file_path)
            
            if not file_info["success"]:
                result["error"] = file_info.get("error", "Errore lettura file")
                return result
            
            if df.empty:
                result["error"] = "File vuoto o senza dati validi"
                return result
            
            # Aggiorna informazioni file
            result.update({
                "rows": len(df),
                "columns": len(df.columns),
                "column_names": df.columns.tolist(),
                "encoding": file_info.get("encoding"),
                "separator": file_info.get("separator")
            })
            
            # Rilevamento tipo file
            detection_result = self._detect_file_type(filename, df)
            result.update(detection_result)
            
            # Se tipo rilevato, analisi approfondita
            if result["detected_type"] != "unknown":
                # Mappatura colonne
                column_mapping = self._map_columns(result["detected_type"], df.columns.tolist())
                result["column_mapping"] = column_mapping
                
                # Analisi qualità dati
                data_quality = self._analyze_data_quality(result["detected_type"], df, column_mapping)
                result["data_quality"] = data_quality
                
                # Raccomandazioni
                recommendations = self._generate_recommendations(result)
                result["recommendations"] = recommendations
            
            result["success"] = True
            logger.info(f"✅ Analisi completata: {result['detected_type']} (confidenza: {result['confidence_score']:.2f})")
            
        except Exception as e:
            logger.error(f"❌ Errore analisi file {filename}: {e}")
            result["error"] = str(e)
        
        return result
    
    def _detect_file_type(self, filename: str, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Rileva il tipo di file basandosi su nome e contenuto.
        
        Args:
            filename: Nome del file
            df: DataFrame con i dati
            
        Returns:
            Risultato del rilevamento
        """
        type_scores = {}
        
        # Analisi per ogni tipo di file
        for file_type, config in self.patterns.items():
            score = 0.0
            
            # Punteggio basato sul pattern del nome file
            filename_score = self._calculate_filename_score(filename, config["name_patterns"])
            score += filename_score * self.config["filename_pattern_weight"]
            
            # Punteggio basato sulle colonne richieste
            required_score = self._calculate_column_score(df.columns.tolist(), config["required_columns"])
            score += required_score * self.config["required_columns_weight"]
            
            # Punteggio basato sulle colonne opzionali
            optional_score = self._calculate_column_score(df.columns.tolist(), config.get("optional_columns", []))
            score += optional_score * self.config["optional_columns_weight"]
            
            # Normalizza il punteggio
            max_possible_score = (
                self.config["filename_pattern_weight"] +
                self.config["required_columns_weight"] +
                self.config["optional_columns_weight"]
            )
            normalized_score = score / max_possible_score
            
            type_scores[file_type] = normalized_score
            
            logger.debug(f"  {file_type}: {normalized_score:.3f} (filename: {filename_score:.2f}, required: {required_score:.2f}, optional: {optional_score:.2f})")
        
        # Determina il tipo con punteggio più alto
        best_type = max(type_scores, key=type_scores.get)
        best_score = type_scores[best_type]
        
        # Verifica soglia minima di confidenza
        if best_score < self.config["min_confidence_threshold"]:
            best_type = "unknown"
            best_score = 0.0
        
        return {
            "detected_type": best_type,
            "confidence_score": best_score,
            "type_scores": type_scores
        }
    
    def _calculate_filename_score(self, filename: str, patterns: List[str]) -> float:
        """
        Calcola il punteggio basato sui pattern del nome file.
        
        Args:
            filename: Nome del file
            patterns: Lista di pattern regex
            
        Returns:
            Punteggio (0.0-1.0)
        """
        filename_lower = filename.lower()
        
        for pattern in patterns:
            if re.search(pattern.lower(), filename_lower):
                return 1.0
        
        return 0.0
    
    def _calculate_column_score(self, actual_columns: List[str], expected_columns: List[List[str]]) -> float:
        """
        Calcola il punteggio basato sulla corrispondenza delle colonne.
        
        Args:
            actual_columns: Colonne effettive del file
            expected_columns: Lista di liste di colonne attese (sinonimi)
            
        Returns:
            Punteggio (0.0-1.0)
        """
        if not expected_columns:
            return 0.0
        
        matches = 0
        actual_lower = [col.lower().strip() for col in actual_columns]
        
        for expected_group in expected_columns:
            # Cerca corrispondenza esatta o fuzzy
            for expected in expected_group:
                expected_lower = expected.lower().strip()
                
                # Corrispondenza esatta
                if expected_lower in actual_lower:
                    matches += 1
                    break
                
                # Corrispondenza fuzzy
                for actual in actual_lower:
                    similarity = SequenceMatcher(None, expected_lower, actual).ratio()
                    if similarity >= self.config["column_fuzzy_threshold"]:
                        matches += 1
                        break
                else:
                    continue
                break
        
        return matches / len(expected_columns)
    
    def _map_columns(self, file_type: str, actual_columns: List[str]) -> Dict[str, str]:
        """
        Mappa le colonne effettive ai nomi standardizzati.
        
        Args:
            file_type: Tipo di file rilevato
            actual_columns: Colonne effettive del file
            
        Returns:
            Mapping colonna_standardizzata -> colonna_effettiva
        """
        if file_type not in self.standardization:
            return {}
        
        mapping = {}
        standard_columns = self.standardization[file_type]
        actual_lower = {col.lower().strip(): col for col in actual_columns}
        
        for standard_name, synonyms in standard_columns.items():
            best_match = None
            best_similarity = 0.0
            
            for synonym in synonyms:
                synonym_lower = synonym.lower().strip()
                
                # Corrispondenza esatta
                if synonym_lower in actual_lower:
                    mapping[standard_name] = actual_lower[synonym_lower]
                    break
                
                # Corrispondenza fuzzy
                for actual_lower_name, actual_name in actual_lower.items():
                    similarity = SequenceMatcher(None, synonym_lower, actual_lower_name).ratio()
                    if similarity > best_similarity and similarity >= self.config["column_fuzzy_threshold"]:
                        best_similarity = similarity
                        best_match = actual_name
            
            if best_match and standard_name not in mapping:
                mapping[standard_name] = best_match
        
        return mapping
    
    def _analyze_data_quality(self, file_type: str, df: pd.DataFrame, column_mapping: Dict[str, str]) -> Dict[str, Any]:
        """
        Analizza la qualità dei dati nel file.
        
        Args:
            file_type: Tipo di file
            df: DataFrame con i dati
            column_mapping: Mapping delle colonne
            
        Returns:
            Analisi qualità dati
        """
        quality = {
            "total_rows": len(df),
            "empty_rows": 0,
            "duplicate_rows": 0,
            "missing_required_fields": [],
            "data_type_issues": [],
            "date_format_issues": [],
            "overall_score": 0.0
        }
        
        if file_type not in self.validation_rules:
            return quality
        
        rules = self.validation_rules[file_type]
        
        # Conta righe vuote
        quality["empty_rows"] = df.isnull().all(axis=1).sum()
        
        # Conta duplicati
        quality["duplicate_rows"] = df.duplicated().sum()
        
        # Verifica campi richiesti
        for required_field in rules.get("required_fields", []):
            if required_field in column_mapping:
                actual_column = column_mapping[required_field]
                if actual_column in df.columns:
                    missing_count = df[actual_column].isnull().sum()
                    if missing_count > 0:
                        quality["missing_required_fields"].append({
                            "field": required_field,
                            "column": actual_column,
                            "missing_count": missing_count,
                            "missing_percentage": (missing_count / len(df)) * 100
                        })
        
        # Calcola punteggio qualità generale
        issues = (
            len(quality["missing_required_fields"]) +
            (1 if quality["empty_rows"] > len(df) * 0.1 else 0) +
            (1 if quality["duplicate_rows"] > len(df) * 0.05 else 0)
        )
        
        quality["overall_score"] = max(0.0, 1.0 - (issues * 0.2))
        
        return quality
    
    def _generate_recommendations(self, analysis_result: Dict[str, Any]) -> List[str]:
        """
        Genera raccomandazioni basate sull'analisi.
        
        Args:
            analysis_result: Risultato dell'analisi
            
        Returns:
            Lista di raccomandazioni
        """
        recommendations = []
        
        # Raccomandazioni basate sulla confidenza
        if analysis_result["confidence_score"] < 0.8:
            recommendations.append("Verificare manualmente il tipo di file rilevato")
        
        # Raccomandazioni basate sulla qualità dati
        data_quality = analysis_result.get("data_quality", {})
        
        if data_quality.get("empty_rows", 0) > 0:
            recommendations.append(f"Rimuovere {data_quality['empty_rows']} righe vuote")
        
        if data_quality.get("duplicate_rows", 0) > 0:
            recommendations.append(f"Verificare {data_quality['duplicate_rows']} righe duplicate")
        
        if data_quality.get("missing_required_fields"):
            recommendations.append("Completare i campi obbligatori mancanti")
        
        if data_quality.get("overall_score", 1.0) < 0.7:
            recommendations.append("Migliorare la qualità generale dei dati")
        
        return recommendations
    
    def _get_file_age(self, file_path: str) -> int:
        """
        Calcola l'età del file in giorni.
        
        Args:
            file_path: Percorso del file
            
        Returns:
            Età in giorni
        """
        try:
            if os.path.exists(file_path):
                file_time = os.path.getmtime(file_path)
                file_date = datetime.fromtimestamp(file_time)
                age = (datetime.now() - file_date).days
                return age
        except Exception:
            pass
        
        return 0
    
    def analyze_directory(self, directory_path: str) -> Dict[str, Any]:
        """
        Analizza tutti i file in una directory.
        
        Args:
            directory_path: Percorso della directory
            
        Returns:
            Risultati analisi per tutti i file
        """
        logger.info(f"📁 Analisi directory: {directory_path}")
        
        results = {
            "directory": directory_path,
            "analysis_timestamp": datetime.now().isoformat(),
            "total_files": 0,
            "analyzed_files": 0,
            "successful_analyses": 0,
            "file_types_found": {},
            "files": []
        }
        
        if not os.path.exists(directory_path):
            results["error"] = "Directory non trovata"
            return results
        
        # Lista file supportati
        supported_extensions = ['.csv', '.xlsx', '.xls', '.CSV']
        files = [f for f in os.listdir(directory_path) 
                if any(f.endswith(ext) for ext in supported_extensions)]
        
        results["total_files"] = len(files)
        
        for filename in files:
            file_path = os.path.join(directory_path, filename)
            
            logger.info(f"  📄 Analisi: {filename}")
            analysis = self.analyze_file(file_path)
            results["files"].append(analysis)
            results["analyzed_files"] += 1
            
            if analysis["success"]:
                results["successful_analyses"] += 1
                file_type = analysis["detected_type"]
                
                if file_type not in results["file_types_found"]:
                    results["file_types_found"][file_type] = 0
                results["file_types_found"][file_type] += 1
        
        logger.info(f"✅ Analisi directory completata: {results['successful_analyses']}/{results['analyzed_files']} file analizzati")
        
        return results

# Istanza globale
real_file_analyzer = RealFileAnalyzer()
