{"environment": "production", "app_name": "sistema-riconoscimento-intelligente", "version": "1.0.0", "python_version": "3.9+", "description": "Sistema di Riconoscimento Intelligente per Analisi Dati Aziendali", "database": {"type": "supabase", "backup_enabled": true, "connection_pool_size": 20, "max_connections": 50, "connection_timeout": 30, "query_timeout": 60, "retry_attempts": 3, "health_check_interval": 300}, "server": {"host": "0.0.0.0", "port": 5000, "workers": 4, "worker_class": "sync", "worker_connections": 1000, "timeout": 300, "keepalive": 2, "max_requests": 1000, "max_requests_jitter": 100, "preload_app": true}, "security": {"https_enabled": true, "ssl_cert_path": "/etc/ssl/certs/app.crt", "ssl_key_path": "/etc/ssl/private/app.key", "secret_key_rotation": true, "session_timeout": 3600, "csrf_protection": true, "secure_headers": true, "rate_limiting": {"enabled": true, "requests_per_minute": 100, "burst_limit": 200}}, "monitoring": {"health_check_enabled": true, "health_check_endpoint": "/health", "health_check_interval": 60, "metrics_enabled": true, "metrics_endpoint": "/metrics", "logging_level": "INFO", "log_retention_days": 30, "log_rotation": true, "log_max_size": "100MB", "alerting": {"enabled": true, "email_notifications": true, "webhook_url": null, "alert_thresholds": {"cpu_usage": 80, "memory_usage": 85, "disk_usage": 90, "response_time": 5000, "error_rate": 5}}}, "performance": {"caching_enabled": true, "cache_type": "memory", "cache_timeout": 3600, "cache_max_size": 1000, "compression_enabled": true, "compression_level": 6, "static_files_cdn": false, "static_files_cache": 86400, "gzip_enabled": true, "minify_assets": true}, "agents": {"max_concurrent_tasks": 10, "task_timeout": 600, "auto_restart": true, "health_check_interval": 120, "task_retry_attempts": 3, "task_queue_size": 100, "agent_configs": {"data_cleaning": {"enabled": true, "max_memory": "512MB", "timeout": 300, "priority": "high"}, "export_management": {"enabled": true, "max_memory": "256MB", "timeout": 180, "priority": "medium"}}}, "llm_integration": {"openrouter_enabled": true, "default_model": "anthropic/claude-3-haiku", "fallback_models": ["openai/gpt-3.5-turbo", "meta-llama/llama-2-7b-chat"], "max_tokens": 4000, "temperature": 0.7, "timeout": 30, "retry_attempts": 3, "rate_limiting": {"requests_per_minute": 60, "tokens_per_minute": 100000}}, "file_processing": {"max_file_size": "50MB", "allowed_extensions": [".xlsx", ".csv", ".xls"], "upload_timeout": 300, "processing_timeout": 600, "temp_file_cleanup": true, "temp_file_retention": 3600, "virus_scanning": false, "file_validation": true}, "cross_analysis": {"enabled": true, "max_records_per_analysis": 10000, "analysis_timeout": 300, "cache_results": true, "cache_duration": 1800, "parallel_processing": true, "max_parallel_analyses": 4}, "reporting": {"enabled": true, "output_formats": ["html", "pdf", "xlsx"], "template_caching": true, "report_timeout": 180, "max_report_size": "10MB", "auto_cleanup": true, "cleanup_after_days": 7}, "backup": {"enabled": true, "schedule": "daily", "retention_days": 30, "backup_location": "./backups", "compress_backups": true, "verify_backups": true, "backup_types": ["config", "logs", "uploads"]}, "maintenance": {"auto_updates": false, "maintenance_window": "02:00-04:00", "log_cleanup_enabled": true, "temp_cleanup_enabled": true, "cache_cleanup_enabled": true, "health_checks": {"database": true, "agents": true, "disk_space": true, "memory": true, "llm_connection": true}}, "scaling": {"auto_scaling": false, "min_workers": 2, "max_workers": 8, "scale_up_threshold": 80, "scale_down_threshold": 30, "scale_check_interval": 300, "load_balancing": false}, "development": {"debug_mode": false, "profiling_enabled": false, "test_mode": false, "mock_external_services": false, "development_tools": false}, "compliance": {"gdpr_compliance": true, "data_retention_policy": true, "audit_logging": true, "data_anonymization": false, "privacy_controls": true}, "integration": {"api_versioning": true, "api_rate_limiting": true, "webhook_support": false, "external_auth": false, "sso_enabled": false, "api_documentation": true}, "deployment": {"deployment_strategy": "blue_green", "rollback_enabled": true, "health_check_before_deploy": true, "zero_downtime": true, "deployment_timeout": 600, "post_deploy_tests": true}}