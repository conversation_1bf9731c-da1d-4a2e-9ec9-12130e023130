#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Intelligent Entity Extractor - Estrazione intelligente di entità dai file.
Utilizza pattern recognition, NLP e machine learning per estrarre automaticamente
entità significative dai dati.
"""

import pandas as pd
import numpy as np
import re
import logging
from typing import Dict, List, Tuple, Any, Optional, Set
from datetime import datetime, timedelta
from collections import Counter, defaultdict
import json

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntelligentEntityExtractor:
    """
    Estrattore intelligente di entità che identifica e estrae automaticamente
    informazioni significative dai dati, come dipendenti, clienti, progetti,
    date, durate, e altre entità di business.
    """

    def __init__(self):
        # Pattern per l'estrazione di entità
        self.ENTITY_PATTERNS = {
            "employee": {
                "patterns": [
                    r"\b[A-Z][a-z]+ [A-Z][a-z]+\b",  # Nome Cognome
                    r"\b[A-Z]{2,4}\d{2,4}\b",        # Codice dipendente
                    r"\b[a-z]+\.[a-z]+@[a-z]+\.[a-z]+\b"  # Email
                ],
                "keywords": ["tecnico", "operatore", "dipendente", "user", "creato", "assegnato"],
                "confidence_weights": [0.8, 0.9, 0.7]
            },

            "client": {
                "patterns": [
                    r"\b[A-Z][a-zA-Z\s&]{3,30}\b",   # Nome azienda
                    r"\b\d{11}\b",                    # Partita IVA
                    r"\bP\.IVA\s*\d{11}\b"           # P.IVA esplicita
                ],
                "keywords": ["cliente", "azienda", "studio", "ufficio", "ditta", "società"],
                "confidence_weights": [0.7, 0.9, 0.9]
            },

            "ticket": {
                "patterns": [
                    r"\b[A-Z]{2,4}-?\d{3,6}\b",      # Formato ticket standard
                    r"\b#\d{3,6}\b",                 # Formato #numero
                    r"\bTK\d{4,6}\b"                 # Formato TK numero
                ],
                "keywords": ["ticket", "id", "numero", "codice"],
                "confidence_weights": [0.9, 0.8, 0.8]
            },

            "project": {
                "patterns": [
                    r"\b[A-Z]{2,4}-[A-Z]{2,4}\b",    # Codice progetto
                    r"\bPRJ-\d{3,4}\b",              # Formato progetto
                    r"\b[A-Z][a-zA-Z\s]{5,25}\b"     # Nome progetto
                ],
                "keywords": ["progetto", "project", "commessa", "lavoro"],
                "confidence_weights": [0.8, 0.9, 0.6]
            },

            "date": {
                "patterns": [
                    r"\b\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}\b",  # Date varie
                    r"\b\d{4}[\/\-\.]\d{1,2}[\/\-\.]\d{1,2}\b",    # ISO date
                    r"\b\d{1,2}\s+[A-Za-z]{3,9}\s+\d{2,4}\b"       # Date testuali
                ],
                "keywords": ["data", "date", "giorno", "inizio", "fine"],
                "confidence_weights": [0.9, 0.9, 0.8]
            },

            "time": {
                "patterns": [
                    r"\b\d{1,2}:\d{2}(:\d{2})?\b",   # Orari
                    r"\b\d{1,3}:\d{2}\b",            # Durate
                    r"\b\d+[hm]\b"                   # Formato ore/minuti
                ],
                "keywords": ["ora", "time", "durata", "tempo", "inizio", "fine"],
                "confidence_weights": [0.9, 0.8, 0.7]
            },

            "location": {
                "patterns": [
                    r"\bSala\s+[A-Za-z0-9\s]{1,20}\b",  # Sale riunioni
                    r"\bUfficio\s+[A-Za-z0-9\s]{1,20}\b", # Uffici
                    r"\b[A-Z][a-zA-Z\s]{3,25},\s*[A-Z]{2}\b"  # Città, Provincia
                ],
                "keywords": ["luogo", "location", "sede", "ufficio", "sala"],
                "confidence_weights": [0.8, 0.8, 0.7]
            },

            "vehicle": {
                "patterns": [
                    r"\b[A-Z]{2}\d{3}[A-Z]{2}\b",    # Targa italiana
                    r"\b[A-Z]{1,3}\d{2,4}[A-Z]{0,2}\b", # Targa generica
                    r"\b\d{1,6}\s*km\b"              # Chilometraggio
                ],
                "keywords": ["targa", "auto", "veicolo", "macchina", "km"],
                "confidence_weights": [0.9, 0.7, 0.8]
            }
        }

        # Configurazione estrazione
        self.MIN_CONFIDENCE = 0.6
        self.MAX_ENTITIES_PER_TYPE = 100
        self.SIMILARITY_THRESHOLD = 0.8

    def extract_entities(self, df: pd.DataFrame, file_type: str = "unknown") -> Dict[str, Any]:
        """
        Estrae entità intelligentemente da un DataFrame.

        Args:
            df: DataFrame da cui estrarre entità
            file_type: Tipo di file per ottimizzare l'estrazione

        Returns:
            Dizionario con entità estratte
        """
        logger.info(f"🔍 Estrazione entità per file tipo: {file_type}")

        if df is None or df.empty:
            return self._create_empty_extraction_result("DataFrame vuoto")

        # Risultato dell'estrazione
        extraction_result = {
            "file_type": file_type,
            "total_rows": len(df),
            "total_columns": len(df.columns),
            "extraction_timestamp": datetime.now().isoformat(),
            "entities": {},
            "entity_statistics": {},
            "column_mapping": {},
            "confidence_scores": {},
            "recommendations": []
        }

        try:
            # Estrai entità per ogni tipo
            for entity_type in self.ENTITY_PATTERNS.keys():
                entities = self._extract_entity_type(df, entity_type, file_type)
                if entities:
                    extraction_result["entities"][entity_type] = entities
                    extraction_result["entity_statistics"][entity_type] = len(entities)

            # Mappa colonne a tipi di entità
            column_mapping = self._map_columns_to_entities(df)
            extraction_result["column_mapping"] = column_mapping

            # Calcola punteggi di confidenza
            confidence_scores = self._calculate_entity_confidence(df, extraction_result["entities"])
            extraction_result["confidence_scores"] = confidence_scores

            # Genera raccomandazioni
            recommendations = self._generate_extraction_recommendations(df, extraction_result)
            extraction_result["recommendations"] = recommendations

            # Statistiche finali
            total_entities = sum(extraction_result["entity_statistics"].values())
            logger.info(f"✅ Estratte {total_entities} entità di {len(extraction_result['entities'])} tipi")

        except Exception as e:
            logger.error(f"❌ Errore nell'estrazione entità: {str(e)}")
            extraction_result["error"] = str(e)

        return extraction_result

    def _extract_entity_type(self, df: pd.DataFrame, entity_type: str, file_type: str) -> List[Dict[str, Any]]:
        """Estrae entità di un tipo specifico."""
        entities = []
        entity_config = self.ENTITY_PATTERNS[entity_type]

        # Ottimizza l'estrazione basata sul tipo di file
        relevant_columns = self._get_relevant_columns(df, entity_type, file_type)

        for col in relevant_columns:
            column_entities = self._extract_from_column(df[col], entity_type, col)
            entities.extend(column_entities)

        # Rimuovi duplicati e filtra per confidenza
        unique_entities = self._deduplicate_entities(entities)
        filtered_entities = [e for e in unique_entities if e["confidence"] >= self.MIN_CONFIDENCE]

        # Limita il numero di entità
        return filtered_entities[:self.MAX_ENTITIES_PER_TYPE]

    def _get_relevant_columns(self, df: pd.DataFrame, entity_type: str, file_type: str) -> List[str]:
        """Identifica le colonne più rilevanti per un tipo di entità."""
        relevant_columns = []
        entity_keywords = self.ENTITY_PATTERNS[entity_type]["keywords"]

        # Priorità basata sul tipo di file
        priority_keywords = self._get_priority_keywords(entity_type, file_type)

        for col in df.columns:
            col_lower = str(col).lower()
            relevance_score = 0.0

            # Controlla parole chiave prioritarie
            for keyword in priority_keywords:
                if keyword in col_lower:
                    relevance_score += 2.0

            # Controlla parole chiave generali
            for keyword in entity_keywords:
                if keyword in col_lower:
                    relevance_score += 1.0

            if relevance_score > 0:
                relevant_columns.append(col)

        # Se non trova colonne rilevanti, usa tutte
        if not relevant_columns:
            relevant_columns = df.columns.tolist()

        return relevant_columns

    def _get_priority_keywords(self, entity_type: str, file_type: str) -> List[str]:
        """Restituisce parole chiave prioritarie basate sul tipo di file."""
        priority_map = {
            "attivita": {
                "employee": ["tecnico", "operatore", "creato", "assegnato"],
                "client": ["cliente", "azienda"],
                "ticket": ["ticket", "id", "numero"],
                "project": ["progetto", "commessa"]
            },
            "timbrature": {
                "employee": ["dipendente", "operatore", "user"],
                "time": ["ora", "entrata", "uscita", "pausa"]
            },
            "calendario": {
                "location": ["luogo", "sala", "ufficio"],
                "date": ["data", "giorno"],
                "time": ["ora", "inizio", "fine"]
            },
            "teamviewer": {
                "employee": ["user", "utente"],
                "location": ["computer", "pc"]
            },
            "registro_auto": {
                "vehicle": ["targa", "auto", "veicolo"],
                "employee": ["conducente", "autista"]
            }
        }

        return priority_map.get(file_type, {}).get(entity_type, [])

    def _extract_from_column(self, series: pd.Series, entity_type: str, column_name: str) -> List[Dict[str, Any]]:
        """Estrae entità da una singola colonna."""
        entities = []
        entity_config = self.ENTITY_PATTERNS[entity_type]
        patterns = entity_config["patterns"]
        weights = entity_config["confidence_weights"]

        for idx, value in series.dropna().items():
            value_str = str(value).strip()
            if not value_str:
                continue

            # Prova ogni pattern
            for i, pattern in enumerate(patterns):
                matches = re.findall(pattern, value_str, re.IGNORECASE)
                for match in matches:
                    confidence = weights[i] if i < len(weights) else 0.5

                    # Aggiusta confidenza basata sul contesto
                    context_bonus = self._calculate_context_bonus(column_name, entity_type)
                    final_confidence = min(1.0, confidence + context_bonus)

                    entity = {
                        "value": match,
                        "type": entity_type,
                        "confidence": final_confidence,
                        "source_column": column_name,
                        "source_row": int(idx),
                        "pattern_used": i,
                        "raw_value": value_str
                    }
                    entities.append(entity)

        return entities

    def _calculate_context_bonus(self, column_name: str, entity_type: str) -> float:
        """Calcola bonus di confidenza basato sul contesto della colonna."""
        column_lower = column_name.lower()
        entity_keywords = self.ENTITY_PATTERNS[entity_type]["keywords"]

        bonus = 0.0
        for keyword in entity_keywords:
            if keyword in column_lower:
                bonus += 0.1

        return min(0.3, bonus)  # Massimo 30% di bonus

    def _deduplicate_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Rimuove entità duplicate mantenendo quella con confidenza maggiore."""
        if not entities:
            return []

        # Raggruppa per valore normalizzato
        entity_groups = defaultdict(list)
        for entity in entities:
            normalized_value = self._normalize_entity_value(entity["value"])
            entity_groups[normalized_value].append(entity)

        # Mantieni l'entità con confidenza maggiore per ogni gruppo
        unique_entities = []
        for group in entity_groups.values():
            best_entity = max(group, key=lambda x: x["confidence"])
            unique_entities.append(best_entity)

        return unique_entities

    def _normalize_entity_value(self, value: str) -> str:
        """Normalizza un valore di entità per il confronto."""
        if not isinstance(value, str):
            value = str(value)

        # Rimuovi spazi extra, converti in minuscolo
        normalized = re.sub(r'\s+', ' ', value.strip().lower())

        # Rimuovi caratteri speciali comuni
        normalized = re.sub(r'[^\w\s@.-]', '', normalized)

        return normalized

    def _map_columns_to_entities(self, df: pd.DataFrame) -> Dict[str, List[str]]:
        """Mappa le colonne ai tipi di entità più probabili."""
        column_mapping = {}

        for col in df.columns:
            col_lower = str(col).lower()
            entity_scores = {}

            # Calcola punteggio per ogni tipo di entità
            for entity_type, config in self.ENTITY_PATTERNS.items():
                score = 0.0
                for keyword in config["keywords"]:
                    if keyword in col_lower:
                        score += 1.0

                if score > 0:
                    entity_scores[entity_type] = score

            # Ordina per punteggio
            if entity_scores:
                sorted_entities = sorted(entity_scores.items(), key=lambda x: x[1], reverse=True)
                column_mapping[col] = [entity_type for entity_type, _ in sorted_entities]

        return column_mapping

    def _calculate_entity_confidence(self, df: pd.DataFrame, entities: Dict[str, List[Dict[str, Any]]]) -> Dict[str, float]:
        """Calcola punteggi di confidenza complessivi per tipo di entità."""
        confidence_scores = {}

        for entity_type, entity_list in entities.items():
            if not entity_list:
                confidence_scores[entity_type] = 0.0
                continue

            # Media delle confidenze individuali
            individual_confidences = [e["confidence"] for e in entity_list]
            avg_confidence = sum(individual_confidences) / len(individual_confidences)

            # Bonus per quantità (più entità = più affidabile)
            quantity_bonus = min(0.2, len(entity_list) * 0.02)

            # Bonus per diversità delle colonne sorgente
            source_columns = set(e["source_column"] for e in entity_list)
            diversity_bonus = min(0.1, len(source_columns) * 0.05)

            final_confidence = min(1.0, avg_confidence + quantity_bonus + diversity_bonus)
            confidence_scores[entity_type] = final_confidence

        return confidence_scores

    def _generate_extraction_recommendations(self, df: pd.DataFrame, extraction_result: Dict[str, Any]) -> List[str]:
        """Genera raccomandazioni per migliorare l'estrazione."""
        recommendations = []

        entities = extraction_result.get("entities", {})
        confidence_scores = extraction_result.get("confidence_scores", {})

        # Raccomandazioni basate sulla confidenza
        low_confidence_types = [
            entity_type for entity_type, confidence in confidence_scores.items()
            if confidence < 0.7
        ]

        if low_confidence_types:
            recommendations.append(
                f"⚠️ Bassa confidenza per: {', '.join(low_confidence_types)}. "
                "Verificare manualmente i risultati."
            )

        # Raccomandazioni basate sulla quantità
        empty_types = [
            entity_type for entity_type in self.ENTITY_PATTERNS.keys()
            if entity_type not in entities or not entities[entity_type]
        ]

        if empty_types:
            recommendations.append(
                f"🔍 Nessuna entità trovata per: {', '.join(empty_types)}. "
                "Verificare se i dati contengono queste informazioni."
            )

        # Raccomandazioni specifiche per tipo di file
        file_type = extraction_result.get("file_type", "unknown")
        if file_type != "unknown":
            type_recommendations = self._get_file_type_recommendations(file_type, entities)
            recommendations.extend(type_recommendations)

        # Raccomandazioni per migliorare la qualità
        if len(df) < 10:
            recommendations.append("📊 Dataset piccolo: risultati potrebbero essere meno affidabili.")

        return recommendations

    def _get_file_type_recommendations(self, file_type: str, entities: Dict[str, List[Dict[str, Any]]]) -> List[str]:
        """Genera raccomandazioni specifiche per tipo di file."""
        recommendations = []

        type_expectations = {
            "attivita": ["employee", "client", "ticket", "date"],
            "timbrature": ["employee", "time", "date"],
            "calendario": ["date", "time", "location"],
            "teamviewer": ["employee", "location"],
            "registro_auto": ["vehicle", "employee", "location"]
        }

        expected_entities = type_expectations.get(file_type, [])
        missing_entities = [
            entity_type for entity_type in expected_entities
            if entity_type not in entities or not entities[entity_type]
        ]

        if missing_entities:
            recommendations.append(
                f"📋 Per file di tipo '{file_type}' ci si aspetterebbe: {', '.join(missing_entities)}"
            )

        return recommendations

    def _create_empty_extraction_result(self, error_message: str) -> Dict[str, Any]:
        """Crea un risultato vuoto con messaggio di errore."""
        return {
            "file_type": "unknown",
            "total_rows": 0,
            "total_columns": 0,
            "extraction_timestamp": datetime.now().isoformat(),
            "entities": {},
            "entity_statistics": {},
            "column_mapping": {},
            "confidence_scores": {},
            "recommendations": [f"❌ Errore: {error_message}"],
            "error": error_message
        }

    def extract_master_entities(self, df: pd.DataFrame) -> Dict[str, Set[str]]:
        """
        Estrae entità master (dipendenti, clienti) per la gestione centralizzata.

        Args:
            df: DataFrame da cui estrarre entità master

        Returns:
            Dizionario con set di entità master
        """
        master_entities = {
            "employees": set(),
            "clients": set(),
            "projects": set(),
            "locations": set()
        }

        # Estrai entità complete
        extraction_result = self.extract_entities(df)
        entities = extraction_result.get("entities", {})

        # Mappa entità a categorie master
        if "employee" in entities:
            for entity in entities["employee"]:
                if entity["confidence"] >= 0.7:  # Solo alta confidenza per master
                    master_entities["employees"].add(entity["value"])

        if "client" in entities:
            for entity in entities["client"]:
                if entity["confidence"] >= 0.7:
                    master_entities["clients"].add(entity["value"])

        if "project" in entities:
            for entity in entities["project"]:
                if entity["confidence"] >= 0.7:
                    master_entities["projects"].add(entity["value"])

        if "location" in entities:
            for entity in entities["location"]:
                if entity["confidence"] >= 0.7:
                    master_entities["locations"].add(entity["value"])

        return master_entities

# Istanza globale
intelligent_entity_extractor = IntelligentEntityExtractor()
