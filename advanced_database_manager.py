#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Advanced Database Manager per il sistema di riconoscimento intelligente.
Gestisce operazioni avanzate su Supabase con entità master e dati normalizzati.
"""

import logging
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import json
import uuid
from dataclasses import dataclass

try:
    from supabase import create_client, Client
    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False

from supabase_integration import SupabaseManager

# Configurazione logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class EntityMatch:
    """Rappresenta un match di entità con score di confidenza."""
    entity_id: int
    entity_name: str
    confidence: float
    match_type: str  # 'exact', 'fuzzy', 'new'

@dataclass
class ProcessingResult:
    """Risultato di un'operazione di processing."""
    success: bool
    records_processed: int
    entities_created: int
    entities_matched: int
    errors: List[str]
    processing_time_ms: int
    metadata: Dict[str, Any]

class AdvancedDatabaseManager:
    """
    Gestore database avanzato per il sistema intelligente.
    Estende SupabaseManager con funzionalità per entità master e normalizzazione.
    """

    def __init__(self, supabase_manager: Optional[SupabaseManager] = None):
        """
        Inizializza il gestore database avanzato.

        Args:
            supabase_manager: Istanza di SupabaseManager (opzionale)
        """
        self.supabase_manager = supabase_manager or SupabaseManager()
        self.client = self.supabase_manager.client
        self.is_connected = self.supabase_manager.is_connected

        # Cache per entità master
        self._technicians_cache = {}
        self._clients_cache = {}
        self._projects_cache = {}
        self._vehicles_cache = {}

        # Configurazioni
        self.config = self._load_system_config()

        logger.info("AdvancedDatabaseManager inizializzato")

    def _load_system_config(self) -> Dict[str, Any]:
        """Carica configurazioni sistema dal database."""
        try:
            if not self.is_connected:
                return self._get_default_config()

            result = self.client.table("intelligent_system_config").select("*").execute()

            config = {}
            for row in result.data:
                category = row['config_category']
                key = row['config_key']
                value = row['config_value']

                if category not in config:
                    config[category] = {}
                config[category][key] = value

            logger.info(f"Configurazioni caricate: {len(result.data)} elementi")
            return config

        except Exception as e:
            logger.error(f"Errore caricamento configurazioni: {str(e)}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Restituisce configurazioni di default."""
        return {
            'file_detection': {
                'confidence_threshold': 0.7,
                'fuzzy_threshold': 0.8
            },
            'entity_extraction': {
                'min_confidence': 0.6,
                'auto_learning': True
            },
            'standardization': {
                'auto_standardize': True,
                'similarity_threshold': 0.85
            },
            'quality_control': {
                'min_quality_score': 0.7
            }
        }

    def process_extracted_entities(self, file_upload_id: int, extraction_result: Dict[str, Any]) -> ProcessingResult:
        """
        Processa entità estratte e le inserisce nelle tabelle master.

        Args:
            file_upload_id: ID del file caricato
            extraction_result: Risultato dell'estrazione entità

        Returns:
            ProcessingResult con dettagli dell'operazione
        """
        start_time = datetime.now()

        try:
            logger.info(f"🔄 Processing entità per file {file_upload_id}")

            entities = extraction_result.get('entities', {})
            records_processed = 0
            entities_created = 0
            entities_matched = 0
            errors = []

            # Processa tecnici
            if 'technicians' in entities:
                tech_result = self._process_technicians(entities['technicians'])
                records_processed += tech_result['processed']
                entities_created += tech_result['created']
                entities_matched += tech_result['matched']
                errors.extend(tech_result['errors'])

            # Processa clienti
            if 'clients' in entities:
                client_result = self._process_clients(entities['clients'])
                records_processed += client_result['processed']
                entities_created += client_result['created']
                entities_matched += client_result['matched']
                errors.extend(client_result['errors'])

            # Processa progetti
            if 'projects' in entities:
                project_result = self._process_projects(entities['projects'])
                records_processed += project_result['processed']
                entities_created += project_result['created']
                entities_matched += project_result['matched']
                errors.extend(project_result['errors'])

            # Processa veicoli
            if 'vehicles' in entities:
                vehicle_result = self._process_vehicles(entities['vehicles'])
                records_processed += vehicle_result['processed']
                entities_created += vehicle_result['created']
                entities_matched += vehicle_result['matched']
                errors.extend(vehicle_result['errors'])

            # Log dell'operazione
            self._log_entity_extraction(file_upload_id, extraction_result, errors)

            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            result = ProcessingResult(
                success=len(errors) == 0,
                records_processed=records_processed,
                entities_created=entities_created,
                entities_matched=entities_matched,
                errors=errors,
                processing_time_ms=int(processing_time),
                metadata={
                    'file_upload_id': file_upload_id,
                    'extraction_timestamp': start_time.isoformat()
                }
            )

            logger.info(f"✅ Processing completato: {records_processed} record, {entities_created} nuove entità")
            return result

        except Exception as e:
            logger.error(f"❌ Errore processing entità: {str(e)}")
            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            return ProcessingResult(
                success=False,
                records_processed=0,
                entities_created=0,
                entities_matched=0,
                errors=[str(e)],
                processing_time_ms=int(processing_time),
                metadata={'file_upload_id': file_upload_id}
            )

    def _process_technicians(self, technicians: List[Dict]) -> Dict[str, Any]:
        """Processa lista di tecnici."""
        processed = 0
        created = 0
        matched = 0
        errors = []

        try:
            for tech in technicians:
                processed += 1

                # Cerca match esistente
                match = self._find_technician_match(tech['normalized'])

                if match.match_type == 'new':
                    # Crea nuovo tecnico
                    new_tech = self._create_technician(tech)
                    if new_tech:
                        created += 1
                        self._technicians_cache[tech['normalized']] = new_tech['id']
                else:
                    # Aggiorna tecnico esistente
                    self._update_technician(match.entity_id, tech)
                    matched += 1

        except Exception as e:
            errors.append(f"Errore processing tecnici: {str(e)}")

        return {
            'processed': processed,
            'created': created,
            'matched': matched,
            'errors': errors
        }

    def _find_technician_match(self, normalized_name: str) -> EntityMatch:
        """Trova match per un tecnico."""
        try:
            # Cerca in cache
            if normalized_name in self._technicians_cache:
                tech_id = self._technicians_cache[normalized_name]
                return EntityMatch(tech_id, normalized_name, 1.0, 'exact')

            # Cerca nel database
            result = self.client.table("master_technicians").select("*").eq("normalized_name", normalized_name).execute()

            if result.data:
                tech = result.data[0]
                self._technicians_cache[normalized_name] = tech['id']
                return EntityMatch(tech['id'], tech['normalized_name'], 1.0, 'exact')

            # Cerca fuzzy match
            fuzzy_result = self.client.table("master_technicians").select("*").ilike("normalized_name", f"%{normalized_name}%").execute()

            if fuzzy_result.data:
                # Calcola similarità e prendi il migliore
                best_match = None
                best_score = 0

                for tech in fuzzy_result.data:
                    score = self._calculate_similarity(normalized_name, tech['normalized_name'])
                    if score > best_score and score >= self.config['standardization']['similarity_threshold']:
                        best_score = score
                        best_match = tech

                if best_match:
                    return EntityMatch(best_match['id'], best_match['normalized_name'], best_score, 'fuzzy')

            # Nessun match trovato
            return EntityMatch(0, normalized_name, 0.0, 'new')

        except Exception as e:
            logger.error(f"Errore ricerca tecnico: {str(e)}")
            return EntityMatch(0, normalized_name, 0.0, 'new')

    def _create_technician(self, tech_data: Dict) -> Optional[Dict]:
        """Crea nuovo tecnico."""
        try:
            # Estrai nome e cognome se possibile
            name_parts = tech_data['normalized'].split()
            first_name = name_parts[0] if name_parts else ""
            last_name = " ".join(name_parts[1:]) if len(name_parts) > 1 else ""

            new_tech = {
                'normalized_name': tech_data['normalized'],
                'original_names': [tech_data['value']],
                'first_name': first_name,
                'last_name': last_name,
                'confidence_score': tech_data.get('confidence', 0.8),
                'is_active': True
            }

            result = self.client.table("master_technicians").insert(new_tech).execute()

            if result.data:
                logger.info(f"✅ Nuovo tecnico creato: {tech_data['normalized']}")
                return result.data[0]

        except Exception as e:
            logger.error(f"❌ Errore creazione tecnico: {str(e)}")

        return None

    def _update_technician(self, tech_id: int, tech_data: Dict) -> bool:
        """Aggiorna tecnico esistente."""
        try:
            # Recupera tecnico esistente
            result = self.client.table("master_technicians").select("*").eq("id", tech_id).execute()

            if not result.data:
                return False

            existing_tech = result.data[0]

            # Aggiorna original_names se necessario
            original_names = existing_tech.get('original_names', [])
            if tech_data['value'] not in original_names:
                original_names.append(tech_data['value'])

                update_data = {
                    'original_names': original_names,
                    'updated_at': datetime.now().isoformat()
                }

                self.client.table("master_technicians").update(update_data).eq("id", tech_id).execute()
                logger.info(f"✅ Tecnico aggiornato: {existing_tech['normalized_name']}")

            return True

        except Exception as e:
            logger.error(f"❌ Errore aggiornamento tecnico: {str(e)}")
            return False

    def _process_clients(self, clients: List[Dict]) -> Dict[str, Any]:
        """Processa lista di clienti."""
        processed = 0
        created = 0
        matched = 0
        errors = []

        try:
            for client in clients:
                processed += 1

                # Cerca match esistente
                match = self._find_client_match(client['normalized'])

                if match.match_type == 'new':
                    # Crea nuovo cliente
                    new_client = self._create_client(client)
                    if new_client:
                        created += 1
                        self._clients_cache[client['normalized']] = new_client['id']
                else:
                    # Aggiorna cliente esistente
                    self._update_client(match.entity_id, client)
                    matched += 1

        except Exception as e:
            errors.append(f"Errore processing clienti: {str(e)}")

        return {
            'processed': processed,
            'created': created,
            'matched': matched,
            'errors': errors
        }

    def _find_client_match(self, normalized_name: str) -> EntityMatch:
        """Trova match per un cliente."""
        try:
            # Cerca in cache
            if normalized_name in self._clients_cache:
                client_id = self._clients_cache[normalized_name]
                return EntityMatch(client_id, normalized_name, 1.0, 'exact')

            # Cerca nel database
            result = self.client.table("master_clients").select("*").eq("normalized_name", normalized_name).execute()

            if result.data:
                client = result.data[0]
                self._clients_cache[normalized_name] = client['id']
                return EntityMatch(client['id'], client['normalized_name'], 1.0, 'exact')

            # Cerca fuzzy match
            fuzzy_result = self.client.table("master_clients").select("*").ilike("normalized_name", f"%{normalized_name}%").execute()

            if fuzzy_result.data:
                best_match = None
                best_score = 0

                for client in fuzzy_result.data:
                    score = self._calculate_similarity(normalized_name, client['normalized_name'])
                    if score > best_score and score >= self.config['standardization']['similarity_threshold']:
                        best_score = score
                        best_match = client

                if best_match:
                    return EntityMatch(best_match['id'], best_match['normalized_name'], best_score, 'fuzzy')

            return EntityMatch(0, normalized_name, 0.0, 'new')

        except Exception as e:
            logger.error(f"Errore ricerca cliente: {str(e)}")
            return EntityMatch(0, normalized_name, 0.0, 'new')

    def _create_client(self, client_data: Dict) -> Optional[Dict]:
        """Crea nuovo cliente."""
        try:
            # Estrai tipo società se presente
            company_type = None
            normalized_name = client_data['normalized']

            for suffix in ['SRL', 'SPA', 'SNC', 'SAS', 'COOP']:
                if suffix in normalized_name:
                    company_type = suffix
                    break

            new_client = {
                'normalized_name': normalized_name,
                'original_names': [client_data['value']],
                'company_type': company_type,
                'confidence_score': client_data.get('confidence', 0.8),
                'is_active': True
            }

            result = self.client.table("master_clients").insert(new_client).execute()

            if result.data:
                logger.info(f"✅ Nuovo cliente creato: {normalized_name}")
                return result.data[0]

        except Exception as e:
            logger.error(f"❌ Errore creazione cliente: {str(e)}")

        return None

    def _update_client(self, client_id: int, client_data: Dict) -> bool:
        """Aggiorna cliente esistente."""
        try:
            result = self.client.table("master_clients").select("*").eq("id", client_id).execute()

            if not result.data:
                return False

            existing_client = result.data[0]
            original_names = existing_client.get('original_names', [])

            if client_data['value'] not in original_names:
                original_names.append(client_data['value'])

                update_data = {
                    'original_names': original_names,
                    'updated_at': datetime.now().isoformat()
                }

                self.client.table("master_clients").update(update_data).eq("id", client_id).execute()
                logger.info(f"✅ Cliente aggiornato: {existing_client['normalized_name']}")

            return True

        except Exception as e:
            logger.error(f"❌ Errore aggiornamento cliente: {str(e)}")
            return False

    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """Calcola similarità tra due stringhe."""
        try:
            from difflib import SequenceMatcher
            return SequenceMatcher(None, str1.lower(), str2.lower()).ratio()
        except:
            return 0.0

    def _log_entity_extraction(self, file_upload_id: int, extraction_result: Dict, errors: List[str]) -> None:
        """Log dell'operazione di estrazione entità."""
        try:
            log_entry = {
                'file_upload_id': file_upload_id,
                'extraction_timestamp': datetime.now().isoformat(),
                'file_type': extraction_result.get('file_type', 'unknown'),
                'entities_extracted': extraction_result.get('entities', {}),
                'column_mapping': extraction_result.get('column_mapping', {}),
                'confidence_scores': extraction_result.get('confidence_scores', {}),
                'statistics': extraction_result.get('statistics', {}),
                'recommendations': extraction_result.get('recommendations', []),
                'processing_time_ms': extraction_result.get('processing_time_ms', 0),
                'success': len(errors) == 0,
                'error_message': '; '.join(errors) if errors else None
            }

            self.client.table("entity_extraction_log").insert(log_entry).execute()

        except Exception as e:
            logger.error(f"Errore logging estrazione entità: {str(e)}")

    def get_master_entities(self) -> Dict[str, List[Dict]]:
        """Recupera tutte le entità master."""
        try:
            entities = {}

            # Tecnici
            tech_result = self.client.table("master_technicians").select("*").eq("is_active", True).execute()
            entities['technicians'] = tech_result.data

            # Clienti
            client_result = self.client.table("master_clients").select("*").eq("is_active", True).execute()
            entities['clients'] = client_result.data

            # Progetti
            project_result = self.client.table("master_projects").select("*").eq("is_active", True).execute()
            entities['projects'] = project_result.data

            # Veicoli
            vehicle_result = self.client.table("master_vehicles").select("*").eq("is_active", True).execute()
            entities['vehicles'] = vehicle_result.data

            logger.info(f"Entità master recuperate: {sum(len(v) for v in entities.values())} totali")
            return entities

        except Exception as e:
            logger.error(f"Errore recupero entità master: {str(e)}")
            return {}

    def get_normalized_activities(self, limit: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Recupera attività normalizzate dal database.

        Args:
            limit: Numero massimo di record da recuperare
            offset: Offset per paginazione

        Returns:
            Lista di attività normalizzate
        """
        try:
            if not self.is_connected:
                logger.warning("Database non connesso")
                return []

            logger.info(f"🔍 Recupero attività normalizzate (limit: {limit}, offset: {offset})")

            # Query per recuperare attività normalizzate con join alle entità master
            result = self.client.table("normalized_activities").select("""
                *,
                master_technicians(id, normalized_name, first_name, last_name),
                master_clients(id, normalized_name, company_type),
                master_projects(id, project_code, project_name)
            """).range(offset, offset + limit - 1).order("activity_date", desc=True).execute()

            if result.data:
                logger.info(f"✅ Recuperate {len(result.data)} attività normalizzate")
                return result.data
            else:
                logger.info("📭 Nessuna attività normalizzata trovata")
                return []

        except Exception as e:
            logger.error(f"❌ Errore recupero attività normalizzate: {str(e)}")
            return []

    def get_normalized_teamviewer(self, limit: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Recupera sessioni TeamViewer normalizzate dal database.

        Args:
            limit: Numero massimo di record da recuperare
            offset: Offset per paginazione

        Returns:
            Lista di sessioni TeamViewer normalizzate
        """
        try:
            if not self.is_connected:
                logger.warning("Database non connesso")
                return []

            logger.info(f"🔍 Recupero sessioni TeamViewer normalizzate (limit: {limit}, offset: {offset})")

            # Query per recuperare sessioni TeamViewer normalizzate
            result = self.client.table("normalized_teamviewer").select("""
                *,
                master_technicians(id, normalized_name, first_name, last_name),
                master_clients(id, normalized_name, company_type)
            """).range(offset, offset + limit - 1).order("session_start", desc=True).execute()

            if result.data:
                logger.info(f"✅ Recuperate {len(result.data)} sessioni TeamViewer normalizzate")
                return result.data
            else:
                logger.info("📭 Nessuna sessione TeamViewer normalizzata trovata")
                return []

        except Exception as e:
            logger.error(f"❌ Errore recupero sessioni TeamViewer normalizzate: {str(e)}")
            return []

    def get_normalized_vehicles(self, limit: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Recupera utilizzi veicoli normalizzati dal database.

        Args:
            limit: Numero massimo di record da recuperare
            offset: Offset per paginazione

        Returns:
            Lista di utilizzi veicoli normalizzati
        """
        try:
            if not self.is_connected:
                logger.warning("Database non connesso")
                return []

            logger.info(f"🔍 Recupero utilizzi veicoli normalizzati (limit: {limit}, offset: {offset})")

            # Query per recuperare utilizzi veicoli normalizzati
            result = self.client.table("normalized_vehicle_usage").select("""
                *,
                master_technicians(id, normalized_name, first_name, last_name),
                master_vehicles(id, vehicle_name, vehicle_type),
                master_clients(id, normalized_name, company_type)
            """).range(offset, offset + limit - 1).order("usage_date", desc=True).execute()

            if result.data:
                logger.info(f"✅ Recuperati {len(result.data)} utilizzi veicoli normalizzati")
                return result.data
            else:
                logger.info("📭 Nessun utilizzo veicoli normalizzato trovato")
                return []

        except Exception as e:
            logger.error(f"❌ Errore recupero utilizzi veicoli normalizzati: {str(e)}")
            return []

    def get_cross_analysis_data(self, date_from: Optional[str] = None,
                              date_to: Optional[str] = None) -> Dict[str, Any]:
        """
        Recupera dati per analisi incrociate.

        Args:
            date_from: Data inizio (formato YYYY-MM-DD)
            date_to: Data fine (formato YYYY-MM-DD)

        Returns:
            Dizionario con dati aggregati per analisi
        """
        try:
            logger.info(f"🔍 Recupero dati per analisi incrociate: {date_from} - {date_to}")

            # Costruisci filtri data
            date_filter = {}
            if date_from:
                date_filter['gte'] = date_from
            if date_to:
                date_filter['lte'] = date_to

            analysis_data = {}

            # Dati attività
            activities_query = self.client.table("normalized_activities").select("""
                *,
                master_technicians(normalized_name),
                master_clients(normalized_name),
                master_projects(project_code, project_name)
            """)

            if date_filter:
                activities_query = activities_query.filter("activity_date", "gte", date_filter.get('gte', '1900-01-01'))
                activities_query = activities_query.filter("activity_date", "lte", date_filter.get('lte', '2100-12-31'))

            activities_result = activities_query.execute()
            analysis_data['activities'] = activities_result.data

            # Dati TeamViewer
            teamviewer_query = self.client.table("normalized_teamviewer").select("""
                *,
                master_technicians(normalized_name),
                master_clients(normalized_name)
            """)

            if date_filter:
                teamviewer_query = teamviewer_query.filter("session_start", "gte", f"{date_filter.get('gte', '1900-01-01')}T00:00:00")
                teamviewer_query = teamviewer_query.filter("session_start", "lte", f"{date_filter.get('lte', '2100-12-31')}T23:59:59")

            teamviewer_result = teamviewer_query.execute()
            analysis_data['teamviewer'] = teamviewer_result.data

            # Dati veicoli
            vehicles_query = self.client.table("normalized_vehicle_usage").select("""
                *,
                master_technicians(normalized_name),
                master_vehicles(vehicle_name),
                master_clients(normalized_name)
            """)

            if date_filter:
                vehicles_query = vehicles_query.filter("usage_date", "gte", date_filter.get('gte', '1900-01-01'))
                vehicles_query = vehicles_query.filter("usage_date", "lte", date_filter.get('lte', '2100-12-31'))

            vehicles_result = vehicles_query.execute()
            analysis_data['vehicles'] = vehicles_result.data

            # Statistiche aggregate
            analysis_data['statistics'] = self._calculate_cross_analysis_stats(analysis_data)

            logger.info(f"✅ Dati analisi recuperati: {len(analysis_data)} categorie")
            return analysis_data

        except Exception as e:
            logger.error(f"❌ Errore recupero dati analisi: {str(e)}")
            return {}

    def _calculate_cross_analysis_stats(self, data: Dict[str, List]) -> Dict[str, Any]:
        """Calcola statistiche aggregate per analisi incrociate."""
        try:
            stats = {
                'total_activities': len(data.get('activities', [])),
                'total_teamviewer_sessions': len(data.get('teamviewer', [])),
                'total_vehicle_usage': len(data.get('vehicles', [])),
                'unique_technicians': set(),
                'unique_clients': set(),
                'total_hours': 0,
                'total_revenue': 0
            }

            # Analizza attività
            for activity in data.get('activities', []):
                if activity.get('master_technicians'):
                    stats['unique_technicians'].add(activity['master_technicians']['normalized_name'])
                if activity.get('master_clients'):
                    stats['unique_clients'].add(activity['master_clients']['normalized_name'])

                stats['total_hours'] += activity.get('duration_hours', 0) or 0
                stats['total_revenue'] += activity.get('total_cost', 0) or 0

            # Analizza TeamViewer
            for session in data.get('teamviewer', []):
                if session.get('master_technicians'):
                    stats['unique_technicians'].add(session['master_technicians']['normalized_name'])
                if session.get('master_clients'):
                    stats['unique_clients'].add(session['master_clients']['normalized_name'])

                duration_hours = (session.get('duration_minutes', 0) or 0) / 60
                stats['total_hours'] += duration_hours
                stats['total_revenue'] += session.get('total_cost', 0) or 0

            # Converti set in conteggi
            stats['unique_technicians'] = len(stats['unique_technicians'])
            stats['unique_clients'] = len(stats['unique_clients'])

            return stats

        except Exception as e:
            logger.error(f"Errore calcolo statistiche: {str(e)}")
            return {}

    def get_quality_report(self, date_from: Optional[str] = None,
                          date_to: Optional[str] = None) -> Dict[str, Any]:
        """
        Genera report qualità dati.

        Args:
            date_from: Data inizio analisi
            date_to: Data fine analisi

        Returns:
            Report qualità con metriche e raccomandazioni
        """
        try:
            logger.info("📊 Generazione report qualità dati")

            report = {
                'timestamp': datetime.now().isoformat(),
                'period': f"{date_from or 'N/A'} - {date_to or 'N/A'}",
                'quality_metrics': {},
                'data_integrity': {},
                'recommendations': []
            }

            # Metriche qualità per tabelle normalizzate
            tables = ['normalized_activities', 'normalized_teamviewer', 'normalized_vehicle_usage']

            for table in tables:
                query = self.client.table(table).select("quality_score")

                if date_from and table == 'normalized_activities':
                    query = query.filter("activity_date", "gte", date_from)
                elif date_from and table == 'normalized_teamviewer':
                    query = query.filter("session_start", "gte", f"{date_from}T00:00:00")
                elif date_from and table == 'normalized_vehicle_usage':
                    query = query.filter("usage_date", "gte", date_from)

                result = query.execute()

                if result.data:
                    scores = [row.get('quality_score', 0) for row in result.data if row.get('quality_score')]
                    if scores:
                        report['quality_metrics'][table] = {
                            'total_records': len(result.data),
                            'avg_quality_score': sum(scores) / len(scores),
                            'min_quality_score': min(scores),
                            'max_quality_score': max(scores),
                            'low_quality_count': len([s for s in scores if s < 0.7])
                        }

            # Integrità dati entità master
            master_tables = ['master_technicians', 'master_clients', 'master_projects', 'master_vehicles']

            for table in master_tables:
                result = self.client.table(table).select("confidence_score, is_active").execute()

                if result.data:
                    active_count = len([r for r in result.data if r.get('is_active', True)])
                    confidence_scores = [r.get('confidence_score', 0) for r in result.data if r.get('confidence_score')]

                    report['data_integrity'][table] = {
                        'total_entities': len(result.data),
                        'active_entities': active_count,
                        'avg_confidence': sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0,
                        'low_confidence_count': len([s for s in confidence_scores if s < 0.6])
                    }

            # Genera raccomandazioni
            report['recommendations'] = self._generate_quality_recommendations(report)

            logger.info("✅ Report qualità generato")
            return report

        except Exception as e:
            logger.error(f"❌ Errore generazione report qualità: {str(e)}")
            return {'error': str(e)}

    def _generate_quality_recommendations(self, report: Dict[str, Any]) -> List[str]:
        """Genera raccomandazioni basate sul report qualità."""
        recommendations = []

        try:
            # Analizza metriche qualità
            for table, metrics in report.get('quality_metrics', {}).items():
                avg_quality = metrics.get('avg_quality_score', 0)
                low_quality_count = metrics.get('low_quality_count', 0)

                if avg_quality < 0.7:
                    recommendations.append(f"⚠️ Qualità dati bassa per {table} (media: {avg_quality:.2f})")

                if low_quality_count > 0:
                    recommendations.append(f"🔍 {low_quality_count} record con qualità bassa in {table}")

            # Analizza integrità entità
            for table, integrity in report.get('data_integrity', {}).items():
                avg_confidence = integrity.get('avg_confidence', 0)
                low_confidence_count = integrity.get('low_confidence_count', 0)

                if avg_confidence < 0.7:
                    recommendations.append(f"⚠️ Confidenza bassa per entità {table} (media: {avg_confidence:.2f})")

                if low_confidence_count > 0:
                    recommendations.append(f"🔍 {low_confidence_count} entità con confidenza bassa in {table}")

            if not recommendations:
                recommendations.append("✅ Qualità dati ottimale - nessuna azione richiesta")

        except Exception as e:
            recommendations.append(f"❌ Errore generazione raccomandazioni: {str(e)}")

        return recommendations
