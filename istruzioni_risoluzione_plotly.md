# Istruzioni per Risolvere il Problema del Pacchetto ~lotly

## Problema
L'applicazione presenta un warning persistente relativo al pacchetto `~lotly` che non può essere risolto con i normali metodi di disinstallazione/reinstallazione. Ogni tentativo di riparazione richiede circa 15 minuti e il problema si ripresenta al riavvio successivo.

## Soluzione: Creazione di un Ambiente Virtuale Pulito

Seguire questi passaggi per creare un nuovo ambiente virtuale pulito che eviti completamente il problema:

### Passo 1: Creare un nuovo script per l'ambiente pulito

1. Creare un file chiamato `create_clean_env.bat` con il seguente contenuto:
   ```batch
   @echo off
   setlocal enabledelayedexpansion
   echo ===================================
   echo Creazione di un ambiente virtuale pulito
   echo ===================================
   echo.

   REM Definisci il nome del nuovo ambiente
   set NEW_ENV=clean_env

   echo Questo script creerà un nuovo ambiente virtuale chiamato "%NEW_ENV%"
   echo senza toccare l'ambiente "venv" esistente.
   echo.
   set /p CONFIRM=Vuoi procedere? (S/N): 
   if /i not "%CONFIRM%"=="S" (
       echo Operazione annullata.
       goto :EOF
   )

   REM Crea il nuovo ambiente virtuale
   echo.
   echo Creazione del nuovo ambiente virtuale "%NEW_ENV%"...
   python -m venv %NEW_ENV%
   if %errorlevel% neq 0 (
       echo ERRORE: Impossibile creare il nuovo ambiente virtuale.
       goto :EOF
   )
   echo Nuovo ambiente virtuale creato con successo.
   echo.

   REM Attiva il nuovo ambiente
   echo Attivazione del nuovo ambiente...
   call %NEW_ENV%\Scripts\activate
   if %errorlevel% neq 0 (
       echo ERRORE: Impossibile attivare il nuovo ambiente.
       goto :EOF
   )
   echo Ambiente attivato.
   echo.

   REM Aggiorna pip all'ultima versione
   echo Aggiornamento di pip...
   python -m pip install --upgrade pip
   echo.

   REM Installa le dipendenze di base direttamente (senza usare requirements.txt)
   echo Installazione delle dipendenze di base...
   pip install python-dotenv fastapi flask pandas
   echo.

   REM Installa plotly con un approccio diverso
   echo Installazione di plotly con approccio alternativo...
   pip install --no-deps plotly==5.16.1
   pip install tenacity packaging
   echo.

   REM Verifica l'installazione di plotly
   echo Verifica dell'installazione di plotly...
   python -c "import plotly; print(f'Plotly {plotly.__version__} installato correttamente')"
   if %errorlevel% neq 0 (
       echo ERRORE: Verifica di plotly fallita.
   ) else (
       echo Plotly verificato con successo.
   )
   echo.

   REM Installa le altre dipendenze dal requirements.txt, escludendo plotly
   echo Installazione delle altre dipendenze...
   pip install -r requirements.txt --no-deps
   echo.

   echo ===================================
   echo Ambiente "%NEW_ENV%" creato con successo!
   echo.
   echo Per utilizzare questo nuovo ambiente:
   echo   call %NEW_ENV%\Scripts\activate
   echo.
   echo Per aggiornare gli script di avvio esistenti:
   echo   1. Modifica start_app.bat, start_app_with_env.bat, ecc.
   echo   2. Sostituisci "venv" con "%NEW_ENV%" in tutti i percorsi
   echo ===================================

   pause
   ```

### Passo 2: Creare un nuovo script di avvio

2. Creare un file chiamato `use_clean_env.bat` con il seguente contenuto:
   ```batch
   @echo off
   setlocal enabledelayedexpansion
   echo ===================================
   echo Avvio dell'applicazione con l'ambiente pulito
   echo ===================================
   echo.

   REM Definisci il nome dell'ambiente pulito
   set CLEAN_ENV=clean_env

   REM Verifica se l'ambiente pulito esiste
   if not exist %CLEAN_ENV% (
       echo L'ambiente %CLEAN_ENV% non esiste.
       echo Esegui prima create_clean_env.bat per crearlo.
       goto :EOF
   )

   REM Attiva l'ambiente pulito
   echo Attivazione dell'ambiente pulito...
   call %CLEAN_ENV%\Scripts\activate
   echo Ambiente attivato.
   echo.

   REM Imposta le variabili d'ambiente
   set OPENROUTER_API_KEY=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
   set APP_URL=http://localhost:5000
   set MCP_URL=http://localhost:8000

   echo Variabili d'ambiente impostate:
   echo OPENROUTER_API_KEY: %OPENROUTER_API_KEY%
   echo APP_URL: %APP_URL%
   echo MCP_URL: %MCP_URL%
   echo.

   REM Avvia il server MCP in una nuova finestra
   echo Avvio del server MCP...
   start "MCP Server" cmd /k "call %CLEAN_ENV%\Scripts\activate && cd mcp_server && python run_server.py"
   echo Server MCP avviato in una nuova finestra.
   echo.

   REM Attendi 5 secondi per dare tempo al server MCP di avviarsi
   echo Attesa di 5 secondi per l'avvio del server MCP...
   ping 127.0.0.1 -n 6 > nul
   echo.

   REM Avvia l'applicazione principale
   echo Avvio dell'applicazione principale...
   start "App Roberto" cmd /k "call %CLEAN_ENV%\Scripts\activate && python app.py"
   echo Applicazione principale avviata in una nuova finestra.
   echo.

   echo ===================================
   echo Applicazione avviata con successo!
   echo - Flask: http://localhost:5000
   echo - MCP: http://localhost:8000
   echo ===================================
   echo.
   echo Premi un tasto per chiudere questa finestra...
   pause > nul
   ```

### Passo 3: Eseguire i nuovi script

1. Eseguire `create_clean_env.bat` per creare il nuovo ambiente virtuale pulito
2. Una volta completato con successo, eseguire `use_clean_env.bat` per avviare l'applicazione con il nuovo ambiente

### Passo 4: Verifica e adozione permanente

1. Verificare che l'applicazione funzioni correttamente con il nuovo ambiente
2. Se tutto funziona senza warning, è possibile:
   - Continuare a utilizzare `use_clean_env.bat` per avviare l'applicazione
   - Oppure aggiornare tutti gli script di avvio esistenti sostituendo `venv` con `clean_env`

### Vantaggi di questo approccio

- **Non distruttivo**: L'ambiente originale rimane intatto finché non siamo sicuri che il nuovo funzioni
- **Veloce**: Non c'è bisogno di disinstallare/reinstallare continuamente
- **Pulito**: Partiamo da zero, evitando qualsiasi file corrotto
- **Verificabile**: Testiamo subito che plotly funzioni correttamente

### Note tecniche

- L'installazione di plotly viene eseguita con `--no-deps` per evitare problemi di risoluzione delle dipendenze
- Le dipendenze di plotly (tenacity e packaging) vengono installate separatamente
- Viene eseguita una verifica immediata dell'installazione di plotly
- Le altre dipendenze vengono installate dal requirements.txt esistente