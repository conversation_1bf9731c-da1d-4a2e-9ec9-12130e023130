#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Analisi specifica del file progetti per migliorare il riconoscimento.
"""

import pandas as pd
import sys
import os
from pathlib import Path

# Aggiungi il percorso del progetto
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from universal_file_reader import UniversalFileReader

def analyze_progetti_file():
    """
    Analizza il file progetti per capire come migliorare il riconoscimento.
    """
    print("🔍 ANALISI FILE PROGETTI")
    print("=" * 40)
    
    file_path = "test_file_grezzi/progetti_230525.xlsx"
    
    if not Path(file_path).exists():
        print(f"❌ File non trovato: {file_path}")
        return
    
    # Usa UniversalFileReader
    reader = UniversalFileReader()
    
    try:
        df, file_info = reader.read_file(file_path)
        
        if not file_info["success"]:
            print(f"❌ Errore lettura: {file_info.get('error', 'Sconosciuto')}")
            return
        
        print(f"✅ File letto: {len(df)} righe, {len(df.columns)} colonne")
        print()
        
        print("📋 COLONNE:")
        for i, col in enumerate(df.columns):
            print(f"   {i+1}. {col}")
        print()
        
        print("📊 PRIME 5 RIGHE:")
        print(df.head().to_string())
        print()
        
        print("🔍 ANALISI CONTENUTO PER COLONNA:")
        for col in df.columns:
            print(f"\n--- {col} ---")
            unique_vals = df[col].dropna().unique()
            print(f"Valori unici: {len(unique_vals)}")
            print(f"Primi 5 valori: {unique_vals[:5].tolist()}")
            
            # Analizza pattern
            sample_values = df[col].dropna().head(10).astype(str)
            print(f"Campione valori: {sample_values.tolist()}")
        
        print("\n🎯 PATTERN IDENTIFICATI:")
        
        # Cerca pattern tipici dei progetti
        project_indicators = []
        
        for col in df.columns:
            col_lower = col.lower()
            sample_values = df[col].dropna().head(10).astype(str)
            
            # Pattern per progetti
            if any(keyword in col_lower for keyword in ['progetto', 'project', 'codice', 'code']):
                project_indicators.append(f"Colonna '{col}' contiene keyword progetto")
            
            if any(keyword in col_lower for keyword in ['stato', 'status', 'fase', 'phase']):
                project_indicators.append(f"Colonna '{col}' contiene keyword stato")
            
            if any(keyword in col_lower for keyword in ['priorita', 'priority', 'urgenza']):
                project_indicators.append(f"Colonna '{col}' contiene keyword priorità")
            
            if any(keyword in col_lower for keyword in ['capo', 'manager', 'responsabile', 'leader']):
                project_indicators.append(f"Colonna '{col}' contiene keyword responsabile")
            
            # Analizza valori per pattern progetti
            for value in sample_values:
                value_str = str(value).strip()
                
                # Pattern codici progetto
                if len(value_str) >= 3 and any(char.isdigit() for char in value_str) and any(char.isalpha() for char in value_str):
                    project_indicators.append(f"Colonna '{col}' contiene possibili codici progetto: {value_str}")
                    break
        
        if project_indicators:
            print("✅ INDICATORI PROGETTO TROVATI:")
            for indicator in project_indicators:
                print(f"   - {indicator}")
        else:
            print("❌ NESSUN INDICATORE PROGETTO CHIARO TROVATO")
        
        print("\n🔧 RACCOMANDAZIONI:")
        print("1. Verificare se il file contiene effettivamente dati di progetti")
        print("2. Aggiungere pattern specifici per questo tipo di file")
        print("3. Considerare se è un file calendario con progetti/eventi")
        
    except Exception as e:
        print(f"❌ Errore durante l'analisi: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_progetti_file()
