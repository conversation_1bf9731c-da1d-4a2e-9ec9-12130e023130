#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test per verificare la registrazione delle route Flask
"""

import requests
import time

def test_flask_routes_registration():
    """Test per verificare quali route Flask sono registrate"""
    
    print("🔍 TEST REGISTRAZIONE ROUTE FLASK")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # Lista delle route che dovrebbero essere registrate
    expected_routes = [
        ("GET", "/", "Homepage"),
        ("GET", "/dashboard", "Dashboard Standard"),
        ("GET", "/advanced-dashboard", "Dashboard Avanzata"),
        ("GET", "/interactive-charts", "Grafici Interattivi"),
        ("GET", "/setup-wizard", "Setup Wizard"),
        ("GET", "/chat", "Chat AI"),
        ("GET", "/api/dashboard_data", "API Dashboard Data"),
        ("GET", "/api/chart_data", "API Chart Data"),
        ("GET", "/api/config/employees", "API Config Employees"),
        ("GET", "/api/config/vehicles", "API Config Vehicles"),
        ("POST", "/api/chat/send", "API Chat Send"),
        ("GET", "/api/health", "API Health"),
        ("POST", "/upload", "Upload File"),
    ]
    
    working_routes = []
    missing_routes = []
    error_routes = []
    
    print(f"🧪 Testando {len(expected_routes)} route...")
    print()
    
    for method, path, description in expected_routes:
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{path}", timeout=5)
            elif method == "POST":
                response = requests.post(f"{base_url}{path}", json={}, timeout=5)
            else:
                continue
                
            if response.status_code == 404:
                missing_routes.append((method, path, description))
                print(f"❌ {method} {path} - 404 NOT FOUND ({description})")
            elif response.status_code < 500:
                working_routes.append((method, path, description))
                print(f"✅ {method} {path} - {response.status_code} ({description})")
            else:
                error_routes.append((method, path, description, response.status_code))
                print(f"⚠️ {method} {path} - {response.status_code} ({description})")
                
        except requests.exceptions.ConnectionError:
            error_routes.append((method, path, description, "CONNECTION_ERROR"))
            print(f"💥 {method} {path} - CONNECTION ERROR ({description})")
        except requests.exceptions.Timeout:
            error_routes.append((method, path, description, "TIMEOUT"))
            print(f"⏰ {method} {path} - TIMEOUT ({description})")
        except Exception as e:
            error_routes.append((method, path, description, str(e)))
            print(f"💥 {method} {path} - ERROR: {str(e)} ({description})")
    
    print("\n" + "=" * 50)
    print("📊 RIEPILOGO RISULTATI")
    print("=" * 50)
    
    print(f"✅ Route funzionanti: {len(working_routes)}")
    for method, path, description in working_routes:
        print(f"   {method} {path} - {description}")
    
    print(f"\n❌ Route mancanti (404): {len(missing_routes)}")
    for method, path, description in missing_routes:
        print(f"   {method} {path} - {description}")
    
    print(f"\n⚠️ Route con errori: {len(error_routes)}")
    for item in error_routes:
        if len(item) == 4:
            method, path, description, error = item
            print(f"   {method} {path} - {error} ({description})")
    
    # Test specifico per le route API problematiche
    print(f"\n🎯 ANALISI ROUTE API PROBLEMATICHE:")
    
    problematic_apis = [
        "/api/dashboard_data",
        "/api/config/employees", 
        "/api/config/vehicles"
    ]
    
    for api_path in problematic_apis:
        found_working = any(path == api_path for _, path, _ in working_routes)
        found_missing = any(path == api_path for _, path, _ in missing_routes)
        
        if found_working:
            print(f"   ✅ {api_path} è registrato e funzionante")
        elif found_missing:
            print(f"   ❌ {api_path} NON è registrato come route Flask")
            print(f"      📝 La route è definita nel codice ma non registrata")
            print(f"      🔧 Possibili cause:")
            print(f"         - Errore di sintassi prima della definizione")
            print(f"         - Problema di importazione")
            print(f"         - Errore nell'applicazione che impedisce la registrazione")
        else:
            print(f"   ❓ {api_path} stato sconosciuto")
    
    return {
        'working': len(working_routes),
        'missing': len(missing_routes),
        'errors': len(error_routes),
        'total': len(expected_routes)
    }

if __name__ == "__main__":
    print("🚀 Avvio test registrazione route Flask...")
    print("⏰ Attendere qualche secondo per l'avvio del server...")
    time.sleep(2)
    
    results = test_flask_routes_registration()
    
    print(f"\n🏁 TEST COMPLETATO")
    print(f"📈 Risultati: {results['working']}/{results['total']} route funzionanti")
    
    if results['missing'] > 0:
        print(f"⚠️ ATTENZIONE: {results['missing']} route non registrate!")
        print("🔧 Verificare il file app.py per errori di registrazione")
    else:
        print("✅ Tutte le route sono registrate correttamente!")
