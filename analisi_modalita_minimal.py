#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
🔍 ANALISI MODALITÀ MINIMAL - APP ROBERTO
Analizza specificamente l'impatto della modalità minimal sui componenti dell'app.

OBIETTIVO: Identificare esattamente quali funzioni sono compromesse dalla modalità minimal.
"""

import os
import sys
import json
import time
import subprocess
import re
from datetime import datetime
from typing import Dict, List, Tuple, Any

class ModalitaMinimalAnalyzer:
    """Analizzatore specifico per la modalità minimal."""
    
    def __init__(self):
        self.minimal_indicators = []
        self.disabled_systems = []
        self.performance_impact = {}
        self.functionality_impact = {}
        self.startup_logs = []
        
    def analyze_app_startup(self):
        """Analizza l'avvio dell'app per identificare sistemi disabilitati."""
        print("🔍 ANALISI AVVIO APP - MODALITÀ MINIMAL")
        print("=" * 50)
        
        try:
            # Avvia l'app e cattura l'output
            process = subprocess.Popen(
                [sys.executable, "app.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                universal_newlines=True
            )
            
            # Leggi output per 30 secondi
            start_time = time.time()
            while time.time() - start_time < 30:
                line = process.stdout.readline()
                if not line:
                    break
                
                line = line.strip()
                self.startup_logs.append(line)
                
                # Analizza linea per indicatori modalità minimal
                self.analyze_startup_line(line)
                
                # Se l'app è avviata, interrompi
                if "Serving Flask app" in line:
                    time.sleep(5)  # Aspetta ancora un po'
                    break
            
            # Termina processo
            process.terminate()
            process.wait()
            
        except Exception as e:
            print(f"❌ Errore durante analisi avvio: {e}")
    
    def analyze_startup_line(self, line: str):
        """Analizza una singola linea di log per indicatori minimal."""
        
        # Pattern per modalità minimal
        minimal_patterns = [
            r"MODALITÀ MINIMAL",
            r"modalità minimal",
            r"disabilitato.*minimal",
            r"disabled.*minimal",
            r"ottimizzazione disabilitata",
            r"monitoraggio disabilitato",
            r"tuning disabilitato",
            r"lazy loading",
            r"sistemi.*disabilitati"
        ]
        
        for pattern in minimal_patterns:
            if re.search(pattern, line, re.IGNORECASE):
                self.minimal_indicators.append(line)
                
                # Estrai sistema specifico disabilitato
                if "disabilitato" in line.lower() or "disabled" in line.lower():
                    self.extract_disabled_system(line)
    
    def extract_disabled_system(self, line: str):
        """Estrae il nome del sistema disabilitato dalla linea di log."""
        
        # Pattern per estrarre nomi sistemi
        system_patterns = [
            r"(\w+)\s+inizializzato ma.*disabilitato",
            r"(\w+)\s+.*disabilitato",
            r"(\w+System)\s+.*disabilitato",
            r"(\w+Manager)\s+.*disabilitato",
            r"(\w+Agent)\s+.*disabilitato"
        ]
        
        for pattern in system_patterns:
            match = re.search(pattern, line, re.IGNORECASE)
            if match:
                system_name = match.group(1)
                if system_name not in self.disabled_systems:
                    self.disabled_systems.append(system_name)
    
    def analyze_code_for_minimal_mode(self):
        """Analizza il codice per identificare controlli modalità minimal."""
        print("\n🔍 ANALISI CODICE - CONTROLLI MODALITÀ MINIMAL")
        print("=" * 50)
        
        minimal_code_patterns = []
        
        try:
            with open("app.py", "r", encoding="utf-8") as f:
                content = f.read()
                
                # Cerca pattern modalità minimal nel codice
                patterns = [
                    r"MINIMAL_MODE\s*=\s*True",
                    r"minimal.*=.*True",
                    r"if.*minimal",
                    r"modalità minimal",
                    r"lazy loading",
                    r"disabilita.*minimal"
                ]
                
                for pattern in patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)
                    for match in matches:
                        # Trova numero di linea
                        line_num = content[:match.start()].count('\n') + 1
                        line_content = content.split('\n')[line_num - 1].strip()
                        
                        minimal_code_patterns.append({
                            'line': line_num,
                            'content': line_content,
                            'pattern': pattern
                        })
        
        except Exception as e:
            print(f"❌ Errore analisi codice: {e}")
        
        if minimal_code_patterns:
            print("📋 CONTROLLI MODALITÀ MINIMAL TROVATI:")
            for item in minimal_code_patterns:
                print(f"   📍 Linea {item['line']}: {item['content']}")
        else:
            print("ℹ️ Nessun controllo modalità minimal esplicito trovato")
    
    def test_functionality_with_minimal_mode(self):
        """Testa funzionalità specifiche che potrebbero essere compromesse."""
        print("\n🧪 TEST FUNZIONALITÀ CON MODALITÀ MINIMAL")
        print("=" * 50)
        
        # Funzionalità da testare
        functionalities_to_test = [
            ("IntelligentCacheSystem", "Sistema cache intelligente"),
            ("QueryOptimizer", "Ottimizzatore query"),
            ("PerformanceProfiler", "Profiler performance"),
            ("AutoTuner", "Auto-tuner sistema"),
            ("MonitoringSystem", "Sistema monitoraggio"),
            ("AdvancedFeatures", "Funzionalità avanzate"),
            ("AIAgents", "Agenti AI"),
            ("IntelligentAutomation", "Automazione intelligente")
        ]
        
        for system_name, description in functionalities_to_test:
            impact = self.assess_system_impact(system_name, description)
            self.functionality_impact[system_name] = impact
    
    def assess_system_impact(self, system_name: str, description: str) -> Dict[str, Any]:
        """Valuta l'impatto della modalità minimal su un sistema specifico."""
        
        # Controlla se il sistema è nei log di disabilitazione
        is_disabled = any(system_name.lower() in log.lower() for log in self.minimal_indicators)
        
        # Controlla se è nei sistemi disabilitati
        is_in_disabled_list = any(system_name.lower() in disabled.lower() for disabled in self.disabled_systems)
        
        # Valuta impatto
        if is_disabled or is_in_disabled_list:
            impact_level = "HIGH"
            status = "DISABLED"
            recommendation = f"Sistema {system_name} disabilitato - valutare se necessario per produzione"
        else:
            impact_level = "LOW"
            status = "ACTIVE"
            recommendation = f"Sistema {system_name} attivo - nessun impatto"
        
        print(f"   {'❌' if status == 'DISABLED' else '✅'} {description}: {status}")
        
        return {
            'status': status,
            'impact_level': impact_level,
            'recommendation': recommendation,
            'description': description
        }
    
    def analyze_performance_impact(self):
        """Analizza l'impatto della modalità minimal sulle performance."""
        print("\n⚡ ANALISI IMPATTO PERFORMANCE")
        print("=" * 50)
        
        # Estrai metriche performance dai log
        performance_logs = [log for log in self.startup_logs if any(
            keyword in log.lower() for keyword in ['cpu', 'memory', 'baseline', 'performance']
        )]
        
        if performance_logs:
            print("📊 METRICHE PERFORMANCE RILEVATE:")
            for log in performance_logs:
                print(f"   📈 {log}")
        else:
            print("ℹ️ Nessuna metrica performance rilevata nei log")
        
        # Valuta impatto generale
        disabled_count = len(self.disabled_systems)
        if disabled_count == 0:
            performance_impact = "MINIMAL"
            performance_desc = "Nessun sistema critico disabilitato"
        elif disabled_count <= 3:
            performance_impact = "LOW"
            performance_desc = f"{disabled_count} sistemi disabilitati - impatto limitato"
        elif disabled_count <= 6:
            performance_impact = "MEDIUM"
            performance_desc = f"{disabled_count} sistemi disabilitati - impatto moderato"
        else:
            performance_impact = "HIGH"
            performance_desc = f"{disabled_count} sistemi disabilitati - impatto significativo"
        
        self.performance_impact = {
            'level': performance_impact,
            'description': performance_desc,
            'disabled_systems_count': disabled_count,
            'logs': performance_logs
        }
        
        print(f"🎯 IMPATTO GENERALE: {performance_impact}")
        print(f"📝 {performance_desc}")
    
    def generate_minimal_mode_report(self):
        """Genera report dettagliato sulla modalità minimal."""
        
        report = f"""# 🔍 REPORT ANALISI MODALITÀ MINIMAL - APP ROBERTO

## 📊 RIEPILOGO ANALISI
- **Data analisi**: {datetime.now().isoformat()}
- **Sistemi disabilitati**: {len(self.disabled_systems)}
- **Indicatori minimal**: {len(self.minimal_indicators)}
- **Impatto performance**: {self.performance_impact.get('level', 'UNKNOWN')}

## 🚨 SISTEMI DISABILITATI

"""
        
        if self.disabled_systems:
            for system in self.disabled_systems:
                report += f"- ❌ **{system}**: Disabilitato in modalità minimal\n"
        else:
            report += "- ✅ Nessun sistema esplicitamente disabilitato\n"
        
        report += f"""
## 📋 INDICATORI MODALITÀ MINIMAL

"""
        
        if self.minimal_indicators:
            for indicator in self.minimal_indicators:
                report += f"- 📝 `{indicator}`\n"
        else:
            report += "- ℹ️ Nessun indicatore modalità minimal rilevato\n"
        
        report += f"""
## 🎯 IMPATTO FUNZIONALITÀ

"""
        
        for system_name, impact in self.functionality_impact.items():
            status_icon = "❌" if impact['status'] == 'DISABLED' else "✅"
            report += f"- {status_icon} **{impact['description']}**: {impact['status']}\n"
            report += f"  - 📊 Impatto: {impact['impact_level']}\n"
            report += f"  - 💡 {impact['recommendation']}\n\n"
        
        report += f"""
## ⚡ IMPATTO PERFORMANCE

- **Livello impatto**: {self.performance_impact.get('level', 'UNKNOWN')}
- **Descrizione**: {self.performance_impact.get('description', 'N/A')}
- **Sistemi disabilitati**: {self.performance_impact.get('disabled_systems_count', 0)}

## 🎯 RACCOMANDAZIONI

### ✅ SISTEMI OPERATIVI
"""
        
        active_systems = [name for name, impact in self.functionality_impact.items() 
                         if impact['status'] == 'ACTIVE']
        
        if active_systems:
            for system in active_systems:
                report += f"- ✅ {self.functionality_impact[system]['description']}\n"
        else:
            report += "- ⚠️ Nessun sistema confermato come attivo\n"
        
        report += f"""
### ❌ SISTEMI DISABILITATI
"""
        
        disabled_systems = [name for name, impact in self.functionality_impact.items() 
                           if impact['status'] == 'DISABLED']
        
        if disabled_systems:
            for system in disabled_systems:
                report += f"- ❌ {self.functionality_impact[system]['description']}\n"
        else:
            report += "- ✅ Nessun sistema critico disabilitato\n"
        
        report += f"""
### 🔧 AZIONI CONSIGLIATE

"""
        
        if len(disabled_systems) == 0:
            report += "- ✅ **Sistema pronto per produzione** - modalità minimal non compromette funzionalità critiche\n"
        elif len(disabled_systems) <= 2:
            report += "- 🔧 **Valutare riattivazione sistemi** - alcuni sistemi disabilitati potrebbero essere utili\n"
        else:
            report += "- 🚨 **Riattivare sistemi critici** - troppi sistemi disabilitati per uso produttivo\n"
        
        report += f"""
- 📊 **Monitorare performance** - verificare se la modalità minimal impatta le prestazioni
- 🔄 **Test completo** - eseguire test funzionalità per confermare operatività
- 📋 **Documentare configurazione** - mantenere traccia dei sistemi disabilitati

## 🎯 CONCLUSIONI

"""
        
        if self.performance_impact.get('level') in ['MINIMAL', 'LOW']:
            report += "✅ **La modalità minimal non compromette significativamente il sistema**\n"
            report += "🚀 **Sistema pronto per lavoro produttivo**\n"
        else:
            report += "⚠️ **La modalità minimal ha un impatto significativo**\n"
            report += "🔧 **Valutare disattivazione modalità minimal per uso produttivo**\n"
        
        report += f"""
---
**Report generato**: {datetime.now().isoformat()}
**Analizzatore**: ModalitaMinimalAnalyzer v1.0
"""
        
        # Salva report
        with open("ANALISI_MODALITA_MINIMAL.md", "w", encoding="utf-8") as f:
            f.write(report)
        
        print(f"\n📄 Report salvato: ANALISI_MODALITA_MINIMAL.md")
        return report
    
    def run_complete_analysis(self):
        """Esegue l'analisi completa della modalità minimal."""
        print("🔍 ANALISI COMPLETA MODALITÀ MINIMAL")
        print("🎯 OBIETTIVO: Identificare impatto su funzionalità produttive")
        print("=" * 60)
        
        # 1. Analizza avvio app
        self.analyze_app_startup()
        
        # 2. Analizza codice
        self.analyze_code_for_minimal_mode()
        
        # 3. Testa funzionalità
        self.test_functionality_with_minimal_mode()
        
        # 4. Analizza performance
        self.analyze_performance_impact()
        
        # 5. Genera report
        self.generate_minimal_mode_report()
        
        print("\n🎉 ANALISI MODALITÀ MINIMAL COMPLETATA")
        print("📋 Controlla ANALISI_MODALITA_MINIMAL.md per dettagli completi")


def main():
    """Funzione principale."""
    analyzer = ModalitaMinimalAnalyzer()
    analyzer.run_complete_analysis()


if __name__ == "__main__":
    main()
