#!/usr/bin/env python3
"""
Test finale per debug delle route
"""

import requests
import json

def test_final_debug():
    """Test finale per debug"""
    
    print("🔍 TEST FINALE DEBUG")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test 1: Verifica connettività
    print("1️⃣ Test connettività...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   Homepage: {response.status_code} ({'✅' if response.status_code == 200 else '❌'})")
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
        return
    
    # Test 2: Test route semplificata
    print("\n2️⃣ Test route semplificata...")
    try:
        response = requests.post(
            f"{base_url}/api/get-processed-data-simple",
            json={"test": True},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Route semplificata funziona!")
            try:
                data = response.json()
                print(f"   📄 Risposta: {data.get('message', 'N/A')}")
            except:
                print("   📄 Risposta non JSON")
        elif response.status_code == 404:
            print("   ❌ Route semplificata NON registrata!")
        else:
            print(f"   ⚠️ Status inaspettato: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    # Test 3: Test route problematica
    print("\n3️⃣ Test route problematica...")
    try:
        response = requests.post(
            f"{base_url}/api/get-processed-data",
            json={"filename": "test.csv"},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Route problematica ora funziona!")
            try:
                data = response.json()
                print(f"   📄 Risposta: {data.get('success', 'N/A')}")
            except:
                print("   📄 Risposta non JSON")
        elif response.status_code == 404:
            print("   ❌ Route problematica ancora 404!")
        elif response.status_code == 400:
            print("   ⚠️ Route registrata ma errore 400 (normale per dati test)")
        else:
            print(f"   ⚠️ Status inaspettato: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    # Test 4: Test route di debug
    print("\n4️⃣ Test route di debug...")
    try:
        response = requests.post(
            f"{base_url}/api/test-debug",
            json={"test": True},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Route di debug funziona!")
        elif response.status_code == 404:
            print("   ❌ Route di debug NON registrata!")
        else:
            print(f"   ⚠️ Status inaspettato: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    # Test 5: Lista endpoint disponibili
    print("\n5️⃣ Test lista endpoint...")
    try:
        response = requests.get(f"{base_url}/api/endpoints", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and 'data' in data:
                endpoints = data['data']
                print(f"   ✅ Trovati {len(endpoints)} endpoint registrati")
                
                # Cerca endpoint che contengono "processed"
                processed_endpoints = [ep for ep in endpoints if 'processed' in ep.get('path', '').lower()]
                if processed_endpoints:
                    print("   📋 Endpoint con 'processed':")
                    for ep in processed_endpoints:
                        print(f"      - {ep.get('method', 'N/A')} {ep.get('path', 'N/A')}")
                else:
                    print("   ❌ Nessun endpoint con 'processed' trovato!")
                    
                # Cerca endpoint di debug
                debug_endpoints = [ep for ep in endpoints if 'debug' in ep.get('path', '').lower() or 'test' in ep.get('path', '').lower()]
                if debug_endpoints:
                    print("   📋 Endpoint di debug/test:")
                    for ep in debug_endpoints:
                        print(f"      - {ep.get('method', 'N/A')} {ep.get('path', 'N/A')}")
            else:
                print(f"   ⚠️ Risposta non valida: {data}")
        else:
            print(f"   ❌ Errore: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    print("\n" + "=" * 50)
    print("📊 RISULTATI FINALI")
    print("=" * 50)
    print("Se la route semplificata funziona ma quella completa no:")
    print("- Il problema è nella logica della funzione completa")
    print("- Potrebbe essere un errore di sintassi o variabile non definita")
    print("- Potrebbe essere un'eccezione durante la definizione")
    
    return True

if __name__ == "__main__":
    test_final_debug()
