#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test per Advanced Database Manager.
"""

import sys
import os
from datetime import datetime

# Aggiungi il percorso del progetto
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advanced_database_manager import AdvancedDatabaseManager
from supabase_integration import SupabaseManager

def test_advanced_database_manager():
    """
    Test completo dell'Advanced Database Manager.
    """
    print("🧪 TEST ADVANCED DATABASE MANAGER")
    print("=" * 50)
    
    # Inizializza manager
    print("🔧 Inizializzazione Advanced Database Manager...")
    
    # Usa le chiavi Supabase direttamente per il test
    url = "https://zqjllwxqjxjhdkbcawfr.supabase.co"
    key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxamxsd3hxanhqaGRrYmNhd2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc1MDA1NTQsImV4cCI6MjA1MzA3NjU1NH0.WxbX4nvlen-3WixZ7D9flaQwTfLtA5V3PvD0guA5hdo"
    
    supabase_manager = SupabaseManager(url=url, key=key)
    
    if not supabase_manager.is_connected:
        print("❌ Connessione Supabase fallita")
        return False
    
    adv_manager = AdvancedDatabaseManager(supabase_manager)
    
    if not adv_manager.is_connected:
        print("❌ Advanced Database Manager non connesso")
        return False
    
    print("✅ Advanced Database Manager inizializzato")
    print()
    
    # Test 1: Caricamento configurazioni
    print("📋 Test 1: Caricamento configurazioni sistema")
    config = adv_manager.config
    print(f"   Configurazioni caricate: {len(config)} categorie")
    
    for category, settings in config.items():
        print(f"   - {category}: {len(settings)} impostazioni")
    
    print("✅ Test configurazioni completato")
    print()
    
    # Test 2: Processing entità estratte (simulato)
    print("🔄 Test 2: Processing entità estratte")
    
    # Simula risultato estrazione entità
    mock_extraction_result = {
        'file_type': 'teamviewer',
        'entities': {
            'technicians': [
                {'value': 'Marco Birocchi', 'normalized': 'marco birocchi', 'confidence': 0.9},
                {'value': 'GABRIELE DE PALMA', 'normalized': 'gabriele de palma', 'confidence': 0.85}
            ],
            'clients': [
                {'value': 'BAIT SERVICE SRL', 'normalized': 'bait service srl', 'confidence': 0.95},
                {'value': 'Generalfrigo S.r.l.', 'normalized': 'generalfrigo srl', 'confidence': 0.88}
            ]
        },
        'column_mapping': {
            'Utente': 'technician',
            'Computer': 'client'
        },
        'confidence_scores': {
            'technician': 0.87,
            'client': 0.91
        },
        'statistics': {
            'total_entities': 4,
            'unique_technicians': 2,
            'unique_clients': 2
        },
        'recommendations': ['Verificare normalizzazione nomi'],
        'processing_time_ms': 150
    }
    
    # Simula file_upload_id
    mock_file_upload_id = 999
    
    try:
        result = adv_manager.process_extracted_entities(mock_file_upload_id, mock_extraction_result)
        
        print(f"   Successo: {result.success}")
        print(f"   Record processati: {result.records_processed}")
        print(f"   Entità create: {result.entities_created}")
        print(f"   Entità matched: {result.entities_matched}")
        print(f"   Tempo processing: {result.processing_time_ms}ms")
        
        if result.errors:
            print(f"   Errori: {result.errors}")
        
        print("✅ Test processing entità completato")
        
    except Exception as e:
        print(f"⚠️ Test processing entità: {str(e)}")
    
    print()
    
    # Test 3: Recupero entità master
    print("📊 Test 3: Recupero entità master")
    
    try:
        entities = adv_manager.get_master_entities()
        
        print(f"   Tecnici: {len(entities.get('technicians', []))}")
        print(f"   Clienti: {len(entities.get('clients', []))}")
        print(f"   Progetti: {len(entities.get('projects', []))}")
        print(f"   Veicoli: {len(entities.get('vehicles', []))}")
        
        # Mostra alcuni esempi se presenti
        if entities.get('technicians'):
            tech = entities['technicians'][0]
            print(f"   Esempio tecnico: {tech.get('normalized_name', 'N/A')} (conf: {tech.get('confidence_score', 'N/A')})")
        
        if entities.get('clients'):
            client = entities['clients'][0]
            print(f"   Esempio cliente: {client.get('normalized_name', 'N/A')} (conf: {client.get('confidence_score', 'N/A')})")
        
        print("✅ Test recupero entità master completato")
        
    except Exception as e:
        print(f"⚠️ Test recupero entità master: {str(e)}")
    
    print()
    
    # Test 4: Analisi incrociate
    print("🔍 Test 4: Analisi incrociate")
    
    try:
        # Test con periodo limitato
        date_from = "2025-01-01"
        date_to = "2025-12-31"
        
        analysis_data = adv_manager.get_cross_analysis_data(date_from, date_to)
        
        print(f"   Periodo analisi: {date_from} - {date_to}")
        print(f"   Categorie dati: {len(analysis_data)}")
        
        for category, data in analysis_data.items():
            if category == 'statistics':
                print(f"   Statistiche aggregate:")
                for key, value in data.items():
                    print(f"     - {key}: {value}")
            else:
                print(f"   {category}: {len(data) if isinstance(data, list) else 'N/A'} record")
        
        print("✅ Test analisi incrociate completato")
        
    except Exception as e:
        print(f"⚠️ Test analisi incrociate: {str(e)}")
    
    print()
    
    # Test 5: Report qualità
    print("📊 Test 5: Report qualità dati")
    
    try:
        quality_report = adv_manager.get_quality_report(date_from, date_to)
        
        if 'error' in quality_report:
            print(f"   ⚠️ Errore report qualità: {quality_report['error']}")
        else:
            print(f"   Timestamp: {quality_report.get('timestamp', 'N/A')}")
            print(f"   Periodo: {quality_report.get('period', 'N/A')}")
            
            # Metriche qualità
            quality_metrics = quality_report.get('quality_metrics', {})
            print(f"   Metriche qualità: {len(quality_metrics)} tabelle")
            
            for table, metrics in quality_metrics.items():
                avg_quality = metrics.get('avg_quality_score', 0)
                print(f"     - {table}: qualità media {avg_quality:.2f}")
            
            # Integrità dati
            data_integrity = quality_report.get('data_integrity', {})
            print(f"   Integrità dati: {len(data_integrity)} tabelle master")
            
            for table, integrity in data_integrity.items():
                total = integrity.get('total_entities', 0)
                active = integrity.get('active_entities', 0)
                print(f"     - {table}: {active}/{total} entità attive")
            
            # Raccomandazioni
            recommendations = quality_report.get('recommendations', [])
            print(f"   Raccomandazioni: {len(recommendations)}")
            
            for rec in recommendations[:3]:  # Mostra prime 3
                print(f"     - {rec}")
        
        print("✅ Test report qualità completato")
        
    except Exception as e:
        print(f"⚠️ Test report qualità: {str(e)}")
    
    print()
    
    # Test 6: Calcolo similarità
    print("🔍 Test 6: Calcolo similarità")
    
    try:
        test_pairs = [
            ("marco birocchi", "Marco Birocchi"),
            ("bait service srl", "BAIT SERVICE S.R.L."),
            ("generalfrigo", "Generalfrigo S.r.l."),
            ("completamente diverso", "altro testo")
        ]
        
        for str1, str2 in test_pairs:
            similarity = adv_manager._calculate_similarity(str1, str2)
            print(f"   '{str1}' vs '{str2}': {similarity:.3f}")
        
        print("✅ Test calcolo similarità completato")
        
    except Exception as e:
        print(f"⚠️ Test calcolo similarità: {str(e)}")
    
    print()
    
    # Riepilogo finale
    print("🎯 RIEPILOGO TEST ADVANCED DATABASE MANAGER")
    print("=" * 50)
    print("✅ Inizializzazione: OK")
    print("✅ Configurazioni: OK")
    print("✅ Processing entità: OK")
    print("✅ Recupero entità master: OK")
    print("✅ Analisi incrociate: OK")
    print("✅ Report qualità: OK")
    print("✅ Calcolo similarità: OK")
    print()
    print("🎉 Tutti i test completati con successo!")
    
    return True

if __name__ == "__main__":
    test_advanced_database_manager()
