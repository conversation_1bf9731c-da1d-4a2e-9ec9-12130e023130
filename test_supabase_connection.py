#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test di connessione Supabase per app-roberto
"""

import os
import sys

# Carica le variabili d'ambiente
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("⚠️ dotenv non installato")

# Test connessione Supabase
from supabase_integration import SupabaseManager

def test_supabase_connection():
    """Test della connessione Supabase"""
    print('🔧 Test Connessione Supabase')
    print('=' * 40)

    # Usa le chiavi aggiornate direttamente
    url = "https://zqjllwxqjxjhdkbcawfr.supabase.co"
    key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxamxsd3hxanhqaGRrYmNhd2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc1MDA1NTQsImV4cCI6MjA1MzA3NjU1NH0.WxbX4nvlen-3WixZ7D9flaQwTfLtA5V3PvD0guA5hdo"

    print(f'SUPABASE_URL: {url[:50]}...')
    print(f'SUPABASE_KEY: {key[:20]}...')
    print()

    # Test connessione con chiavi dirette
    manager = SupabaseManager(url=url, key=key)
    print(f'Connessione stabilita: {manager.is_connected}')

    if manager.is_connected:
        print('Test connessione database...')
        test_result = manager.test_connection()
        print(f'Test connessione: {"SUCCESSO" if test_result else "FALLITO"}')

        if test_result:
            print('✅ Connessione Supabase funzionante!')
            return True
        else:
            print('❌ Test connessione fallito')
            return False
    else:
        print('❌ Impossibile stabilire la connessione')
        return False

if __name__ == "__main__":
    test_supabase_connection()
