#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test per verificare che la correzione del riconoscimento timbrature funzioni.
"""

import pandas as pd
import sys
import os
from enhanced_file_detector import EnhancedFileDetector

def test_correzione_timbrature():
    """Testa la correzione per il riconoscimento dei file di timbrature."""
    
    print("🔧 TEST CORREZIONE RICONOSCIMENTO TIMBRATURE")
    print("=" * 55)
    
    # Crea un DataFrame di esempio che simula un file di timbrature
    timbrature_data = {
        'Data': ['2025-05-01', '2025-05-02', '2025-05-03'],
        'Dipendente': ['<PERSON>', '<PERSON>', '<PERSON>'],
        'Entrata': ['08:00', '08:15', '08:30'],
        'Uscita': ['17:00', '17:30', '17:15'],
        'Ore Lavorate': [8.0, 8.25, 7.75],
        'Straordinario': [0.0, 0.25, 0.0],
        'Note': ['', 'Riunione prolungata', '']
    }
    
    df = pd.DataFrame(timbrature_data)
    
    print("📊 DATI DI TEST (File Timbrature):")
    print(f"Dimensioni: {df.shape[0]} righe x {df.shape[1]} colonne")
    print(f"Colonne: {df.columns.tolist()}")
    print()
    
    # Test 1: Senza nome file (comportamento originale)
    print("🧪 TEST 1: Riconoscimento SENZA nome file")
    print("-" * 45)
    
    detector = EnhancedFileDetector()
    detected_type_1, confidence_1, scores_1 = detector.detect_file_type(df)
    
    print(f"Tipo rilevato: {detected_type_1}")
    print(f"Confidenza: {confidence_1:.3f}")
    print("Top 3 punteggi:")
    sorted_scores_1 = sorted(scores_1.items(), key=lambda x: x[1], reverse=True)[:3]
    for tipo, punteggio in sorted_scores_1:
        print(f"  {tipo}: {punteggio:.3f}")
    print()
    
    # Test 2: Con nome file di timbrature (comportamento corretto)
    print("🧪 TEST 2: Riconoscimento CON nome file di timbrature")
    print("-" * 45)
    
    filename_timbrature = "apprilevazionepresenze-timbrature-totali-base-2025-05-01-2025-05-31_2.xlsx"
    detected_type_2, confidence_2, scores_2 = detector.detect_file_type(df, filename_timbrature)
    
    print(f"Nome file: {filename_timbrature}")
    print(f"Tipo rilevato: {detected_type_2}")
    print(f"Confidenza: {confidence_2:.3f}")
    print("Top 3 punteggi:")
    sorted_scores_2 = sorted(scores_2.items(), key=lambda x: x[1], reverse=True)[:3]
    for tipo, punteggio in sorted_scores_2:
        print(f"  {tipo}: {punteggio:.3f}")
    print()
    
    # Test 3: Con nome file generico (comportamento normale)
    print("🧪 TEST 3: Riconoscimento CON nome file generico")
    print("-" * 45)
    
    filename_generico = "dati_export_2025.xlsx"
    detected_type_3, confidence_3, scores_3 = detector.detect_file_type(df, filename_generico)
    
    print(f"Nome file: {filename_generico}")
    print(f"Tipo rilevato: {detected_type_3}")
    print(f"Confidenza: {confidence_3:.3f}")
    print("Top 3 punteggi:")
    sorted_scores_3 = sorted(scores_3.items(), key=lambda x: x[1], reverse=True)[:3]
    for tipo, punteggio in sorted_scores_3:
        print(f"  {tipo}: {punteggio:.3f}")
    print()
    
    # Test 4: File con colonne diverse ma nome timbrature
    print("🧪 TEST 4: File con colonne NON di timbrature ma nome timbrature")
    print("-" * 45)
    
    # Crea un DataFrame che NON è di timbrature
    fake_data = {
        'Progetto': ['Progetto A', 'Progetto B', 'Progetto C'],
        'Cliente': ['Cliente A', 'Cliente B', 'Cliente C'],
        'Attività': ['Sviluppo', 'Testing', 'Deploy'],
        'Durata': [2.5, 1.5, 3.0]
    }
    
    df_fake = pd.DataFrame(fake_data)
    detected_type_4, confidence_4, scores_4 = detector.detect_file_type(df_fake, filename_timbrature)
    
    print(f"Colonne fake: {df_fake.columns.tolist()}")
    print(f"Tipo rilevato: {detected_type_4}")
    print(f"Confidenza: {confidence_4:.3f}")
    
    # Analisi risultati
    print("\n📋 ANALISI RISULTATI")
    print("=" * 55)
    
    print(f"Test 1 (senza filename): {detected_type_1} (confidenza: {confidence_1:.3f})")
    print(f"Test 2 (con filename timbrature): {detected_type_2} (confidenza: {confidence_2:.3f})")
    print(f"Test 3 (con filename generico): {detected_type_3} (confidenza: {confidence_3:.3f})")
    print(f"Test 4 (fake data + filename timbrature): {detected_type_4} (confidenza: {confidence_4:.3f})")
    print()
    
    # Verifica correzione
    success = True
    
    if detected_type_2 == "timbrature" and confidence_2 > 0.9:
        print("✅ CORREZIONE FUNZIONA!")
        print("   - Il file con nome 'apprilevazionepresenze-timbrature' viene riconosciuto come 'timbrature'")
        print(f"   - Confidenza alta: {confidence_2:.3f}")
        print("   - Il sistema ora riconosce correttamente i file di timbrature dal nome")
    else:
        print("❌ CORREZIONE NON FUNZIONA")
        print(f"   - Tipo rilevato: {detected_type_2} (dovrebbe essere 'timbrature')")
        print(f"   - Confidenza: {confidence_2:.3f} (dovrebbe essere > 0.9)")
        success = False
    
    # Verifica che non influenzi altri file
    if detected_type_3 != "timbrature" or confidence_3 < 0.9:
        print("✅ CORREZIONE NON INFLUENZA ALTRI FILE")
        print("   - File con nome generico non viene forzato come 'timbrature'")
    else:
        print("⚠️ CORREZIONE POTREBBE INFLUENZARE ALTRI FILE")
        success = False
    
    # Verifica intelligenza della correzione
    if detected_type_4 != "timbrature":
        print("✅ CORREZIONE È INTELLIGENTE")
        print("   - Non forza 'timbrature' se le colonne non corrispondono")
    else:
        print("⚠️ CORREZIONE TROPPO AGGRESSIVA")
        print("   - Forza 'timbrature' anche senza colonne appropriate")
        success = False
    
    return success

def test_pattern_timbrature():
    """Testa i pattern specifici per timbrature."""
    print("\n🧪 TEST PATTERN TIMBRATURE SPECIFICI")
    print("=" * 55)
    
    # Test vari tipi di file timbrature
    test_cases = [
        {
            'name': 'apprilevazionepresenze-timbrature-totali-base-2025.xlsx',
            'expected': 'timbrature',
            'description': 'File timbrature totali'
        },
        {
            'name': 'apprilevazionepresenze-timbrature-dettaglio-2025.xlsx', 
            'expected': 'timbrature',
            'description': 'File timbrature dettaglio'
        },
        {
            'name': 'timbrature-maggio-2025.xlsx',
            'expected': 'timbrature',
            'description': 'File timbrature generico'
        },
        {
            'name': 'apprilevazionepresenze-richieste-2025.xlsx',
            'expected': 'permessi',
            'description': 'File permessi (controllo non interferenza)'
        },
        {
            'name': 'export_attivita_2025.xlsx',
            'expected': 'attivita',
            'description': 'File attività (controllo non interferenza)'
        }
    ]
    
    detector = EnhancedFileDetector()
    
    # Dati timbrature standard
    timbrature_data = {
        'Data': ['2025-05-01', '2025-05-02'],
        'Dipendente': ['Mario Rossi', 'Luigi Verdi'],
        'Entrata': ['08:00', '08:15'],
        'Uscita': ['17:00', '17:30'],
        'Ore Lavorate': [8.0, 8.25]
    }
    df_timbrature = pd.DataFrame(timbrature_data)
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}: {test_case['description']}")
        print(f"   File: {test_case['name']}")
        
        detected_type, confidence, _ = detector.detect_file_type(df_timbrature, test_case['name'])
        
        print(f"   Rilevato: {detected_type} (confidenza: {confidence:.3f})")
        print(f"   Atteso: {test_case['expected']}")
        
        if detected_type == test_case['expected']:
            print("   ✅ SUCCESSO")
            success_count += 1
        else:
            print("   ❌ FALLIMENTO")
    
    print(f"\n📊 RISULTATO PATTERN TEST: {success_count}/{len(test_cases)} successi")
    
    return success_count == len(test_cases)

def main():
    """Esegue il test completo."""
    print("🚀 AVVIO TEST CORREZIONE TIMBRATURE")
    print("=" * 60)
    print()
    
    success1 = test_correzione_timbrature()
    success2 = test_pattern_timbrature()
    
    print("\n" + "=" * 60)
    print("🎯 RISULTATO FINALE")
    print("=" * 60)
    
    if success1 and success2:
        print("🎉 CORREZIONE IMPLEMENTATA CON SUCCESSO!")
        print("✅ Il sistema ora riconosce correttamente i file di timbrature")
        print("✅ La correzione è intelligente e non influenza altri file")
        print("✅ Il problema di classificazione errata è risolto")
        print("\n💡 Il file 'apprilevazionepresenze-timbrature-*' sarà ora")
        print("   classificato correttamente come 'timbrature' invece di 'attivita'")
    else:
        print("❌ CORREZIONE NON FUNZIONA CORRETTAMENTE")
        print("🔧 Necessarie ulteriori modifiche al sistema")
    
    return success1 and success2

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
