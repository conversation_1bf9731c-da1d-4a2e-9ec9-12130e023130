#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test per verificare che il warning COM sia risolto:
"Librerie COM non disponibili. Alcune funzionalità Excel potrebbero non funzionare."
"""

import sys
import os
import logging
import io
from datetime import datetime

def test_com_libraries():
    """Testa che le librerie COM siano disponibili."""
    print("🔧 TEST LIBRERIE COM")
    print("=" * 30)
    
    try:
        import win32com.client
        import pythoncom
        from win32com.client import constants as const
        
        print("✅ win32com.client importato con successo")
        print("✅ pythoncom importato con successo")
        print("✅ constants importato con successo")
        
        # Test creazione oggetto COM (senza aprire Excel)
        try:
            # Test se possiamo accedere al registry COM
            pythoncom.CoInitialize()
            print("✅ COM inizializzato correttamente")
            pythoncom.CoUninitialize()
            print("✅ COM deinitializzato correttamente")
        except Exception as e:
            print(f"⚠️ Warning COM init: {str(e)}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Errore import librerie COM: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Errore test COM: {str(e)}")
        return False

def test_excel_service_import():
    """Testa che ExcelService si importi senza warning."""
    print("\n📊 TEST EXCEL SERVICE")
    print("=" * 30)
    
    # Cattura i log per verificare l'assenza di warning
    log_capture = io.StringIO()
    handler = logging.StreamHandler(log_capture)
    handler.setLevel(logging.WARNING)
    
    # Configura il logger root per catturare i warning
    root_logger = logging.getLogger()
    original_level = root_logger.level
    root_logger.setLevel(logging.WARNING)
    root_logger.addHandler(handler)
    
    try:
        # Aggiungi il percorso del progetto
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        # Importa ExcelService
        from excel_service import ExcelService
        
        print("✅ ExcelService importato con successo")
        
        # Verifica che non ci siano warning nei log
        log_output = log_capture.getvalue()
        
        if "Librerie COM non disponibili" in log_output:
            print("❌ Warning COM ancora presente nei log")
            print(f"Log catturato: {log_output}")
            return False
        else:
            print("✅ Nessun warning COM nei log")
            print("✅ ExcelService caricato senza problemi")
            return True
            
    except ImportError as e:
        print(f"❌ Errore import ExcelService: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Errore test ExcelService: {str(e)}")
        return False
    finally:
        # Ripristina il logger
        root_logger.removeHandler(handler)
        root_logger.setLevel(original_level)
        handler.close()

def test_excel_functionality():
    """Testa le funzionalità Excel di base."""
    print("\n📈 TEST FUNZIONALITÀ EXCEL")
    print("=" * 30)
    
    try:
        # Test pandas con Excel
        import pandas as pd
        import openpyxl
        
        print("✅ pandas importato con successo")
        print("✅ openpyxl importato con successo")
        
        # Test creazione DataFrame e salvataggio Excel
        df = pd.DataFrame({
            'Nome': ['Test1', 'Test2', 'Test3'],
            'Valore': [10, 20, 30],
            'Data': pd.date_range('2025-01-01', periods=3)
        })
        
        # Test salvataggio Excel
        test_file = 'test_excel_com.xlsx'
        df.to_excel(test_file, index=False)
        print(f"✅ File Excel creato: {test_file}")
        
        # Test lettura Excel
        df_read = pd.read_excel(test_file)
        print(f"✅ File Excel letto correttamente ({len(df_read)} righe)")
        
        # Pulizia
        if os.path.exists(test_file):
            os.remove(test_file)
            print("✅ File test pulito")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore test Excel: {str(e)}")
        return False

def test_xlwings_availability():
    """Testa che xlwings sia disponibile per funzionalità avanzate."""
    print("\n🔗 TEST XLWINGS")
    print("=" * 30)
    
    try:
        import xlwings as xw
        print(f"✅ xlwings importato con successo")
        print(f"📦 Versione xlwings: {xw.__version__}")
        
        # Test configurazione xlwings (senza aprire Excel)
        print("✅ xlwings configurato per funzionalità avanzate Excel")
        return True
        
    except ImportError as e:
        print(f"❌ xlwings non disponibile: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Errore test xlwings: {str(e)}")
        return False

def main():
    """Esegue tutti i test per verificare la risoluzione del warning COM."""
    print("🧪 TEST RISOLUZIONE WARNING COM EXCEL")
    print("=" * 50)
    print(f"📅 Data: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test 1: Librerie COM
    com_ok = test_com_libraries()
    
    # Test 2: ExcelService senza warning
    excel_service_ok = test_excel_service_import()
    
    # Test 3: Funzionalità Excel
    excel_func_ok = test_excel_functionality()
    
    # Test 4: xlwings
    xlwings_ok = test_xlwings_availability()
    
    # Risultato finale
    print("\n" + "=" * 50)
    print("📋 RISULTATO FINALE")
    print("=" * 50)
    
    tests_passed = sum([com_ok, excel_service_ok, excel_func_ok, xlwings_ok])
    total_tests = 4
    
    if com_ok:
        print("✅ LIBRERIE COM: Disponibili e funzionanti")
    else:
        print("❌ LIBRERIE COM: Non disponibili")
    
    if excel_service_ok:
        print("✅ EXCEL SERVICE: Importato senza warning")
    else:
        print("❌ EXCEL SERVICE: Warning ancora presente")
    
    if excel_func_ok:
        print("✅ FUNZIONALITÀ EXCEL: Lettura/scrittura operativa")
    else:
        print("❌ FUNZIONALITÀ EXCEL: Problemi rilevati")
    
    if xlwings_ok:
        print("✅ XLWINGS: Disponibile per funzionalità avanzate")
    else:
        print("⚠️ XLWINGS: Non disponibile (opzionale)")
    
    print(f"\n🎯 RISULTATO: {tests_passed}/{total_tests} test superati")
    
    if com_ok and excel_service_ok:
        print("🎉 WARNING COM RISOLTO!")
        print("✅ 'Librerie COM non disponibili' non dovrebbe più apparire")
        print("✅ Funzionalità Excel completamente operative")
        print("✅ ExcelService caricato senza problemi")
        return True
    else:
        print("⚠️ Warning COM potrebbe persistere")
        print("💡 Verificare installazione pywin32 e presenza di Excel")
        return False

if __name__ == "__main__":
    success = main()
    
    # Salva risultati
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f'test_warning_com_results_{timestamp}.txt'
    
    with open(results_file, 'w', encoding='utf-8') as f:
        f.write(f"Test Warning COM - {timestamp}\n")
        f.write(f"Risultato: {'SUCCESS' if success else 'FAIL'}\n")
        f.write("Warning testato: 'Librerie COM non disponibili'\n")
        f.write("Librerie installate: pywin32, xlwings\n")
    
    print(f"\n📄 Risultati salvati in: {results_file}")
    
    exit(0 if success else 1)
