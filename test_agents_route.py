#!/usr/bin/env python3
"""
Test specifico per la route degli agenti
"""

import requests
import json

def test_agents_route():
    """Test route agenti"""
    
    print("🤖 TEST ROUTE AGENTI AI")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test 1: Verifica connettività
    print("1️⃣ Test connettività...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   Homepage: {response.status_code} ({'✅' if response.status_code == 200 else '❌'})")
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
        return
    
    # Test 2: Test route principale agenti
    print("\n2️⃣ Test route principale /agents...")
    try:
        response = requests.get(f"{base_url}/agents", timeout=5, allow_redirects=False)
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 302:
            print("   ✅ Redirect corretto (302)")
            redirect_location = response.headers.get('Location', '')
            print(f"   📍 Redirect a: {redirect_location}")
        elif response.status_code == 200:
            print("   ✅ Pagina agenti accessibile direttamente")
        elif response.status_code == 404:
            print("   ❌ Route agenti NON trovata!")
        else:
            print(f"   ⚠️ Status inaspettato: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    # Test 3: Test route dashboard agenti
    print("\n3️⃣ Test route dashboard agenti...")
    try:
        response = requests.get(f"{base_url}/agents/dashboard", timeout=5)
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Dashboard agenti accessibile")
        elif response.status_code == 404:
            print("   ❌ Dashboard agenti NON trovata!")
        else:
            print(f"   ⚠️ Status inaspettato: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    # Test 4: Test API agenti
    print("\n4️⃣ Test API agenti...")
    
    api_endpoints = [
        ("GET", "/agents/list", "Lista agenti"),
        ("GET", "/api/agents/status", "Status agenti"),
    ]
    
    for method, path, description in api_endpoints:
        try:
            print(f"   Test {method} {path} ({description})...")
            
            if method == "GET":
                response = requests.get(f"{base_url}{path}", timeout=5)
            else:
                response = requests.post(
                    f"{base_url}{path}",
                    json={"test": True},
                    headers={"Content-Type": "application/json"},
                    timeout=5
                )
            
            if response.status_code == 200:
                print(f"      ✅ Status: {response.status_code}")
                try:
                    data = response.json()
                    if 'agents' in data:
                        print(f"      📋 Agenti trovati: {len(data['agents'])}")
                    elif 'success' in data:
                        print(f"      📄 Success: {data['success']}")
                except:
                    print("      📄 Risposta non JSON")
            elif response.status_code == 404:
                print(f"      ❌ Status: {response.status_code} - Endpoint non trovato")
            elif response.status_code == 503:
                print(f"      ⚠️ Status: {response.status_code} - Servizio non disponibile")
            else:
                print(f"      ⚠️ Status: {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ Errore: {str(e)}")
    
    # Test 5: Test con redirect seguiti
    print("\n5️⃣ Test con redirect seguiti...")
    try:
        response = requests.get(f"{base_url}/agents", timeout=5, allow_redirects=True)
        
        print(f"   Status finale: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Pagina agenti raggiunta con successo")
            # Controlla se è una pagina HTML valida
            if 'html' in response.headers.get('content-type', '').lower():
                print("   📄 Contenuto HTML valido")
                if 'agenti' in response.text.lower() or 'agent' in response.text.lower():
                    print("   🤖 Contenuto agenti rilevato")
        else:
            print(f"   ❌ Errore finale: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    print("\n" + "=" * 50)
    print("📊 RISULTATI")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    test_agents_route()
