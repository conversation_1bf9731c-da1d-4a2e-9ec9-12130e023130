/**
 * UI/UX Enhancements - App Roberto
 * Sistema di miglioramenti interfaccia utente e esperienza utente
 * Versione: 1.0.0 - FASE 5 Task 5.2
 */

console.log('🎨 UI/UX Enhancements v1.0.0 caricato');

// ===== GESTIONE PERFORMANCE E ACCESSIBILITÀ =====

/**
 * Gestisce le preferenze di movimento ridotto
 */
function handleReducedMotion() {
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    function updateMotionPreference(mediaQuery) {
        if (mediaQuery.matches) {
            document.documentElement.style.setProperty('--animation-duration', '0.01ms');
            document.documentElement.style.setProperty('--transition-duration', '0.01ms');
            console.log('🔧 Animazioni ridotte per accessibilità');
        } else {
            document.documentElement.style.removeProperty('--animation-duration');
            document.documentElement.style.removeProperty('--transition-duration');
        }
    }
    
    // Applica immediatamente
    updateMotionPreference(prefersReducedMotion);
    
    // Ascolta i cambiamenti
    prefersReducedMotion.addListener(updateMotionPreference);
}

/**
 * Gestisce il lazy loading delle immagini
 */
function initLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

/**
 * Gestisce il preloading delle risorse critiche
 */
function preloadCriticalResources() {
    const criticalResources = [
        '/static/css/style.css',
        '/static/css/dark-theme.css',
        '/static/js/theme-manager.js'
    ];
    
    criticalResources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = resource.endsWith('.css') ? 'style' : 'script';
        link.href = resource;
        document.head.appendChild(link);
    });
}

// ===== COMPONENTI UI INTERATTIVI =====

/**
 * Inizializza tooltips personalizzati
 */
function initCustomTooltips() {
    document.querySelectorAll('[data-tooltip]').forEach(element => {
        element.classList.add('tooltip-custom');
        
        // Gestisce il posizionamento dinamico
        element.addEventListener('mouseenter', function() {
            const tooltip = this.querySelector('::after');
            if (tooltip) {
                const rect = this.getBoundingClientRect();
                const tooltipRect = tooltip.getBoundingClientRect();
                
                // Aggiusta posizione se esce dallo schermo
                if (rect.left + tooltipRect.width > window.innerWidth) {
                    this.style.setProperty('--tooltip-position', 'right');
                }
            }
        });
    });
}

/**
 * Gestisce gli stati di loading
 */
class LoadingManager {
    static show(element, text = 'Caricamento...') {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (!element) return;
        
        const loadingHTML = `
            <div class="loading-overlay d-flex align-items-center justify-content-center">
                <div class="text-center">
                    <div class="loading-spinner mb-2"></div>
                    <div class="loading-text">${text}</div>
                </div>
            </div>
        `;
        
        element.style.position = 'relative';
        element.insertAdjacentHTML('beforeend', loadingHTML);
        
        // Aggiungi stili per l'overlay
        const overlay = element.querySelector('.loading-overlay');
        Object.assign(overlay.style, {
            position: 'absolute',
            top: '0',
            left: '0',
            right: '0',
            bottom: '0',
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            zIndex: '1000',
            borderRadius: 'inherit'
        });
    }
    
    static hide(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (!element) return;
        
        const overlay = element.querySelector('.loading-overlay');
        if (overlay) {
            overlay.remove();
        }
    }
}

/**
 * Gestisce le notifiche toast
 */
class ToastManager {
    static show(message, type = 'info', duration = 5000) {
        const toastContainer = this.getOrCreateContainer();
        
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');
        
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${this.getIcon(type)} ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" 
                        data-bs-dismiss="toast" aria-label="Chiudi"></button>
            </div>
        `;
        
        toastContainer.appendChild(toast);
        
        // Inizializza Bootstrap toast
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: duration
        });
        
        bsToast.show();
        
        // Rimuovi dopo la chiusura
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
        
        return bsToast;
    }
    
    static getOrCreateContainer() {
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            container.style.zIndex = '1055';
            document.body.appendChild(container);
        }
        return container;
    }
    
    static getIcon(type) {
        const icons = {
            success: '<i class="fas fa-check-circle me-2"></i>',
            error: '<i class="fas fa-exclamation-circle me-2"></i>',
            warning: '<i class="fas fa-exclamation-triangle me-2"></i>',
            info: '<i class="fas fa-info-circle me-2"></i>'
        };
        return icons[type] || icons.info;
    }
}

/**
 * Gestisce gli effetti di parallax leggeri
 */
function initParallaxEffects() {
    const parallaxElements = document.querySelectorAll('[data-parallax]');
    
    if (parallaxElements.length === 0) return;
    
    function updateParallax() {
        const scrollTop = window.pageYOffset;
        
        parallaxElements.forEach(element => {
            const speed = parseFloat(element.dataset.parallax) || 0.5;
            const yPos = -(scrollTop * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
    }
    
    // Throttle per performance
    let ticking = false;
    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
        }
    }
    
    window.addEventListener('scroll', () => {
        requestTick();
        ticking = false;
    });
}

/**
 * Gestisce l'auto-resize delle textarea
 */
function initAutoResizeTextareas() {
    document.querySelectorAll('textarea[data-auto-resize]').forEach(textarea => {
        function resize() {
            textarea.style.height = 'auto';
            textarea.style.height = textarea.scrollHeight + 'px';
        }
        
        textarea.addEventListener('input', resize);
        textarea.addEventListener('change', resize);
        
        // Resize iniziale
        resize();
    });
}

// ===== INIZIALIZZAZIONE =====

/**
 * Inizializza tutti i miglioramenti UI/UX
 */
function initUIEnhancements() {
    console.log('🚀 Inizializzazione UI/UX Enhancements...');
    
    // Performance e accessibilità
    handleReducedMotion();
    initLazyLoading();
    preloadCriticalResources();
    
    // Componenti interattivi
    initCustomTooltips();
    initParallaxEffects();
    initAutoResizeTextareas();
    
    // Esponi utilità globalmente
    window.LoadingManager = LoadingManager;
    window.ToastManager = ToastManager;
    
    console.log('✅ UI/UX Enhancements inizializzati');
}

// Inizializza quando il DOM è pronto
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initUIEnhancements);
} else {
    initUIEnhancements();
}

// Esporta per uso in altri moduli
window.UIEnhancements = {
    LoadingManager,
    ToastManager,
    initCustomTooltips,
    initParallaxEffects,
    initAutoResizeTextareas
};
