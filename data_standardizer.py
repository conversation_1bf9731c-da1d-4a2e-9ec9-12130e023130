#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Data Standardizer per la normalizzazione e standardizzazione intelligente dei dati.
Gestisce formati date/ora, nomi persone, aziende, durate e deduplicazione.
"""

import pandas as pd
import logging
import re
from typing import Dict, List, Tuple, Any, Optional, Set
from datetime import datetime, timedelta
import numpy as np
from difflib import SequenceMatcher
import unicodedata

# Configurazione del logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataStandardizer:
    """
    Standardizzatore intelligente di dati che normalizza:
    - Formati data/ora
    - Nomi persone (maiuscole/minuscole, abbreviazioni)
    - Nomi aziende (ragioni sociali, abbreviazioni)
    - Durate (conversione in formato standard)
    - Deduplicazione intelligente
    """

    def __init__(self):
        """
        Inizializza il standardizzatore con regole e pattern di normalizzazione.
        """
        # Pattern per date in vari formati
        self.DATE_PATTERNS = [
            (r'(\d{4})-(\d{1,2})-(\d{1,2})', '%Y-%m-%d'),           # 2025-05-25
            (r'(\d{1,2})/(\d{1,2})/(\d{4})', '%d/%m/%Y'),           # 25/05/2025
            (r'(\d{1,2})-(\d{1,2})-(\d{4})', '%d-%m-%Y'),           # 25-05-2025
            (r'(\d{1,2})\.(\d{1,2})\.(\d{4})', '%d.%m.%Y'),         # 25.05.2025
            (r'(\d{4})/(\d{1,2})/(\d{1,2})', '%Y/%m/%d'),           # 2025/05/25
        ]

        # Pattern per orari
        self.TIME_PATTERNS = [
            (r'(\d{1,2}):(\d{2}):(\d{2})', '%H:%M:%S'),             # 14:30:00
            (r'(\d{1,2}):(\d{2})', '%H:%M'),                        # 14:30
            (r'(\d{1,2})\.(\d{2})', '%H.%M'),                       # 14.30
        ]

        # Pattern per durate
        self.DURATION_PATTERNS = [
            (r'(\d+)h\s*(\d*)m?', 'hours_minutes'),                 # 2h 30m
            (r'(\d+)[hH]', 'hours_only'),                           # 2h
            (r'(\d+)[mM]', 'minutes_only'),                         # 30m
            (r'(\d+)[,\.](\d+)', 'decimal_hours'),                  # 2,5 o 2.5
            (r'^(\d+)$', 'minutes_number'),                         # 120 (assumiamo minuti)
        ]

        # Suffissi aziendali comuni
        self.COMPANY_SUFFIXES = [
            'SRL', 'S.R.L.', 'SPA', 'S.P.A.', 'SNC', 'S.N.C.',
            'SAS', 'S.A.S.', 'COOP', 'COOPERATIVA', 'ONLUS',
            'ASSOCIAZIONE', 'FONDAZIONE', 'ENTE', 'COMUNE',
            'PROVINCIA', 'REGIONE', 'STUDIO', 'UFFICIO'
        ]

        # Prefissi e titoli da rimuovere dai nomi
        self.NAME_PREFIXES = [
            'DR.', 'DOTT.', 'DOTTORE', 'DOTTORESSA', 'ING.', 'INGEGNERE',
            'PROF.', 'PROFESSORE', 'PROFESSORESSA', 'SIG.', 'SIGNORE',
            'SIG.RA', 'SIGNORA', 'SIGNORINA', 'AVV.', 'AVVOCATO'
        ]

        # Soglie per deduplicazione
        self.SIMILARITY_THRESHOLD = 0.85
        self.FUZZY_THRESHOLD = 0.80

        # Cache per performance
        self._standardization_cache = {}

    def standardize_data(self, df: pd.DataFrame, file_type: str, entity_mapping: Dict[str, str]) -> Dict[str, Any]:
        """
        Standardizza tutti i dati in un DataFrame.

        Args:
            df: DataFrame da standardizzare
            file_type: Tipo di file per ottimizzazioni specifiche
            entity_mapping: Mappatura colonne -> tipi entità

        Returns:
            Dizionario con dati standardizzati e metadati
        """
        logger.info(f"🔧 Standardizzazione dati per file tipo: {file_type}")

        result = {
            "file_type": file_type,
            "standardization_timestamp": datetime.now().isoformat(),
            "original_shape": df.shape,
            "standardized_data": df.copy(),
            "standardization_log": [],
            "entity_mapping": entity_mapping,
            "quality_metrics": {},
            "recommendations": []
        }

        if df is None or df.empty:
            result["error"] = "DataFrame vuoto"
            return result

        try:
            # Standardizza ogni colonna basandosi sul tipo di entità
            for col_name, entity_type in entity_mapping.items():
                if col_name in df.columns:
                    standardized_column, log_entries = self._standardize_column(
                        df[col_name], entity_type, col_name
                    )
                    result["standardized_data"][col_name] = standardized_column
                    result["standardization_log"].extend(log_entries)

            # Standardizza colonne non mappate con euristica
            unmapped_columns = set(df.columns) - set(entity_mapping.keys())
            for col_name in unmapped_columns:
                inferred_type = self._infer_column_type(df[col_name])
                if inferred_type != "unknown":
                    standardized_column, log_entries = self._standardize_column(
                        df[col_name], inferred_type, col_name
                    )
                    result["standardized_data"][col_name] = standardized_column
                    result["standardization_log"].extend(log_entries)

            # Calcola metriche di qualità
            result["quality_metrics"] = self._calculate_quality_metrics(
                df, result["standardized_data"], result["standardization_log"]
            )

            # Genera raccomandazioni
            result["recommendations"] = self._generate_standardization_recommendations(result)

            logger.info(f"✅ Standardizzazione completata: {len(result['standardization_log'])} operazioni")

        except Exception as e:
            logger.error(f"❌ Errore nella standardizzazione: {str(e)}")
            result["error"] = str(e)

        return result

    def _standardize_column(self, series: pd.Series, entity_type: str, col_name: str) -> Tuple[pd.Series, List[Dict]]:
        """
        Standardizza una singola colonna basandosi sul tipo di entità.

        Args:
            series: Serie da standardizzare
            entity_type: Tipo di entità (technician, client, date, etc.)
            col_name: Nome della colonna

        Returns:
            Tuple (serie_standardizzata, log_operazioni)
        """
        log_entries = []
        standardized_series = series.copy()

        # Applica standardizzazione specifica per tipo
        if entity_type == "technician":
            standardized_series, entries = self._standardize_person_names(series, col_name)
            log_entries.extend(entries)

        elif entity_type == "client":
            standardized_series, entries = self._standardize_company_names(series, col_name)
            log_entries.extend(entries)

        elif entity_type == "date":
            standardized_series, entries = self._standardize_dates(series, col_name)
            log_entries.extend(entries)

        elif entity_type == "time":
            standardized_series, entries = self._standardize_times(series, col_name)
            log_entries.extend(entries)

        elif entity_type == "duration":
            standardized_series, entries = self._standardize_durations(series, col_name)
            log_entries.extend(entries)

        elif entity_type == "project":
            standardized_series, entries = self._standardize_project_codes(series, col_name)
            log_entries.extend(entries)

        # Deduplicazione intelligente
        if entity_type in ["technician", "client", "project"]:
            deduplicated_series, dedup_entries = self._deduplicate_values(standardized_series, col_name)
            standardized_series = deduplicated_series
            log_entries.extend(dedup_entries)

        return standardized_series, log_entries

    def _standardize_person_names(self, series: pd.Series, col_name: str) -> Tuple[pd.Series, List[Dict]]:
        """Standardizza nomi di persone."""
        log_entries = []
        standardized = series.copy()

        for idx, value in series.dropna().items():
            original_value = str(value).strip()
            if not original_value:
                continue

            # Rimuovi prefissi/titoli
            cleaned_name = self._remove_name_prefixes(original_value)

            # Normalizza capitalizzazione
            normalized_name = self._normalize_person_name_case(cleaned_name)

            # Rimuovi caratteri speciali extra
            final_name = re.sub(r'[^\w\s\'-]', '', normalized_name).strip()
            final_name = re.sub(r'\s+', ' ', final_name)

            if final_name != original_value:
                standardized.iloc[idx] = final_name
                log_entries.append({
                    "column": col_name,
                    "row": int(idx),
                    "operation": "person_name_standardization",
                    "original": original_value,
                    "standardized": final_name,
                    "changes": self._describe_name_changes(original_value, final_name)
                })

        return standardized, log_entries

    def _standardize_company_names(self, series: pd.Series, col_name: str) -> Tuple[pd.Series, List[Dict]]:
        """Standardizza nomi di aziende."""
        log_entries = []
        standardized = series.copy()

        for idx, value in series.dropna().items():
            original_value = str(value).strip()
            if not original_value:
                continue

            # Normalizza maiuscole/minuscole
            normalized_company = self._normalize_company_name_case(original_value)

            # Standardizza suffissi societari
            standardized_company = self._standardize_company_suffixes(normalized_company)

            # Rimuovi caratteri speciali extra
            final_company = re.sub(r'[^\w\s\.\-&]', '', standardized_company).strip()
            final_company = re.sub(r'\s+', ' ', final_company)

            if final_company != original_value:
                standardized.iloc[idx] = final_company
                log_entries.append({
                    "column": col_name,
                    "row": int(idx),
                    "operation": "company_name_standardization",
                    "original": original_value,
                    "standardized": final_company,
                    "changes": self._describe_company_changes(original_value, final_company)
                })

        return standardized, log_entries

    def _standardize_dates(self, series: pd.Series, col_name: str) -> Tuple[pd.Series, List[Dict]]:
        """Standardizza date in formato ISO."""
        log_entries = []
        standardized = series.copy()

        for idx, value in series.dropna().items():
            original_value = str(value).strip()
            if not original_value:
                continue

            # Prova a parsare la data
            parsed_date = self._parse_date(original_value)

            if parsed_date:
                iso_date = parsed_date.strftime('%Y-%m-%d')
                if iso_date != original_value:
                    standardized.iloc[idx] = iso_date
                    log_entries.append({
                        "column": col_name,
                        "row": int(idx),
                        "operation": "date_standardization",
                        "original": original_value,
                        "standardized": iso_date,
                        "parsed_date": parsed_date.isoformat()
                    })

        return standardized, log_entries

    def _standardize_durations(self, series: pd.Series, col_name: str) -> Tuple[pd.Series, List[Dict]]:
        """Standardizza durate in minuti."""
        log_entries = []
        standardized = series.copy()

        for idx, value in series.dropna().items():
            original_value = str(value).strip()
            if not original_value:
                continue

            # Converte durata in minuti
            minutes = self._parse_duration_to_minutes(original_value)

            if minutes is not None:
                # Formato standard: "Xh Ym" o solo "Xm"
                if minutes >= 60:
                    hours = minutes // 60
                    remaining_minutes = minutes % 60
                    if remaining_minutes > 0:
                        standard_format = f"{hours}h {remaining_minutes}m"
                    else:
                        standard_format = f"{hours}h"
                else:
                    standard_format = f"{minutes}m"

                if standard_format != original_value:
                    standardized.iloc[idx] = standard_format
                    log_entries.append({
                        "column": col_name,
                        "row": int(idx),
                        "operation": "duration_standardization",
                        "original": original_value,
                        "standardized": standard_format,
                        "total_minutes": minutes
                    })

        return standardized, log_entries

    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """Prova a parsare una data da vari formati."""
        cache_key = f"date_{date_str}"
        if cache_key in self._standardization_cache:
            return self._standardization_cache[cache_key]

        for pattern, format_str in self.DATE_PATTERNS:
            try:
                match = re.match(pattern, date_str.strip())
                if match:
                    parsed_date = datetime.strptime(date_str.strip(), format_str)
                    self._standardization_cache[cache_key] = parsed_date
                    return parsed_date
            except (ValueError, AttributeError):
                continue

        # Prova parsing automatico pandas
        try:
            parsed_date = pd.to_datetime(date_str, dayfirst=True)
            if not pd.isna(parsed_date):
                self._standardization_cache[cache_key] = parsed_date.to_pydatetime()
                return parsed_date.to_pydatetime()
        except:
            pass

        self._standardization_cache[cache_key] = None
        return None

    def _parse_duration_to_minutes(self, duration_str: str) -> Optional[int]:
        """Converte stringa durata in minuti."""
        cache_key = f"duration_{duration_str}"
        if cache_key in self._standardization_cache:
            return self._standardization_cache[cache_key]

        duration_str = duration_str.strip().lower()

        for pattern, pattern_type in self.DURATION_PATTERNS:
            match = re.match(pattern, duration_str)
            if match:
                try:
                    if pattern_type == 'hours_minutes':
                        hours = int(match.group(1))
                        minutes = int(match.group(2)) if match.group(2) else 0
                        total_minutes = hours * 60 + minutes
                    elif pattern_type == 'hours_only':
                        total_minutes = int(match.group(1)) * 60
                    elif pattern_type == 'minutes_only':
                        total_minutes = int(match.group(1))
                    elif pattern_type == 'decimal_hours':
                        decimal_hours = float(f"{match.group(1)}.{match.group(2)}")
                        total_minutes = int(decimal_hours * 60)
                    elif pattern_type == 'minutes_number':
                        total_minutes = int(match.group(1))
                    else:
                        continue

                    self._standardization_cache[cache_key] = total_minutes
                    return total_minutes
                except (ValueError, IndexError):
                    continue

        self._standardization_cache[cache_key] = None
        return None

    def _standardize_times(self, series: pd.Series, col_name: str) -> Tuple[pd.Series, List[Dict]]:
        """Standardizza orari in formato HH:MM."""
        log_entries = []
        standardized = series.copy()

        for idx, value in series.dropna().items():
            original_value = str(value).strip()
            if not original_value:
                continue

            # Prova a parsare l'orario
            parsed_time = self._parse_time(original_value)

            if parsed_time:
                standard_time = parsed_time.strftime('%H:%M')
                if standard_time != original_value:
                    standardized.iloc[idx] = standard_time
                    log_entries.append({
                        "column": col_name,
                        "row": int(idx),
                        "operation": "time_standardization",
                        "original": original_value,
                        "standardized": standard_time
                    })

        return standardized, log_entries

    def _parse_time(self, time_str: str) -> Optional[datetime]:
        """Prova a parsare un orario da vari formati."""
        for pattern, format_str in self.TIME_PATTERNS:
            try:
                match = re.match(pattern, time_str.strip())
                if match:
                    parsed_time = datetime.strptime(time_str.strip(), format_str)
                    return parsed_time
            except (ValueError, AttributeError):
                continue
        return None

    def _standardize_project_codes(self, series: pd.Series, col_name: str) -> Tuple[pd.Series, List[Dict]]:
        """Standardizza codici progetto."""
        log_entries = []
        standardized = series.copy()

        for idx, value in series.dropna().items():
            original_value = str(value).strip()
            if not original_value:
                continue

            # Normalizza formato codice progetto
            standardized_code = self._normalize_project_code(original_value)

            if standardized_code != original_value:
                standardized.iloc[idx] = standardized_code
                log_entries.append({
                    "column": col_name,
                    "row": int(idx),
                    "operation": "project_code_standardization",
                    "original": original_value,
                    "standardized": standardized_code
                })

        return standardized, log_entries

    def _normalize_project_code(self, code: str) -> str:
        """Normalizza un codice progetto."""
        # Rimuovi spazi e converti in maiuscolo
        normalized = re.sub(r'\s+', '', code.upper())

        # Pattern standard: P25-123
        if re.match(r'^[A-Z]\d{2}-?\d{3}$', normalized):
            if '-' not in normalized and len(normalized) == 6:
                # Aggiungi trattino: P25123 -> P25-123
                normalized = f"{normalized[:3]}-{normalized[3:]}"

        return normalized

    def _remove_name_prefixes(self, name: str) -> str:
        """Rimuove prefissi e titoli dai nomi."""
        name_upper = name.upper()
        for prefix in self.NAME_PREFIXES:
            if name_upper.startswith(prefix):
                name = name[len(prefix):].strip()
                break
        return name

    def _normalize_person_name_case(self, name: str) -> str:
        """Normalizza maiuscole/minuscole per nomi di persone."""
        # Dividi in parole e capitalizza ogni parola
        words = name.split()
        normalized_words = []

        for word in words:
            # Gestisci apostrofi (es. D'Angelo)
            if "'" in word:
                parts = word.split("'")
                normalized_parts = [part.capitalize() for part in parts]
                normalized_words.append("'".join(normalized_parts))
            else:
                normalized_words.append(word.capitalize())

        return " ".join(normalized_words)

    def _normalize_company_name_case(self, company: str) -> str:
        """Normalizza maiuscole/minuscole per nomi aziende."""
        # Le aziende spesso sono in maiuscolo
        return company.upper()

    def _standardize_company_suffixes(self, company: str) -> str:
        """Standardizza suffissi societari."""
        company_upper = company.upper()

        # Mappa suffissi non standard a standard
        suffix_mapping = {
            'S.R.L.': 'SRL',
            'S.P.A.': 'SPA',
            'S.N.C.': 'SNC',
            'S.A.S.': 'SAS'
        }

        for old_suffix, new_suffix in suffix_mapping.items():
            if company_upper.endswith(old_suffix):
                return company_upper[:-len(old_suffix)] + new_suffix

        return company_upper

    def _describe_name_changes(self, original: str, standardized: str) -> List[str]:
        """Descrive i cambiamenti apportati a un nome."""
        changes = []

        if original.lower() != standardized.lower():
            changes.append("case_normalization")

        if len(original.split()) != len(standardized.split()):
            changes.append("word_count_change")

        if any(char in original for char in ['DR.', 'DOTT.', 'ING.', 'SIG.']):
            changes.append("prefix_removal")

        return changes

    def _describe_company_changes(self, original: str, standardized: str) -> List[str]:
        """Descrive i cambiamenti apportati a un nome azienda."""
        changes = []

        if original != original.upper():
            changes.append("uppercase_conversion")

        if any(suffix in original for suffix in ['S.R.L.', 'S.P.A.']):
            changes.append("suffix_standardization")

        return changes

    def _infer_column_type(self, series: pd.Series) -> str:
        """Inferisce il tipo di una colonna non mappata."""
        sample_values = series.dropna().head(10).astype(str)

        # Conta pattern matches per ogni tipo
        type_scores = {
            "date": 0,
            "time": 0,
            "duration": 0,
            "technician": 0,
            "client": 0
        }

        for value in sample_values:
            value_str = str(value).strip()

            # Test date patterns
            if self._parse_date(value_str):
                type_scores["date"] += 1

            # Test duration patterns
            if self._parse_duration_to_minutes(value_str):
                type_scores["duration"] += 1

            # Test person name patterns
            if re.match(r'^[A-Z][a-z]+ [A-Z][a-z]+$', value_str):
                type_scores["technician"] += 1

            # Test company patterns
            if any(suffix in value_str.upper() for suffix in self.COMPANY_SUFFIXES):
                type_scores["client"] += 1

        # Restituisci il tipo con punteggio più alto
        best_type = max(type_scores.items(), key=lambda x: x[1])
        return best_type[0] if best_type[1] > 0 else "unknown"

    def _deduplicate_values(self, series: pd.Series, col_name: str) -> Tuple[pd.Series, List[Dict]]:
        """Deduplicazione intelligente dei valori."""
        log_entries = []
        deduplicated = series.copy()

        # Raggruppa valori simili
        value_groups = self._group_similar_values(series.dropna().unique())

        # Per ogni gruppo, usa il valore più frequente come standard
        for group in value_groups:
            if len(group) > 1:
                # Trova il valore più frequente nel gruppo
                group_counts = series.value_counts()
                most_frequent = max(group, key=lambda x: group_counts.get(x, 0))

                # Sostituisci tutti i valori del gruppo con il più frequente
                for value in group:
                    if value != most_frequent:
                        mask = series == value
                        deduplicated[mask] = most_frequent

                        log_entries.append({
                            "column": col_name,
                            "operation": "deduplication",
                            "original": value,
                            "standardized": most_frequent,
                            "similarity_group": group,
                            "reason": "similar_values_merged"
                        })

        return deduplicated, log_entries

    def _group_similar_values(self, values: List[str]) -> List[List[str]]:
        """Raggruppa valori simili per deduplicazione."""
        groups = []
        processed = set()

        for value in values:
            if value in processed:
                continue

            # Trova valori simili
            similar_group = [value]
            for other_value in values:
                if other_value != value and other_value not in processed:
                    similarity = SequenceMatcher(None, value.lower(), other_value.lower()).ratio()
                    if similarity >= self.SIMILARITY_THRESHOLD:
                        similar_group.append(other_value)

            # Aggiungi gruppo se ha più di un elemento
            if len(similar_group) > 1:
                groups.append(similar_group)
                processed.update(similar_group)
            else:
                processed.add(value)

        return groups

    def _calculate_quality_metrics(self, original_df: pd.DataFrame, standardized_df: pd.DataFrame, log_entries: List[Dict]) -> Dict[str, Any]:
        """Calcola metriche di qualità della standardizzazione."""
        metrics = {
            "total_changes": len(log_entries),
            "changes_by_operation": {},
            "changes_by_column": {},
            "data_quality_improvement": {},
            "standardization_coverage": {}
        }

        # Conta cambiamenti per operazione
        for entry in log_entries:
            operation = entry.get("operation", "unknown")
            metrics["changes_by_operation"][operation] = metrics["changes_by_operation"].get(operation, 0) + 1

        # Conta cambiamenti per colonna
        for entry in log_entries:
            column = entry.get("column", "unknown")
            metrics["changes_by_column"][column] = metrics["changes_by_column"].get(column, 0) + 1

        # Calcola copertura standardizzazione
        for col in original_df.columns:
            if col in standardized_df.columns:
                original_unique = len(original_df[col].dropna().unique())
                standardized_unique = len(standardized_df[col].dropna().unique())

                if original_unique > 0:
                    reduction_ratio = (original_unique - standardized_unique) / original_unique
                    metrics["standardization_coverage"][col] = {
                        "original_unique_values": original_unique,
                        "standardized_unique_values": standardized_unique,
                        "reduction_ratio": reduction_ratio
                    }

        return metrics

    def _generate_standardization_recommendations(self, result: Dict[str, Any]) -> List[str]:
        """Genera raccomandazioni per migliorare la standardizzazione."""
        recommendations = []

        quality_metrics = result.get("quality_metrics", {})
        total_changes = quality_metrics.get("total_changes", 0)

        if total_changes == 0:
            recommendations.append("✅ Nessuna standardizzazione necessaria - dati già in formato ottimale")
        elif total_changes > 100:
            recommendations.append("⚠️ Molte standardizzazioni applicate - verificare la qualità dei dati originali")

        # Raccomandazioni per colonne con molti cambiamenti
        changes_by_column = quality_metrics.get("changes_by_column", {})
        high_change_columns = [col for col, count in changes_by_column.items() if count > 20]

        if high_change_columns:
            recommendations.append(f"🔍 Colonne con molti cambiamenti: {', '.join(high_change_columns)} - verificare pattern dati")

        # Raccomandazioni per copertura standardizzazione
        coverage = quality_metrics.get("standardization_coverage", {})
        low_coverage_columns = [
            col for col, metrics in coverage.items()
            if metrics.get("reduction_ratio", 0) < 0.1
        ]

        if low_coverage_columns:
            recommendations.append(f"📊 Bassa standardizzazione per: {', '.join(low_coverage_columns)} - possibili dati già puliti")

        return recommendations