{% extends "base.html" %}

{% block title %}Grafici Interattivi - Analisi Dati Aziendali{% endblock %}

{% block content %}
<main class="row mb-4">
    <section class="col-12">
        <article class="card shadow">
            <header class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>Grafici Interattivi
                    <span id="data-source-badge" class="badge bg-info text-white ms-2">Supabase-First</span>
                    <span id="supabase-connection-badge" class="badge bg-success text-white ms-1">
                        <i class="fas fa-database me-1"></i>Connesso
                    </span>
                </h4>
            </header>
            <div class="card-body">
                <!-- Status Supabase -->
                <div id="supabase-status" class="alert alert-info d-none mb-3">
                    <i class="fas fa-database me-2"></i>
                    <span id="status-text">Verificando connessione Supabase...</span>
                </div>
                {% if not data %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Nessun dato disponibile. Carica un file dalla pagina principale per visualizzare i grafici.
                </div>
                {% else %}
                <div class="alert alert-info d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-info-circle me-2"></i>
                        Visualizzazione dati dal file: <strong>{{ filename }}</strong>
                        <span class="badge {% if file_type == 'unknown' or file_type == 'generico' %}bg-warning{% else %}bg-success{% endif %} text-white ms-2">
                            <i class="fas {% if file_type == 'attivita' %}fa-tasks{% elif 'teamviewer' in file_type %}fa-desktop{% elif file_type == 'calendario' %}fa-calendar{% elif file_type == 'timbrature' %}fa-clock{% elif file_type == 'permessi' %}fa-calendar-check{% else %}fa-file{% endif %} me-1"></i>
                            Tipo: {{ file_type }}
                        </span>
                        {% if session.get('mcp_file_id') %}
                        <span class="badge bg-info text-white ms-2" title="Elaborato con MCP">
                            <i class="fas fa-server me-1"></i>MCP
                        </span>
                        {% endif %}
                    </div>
                    <div>
                        <a href="{{ url_for('dashboard') }}" class="btn btn-outline-primary btn-sm me-2">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                        <a href="{{ url_for('raw_data') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-table me-1"></i>Dati Grezzi
                        </a>
                    </div>
                </div>

                <!-- Controlli per i grafici interattivi -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="fas fa-sliders-h me-2"></i>Configurazione Grafico</h5>
                    </div>
                    <div class="card-body">
                        <form id="chart-config-form">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="chart-type" class="form-label">Tipo di Grafico</label>
                                    <select class="form-select" id="chart-type">
                                        <option value="bar">Grafico a Barre</option>
                                        <option value="line">Grafico a Linee</option>
                                        <option value="scatter">Grafico a Dispersione</option>
                                        <option value="pie">Grafico a Torta</option>
                                        <option value="heatmap">Mappa di Calore</option>
                                        <option value="box">Box Plot</option>
                                        <option value="histogram">Istogramma</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="data-source" class="form-label">Fonte Dati</label>
                                    <select class="form-select" id="data-source">
                                        <option value="normalized_activities" selected>🚀 Attività Normalizzate (Supabase)</option>
                                        <option value="normalized_teamviewer">🖥️ TeamViewer Normalizzato (Supabase)</option>
                                        <option value="cross_analysis">📊 Analisi Incrociata (Supabase)</option>
                                        <option value="processed">📁 Dati Elaborati (Legacy)</option>
                                        <option value="raw">📄 Dati Grezzi (Legacy)</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="aggregation" class="form-label">Aggregazione</label>
                                    <select class="form-select" id="aggregation">
                                        <option value="sum">Somma</option>
                                        <option value="mean">Media</option>
                                        <option value="count">Conteggio</option>
                                        <option value="min">Minimo</option>
                                        <option value="max">Massimo</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="x-column" class="form-label">Colonna X (Asse Orizzontale)</label>
                                    <select class="form-select" id="x-column">
                                        <option value="">Seleziona una colonna</option>
                                        {% for column in columns %}
                                        <option value="{{ column }}">{{ column }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="y-column" class="form-label">Colonna Y (Asse Verticale)</label>
                                    <select class="form-select" id="y-column">
                                        <option value="">Nessuna (conteggio)</option>
                                        {% for column in columns %}
                                        <option value="{{ column }}">{{ column }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="group-by" class="form-label">Raggruppa Per</label>
                                    <select class="form-select" id="group-by">
                                        <option value="">Nessun raggruppamento</option>
                                        {% for column in columns %}
                                        <option value="{{ column }}">{{ column }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="d-flex justify-content-end">
                                <button type="button" class="btn btn-outline-secondary me-2" id="reset-chart">
                                    <i class="fas fa-redo me-1"></i>Reset
                                </button>
                                <button type="button" class="btn btn-primary" id="update-chart">
                                    <i class="fas fa-sync me-1"></i>Aggiorna Grafico
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Grafico interattivo -->
                <div class="card shadow">
                    <div class="card-header bg-light">
                        <h5 class="mb-0" id="chart-title">Grafico Interattivo</h5>
                    </div>
                    <div class="card-body">
                        <div id="interactive-chart" class="chart-container">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Caricamento...</span>
                                </div>
                                <p class="mt-2">Caricamento grafico...</p>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </article>
    </section>
</main>
{% endblock %}

{% block extra_css %}
<style>
    .chart-container {
        width: 100%;
        min-height: 400px;
        position: relative;
    }

    .form-label {
        font-weight: 500;
    }

    .card-header h5 {
        font-size: 1.1rem;
    }

    /* Stili per i tooltip di Plotly */
    .plotly-tooltip {
        background-color: rgba(255, 255, 255, 0.9) !important;
        border: 1px solid #ddd !important;
        border-radius: 4px !important;
        padding: 8px !important;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
    }
</style>
{% endblock %}

{% block extra_js %}
<!-- Carica Plotly (versione completa per maggiore compatibilità) -->
<script src="https://cdn.plot.ly/plotly-2.24.1.min.js"></script>

<!-- Carica lo script per i grafici interattivi -->
<script src="{{ url_for('static', filename='js/interactive_charts.js') }}?v=1.0.0"></script>
{% endblock %}



