#!/usr/bin/env python3
"""
Test rapido per Universal File Reader
"""

import sys
sys.path.append('.')

from universal_file_reader import universal_reader

def test_reader():
    print("🔧 Test Universal File Reader...")
    
    # Test file TeamViewer
    print("\n📁 Test file TeamViewer:")
    df, info = universal_reader.read_file('test_raw_file.csv')
    print(f"Successo: {info['success']}")
    print(f"Righe: {info['rows']}, Colonne: {info['columns']}")
    print(f"Separatore: '{info['separator']}'")
    print(f"Encoding: {info['encoding']}")
    if not df.empty:
        print(f"Colonne: {df.columns.tolist()}")
        print(f"Prima riga: {df.iloc[0].to_dict()}")
    
    # Test file Registro Auto
    print("\n🚗 Test file Registro Auto:")
    df2, info2 = universal_reader.read_file('test_registro_auto.csv')
    print(f"Successo: {info2['success']}")
    print(f"Righe: {info2['rows']}, Colonne: {info2['columns']}")
    print(f"Separatore: '{info2['separator']}'")
    print(f"Encoding: {info2['encoding']}")
    if not df2.empty:
        print(f"Colonne: {df2.columns.tolist()}")
        print(f"Prima riga: {df2.iloc[0].to_dict()}")

if __name__ == "__main__":
    test_reader()
