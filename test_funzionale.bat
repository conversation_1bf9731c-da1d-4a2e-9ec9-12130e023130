@echo off
chcp 65001 >nul
echo =====================================
echo TEST FUNZIONALE COMPLETO - APP ROBERTO
echo =====================================

echo.
echo Attivazione ambiente virtuale...
call clean_env\Scripts\activate

echo.
echo Impostazione variabili d'ambiente...
set OPENROUTER_API_KEY=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
set APP_URL=http://localhost:5001
set MCP_URL=http://localhost:8000
set SUPABASE_URL=https://zqjllwxqjxjhdkbcawfr.supabase.co
set SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxamxsd3hxanhqaGRrYmNhd2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc4MjE4NzQsImV4cCI6MjA2MzM5Nzg3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8
set PYTHONPATH=%CD%
set FLASK_DEBUG=0

echo ✓ Variabili d'ambiente impostate

echo.
echo 1. Creazione cartella uploads...
if not exist uploads mkdir uploads
echo ✓ Cartella uploads pronta

echo.
echo 2. Test avvio Flask (modalità test)...
echo Avvio Flask in background...
start /min cmd /c "title Flask App Roberto && call clean_env\Scripts\activate && set OPENROUTER_API_KEY=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 && set PYTHONPATH=%CD% && python app.py && pause"

echo Attesa avvio server (10 secondi)...
timeout /t 10 >nul

echo.
echo 3. Test connessione Flask...
powershell -Command "try { (Invoke-WebRequest -Uri 'http://127.0.0.1:5001' -UseBasicParsing -TimeoutSec 5).StatusCode } catch { 'ERRORE' }" > temp_response.txt
set /p response=<temp_response.txt
del temp_response.txt
if "%response%"=="200" (
    echo ✓ Flask risponde correttamente (HTTP 200)
) else (
    echo ⚠️ Flask non risponde o errore: %response%
)

echo.
echo 4. Test pagina Setup Wizard...
powershell -Command "try { (Invoke-WebRequest -Uri 'http://127.0.0.1:5001/setup' -UseBasicParsing -TimeoutSec 5).StatusCode } catch { 'ERRORE' }" > temp_setup.txt
set /p setup_response=<temp_setup.txt
del temp_setup.txt
if "%setup_response%"=="200" (
    echo ✓ Setup Wizard raggiungibile (HTTP 200)
) else (
    echo ⚠️ Setup Wizard errore: %setup_response%
)

echo.
echo 5. Test API endpoints...
echo Testando /api/health...
powershell -Command "try { (Invoke-WebRequest -Uri 'http://127.0.0.1:5001/api/health' -UseBasicParsing -TimeoutSec 5).StatusCode } catch { 'ERRORE' }" > temp_health.txt
set /p health_response=<temp_health.txt
del temp_health.txt
if "%health_response%"=="200" (
    echo ✓ API Health check OK
) else (
    echo ⚠️ API Health check errore: %health_response%
)

echo.
echo 6. Apertura browser per test manuale...
echo Aprendo il Setup Wizard nel browser...
start http://127.0.0.1:5001/setup

echo.
echo =====================================
echo TEST FUNZIONALE COMPLETATO
echo =====================================
echo.
echo RISULTATI:
if "%response%"=="200" echo ✓ Flask Server: FUNZIONANTE
if "%setup_response%"=="200" echo ✓ Setup Wizard: FUNZIONANTE
if "%health_response%"=="200" echo ✓ API Health: FUNZIONANTE
echo.
echo Il browser dovrebbe mostrare il Setup Wizard.
echo Testa manualmente le funzionalità nel browser.
echo.
echo Premi un tasto per continuare...
pause

echo.
echo Vuoi avviare anche il MCP Server? (s/n)
set /p start_mcp=
if /i "%start_mcp%"=="s" (
    echo.
    echo Avvio MCP Server...
    start "MCP Server" cmd /k "title MCP Server && color 0A && call clean_env\Scripts\activate && cd mcp_server && python main.py"
    echo ✓ MCP Server avviato in nuova finestra
)

echo.
echo =====================================
echo Test completato! Controlla il browser.
echo =====================================