
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ report.title }}</title>
    <style>
        body { font-family: 'Courier New', monospace; margin: 40px; line-height: 1.6; background: #f8f9fa; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { border-bottom: 2px solid #28a745; padding-bottom: 20px; margin-bottom: 30px; }
        .title { color: #28a745; font-size: 2.2em; margin: 0; }
        .section { margin: 25px 0; }
        .section h2 { color: #495057; background: #e9ecef; padding: 10px; border-radius: 5px; }
        .analysis-item { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 3px solid #28a745; }
        .code-block { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; font-family: 'Courier New', monospace; }
        .discrepancy { background: #fff3cd; border-left: 3px solid #ffc107; padding: 10px; margin: 5px 0; }
        .discrepancy.critical { background: #f8d7da; border-left-color: #dc3545; }
        .discrepancy.high { background: #fff3cd; border-left-color: #fd7e14; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">{{ report.title }}</h1>
            <p>Periodo: {{ report.period }} | Generato: {{ generated_at.strftime('%d/%m/%Y %H:%M') }}</p>
        </div>

        <div class="section">
            <h2>Analisi Tecnica Dettagliata</h2>
            {{ report.metadata.technical_insights | markdown | safe }}
        </div>

        {% for analysis_type, result in report.analysis_results.items() %}
        {% if result.discrepancies_found %}
        <div class="section">
            <h2>{{ analysis_type | title }}</h2>
            <div class="analysis-item">
                <strong>Record Analizzati:</strong> {{ result.total_records_analyzed }}<br>
                <strong>Discrepanze Trovate:</strong> {{ result.discrepancies_found | length }}<br>
                <strong>Tempo Elaborazione:</strong> {{ result.processing_time_ms }}ms
            </div>

            {% for disc in result.discrepancies_found[:5] %}
            <div class="discrepancy {{ disc.severity }}">
                <strong>{{ disc.type }}</strong> ({{ disc.severity }})<br>
                {{ disc.description }}<br>
                <em>Azione suggerita: {{ disc.suggested_action }}</em>
            </div>
            {% endfor %}
        </div>
        {% endif %}
        {% endfor %}

        <div class="section">
            <h2>Configurazione Sistema</h2>
            <div class="code-block">
                Componenti Attivi: {{ report.analysis_results | length }}<br>
                Modalità: Analisi Automatica<br>
                Livello Dettaglio: Completo<br>
                Timestamp: {{ generated_at.isoformat() }}
            </div>
        </div>
    </div>
</body>
</html>
        