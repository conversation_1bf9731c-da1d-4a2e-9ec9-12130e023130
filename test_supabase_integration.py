#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test per l'integrazione Supabase.
Verifica la connessione e le funzionalità base del database.
"""

import os
import sys
import logging
from datetime import datetime
from typing import Dict, Any

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_supabase_connection():
    """
    Testa la connessione a Supabase.
    """
    print("🔍 Test connessione Supabase...")
    
    try:
        from supabase_integration import supabase_manager
        
        if not supabase_manager.is_connected:
            print("❌ Supabase non connesso")
            print("💡 Verifica le variabili d'ambiente SUPABASE_URL e SUPABASE_KEY")
            return False
        
        # Test connessione
        connection_test = supabase_manager.test_connection()
        if connection_test:
            print("✅ Connessione Supabase attiva")
            return True
        else:
            print("❌ Test connessione fallito")
            return False
            
    except ImportError as e:
        print(f"❌ Errore import Supabase: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Errore test connessione: {str(e)}")
        return False

def test_enhanced_config_manager():
    """
    Testa il gestore di configurazione avanzato.
    """
    print("\n🔍 Test Enhanced Config Manager...")
    
    try:
        from enhanced_config_manager import enhanced_config_manager
        
        # Test informazioni di sistema
        system_info = enhanced_config_manager.get_system_info()
        print(f"📊 Informazioni sistema:")
        for key, value in system_info.items():
            print(f"   {key}: {value}")
        
        # Test configurazione
        test_key = "test_settings.test_value"
        test_value = f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Imposta valore
        success = enhanced_config_manager.set_advanced(test_key, test_value, save_immediately=False)
        if success:
            print(f"✅ Impostazione configurazione riuscita: {test_key} = {test_value}")
        else:
            print(f"❌ Errore impostazione configurazione")
            return False
        
        # Recupera valore
        retrieved_value = enhanced_config_manager.get_advanced(test_key)
        if retrieved_value == test_value:
            print(f"✅ Recupero configurazione riuscito: {retrieved_value}")
        else:
            print(f"❌ Errore recupero configurazione: atteso {test_value}, ottenuto {retrieved_value}")
            return False
        
        # Test backup
        backup_success = enhanced_config_manager.backup_config()
        if backup_success:
            print("✅ Backup configurazione creato")
        else:
            print("❌ Errore creazione backup")
        
        return True
        
    except ImportError as e:
        print(f"❌ Errore import Enhanced Config Manager: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Errore test Enhanced Config Manager: {str(e)}")
        return False

def test_file_upload_simulation():
    """
    Simula un upload di file per testare l'integrazione.
    """
    print("\n🔍 Test simulazione upload file...")
    
    try:
        from supabase_integration import supabase_manager
        
        if not supabase_manager.is_connected:
            print("⚠️ Supabase non connesso, skip test upload")
            return True
        
        # Simula informazioni file
        file_info = {
            "filename": f"test_file_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
            "original_filename": "test_file.xlsx",
            "file_type": "attivita",
            "file_path": "/test/path/test_file.xlsx",
            "file_size": 1024,
            "status": "uploaded",
            "session_id": f"test_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "mcp_file_id": f"test_mcp_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        }
        
        # Salva file upload
        upload_id = supabase_manager.save_file_upload(file_info)
        if upload_id:
            print(f"✅ File upload salvato con ID: {upload_id}")
            
            # Test recupero file uploads
            recent_uploads = supabase_manager.get_file_uploads(limit=5)
            print(f"📁 File uploads recenti: {len(recent_uploads)}")
            
            # Test salvataggio dati elaborati
            processed_data = {
                "rows": 100,
                "columns": 10,
                "processing_time": 2.5,
                "detected_type": "attivita"
            }
            
            statistics = {
                "total_hours": 150.5,
                "unique_employees": 5,
                "date_range": "2024-01-01 to 2024-01-31"
            }
            
            processed_id = supabase_manager.save_processed_data(
                file_upload_id=upload_id,
                data_type="attivita",
                processed_data=processed_data,
                statistics=statistics
            )
            
            if processed_id:
                print(f"✅ Dati elaborati salvati con ID: {processed_id}")
                
                # Test recupero dati elaborati
                retrieved_data = supabase_manager.get_processed_data(upload_id)
                if retrieved_data:
                    print(f"✅ Dati elaborati recuperati: {retrieved_data['data_type']}")
                else:
                    print("❌ Errore recupero dati elaborati")
                    return False
            else:
                print("❌ Errore salvataggio dati elaborati")
                return False
        else:
            print("❌ Errore salvataggio file upload")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Errore test upload simulation: {str(e)}")
        return False

def test_environment_variables():
    """
    Verifica le variabili d'ambiente necessarie.
    """
    print("\n🔍 Test variabili d'ambiente...")
    
    required_vars = {
        "SUPABASE_URL": "URL del progetto Supabase",
        "SUPABASE_KEY": "Chiave API Supabase",
        "OPENROUTER_API_KEY": "Chiave API OpenRouter (opzionale)"
    }
    
    all_present = True
    
    for var_name, description in required_vars.items():
        value = os.environ.get(var_name)
        if value:
            # Mostra solo i primi e ultimi caratteri per sicurezza
            if len(value) > 10:
                masked_value = f"{value[:4]}...{value[-4:]}"
            else:
                masked_value = "***"
            print(f"✅ {var_name}: {masked_value}")
        else:
            print(f"❌ {var_name}: Non impostata ({description})")
            if var_name in ["SUPABASE_URL", "SUPABASE_KEY"]:
                all_present = False
    
    return all_present

def main():
    """
    Esegue tutti i test di integrazione.
    """
    print("🚀 Avvio test integrazione Supabase per app-roberto")
    print("=" * 60)
    
    tests = [
        ("Variabili d'ambiente", test_environment_variables),
        ("Connessione Supabase", test_supabase_connection),
        ("Enhanced Config Manager", test_enhanced_config_manager),
        ("Simulazione upload file", test_file_upload_simulation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Errore durante {test_name}: {str(e)}")
            results.append((test_name, False))
    
    # Riepilogo risultati
    print("\n" + "=" * 60)
    print("📊 RIEPILOGO TEST")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Risultato: {passed}/{total} test superati")
    
    if passed == total:
        print("🎉 Tutti i test sono stati superati!")
        print("💡 L'integrazione Supabase è pronta per l'uso")
    else:
        print("⚠️ Alcuni test sono falliti")
        print("💡 Verifica la configurazione e riprova")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
