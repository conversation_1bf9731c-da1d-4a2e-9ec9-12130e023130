@echo off
echo ===================================
echo Avvio dell'applicazione principale
echo ===================================
echo.

REM Verifica se l'ambiente virtuale esiste
if not exist venv (
    echo Creazione dell'ambiente virtuale...
    python -m venv venv
    echo Ambiente virtuale creato.
    echo.
)

REM Attiva l'ambiente virtuale
echo Attivazione dell'ambiente virtuale...
call venv\Scripts\activate
echo Ambiente virtuale attivato.
echo.

REM Verifica se le dipendenze sono installate
echo Verifica delle dipendenze...
pip freeze > temp_requirements.txt
findstr /i "flask pandas plotly" temp_requirements.txt > nul
if %errorlevel% neq 0 (
    echo Installazione delle dipendenze...
    pip install -r requirements.txt
    echo Dipendenze installate.
) else (
    echo Le dipendenze sono già installate.
)
del temp_requirements.txt
echo.

REM Crea la cartella uploads se non esiste
if not exist uploads (
    echo Creazione della cartella uploads...
    mkdir uploads
    echo Cartella uploads creata.
    echo.
)

REM Avvia l'applicazione principale
echo Avvio dell'applicazione principale...
python app.py
echo.

REM Disattiva l'ambiente virtuale (questo punto viene raggiunto solo quando l'app viene chiusa)
call venv\Scripts\deactivate
echo Ambiente virtuale disattivato.
echo.

echo ===================================
echo Applicazione principale terminata
echo ===================================
pause
