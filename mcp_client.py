#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import logging
import requests
import threading
from typing import Dict, List, Optional, Any, Union, Tuple
from urllib.parse import urljoin

# Configurazione del logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MCPClient:
    """
    Client per comunicare con il server MCP (Micro-Capability Platform).
    Gestisce le richieste HTTP al server MCP e l'elaborazione delle risposte.
    """

    def __init__(self, base_url: str = "http://localhost:8000", max_retries: int = 1, timeout: int = 3):
        """
        Inizializza il client MCP.

        Args:
            base_url: URL base del server MCP
            max_retries: Numero massimo di tentativi di connessione
            timeout: Timeout in secondi per le richieste
        """
        self.base_url = base_url
        self.session = requests.Session()
        self.is_available = False
        self.timeout = timeout
        self.max_retries = max_retries  # Salva max_retries come attributo

        # Circuit breaker per evitare tentativi ripetuti
        self.circuit_breaker_active = False
        self.last_failure_time = None
        self.circuit_breaker_timeout = 300  # 5 minuti prima di riprovare

        # Configura il numero massimo di tentativi
        adapter = requests.adapters.HTTPAdapter(max_retries=max_retries)
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)

        # Verifica la connessione al server MCP
        try:
            # Prima prova con /health
            try:
                response = self.session.get(urljoin(self.base_url, "/health"), timeout=self.timeout)
                if response.status_code == 200:
                    logger.info(f"Connessione al server MCP stabilita: {self.base_url}")
                    self.is_available = True
                else:
                    logger.warning(f"Server MCP non risponde correttamente all'endpoint /health: {response.status_code}")
                    # Prova con l'endpoint root come fallback
                    root_response = self.session.get(urljoin(self.base_url, "/"), timeout=self.timeout)
                    if root_response.status_code == 200:
                        logger.info(f"Connessione al server MCP stabilita tramite endpoint root: {self.base_url}")
                        self.is_available = True
                    else:
                        logger.warning(f"Server MCP non risponde correttamente: {root_response.status_code}")
                        self.is_available = False
            except Exception as health_error:
                logger.warning(f"Errore nella connessione all'endpoint /health: {str(health_error)}")
                # Prova con l'endpoint root come fallback
                try:
                    root_response = self.session.get(urljoin(self.base_url, "/"), timeout=self.timeout)
                    if root_response.status_code == 200:
                        logger.info(f"Connessione al server MCP stabilita tramite endpoint root: {self.base_url}")
                        self.is_available = True
                    else:
                        logger.warning(f"Server MCP non risponde correttamente: {root_response.status_code}")
                        self.is_available = False
                except Exception as root_error:
                    logger.error(f"Impossibile connettersi al server MCP tramite endpoint root: {str(root_error)}")
                    self.is_available = False
        except Exception as e:
            logger.error(f"Impossibile connettersi al server MCP: {str(e)}")
            self.is_available = False
            self._activate_circuit_breaker()

    def _activate_circuit_breaker(self):
        """Attiva il circuit breaker per evitare tentativi ripetuti."""
        import time
        self.circuit_breaker_active = True
        self.last_failure_time = time.time()
        logger.warning(f"Circuit breaker attivato per {self.circuit_breaker_timeout} secondi")

    def _check_circuit_breaker(self) -> bool:
        """Verifica se il circuit breaker è attivo."""
        if not self.circuit_breaker_active:
            return False

        import time
        if time.time() - self.last_failure_time > self.circuit_breaker_timeout:
            logger.info("Circuit breaker scaduto, riprovo connessione MCP")
            self.circuit_breaker_active = False
            return False

        return True

    def process_file(self, file_id: str, file_path: str, file_type: str,
                    options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Invia un file al server MCP per l'elaborazione.

        Args:
            file_id: ID univoco del file
            file_path: Percorso del file da elaborare
            file_type: Tipo di file (es. "attivita", "teamviewer", ecc.)
            options: Opzioni aggiuntive per l'elaborazione

        Returns:
            Dizionario con i risultati dell'elaborazione
        """
        # Verifica circuit breaker prima di tentare la connessione
        if self._check_circuit_breaker():
            logger.warning("Circuit breaker attivo, salto tentativo MCP")
            return {"error": "Server MCP non disponibile (circuit breaker)", "use_local": True}

        # Se il server MCP non è disponibile, restituisci subito un errore
        if not self.is_available:
            logger.warning("Server MCP non disponibile, elaborazione locale")
            return {"error": "Server MCP non disponibile", "use_local": True}

        try:
            # Verifica che il file esista
            if not os.path.exists(file_path):
                logger.error(f"File non trovato: {file_path}")
                return {"error": f"File non trovato: {file_path}"}

            # Prima carica il file sul server
            upload_result = self.upload_file(file_id, file_path, file_type)

            # Se c'è stato un errore nel caricamento, restituisci l'errore
            if "error" in upload_result:
                return upload_result

            # Prepara i dati per la richiesta di elaborazione
            data = {
                "file_id": file_id,
                "file_type": file_type,
                "options": options or {}
            }

            # Invia la richiesta al server MCP con timeout
            response = self.session.post(
                urljoin(self.base_url, "/process-file/"),
                json=data,
                timeout=self.timeout
            )

            # Verifica la risposta
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Errore nella richiesta al server MCP: {response.status_code} - {response.text}")
                return {"error": f"Errore nella richiesta al server MCP: {response.status_code}", "use_local": True}

        except requests.exceptions.Timeout:
            logger.error(f"Timeout nella connessione al server MCP")
            return {"error": "Timeout nella connessione al server MCP", "use_local": True}
        except requests.exceptions.ConnectionError:
            logger.error(f"Errore di connessione al server MCP")
            return {"error": "Errore di connessione al server MCP", "use_local": True}
        except Exception as e:
            logger.error(f"Errore nell'elaborazione del file: {str(e)}")
            return {"error": str(e), "use_local": True}

    def upload_file(self, file_id: str, file_path: str, file_type: str) -> Dict[str, Any]:
        """
        Carica un file sul server MCP.

        Args:
            file_id: ID univoco del file
            file_path: Percorso del file da caricare
            file_type: Tipo di file (es. "attivita", "teamviewer", ecc.)

        Returns:
            Dizionario con i risultati del caricamento
        """
        # Se il server MCP non è disponibile, restituisci subito un errore
        if not self.is_available:
            logger.warning("Server MCP non disponibile, impossibile caricare il file")
            return {"error": "Server MCP non disponibile", "use_local": True}

        try:
            # Verifica che il file esista
            if not os.path.exists(file_path):
                logger.error(f"File non trovato: {file_path}")
                return {"error": f"File non trovato: {file_path}"}

            # Prepara i dati per la richiesta multipart/form-data
            with open(file_path, 'rb') as file:
                files = {'file': (os.path.basename(file_path), file, 'application/octet-stream')}
                data = {
                    'file_id': file_id,
                    'file_type': file_type
                }

                # Invia la richiesta al server MCP con timeout
                response = self.session.post(
                    urljoin(self.base_url, "/upload-file/"),
                    files=files,
                    data=data,
                    timeout=self.timeout
                )

            # Verifica la risposta
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Errore nel caricamento del file sul server MCP: {response.status_code} - {response.text}")
                return {"error": f"Errore nel caricamento del file: {response.status_code}", "use_local": True}

        except requests.exceptions.Timeout:
            logger.error(f"Timeout nella connessione al server MCP")
            return {"error": "Timeout nella connessione al server MCP", "use_local": True}
        except requests.exceptions.ConnectionError:
            logger.error(f"Errore di connessione al server MCP")
            return {"error": "Errore di connessione al server MCP", "use_local": True}
        except Exception as e:
            logger.error(f"Errore nel caricamento del file: {str(e)}")
            return {"error": str(e), "use_local": True}

    def get_file_summary(self, file_id: str) -> Dict[str, Any]:
        """
        Ottiene un riepilogo delle attività per un file elaborato.

        Args:
            file_id: ID del file

        Returns:
            Dizionario con il riepilogo delle attività
        """
        # Se il server MCP non è disponibile, restituisci subito un errore
        if not self.is_available:
            logger.warning("Server MCP non disponibile, impossibile ottenere il riepilogo")
            return {"error": "Server MCP non disponibile", "use_local": True}

        try:
            # Invia la richiesta al server MCP con timeout
            response = self.session.get(
                urljoin(self.base_url, f"/file-summary/{file_id}"),
                timeout=self.timeout
            )

            # Verifica la risposta
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Errore nella richiesta al server MCP: {response.status_code} - {response.text}")
                return {"error": f"Errore nella richiesta al server MCP: {response.status_code}", "use_local": True}

        except requests.exceptions.Timeout:
            logger.error(f"Timeout nella connessione al server MCP")
            return {"error": "Timeout nella connessione al server MCP", "use_local": True}
        except requests.exceptions.ConnectionError:
            logger.error(f"Errore di connessione al server MCP")
            return {"error": "Errore di connessione al server MCP", "use_local": True}
        except Exception as e:
            logger.error(f"Errore nel recupero del riepilogo del file: {str(e)}")
            return {"error": str(e), "use_local": True}

    def update_column_mapping(self, file_id: str, column_mapping: Dict[str, str]) -> Dict[str, Any]:
        """
        Aggiorna la mappatura delle colonne per un file elaborato.

        Args:
            file_id: ID del file
            column_mapping: Dizionario con la mappatura delle colonne {colonna_originale: colonna_standard}

        Returns:
            Dizionario con il risultato dell'aggiornamento
        """
        # Se il server MCP non è disponibile, restituisci subito un errore
        if not self.is_available:
            logger.warning("Server MCP non disponibile, impossibile aggiornare la mappatura delle colonne")
            return {"error": "Server MCP non disponibile", "use_local": True}

        try:
            # Prepara i dati per la richiesta
            data = {
                "file_id": file_id,
                "column_mapping": column_mapping
            }

            # Invia la richiesta al server MCP con timeout
            response = self.session.post(
                urljoin(self.base_url, "/update-column-mapping/"),
                json=data,
                timeout=self.timeout
            )

            # Verifica la risposta
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Errore nella richiesta al server MCP: {response.status_code} - {response.text}")
                return {"error": f"Errore nella richiesta al server MCP: {response.status_code}", "use_local": True}

        except requests.exceptions.Timeout:
            logger.error(f"Timeout nella connessione al server MCP")
            return {"error": "Timeout nella connessione al server MCP", "use_local": True}
        except requests.exceptions.ConnectionError:
            logger.error(f"Errore di connessione al server MCP")
            return {"error": "Errore di connessione al server MCP", "use_local": True}
        except Exception as e:
            logger.error(f"Errore nell'aggiornamento della mappatura delle colonne: {str(e)}")
            return {"error": str(e), "use_local": True}

    def get_standard_columns(self, file_type: str) -> List[str]:
        """
        Ottiene le colonne standard per un tipo di file.

        Args:
            file_type: Tipo di file

        Returns:
            Lista delle colonne standard
        """
        # Se il server MCP non è disponibile, restituisci una lista vuota
        if not self.is_available:
            logger.warning("Server MCP non disponibile, impossibile ottenere le colonne standard")
            return []

        try:
            # Invia la richiesta al server MCP con timeout
            response = self.session.get(
                urljoin(self.base_url, f"/standard-columns/{file_type}"),
                timeout=self.timeout
            )

            # Verifica la risposta
            if response.status_code == 200:
                return response.json().get("standard_columns", [])
            else:
                logger.error(f"Errore nella richiesta al server MCP: {response.status_code} - {response.text}")
                return []

        except requests.exceptions.Timeout:
            logger.error(f"Timeout nella connessione al server MCP")
            return []
        except requests.exceptions.ConnectionError:
            logger.error(f"Errore di connessione al server MCP")
            return []
        except Exception as e:
            logger.error(f"Errore nel recupero delle colonne standard: {str(e)}")
            return []

    def get_duration_formats(self) -> List[Dict[str, str]]:
        """
        Ottiene i formati di durata supportati.

        Returns:
            Lista dei formati di durata supportati
        """
        # Se il server MCP non è disponibile, restituisci una lista vuota
        if not self.is_available:
            logger.warning("Server MCP non disponibile, impossibile ottenere i formati di durata")
            return []

        try:
            # Invia la richiesta al server MCP con timeout
            response = self.session.get(
                urljoin(self.base_url, "/duration-formats"),
                timeout=self.timeout
            )

            # Verifica la risposta
            if response.status_code == 200:
                return response.json().get("supported_formats", [])
            else:
                logger.error(f"Errore nella richiesta al server MCP: {response.status_code} - {response.text}")
                return []

        except requests.exceptions.Timeout:
            logger.error(f"Timeout nella connessione al server MCP")
            return []
        except requests.exceptions.ConnectionError:
            logger.error(f"Errore di connessione al server MCP")
            return []
        except Exception as e:
            logger.error(f"Errore nel recupero dei formati di durata: {str(e)}")
            return []

    def process_llm_query(self, file_id: str, query: str, model_id: str = "anthropic/claude-3-haiku") -> Dict[str, Any]:
        """
        Invia una query LLM al server MCP per elaborarla con il contesto dei dati.

        Args:
            file_id: ID del file elaborato
            query: Query dell'utente
            model_id: ID del modello LLM da utilizzare

        Returns:
            Dizionario con la risposta dell'LLM
        """
        # Se il server MCP non è disponibile, restituisci subito un errore
        if not self.is_available:
            logger.warning("Server MCP non disponibile, impossibile elaborare la query LLM")
            return {"error": "Server MCP non disponibile", "use_local": True}

        try:
            # Prepara i dati per la richiesta
            data = {
                "file_id": file_id,
                "query": query,
                "model_id": model_id
            }

            # Invia la richiesta al server MCP con timeout
            response = self.session.post(
                urljoin(self.base_url, "/llm-query/"),
                json=data,
                timeout=self.timeout
            )

            # Verifica la risposta
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Errore nella richiesta al server MCP: {response.status_code} - {response.text}")
                return {"error": f"Errore nella richiesta al server MCP: {response.status_code}", "use_local": True}

        except requests.exceptions.Timeout:
            logger.error(f"Timeout nella connessione al server MCP")
            return {"error": "Timeout nella connessione al server MCP", "use_local": True}
        except requests.exceptions.ConnectionError:
            logger.error(f"Errore di connessione al server MCP")
            return {"error": "Errore di connessione al server MCP", "use_local": True}
        except Exception as e:
            logger.error(f"Errore nell'elaborazione della query LLM: {str(e)}")
            return {"error": str(e), "use_local": True}

    def get_processed_data(self, file_id: str) -> Dict[str, Any]:
        """
        Ottiene i dati elaborati per un file.

        Args:
            file_id: ID del file

        Returns:
            Dizionario con i dati elaborati
        """
        # Verifica circuit breaker prima di tentare la connessione
        if self._check_circuit_breaker():
            return {"error": "Server MCP non disponibile (circuit breaker)", "use_local": True}

        # Se il server MCP non è disponibile, restituisci subito un errore
        if not self.is_available:
            logger.warning("Server MCP non disponibile, impossibile ottenere i dati elaborati")
            return {"error": "Server MCP non disponibile", "use_local": True}

        try:
            # Invia la richiesta al server MCP con timeout
            response = self.session.get(
                urljoin(self.base_url, f"/processed-data/{file_id}"),
                timeout=self.timeout
            )

            # Verifica la risposta
            if response.status_code == 200:
                return response.json()
            else:
                # Log dettagliato dell'errore
                logger.error(f"Errore nella richiesta al server MCP: {response.status_code} - {response.text}")

                # Attiva circuit breaker per evitare tentativi ripetuti
                self.is_available = False
                self._activate_circuit_breaker()

                return {"error": f"Errore nella richiesta al server MCP: {response.status_code}", "use_local": True}

        except requests.exceptions.Timeout:
            logger.error(f"Timeout nella connessione al server MCP")
            # Attiva circuit breaker
            self.is_available = False
            self._activate_circuit_breaker()
            return {"error": "Timeout nella connessione al server MCP", "use_local": True}
        except requests.exceptions.ConnectionError:
            logger.error(f"Errore di connessione al server MCP")
            # Attiva circuit breaker
            self.is_available = False
            self._activate_circuit_breaker()
            return {"error": "Errore di connessione al server MCP", "use_local": True}
        except Exception as e:
            logger.error(f"Errore nel recupero dei dati elaborati: {str(e)}")
            return {"error": str(e), "use_local": True}

    def analyze_file_ai(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Richiede un'analisi AI dei dati del file.

        Args:
            analysis_data: Dati dell'analisi da elaborare con AI

        Returns:
            Dict: Risultato dell'analisi AI
        """
        if not self.is_available:
            logger.warning("Server MCP non disponibile, impossibile eseguire analisi AI")
            return {"error": "Server MCP non disponibile", "use_local": True}

        try:
            # Invia la richiesta al server MCP con timeout
            response = self.session.post(
                urljoin(self.base_url, "/analyze-ai/"),
                json=analysis_data,
                timeout=self.timeout
            )

            # Verifica la risposta
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Errore nella richiesta AI al server MCP: {response.status_code} - {response.text}")
                return {"error": f"Errore nella richiesta AI: {response.status_code}", "use_local": True}

        except requests.exceptions.Timeout:
            logger.error(f"Timeout nella richiesta AI al server MCP")
            return {"error": "Timeout nella richiesta AI", "use_local": True}
        except requests.exceptions.ConnectionError:
            logger.error(f"Errore di connessione nella richiesta AI al server MCP")
            return {"error": "Errore di connessione nella richiesta AI", "use_local": True}
        except Exception as e:
            logger.error(f"Errore nell'analisi AI: {str(e)}")
            return {"error": str(e), "use_local": True}

    def _check_connection(self):
        """
        Verifica la connessione al server MCP e aggiorna lo stato is_available.
        """
        try:
            # Prima prova con /health
            try:
                response = self.session.get(urljoin(self.base_url, "/health"), timeout=self.timeout)
                if response.status_code == 200:
                    logger.info(f"Riconnessione al server MCP stabilita: {self.base_url}")
                    self.is_available = True
                else:
                    # Prova con l'endpoint root come fallback
                    root_response = self.session.get(urljoin(self.base_url, "/"), timeout=self.timeout)
                    if root_response.status_code == 200:
                        logger.info(f"Riconnessione al server MCP stabilita tramite endpoint root: {self.base_url}")
                        self.is_available = True
                    else:
                        logger.warning(f"Server MCP ancora non disponibile: {root_response.status_code}")
                        self.is_available = False
            except Exception:
                # Prova con l'endpoint root come fallback
                try:
                    root_response = self.session.get(urljoin(self.base_url, "/"), timeout=self.timeout)
                    if root_response.status_code == 200:
                        logger.info(f"Riconnessione al server MCP stabilita tramite endpoint root: {self.base_url}")
                        self.is_available = True
                    else:
                        logger.warning(f"Server MCP ancora non disponibile: {root_response.status_code}")
                        self.is_available = False
                except Exception:
                    logger.error(f"Impossibile riconnettersi al server MCP")
                    self.is_available = False
        except Exception as e:
            logger.error(f"Errore nel controllo della connessione al server MCP: {str(e)}")
            self.is_available = False


