#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import re
from datetime import datetime
import locale

# Imposta il locale italiano per la gestione delle date
try:
    locale.setlocale(locale.LC_TIME, 'it_IT.UTF-8')
except:
    try:
        locale.setlocale(locale.LC_TIME, 'it_IT')
    except:
        print("Impossibile impostare il locale italiano. Verranno utilizzati i formati predefiniti.")

class DataProcessor:
    """
    Classe per l'elaborazione e la standardizzazione dei dati importati.
    Gestisce formati di data italiani, separatori decimali e mappatura campi.
    """

    def __init__(self):
        # Mappatura dei nomi delle colonne standard
        self.standard_columns = {
            # TeamViewer
            'Utente': 'tecnico',
            'Assegnatario': 'tecnico',
            'Computer': 'cliente',
            'Nome': 'cliente',
            'ID': 'id_sessione',
            'Codice': 'id_sessione',
            'Tipo di sessione': 'tipo_sessione',
            'Gruppo': 'gruppo',
            'Inizio': 'data_inizio',
            'Fine': 'data_fine',
            'Durata': 'durata',
            'Note': 'note',
            'Classificazione': 'valutazione',
            'Commenti del cliente': 'commenti',

            # Calendario
            'SUMMARY': 'titolo',
            'DTSTART': 'data_inizio',
            'DTEND': 'data_fine',
            'ATTENDEE': 'tecnico',
            'LOCATION': 'luogo',
            'PRIORITY': 'priorita',

            # Generico
            'Data': 'data',
            'Ora': 'ora',
            'Tecnico': 'tecnico',
            'Cliente': 'cliente',
            'Attività': 'attivita',
            'Stato': 'stato'
        }

        # Espressioni regolari per il riconoscimento dei formati di data
        self.date_patterns = {
            'it_date': r'(\d{1,2})[/.-](\d{1,2})[/.-](\d{4})',  # GG/MM/AAAA
            'it_datetime': r'(\d{1,2})[/.-](\d{1,2})[/.-](\d{4})\s+(\d{1,2}):(\d{1,2})',  # GG/MM/AAAA HH:MM
            'iso_date': r'(\d{4})[/.-](\d{1,2})[/.-](\d{1,2})',  # YYYY-MM-DD
            'iso_datetime': r'(\d{4})[/.-](\d{1,2})[/.-](\d{1,2})[T\s](\d{1,2}):(\d{1,2})'  # YYYY-MM-DD HH:MM
        }

        # Espressioni regolari per il riconoscimento dei formati di durata
        self.duration_patterns = {
            'minutes': r'^(\d+)$',  # Solo minuti (es. "30")
            'hours_minutes': r'(\d+)h\s*(\d+)m',  # Formato "1h 30m"
            'decimal_hours': r'(\d+[\.,]\d+)h',  # Formato "1.5h"
            'text_format': r'(\d+)\s*(?:ora|ore|h)(?:\s*e\s*)?(?:(\d+)\s*(?:min|minuti|m))?'  # Formato testuale italiano
        }

    def detect_date_format(self, df):
        """
        Rileva automaticamente il formato delle date nel DataFrame
        """
        date_format = 'unknown'
        date_columns = []

        # Cerca colonne che potrebbero contenere date
        for col in df.columns:
            if any(kw in col.lower() for kw in ['data', 'date', 'inizio', 'fine', 'start', 'end', 'dt']):
                # Prendi il primo valore non nullo
                sample = next((str(x) for x in df[col] if pd.notna(x)), '')

                # Controlla i pattern
                if re.search(self.date_patterns['it_datetime'], sample):
                    date_format = 'it_datetime'
                    date_columns.append(col)
                elif re.search(self.date_patterns['it_date'], sample):
                    date_format = 'it_date'
                    date_columns.append(col)
                elif re.search(self.date_patterns['iso_datetime'], sample):
                    date_format = 'iso_datetime'
                    date_columns.append(col)
                elif re.search(self.date_patterns['iso_date'], sample):
                    date_format = 'iso_date'
                    date_columns.append(col)

        return date_format, date_columns

    def parse_italian_date(self, date_str):
        """
        Converte una data in formato italiano (GG/MM/AAAA) in datetime
        """
        if pd.isna(date_str) or not isinstance(date_str, str):
            return None

        # Formato GG/MM/AAAA HH:MM
        match = re.search(self.date_patterns['it_datetime'], date_str)
        if match:
            day, month, year, hour, minute = map(int, match.groups())
            try:
                return datetime(year, month, day, hour, minute)
            except ValueError:
                return None

        # Formato GG/MM/AAAA
        match = re.search(self.date_patterns['it_date'], date_str)
        if match:
            day, month, year = map(int, match.groups())
            try:
                return datetime(year, month, day)
            except ValueError:
                return None

        # Formato ISO
        try:
            return pd.to_datetime(date_str)
        except:
            return None

    def parse_duration(self, duration_str):
        """
        Converte una stringa di durata in minuti
        """
        # Gestisci valori None o NaN
        if duration_str is None or pd.isna(duration_str):
            return 0

        # Se è già un numero, restituiscilo
        if isinstance(duration_str, (int, float)):
            return int(duration_str)

        duration_str = str(duration_str).strip()

        # Solo minuti (es. "30")
        match = re.search(self.duration_patterns['minutes'], duration_str)
        if match:
            return int(match.group(1))

        # Formato "1h 30m"
        match = re.search(self.duration_patterns['hours_minutes'], duration_str)
        if match:
            hours, minutes = map(int, match.groups())
            return hours * 60 + minutes

        # Formato "1.5h"
        match = re.search(self.duration_patterns['decimal_hours'], duration_str)
        if match:
            hours = float(match.group(1).replace(',', '.'))
            return int(hours * 60)

        # Formato testuale italiano
        match = re.search(self.duration_patterns['text_format'], duration_str)
        if match:
            hours = int(match.group(1))
            minutes = int(match.group(2)) if match.group(2) else 0
            return hours * 60 + minutes

        return 0

    def standardize_column_names(self, df):
        """
        Standardizza i nomi delle colonne in base alla mappatura
        """
        renamed_columns = {}

        for col in df.columns:
            if col in self.standard_columns:
                renamed_columns[col] = self.standard_columns[col]

        if renamed_columns:
            return df.rename(columns=renamed_columns)

        return df

    def process_dates(self, df, date_columns=None):
        """
        Elabora le colonne di date convertendole in formato datetime
        """
        if date_columns is None:
            # Rileva automaticamente le colonne di date
            _, date_columns = self.detect_date_format(df)

        for col in date_columns:
            if col in df.columns:
                df[col] = df[col].apply(self.parse_italian_date)

        return df

    def process_durations(self, df, duration_column='durata'):
        """
        Elabora le colonne di durata convertendole in minuti
        """
        if duration_column in df.columns:
            df[duration_column] = df[duration_column].apply(self.parse_duration)

        return df

    def process_dataframe(self, df, standardize=True, process_dates=True, process_durations=True):
        """
        Elabora completamente un DataFrame applicando tutte le trasformazioni
        """
        # Standardizza i nomi delle colonne
        if standardize:
            df = self.standardize_column_names(df)

        # Elabora le date
        if process_dates:
            df = self.process_dates(df)

        # Elabora le durate
        if process_durations:
            df = self.process_durations(df)

        return df
