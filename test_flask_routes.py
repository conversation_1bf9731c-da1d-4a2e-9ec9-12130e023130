#!/usr/bin/env python3
"""
Test per verificare le route Flask effettivamente registrate
"""

import requests
import json

def test_flask_routes():
    """Verifica le route Flask registrate"""
    
    print("🔍 TEST ROUTE FLASK REGISTRATE")
    print("=" * 50)
    
    # Lista di endpoint da testare
    endpoints_to_test = [
        ("GET", "/"),
        ("GET", "/dashboard"),
        ("GET", "/setup-wizard"),
        ("GET", "/api/health"),
        ("GET", "/api/endpoints"),
        ("GET", "/api/data"),
        ("GET", "/api/processed_data"),
        ("GET", "/api/chart_data"),
        ("GET", "/api/models"),
        ("GET", "/api/config/employees"),
        ("GET", "/api/automation/rules"),
        ("GET", "/api/database/status"),
        ("POST", "/upload"),
        ("POST", "/api/chat/send"),
        ("POST", "/api/wizard/complete"),
        ("POST", "/api/calculate-employee-cost"),
        ("POST", "/api/intelligent-system/analyze"),
        ("POST", "/api/onboarding/analyze"),
        ("POST", "/api/onboarding/progress"),
        ("GET", "/api/onboarding/templates"),
        ("GET", "/api/persistence/status"),
        ("POST", "/api/persistence/cleanup")
    ]
    
    working_endpoints = []
    not_found_endpoints = []
    error_endpoints = []
    
    for method, path in endpoints_to_test:
        try:
            if method == "GET":
                response = requests.get(f"http://127.0.0.1:5000{path}", timeout=10)
            elif method == "POST":
                response = requests.post(
                    f"http://127.0.0.1:5000{path}", 
                    json={}, 
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )
            else:
                continue
                
            if response.status_code == 200:
                working_endpoints.append((method, path))
                print(f"✅ {method} {path} - OK")
            elif response.status_code == 404:
                not_found_endpoints.append((method, path))
                print(f"❌ {method} {path} - 404 NOT FOUND")
            else:
                error_endpoints.append((method, path, response.status_code))
                print(f"⚠️ {method} {path} - {response.status_code}")
                
        except requests.exceptions.Timeout:
            error_endpoints.append((method, path, "TIMEOUT"))
            print(f"⏰ {method} {path} - TIMEOUT")
        except Exception as e:
            error_endpoints.append((method, path, str(e)))
            print(f"💥 {method} {path} - ERROR: {str(e)}")
    
    print("\n" + "=" * 50)
    print("📊 RIEPILOGO RISULTATI")
    print("=" * 50)
    
    print(f"✅ Endpoint funzionanti: {len(working_endpoints)}")
    for method, path in working_endpoints:
        print(f"   {method} {path}")
    
    print(f"\n❌ Endpoint 404 (non registrati): {len(not_found_endpoints)}")
    for method, path in not_found_endpoints:
        print(f"   {method} {path}")
    
    print(f"\n⚠️ Endpoint con errori: {len(error_endpoints)}")
    for item in error_endpoints:
        if len(item) == 3:
            method, path, error = item
            print(f"   {method} {path} - {error}")
    
    # Analisi specifica per wizard
    wizard_found = any(path == "/api/wizard/complete" for _, path in working_endpoints)
    
    print(f"\n🎯 ANALISI ENDPOINT WIZARD:")
    if wizard_found:
        print("   ✅ /api/wizard/complete è registrato e funzionante")
    else:
        print("   ❌ /api/wizard/complete NON è registrato come route Flask")
        print("   📝 L'endpoint è nel sistema di standardizzazione ma non in Flask")
        print("   🔧 Possibili cause:")
        print("      - Errore di sintassi prima della definizione della route")
        print("      - Problema di importazione")
        print("      - Route definita ma non registrata")
        print("      - Errore nell'applicazione che impedisce la registrazione")

if __name__ == "__main__":
    test_flask_routes()
