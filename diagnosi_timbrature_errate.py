#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Diagnosi errore classificazione file timbrature come "attività"
Analizza il file di timbrature classificato erroneamente.
"""

import pandas as pd
import sys
import os
from enhanced_file_detector import EnhancedFileDetector

def diagnosi_timbrature_errate():
    """Diagnostica l'errore di classificazione timbrature → attività."""
    
    print("🔍 DIAGNOSI ERRORE CLASSIFICAZIONE TIMBRATURE")
    print("=" * 60)
    
    # Cerca il file di timbrature più recente
    uploads_dir = 'uploads'
    if not os.path.exists(uploads_dir):
        print(f"❌ Directory {uploads_dir} non trovata")
        return
    
    # Cerca file con pattern timbrature
    files = [f for f in os.listdir(uploads_dir) if f.endswith(('.xlsx', '.xls', '.csv'))]
    timbrature_files = [f for f in files if 'timbrature' in f.lower()]
    
    if not timbrature_files:
        print(f"❌ Nessun file timbrature trovato in {uploads_dir}")
        print(f"📁 File disponibili: {files}")
        return
    
    # Prendi il file più recente
    latest_file = max(timbrature_files, key=lambda f: os.path.getctime(os.path.join(uploads_dir, f)))
    file_path = os.path.join(uploads_dir, latest_file)
    
    print(f"📁 File analizzato: {latest_file}")
    print(f"📍 Path completo: {file_path}")
    print()
    
    try:
        # Leggi il file
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        else:
            df = pd.read_excel(file_path)
        
        print(f"📊 Dimensioni file: {df.shape[0]} righe x {df.shape[1]} colonne")
        print()
        
        print("📋 COLONNE PRESENTI NEL FILE:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i}. '{col}'")
        print()
        
        # Rileva il tipo di file
        detector = EnhancedFileDetector()
        detected_type, confidence, scores = detector.detect_file_type(df, latest_file)
        
        print("🎯 RILEVAMENTO TIPO FILE:")
        print(f"   Tipo rilevato: {detected_type}")
        print(f"   Confidenza: {confidence:.3f}")
        print()
        
        print("📊 PUNTEGGI PER TUTTI I TIPI:")
        for tipo, punteggio in sorted(scores.items(), key=lambda x: x[1], reverse=True):
            print(f"   {tipo}: {punteggio:.3f}")
        print()
        
        # Analizza il problema specifico
        print("🔍 ANALISI PROBLEMA CLASSIFICAZIONE:")
        print("-" * 50)
        
        # Verifica nome file
        filename_lower = latest_file.lower()
        print("📁 ANALISI NOME FILE:")
        if 'timbrature' in filename_lower:
            print("   ✅ Nome contiene 'timbrature' → DOVREBBE essere 'timbrature'")
        if 'apprilevazionepresenze' in filename_lower:
            print("   ✅ Nome contiene 'apprilevazionepresenze' → DOVREBBE essere 'timbrature'")
        if 'totali' in filename_lower:
            print("   ✅ Nome contiene 'totali' → Indica dati aggregati timbrature")
        
        print()
        
        # Analizza colonne per timbrature
        columns_lower = [col.lower() for col in df.columns]
        
        print("📋 ANALISI COLONNE PER TIMBRATURE:")
        timbrature_indicators = 0
        
        # Pattern tipici timbrature
        if any("dipendente" in col or "employee" in col or "nome" in col for col in columns_lower):
            print("   ✅ Trovata colonna dipendente/employee/nome")
            timbrature_indicators += 1
        if any("data" in col for col in columns_lower):
            print("   ✅ Trovata colonna data")
            timbrature_indicators += 1
        if any("entrata" in col or "ingresso" in col or "in" in col for col in columns_lower):
            print("   ✅ Trovata colonna entrata/ingresso")
            timbrature_indicators += 1
        if any("uscita" in col or "out" in col for col in columns_lower):
            print("   ✅ Trovata colonna uscita")
            timbrature_indicators += 1
        if any("ore" in col and ("lavorate" in col or "totali" in col) for col in columns_lower):
            print("   ✅ Trovata colonna ore lavorate/totali")
            timbrature_indicators += 1
        if any("straordinario" in col or "overtime" in col for col in columns_lower):
            print("   ✅ Trovata colonna straordinario")
            timbrature_indicators += 1
        
        print()
        
        # Analizza colonne per attività
        print("📋 ANALISI COLONNE PER ATTIVITÀ:")
        attivita_indicators = 0
        
        if any("attivita" in col or "activity" in col or "task" in col for col in columns_lower):
            print("   ⚠️ Trovata colonna attività/activity/task")
            attivita_indicators += 1
        if any("progetto" in col or "project" in col for col in columns_lower):
            print("   ⚠️ Trovata colonna progetto/project")
            attivita_indicators += 1
        if any("cliente" in col or "customer" in col or "client" in col for col in columns_lower):
            print("   ⚠️ Trovata colonna cliente/customer")
            attivita_indicators += 1
        if any("descrizione" in col or "description" in col for col in columns_lower):
            print("   ⚠️ Trovata colonna descrizione")
            attivita_indicators += 1
        
        print()
        print("📈 INDICATORI:")
        print(f"   Timbrature: {timbrature_indicators}/6 pattern trovati")
        print(f"   Attività: {attivita_indicators}/4 pattern trovati")
        print()
        
        # Mostra prime righe per contesto
        print("🔍 PRIME 3 RIGHE DEL FILE:")
        print("-" * 50)
        print(df.head(3).to_string())
        print()
        
        # Conclusione
        print("📋 CONCLUSIONE:")
        print("-" * 30)
        
        if detected_type == "attivita" and timbrature_indicators >= 3:
            print("❌ PROBLEMA CONFERMATO:")
            print("   - File CHIARAMENTE di timbrature")
            print("   - Classificato erroneamente come 'attività'")
            print("   - Nome file contiene 'timbrature'")
            print(f"   - {timbrature_indicators}/6 pattern timbrature trovati")
            print()
            print("🔧 CAUSA PROBABILE:")
            print("   - Pattern 'attività' troppo generico")
            print("   - Controllo nome file insufficiente")
            print("   - Peso pattern timbrature troppo basso")
            
        elif detected_type == "timbrature":
            print("✅ CLASSIFICAZIONE CORRETTA:")
            print("   - File correttamente riconosciuto come 'timbrature'")
            
        else:
            print(f"⚠️ CLASSIFICAZIONE AMBIGUA: {detected_type}")
            print("   - Necessaria analisi più approfondita")
        
        return detected_type, confidence, scores, timbrature_indicators, attivita_indicators
        
    except Exception as e:
        print(f"❌ Errore nell'analisi: {str(e)}")
        return None

def suggerisci_correzioni_timbrature():
    """Suggerisce correzioni per il sistema di riconoscimento timbrature."""
    print("\n🔧 SUGGERIMENTI PER CORREGGERE CLASSIFICAZIONE TIMBRATURE:")
    print("=" * 60)
    print("1. CONTROLLO PRIORITARIO NOME FILE:")
    print("   - Se nome contiene 'timbrature' → classificare come 'timbrature'")
    print("   - Se nome contiene 'apprilevazionepresenze-timbrature' → forzare 'timbrature'")
    print()
    print("2. MIGLIORARE PATTERN TIMBRATURE:")
    print("   - Aumentare peso per 'ore lavorate', 'entrata', 'uscita'")
    print("   - Aggiungere pattern per 'totali', 'straordinario'")
    print("   - Pattern specifici: 'dipendente' + 'data' + 'ore'")
    print()
    print("3. RIDURRE FALSI POSITIVI ATTIVITÀ:")
    print("   - Pattern 'attività' più specifici")
    print("   - Richiedere presenza di 'progetto' o 'cliente' per attività")
    print("   - Non classificare come attività se ha pattern timbrature forti")
    print()
    print("4. IMPLEMENTARE CONTROLLO FILENAME-BASED:")
    print("   - Priorità alta per pattern nome file")
    print("   - 'timbrature' nel nome → forza tipo 'timbrature'")

if __name__ == "__main__":
    risultato = diagnosi_timbrature_errate()
    if risultato:
        suggerisci_correzioni_timbrature()
