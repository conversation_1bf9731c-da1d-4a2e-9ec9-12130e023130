#!/usr/bin/env python3
"""
Debug interno delle route Flask registrate
"""

import requests
import json

def debug_flask_internal():
    """Debug interno delle route Flask"""
    
    print("🔍 DEBUG INTERNO ROUTE FLASK")
    print("=" * 60)
    
    # Test endpoint che sappiamo funzionare
    working_endpoints = [
        "/api/health",
        "/api/endpoints", 
        "/api/persistence/status",
        "/api/persistence/cleanup"
    ]
    
    print("✅ ENDPOINT CHE FUNZIONANO:")
    for endpoint in working_endpoints:
        try:
            if endpoint.endswith("/cleanup"):
                response = requests.post(f"http://127.0.0.1:5000{endpoint}", json={}, timeout=5)
            else:
                response = requests.get(f"http://127.0.0.1:5000{endpoint}", timeout=5)
            print(f"   {endpoint}: {response.status_code} ({'✅' if response.status_code == 200 else '❌'})")
        except Exception as e:
            print(f"   {endpoint}: ❌ ERRORE - {str(e)}")
    
    print()
    
    # Test endpoint che non funzionano
    broken_endpoints = [
        "/api/wizard/complete",
        "/api/config/employees",
        "/api/calculate-employee-cost",
        "/api/intelligent-system/analyze"
    ]
    
    print("❌ ENDPOINT CHE NON FUNZIONANO:")
    for endpoint in broken_endpoints:
        try:
            if endpoint.startswith("/api/wizard") or endpoint.startswith("/api/calculate") or endpoint.startswith("/api/intelligent"):
                response = requests.post(f"http://127.0.0.1:5000{endpoint}", json={}, timeout=5)
            else:
                response = requests.get(f"http://127.0.0.1:5000{endpoint}", timeout=5)
            print(f"   {endpoint}: {response.status_code} ({'✅' if response.status_code == 200 else '❌'})")
        except Exception as e:
            print(f"   {endpoint}: ❌ ERRORE - {str(e)}")
    
    print()
    
    # Analisi del pattern
    print("🔍 ANALISI DEL PATTERN:")
    print("   Gli endpoint che funzionano sono definiti nelle prime righe del file app.py")
    print("   Gli endpoint che non funzionano sono definiti più avanti nel file")
    print("   Questo indica un errore di sintassi o un'eccezione che impedisce")
    print("   l'esecuzione del codice dopo un certo punto")
    
    print()
    
    # Test specifico per trovare il punto di rottura
    print("🎯 TEST SPECIFICO PUNTO DI ROTTURA:")
    
    # Questi endpoint dovrebbero essere definiti in ordine nel file
    test_order = [
        ("/api/health", "GET"),           # Riga ~2215 - FUNZIONA
        ("/api/wizard/complete", "POST"), # Riga ~2266 - NON FUNZIONA
        ("/api/endpoints", "GET"),        # Riga ~2178 - FUNZIONA  
        ("/api/persistence/status", "GET") # Riga ~2684 - FUNZIONA
    ]
    
    for endpoint, method in test_order:
        try:
            if method == "POST":
                response = requests.post(f"http://127.0.0.1:5000{endpoint}", json={}, timeout=5)
            else:
                response = requests.get(f"http://127.0.0.1:5000{endpoint}", timeout=5)
            
            status = "✅ FUNZIONA" if response.status_code == 200 else "❌ NON FUNZIONA"
            print(f"   {method} {endpoint}: {response.status_code} ({status})")
            
        except Exception as e:
            print(f"   {method} {endpoint}: ❌ ERRORE - {str(e)}")
    
    print()
    
    # Conclusioni
    print("🎯 CONCLUSIONI:")
    print("   Il problema è che l'endpoint /api/wizard/complete è definito alla riga 2266")
    print("   ma non viene registrato come route Flask effettiva.")
    print("   Questo indica che c'è un errore di sintassi o un'eccezione")
    print("   che impedisce l'esecuzione del codice dopo la riga 2266.")
    print()
    print("   SOLUZIONE: Spostare l'endpoint in una posizione ancora più sicura")
    print("   o identificare e correggere l'errore di sintassi.")

if __name__ == "__main__":
    debug_flask_internal()
