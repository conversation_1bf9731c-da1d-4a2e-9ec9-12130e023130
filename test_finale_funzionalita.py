#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test finale completo delle nuove funzionalità implementate.
Verifica che tutte le funzionalità delle Fasi 6-7 siano operative.
"""

import requests
import json
import time
from datetime import datetime

def test_complete_functionality():
    """Test completo di tutte le nuove funzionalità."""
    print("🎯 TEST FINALE COMPLETO - NUOVE FUNZIONALITÀ")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    results = {}
    
    # Test 1: Dashboard Agenti
    print("\n1. 🤖 Test Dashboard Agenti...")
    try:
        response = requests.get(f"{base_url}/agents-dashboard", timeout=10)
        results['agents_dashboard'] = {
            'status_code': response.status_code,
            'success': response.status_code == 200,
            'content_length': len(response.content)
        }
        print(f"   ✅ Dashboard Agenti: {response.status_code} - {len(response.content)} bytes")
    except Exception as e:
        results['agents_dashboard'] = {'success': False, 'error': str(e)}
        print(f"   ❌ Dashboard Agenti: {str(e)}")
    
    # Test 2: API Agenti - Lista
    print("\n2. 📋 Test API Lista Agenti...")
    try:
        response = requests.get(f"{base_url}/api/agents/list", timeout=10)
        data = response.json()
        results['agents_list'] = {
            'status_code': response.status_code,
            'success': response.status_code == 200 and data.get('success', False),
            'agents_count': data.get('total_agents', 0),
            'agents': [agent['name'] for agent in data.get('agents', [])]
        }
        print(f"   ✅ Lista Agenti: {data['total_agents']} agenti - {', '.join(results['agents_list']['agents'])}")
    except Exception as e:
        results['agents_list'] = {'success': False, 'error': str(e)}
        print(f"   ❌ Lista Agenti: {str(e)}")
    
    # Test 3: API Agenti - Health
    print("\n3. 💓 Test Health Check Agenti...")
    try:
        response = requests.get(f"{base_url}/api/agents/health", timeout=10)
        data = response.json()
        results['agents_health'] = {
            'status_code': response.status_code,
            'success': response.status_code == 200 and data.get('success', False),
            'orchestrator_status': data.get('health', {}).get('orchestrator_status', 'unknown')
        }
        print(f"   ✅ Health Agenti: {results['agents_health']['orchestrator_status']}")
    except Exception as e:
        results['agents_health'] = {'success': False, 'error': str(e)}
        print(f"   ❌ Health Agenti: {str(e)}")
    
    # Test 4: API Capacità Data Cleaning
    print("\n4. 🧹 Test Capacità Data Cleaning...")
    try:
        response = requests.get(f"{base_url}/api/agents/data-cleaning/capabilities", timeout=10)
        data = response.json()
        results['data_cleaning_caps'] = {
            'status_code': response.status_code,
            'success': response.status_code == 200 and data.get('success', False),
            'capabilities_count': len(data.get('capabilities', [])),
            'capabilities': [cap['name'] for cap in data.get('capabilities', [])]
        }
        print(f"   ✅ Capacità Data Cleaning: {results['data_cleaning_caps']['capabilities_count']} capacità")
        for cap in results['data_cleaning_caps']['capabilities']:
            print(f"      - {cap}")
    except Exception as e:
        results['data_cleaning_caps'] = {'success': False, 'error': str(e)}
        print(f"   ❌ Capacità Data Cleaning: {str(e)}")
    
    # Test 5: API Formati Export
    print("\n5. 📤 Test Formati Export...")
    try:
        response = requests.get(f"{base_url}/api/agents/export/formats", timeout=10)
        data = response.json()
        results['export_formats'] = {
            'status_code': response.status_code,
            'success': response.status_code == 200 and data.get('success', False),
            'formats_count': len(data.get('supported_formats', [])),
            'formats': data.get('supported_formats', []),
            'export_types': data.get('export_types', [])
        }
        print(f"   ✅ Formati Export: {results['export_formats']['formats_count']} formati")
        print(f"      Formati: {', '.join(results['export_formats']['formats'])}")
        print(f"      Tipi: {', '.join(results['export_formats']['export_types'])}")
    except Exception as e:
        results['export_formats'] = {'success': False, 'error': str(e)}
        print(f"   ❌ Formati Export: {str(e)}")
    
    # Test 6: Monitoring Health
    print("\n6. 🏥 Test Monitoring Health...")
    try:
        response = requests.get(f"{base_url}/monitoring/health", timeout=10)
        data = response.json()
        results['monitoring_health'] = {
            'status_code': response.status_code,
            'success': response.status_code == 200,
            'overall_status': data.get('overall_status', 'unknown'),
            'checks': list(data.get('checks', {}).keys())
        }
        print(f"   ✅ Monitoring Health: {results['monitoring_health']['overall_status']}")
        print(f"      Checks: {', '.join(results['monitoring_health']['checks'])}")
    except Exception as e:
        results['monitoring_health'] = {'success': False, 'error': str(e)}
        print(f"   ❌ Monitoring Health: {str(e)}")
    
    # Test 7: Monitoring Metrics
    print("\n7. 📊 Test Monitoring Metrics...")
    try:
        response = requests.get(f"{base_url}/monitoring/metrics", timeout=10)
        data = response.json()
        results['monitoring_metrics'] = {
            'status_code': response.status_code,
            'success': response.status_code == 200,
            'has_system': 'system' in data,
            'has_application': 'application' in data,
            'cpu_usage': data.get('system', {}).get('cpu_usage_percent', 0),
            'memory_usage': data.get('system', {}).get('memory_usage_percent', 0)
        }
        print(f"   ✅ Monitoring Metrics: CPU {results['monitoring_metrics']['cpu_usage']}%, Memory {results['monitoring_metrics']['memory_usage']}%")
    except Exception as e:
        results['monitoring_metrics'] = {'success': False, 'error': str(e)}
        print(f"   ❌ Monitoring Metrics: {str(e)}")
    
    # Test 8: Monitoring Status
    print("\n8. 📈 Test Monitoring Status...")
    try:
        response = requests.get(f"{base_url}/monitoring/status", timeout=10)
        data = response.json()
        results['monitoring_status'] = {
            'status_code': response.status_code,
            'success': response.status_code == 200,
            'uptime': data.get('uptime_seconds', 0),
            'alerts_count': data.get('alerts_count', 0)
        }
        print(f"   ✅ Monitoring Status: Uptime {results['monitoring_status']['uptime']}s, Alerts {results['monitoring_status']['alerts_count']}")
    except Exception as e:
        results['monitoring_status'] = {'success': False, 'error': str(e)}
        print(f"   ❌ Monitoring Status: {str(e)}")
    
    # Test 9: Dashboard Principale (verifica integrazione)
    print("\n9. 🏠 Test Dashboard Principale...")
    try:
        response = requests.get(f"{base_url}/dashboard", timeout=10)
        results['main_dashboard'] = {
            'status_code': response.status_code,
            'success': response.status_code == 200,
            'content_length': len(response.content)
        }
        print(f"   ✅ Dashboard Principale: {response.status_code} - {len(response.content)} bytes")
    except Exception as e:
        results['main_dashboard'] = {'success': False, 'error': str(e)}
        print(f"   ❌ Dashboard Principale: {str(e)}")
    
    # Test 10: Dashboard Intelligente
    print("\n10. 🧠 Test Dashboard Intelligente...")
    try:
        response = requests.get(f"{base_url}/intelligent-dashboard", timeout=10)
        results['intelligent_dashboard'] = {
            'status_code': response.status_code,
            'success': response.status_code == 200,
            'content_length': len(response.content)
        }
        print(f"    ✅ Dashboard Intelligente: {response.status_code} - {len(response.content)} bytes")
    except Exception as e:
        results['intelligent_dashboard'] = {'success': False, 'error': str(e)}
        print(f"    ❌ Dashboard Intelligente: {str(e)}")
    
    # Riepilogo finale
    print("\n" + "=" * 60)
    print("📋 RIEPILOGO FINALE TEST")
    print("=" * 60)
    
    test_categories = [
        ("🤖 Dashboard Agenti", results.get('agents_dashboard', {}).get('success', False)),
        ("📋 API Lista Agenti", results.get('agents_list', {}).get('success', False)),
        ("💓 Health Check Agenti", results.get('agents_health', {}).get('success', False)),
        ("🧹 Capacità Data Cleaning", results.get('data_cleaning_caps', {}).get('success', False)),
        ("📤 Formati Export", results.get('export_formats', {}).get('success', False)),
        ("🏥 Monitoring Health", results.get('monitoring_health', {}).get('success', False)),
        ("📊 Monitoring Metrics", results.get('monitoring_metrics', {}).get('success', False)),
        ("📈 Monitoring Status", results.get('monitoring_status', {}).get('success', False)),
        ("🏠 Dashboard Principale", results.get('main_dashboard', {}).get('success', False)),
        ("🧠 Dashboard Intelligente", results.get('intelligent_dashboard', {}).get('success', False))
    ]
    
    passed_tests = 0
    total_tests = len(test_categories)
    
    for name, success in test_categories:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {name}")
        if success:
            passed_tests += 1
    
    print(f"\n🎯 RISULTATO FINALE: {passed_tests}/{total_tests} test superati")
    success_rate = (passed_tests / total_tests) * 100
    
    if passed_tests == total_tests:
        print("🎉 TUTTI I TEST SUPERATI! Le nuove funzionalità sono completamente operative!")
        print("🚀 Sistema pronto per utilizzo completo!")
    elif success_rate >= 80:
        print("✅ La maggior parte dei test superati. Sistema funzionale con funzionalità avanzate operative.")
    elif success_rate >= 60:
        print("⚠️ Alcuni test falliti ma funzionalità principali operative.")
    else:
        print("❌ Molti test falliti. Verificare la configurazione.")
    
    # Dettagli agenti
    if results.get('agents_list', {}).get('success', False):
        print(f"\n🤖 AGENTI OPERATIVI:")
        agents_data = results['agents_list']
        print(f"   📊 Totale Agenti: {agents_data['agents_count']}")
        for agent in agents_data['agents']:
            print(f"   🔧 {agent}")
    
    # Dettagli capacità
    if results.get('data_cleaning_caps', {}).get('success', False):
        caps_data = results['data_cleaning_caps']
        print(f"\n🧹 CAPACITÀ DATA CLEANING:")
        print(f"   📊 Totale Capacità: {caps_data['capabilities_count']}")
        for cap in caps_data['capabilities']:
            print(f"   ⚙️ {cap}")
    
    # Dettagli formati export
    if results.get('export_formats', {}).get('success', False):
        export_data = results['export_formats']
        print(f"\n📤 FORMATI EXPORT:")
        print(f"   📊 Totale Formati: {export_data['formats_count']}")
        print(f"   📄 Formati: {', '.join(export_data['formats'])}")
        print(f"   🔄 Tipi Export: {', '.join(export_data['export_types'])}")
    
    # Salva risultati
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f'test_finale_results_{timestamp}.json'
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📄 Risultati dettagliati salvati in: {results_file}")
    
    return results, success_rate

if __name__ == "__main__":
    print("⏳ Avvio test finale completo...")
    time.sleep(2)
    
    results, success_rate = test_complete_functionality()
    
    # Exit code basato sui risultati
    if success_rate >= 80:
        exit(0)
    else:
        exit(1)
