#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test completo della Fase 3: Riconoscimento Intelligente Basato sul Contenuto
"""

import pandas as pd
import sys
import time

# Aggiungi il percorso corrente per gli import
sys.path.append('.')

def test_fase3_complete():
    """Test integrato di tutti i componenti della Fase 3."""
    print('🚀 TEST COMPLETO FASE 3: RICONOSCIMENTO INTELLIGENTE')
    print('=' * 60)
    
    # Crea dataset di test realistici
    test_data = create_comprehensive_test_data()
    
    # Test 1: ContentBasedFileAnalyzer
    print('\n📊 TEST 1: ContentBasedFileAnalyzer')
    print('-' * 40)
    
    try:
        from content_based_file_analyzer import ContentBasedFileAnalyzer
        content_analyzer = ContentBasedFileAnalyzer()
        
        content_results = {}
        for file_type, df in test_data.items():
            print(f'\n🔍 Analisi {file_type}:')
            result = content_analyzer.analyze_content(df, f"test_{file_type}.xlsx")
            
            detected = result.get('detected_type', 'unknown')
            confidence = result.get('confidence_score', 0.0)
            
            print(f'   Rilevato: {detected} (confidenza: {confidence:.3f})')
            
            content_results[file_type] = {
                'detected': detected,
                'confidence': confidence,
                'correct': detected == file_type
            }
        
        content_accuracy = sum(1 for r in content_results.values() if r['correct']) / len(content_results) * 100
        print(f'\n✅ ContentBasedFileAnalyzer - Accuratezza: {content_accuracy:.1f}%')
        
    except Exception as e:
        print(f'❌ Errore ContentBasedFileAnalyzer: {e}')
        content_results = {}
    
    # Test 2: MultipleParsingStrategy
    print('\n🎯 TEST 2: MultipleParsingStrategy')
    print('-' * 40)
    
    try:
        from multiple_parsing_strategy import MultipleParsingStrategy
        strategy = MultipleParsingStrategy()
        
        strategy_results = {}
        for file_type, df in test_data.items():
            print(f'\n🔍 Strategia multipla per {file_type}:')
            result = strategy.analyze_with_multiple_strategies(df, f"test_{file_type}.xlsx", "balanced")
            
            final_result = result.get('final_result', {})
            detected = final_result.get('detected_type', 'unknown')
            confidence = final_result.get('confidence_score', 0.0)
            method = final_result.get('method_used', 'none')
            
            consensus = result.get('consensus_analysis', {})
            agreement = consensus.get('agreement_level', 0.0)
            
            print(f'   Rilevato: {detected} (confidenza: {confidence:.3f})')
            print(f'   Metodo: {method}, Accordo: {agreement:.3f}')
            
            strategy_results[file_type] = {
                'detected': detected,
                'confidence': confidence,
                'correct': detected == file_type,
                'agreement': agreement
            }
        
        strategy_accuracy = sum(1 for r in strategy_results.values() if r['correct']) / len(strategy_results) * 100
        print(f'\n✅ MultipleParsingStrategy - Accuratezza: {strategy_accuracy:.1f}%')
        
    except Exception as e:
        print(f'❌ Errore MultipleParsingStrategy: {e}')
        strategy_results = {}
    
    # Test 3: IntelligentEntityExtractor
    print('\n🔍 TEST 3: IntelligentEntityExtractor')
    print('-' * 40)
    
    try:
        from intelligent_entity_extractor import IntelligentEntityExtractor
        entity_extractor = IntelligentEntityExtractor()
        
        entity_results = {}
        total_entities_extracted = 0
        
        for file_type, df in test_data.items():
            print(f'\n🔍 Estrazione entità da {file_type}:')
            result = entity_extractor.extract_entities(df, file_type)
            
            entities = result.get('entities', {})
            entity_stats = result.get('entity_statistics', {})
            confidence_scores = result.get('confidence_scores', {})
            
            total_entities = sum(entity_stats.values())
            avg_confidence = sum(confidence_scores.values()) / len(confidence_scores) if confidence_scores else 0.0
            
            print(f'   Entità estratte: {total_entities}')
            print(f'   Tipi trovati: {len(entities)}')
            print(f'   Confidenza media: {avg_confidence:.3f}')
            
            entity_results[file_type] = {
                'total_entities': total_entities,
                'entity_types': len(entities),
                'avg_confidence': avg_confidence
            }
            
            total_entities_extracted += total_entities
        
        print(f'\n✅ IntelligentEntityExtractor - Entità totali estratte: {total_entities_extracted}')
        
    except Exception as e:
        print(f'❌ Errore IntelligentEntityExtractor: {e}')
        entity_results = {}
    
    # Statistiche finali integrate
    print('\n📈 STATISTICHE FINALI FASE 3')
    print('=' * 40)
    
    print(f'📊 Componenti testati: 3')
    
    if content_results:
        print(f'🎯 ContentBasedFileAnalyzer: {content_accuracy:.1f}% accuratezza')
    
    if strategy_results:
        print(f'🎯 MultipleParsingStrategy: {strategy_accuracy:.1f}% accuratezza')
        avg_agreement = sum(r.get('agreement', 0.0) for r in strategy_results.values()) / len(strategy_results)
        print(f'   Accordo medio analizzatori: {avg_agreement:.3f}')
    
    if entity_results:
        print(f'🎯 IntelligentEntityExtractor: {total_entities_extracted} entità estratte')
        avg_entity_confidence = sum(r.get('avg_confidence', 0.0) for r in entity_results.values()) / len(entity_results)
        print(f'   Confidenza media estrazione: {avg_entity_confidence:.3f}')
    
    # Valutazione complessiva
    print(f'\n🏆 VALUTAZIONE COMPLESSIVA FASE 3')
    print('-' * 40)
    
    components_working = sum([
        1 if content_results else 0,
        1 if strategy_results else 0,
        1 if entity_results else 0
    ])
    
    overall_score = components_working / 3 * 100
    
    if overall_score >= 80:
        status = "🟢 ECCELLENTE"
    elif overall_score >= 60:
        status = "🟡 BUONO"
    else:
        status = "🔴 NECESSITA MIGLIORAMENTI"
    
    print(f'Componenti funzionanti: {components_working}/3')
    print(f'Punteggio complessivo: {overall_score:.1f}%')
    print(f'Stato: {status}')
    
    return {
        'content_results': content_results,
        'strategy_results': strategy_results,
        'entity_results': entity_results,
        'overall_score': overall_score
    }

def create_comprehensive_test_data():
    """Crea dataset di test completi per tutti i tipi di file."""
    
    # Dataset attività
    attivita_data = pd.DataFrame({
        'ID Ticket': ['TK-12345', 'TK-12346', 'TK-12347', 'TK-12348', 'TK-12349'],
        'Titolo Attività': [
            'Installazione software ERP',
            'Riparazione server principale',
            'Configurazione rete WiFi',
            'Backup database clienti',
            'Aggiornamento sistema operativo'
        ],
        'Tecnico Assegnato': ['Mario Rossi', 'Luigi Verdi', 'Anna Bianchi', 'Marco Neri', 'Sara Blu'],
        'Cliente': ['Azienda ABC S.r.l.', 'Studio XYZ', 'Ufficio Milano', 'Ditta DEF', 'Società GHI'],
        'Data Inizio': ['15/01/2024', '16/01/2024', '17/01/2024', '18/01/2024', '19/01/2024'],
        'Data Fine': ['15/01/2024', '17/01/2024', '17/01/2024', '19/01/2024', '20/01/2024'],
        'Durata': ['4:30', '12:00', '2:15', '8:45', '6:30'],
        'Stato': ['Completato', 'In corso', 'Completato', 'In corso', 'Aperto'],
        'Progetto': ['PRJ-001', 'PRJ-002', 'PRJ-001', 'PRJ-003', 'PRJ-002']
    })
    
    # Dataset timbrature
    timbrature_data = pd.DataFrame({
        'Codice Dipendente': ['MR001', 'LV002', 'AB003', 'MN004', 'SB005'],
        'Nome Dipendente': ['Mario Rossi', 'Luigi Verdi', 'Anna Bianchi', 'Marco Neri', 'Sara Blu'],
        'Data': ['22/01/2024', '22/01/2024', '22/01/2024', '22/01/2024', '22/01/2024'],
        'Ora Entrata': ['08:30', '09:00', '08:45', '08:15', '09:15'],
        'Ora Uscita': ['17:30', '18:00', '17:45', '17:00', '18:15'],
        'Pausa Pranzo': ['12:30-13:30', '13:00-14:00', '12:45-13:45', '12:00-13:00', '13:15-14:15'],
        'Ore Lavorate': ['8:00', '8:00', '8:00', '8:00', '8:00'],
        'Sede Lavoro': ['Milano', 'Roma', 'Milano', 'Torino', 'Roma']
    })
    
    # Dataset calendario
    calendario_data = pd.DataFrame({
        'Titolo Evento': [
            'Riunione settimanale team',
            'Presentazione progetto ABC',
            'Formazione sicurezza',
            'Call cliente XYZ',
            'Review codice sviluppo'
        ],
        'Data Evento': ['25/01/2024', '26/01/2024', '27/01/2024', '28/01/2024', '29/01/2024'],
        'Ora Inizio': ['09:00', '14:30', '10:00', '15:00', '11:00'],
        'Ora Fine': ['10:30', '16:00', '12:00', '16:00', '12:30'],
        'Partecipanti': [
            'Mario Rossi, Luigi Verdi, Anna Bianchi',
            'Tutto il team sviluppo',
            'Tutti i dipendenti',
            'Mario Rossi, Cliente ABC',
            'Luigi Verdi, Anna Bianchi'
        ],
        'Luogo': ['Sala Riunioni A', 'Sala Conferenze', 'Aula Magna', 'Online Teams', 'Sala Sviluppo'],
        'Organizzatore': ['Mario Rossi', 'Luigi Verdi', 'HR Manager', 'Mario Rossi', 'Anna Bianchi']
    })
    
    return {
        'attivita': attivita_data,
        'timbrature': timbrature_data,
        'calendario': calendario_data
    }

if __name__ == "__main__":
    try:
        start_time = time.time()
        results = test_fase3_complete()
        end_time = time.time()
        
        print(f'\n⏱️ Tempo totale test: {end_time - start_time:.2f} secondi')
        print(f'🎉 Test Fase 3 completato!')
        
    except Exception as e:
        print(f"❌ Errore generale nel test: {e}")
        import traceback
        traceback.print_exc()
