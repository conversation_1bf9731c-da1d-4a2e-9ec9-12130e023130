#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FASE 6: Health Monitor e Sistema di Stabilizzazione
Sistema di monitoraggio salute applicazione e stabilizzazione automatica.
Versione: 1.0.0 - FASE 6 Testing e Stabilizzazione
"""

import asyncio
import time
import psutil
import threading
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import json
import os
import sys

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('health_monitor.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

@dataclass
class HealthMetric:
    """Metrica di salute del sistema."""
    name: str
    value: float
    unit: str
    status: str  # 'healthy', 'warning', 'critical'
    threshold_warning: float
    threshold_critical: float
    timestamp: datetime

@dataclass
class SystemHealth:
    """Stato di salute del sistema."""
    overall_status: str
    metrics: List[HealthMetric]
    timestamp: datetime
    uptime_seconds: float
    issues: List[str]

class HealthMonitor:
    """Sistema di monitoraggio salute applicazione."""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.metrics_history = []
        self.is_monitoring = False
        self.monitor_thread = None
        self.check_interval = 30  # secondi
        
        # Soglie di allarme
        self.thresholds = {
            'cpu_percent': {'warning': 70.0, 'critical': 90.0},
            'memory_percent': {'warning': 80.0, 'critical': 95.0},
            'disk_percent': {'warning': 85.0, 'critical': 95.0},
            'response_time': {'warning': 2.0, 'critical': 5.0},
            'error_rate': {'warning': 5.0, 'critical': 10.0}
        }
        
        logger.info("🏥 Health Monitor inizializzato")
    
    def start_monitoring(self):
        """Avvia il monitoraggio continuo."""
        if self.is_monitoring:
            logger.warning("Monitoraggio già attivo")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("🚀 Monitoraggio salute avviato")
    
    def stop_monitoring(self):
        """Ferma il monitoraggio."""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("⏹️ Monitoraggio salute fermato")
    
    def _monitor_loop(self):
        """Loop principale di monitoraggio."""
        while self.is_monitoring:
            try:
                health = self.check_system_health()
                self.metrics_history.append(health)
                
                # Mantieni solo le ultime 100 metriche
                if len(self.metrics_history) > 100:
                    self.metrics_history = self.metrics_history[-100:]
                
                # Log se ci sono problemi
                if health.overall_status != 'healthy':
                    logger.warning(f"⚠️ Stato sistema: {health.overall_status}")
                    for issue in health.issues:
                        logger.warning(f"   - {issue}")
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"❌ Errore nel monitoraggio: {str(e)}")
                time.sleep(self.check_interval)
    
    def check_system_health(self) -> SystemHealth:
        """Controlla lo stato di salute del sistema."""
        metrics = []
        issues = []
        
        try:
            # CPU
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_metric = self._create_metric(
                'cpu_percent', cpu_percent, '%', 'cpu_percent'
            )
            metrics.append(cpu_metric)
            if cpu_metric.status != 'healthy':
                issues.append(f"CPU usage: {cpu_percent:.1f}%")
            
            # Memoria
            memory = psutil.virtual_memory()
            memory_metric = self._create_metric(
                'memory_percent', memory.percent, '%', 'memory_percent'
            )
            metrics.append(memory_metric)
            if memory_metric.status != 'healthy':
                issues.append(f"Memory usage: {memory.percent:.1f}%")
            
            # Disco
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            disk_metric = self._create_metric(
                'disk_percent', disk_percent, '%', 'disk_percent'
            )
            metrics.append(disk_metric)
            if disk_metric.status != 'healthy':
                issues.append(f"Disk usage: {disk_percent:.1f}%")
            
            # Uptime
            uptime = (datetime.now() - self.start_time).total_seconds()
            
            # Stato generale
            critical_count = sum(1 for m in metrics if m.status == 'critical')
            warning_count = sum(1 for m in metrics if m.status == 'warning')
            
            if critical_count > 0:
                overall_status = 'critical'
            elif warning_count > 0:
                overall_status = 'warning'
            else:
                overall_status = 'healthy'
            
            return SystemHealth(
                overall_status=overall_status,
                metrics=metrics,
                timestamp=datetime.now(),
                uptime_seconds=uptime,
                issues=issues
            )
            
        except Exception as e:
            logger.error(f"❌ Errore nel check salute: {str(e)}")
            return SystemHealth(
                overall_status='critical',
                metrics=[],
                timestamp=datetime.now(),
                uptime_seconds=0,
                issues=[f"Errore monitoraggio: {str(e)}"]
            )
    
    def _create_metric(self, name: str, value: float, unit: str, threshold_key: str) -> HealthMetric:
        """Crea una metrica di salute."""
        thresholds = self.thresholds.get(threshold_key, {'warning': 80, 'critical': 95})
        
        if value >= thresholds['critical']:
            status = 'critical'
        elif value >= thresholds['warning']:
            status = 'warning'
        else:
            status = 'healthy'
        
        return HealthMetric(
            name=name,
            value=value,
            unit=unit,
            status=status,
            threshold_warning=thresholds['warning'],
            threshold_critical=thresholds['critical'],
            timestamp=datetime.now()
        )
    
    def get_health_report(self) -> Dict[str, Any]:
        """Ottiene report completo di salute."""
        current_health = self.check_system_health()
        
        # Statistiche storiche
        if self.metrics_history:
            avg_cpu = sum(h.metrics[0].value for h in self.metrics_history if h.metrics) / len(self.metrics_history)
            avg_memory = sum(h.metrics[1].value for h in self.metrics_history if len(h.metrics) > 1) / len(self.metrics_history)
        else:
            avg_cpu = avg_memory = 0
        
        return {
            'current_status': current_health.overall_status,
            'uptime_hours': current_health.uptime_seconds / 3600,
            'current_metrics': {
                metric.name: {
                    'value': metric.value,
                    'unit': metric.unit,
                    'status': metric.status
                } for metric in current_health.metrics
            },
            'historical_averages': {
                'cpu_percent': avg_cpu,
                'memory_percent': avg_memory
            },
            'issues': current_health.issues,
            'monitoring_active': self.is_monitoring,
            'last_check': current_health.timestamp.isoformat()
        }
    
    def export_metrics(self, filepath: str):
        """Esporta metriche in file JSON."""
        try:
            data = {
                'export_time': datetime.now().isoformat(),
                'metrics_count': len(self.metrics_history),
                'metrics': [
                    {
                        'timestamp': h.timestamp.isoformat(),
                        'status': h.overall_status,
                        'uptime': h.uptime_seconds,
                        'metrics': {
                            m.name: {
                                'value': m.value,
                                'unit': m.unit,
                                'status': m.status
                            } for m in h.metrics
                        },
                        'issues': h.issues
                    } for h in self.metrics_history
                ]
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"📊 Metriche esportate in {filepath}")
            
        except Exception as e:
            logger.error(f"❌ Errore esportazione metriche: {str(e)}")

# Istanza globale
health_monitor = HealthMonitor()

def main():
    """Test del sistema di monitoraggio."""
    print("🏥 Test Health Monitor")
    print("=" * 50)
    
    # Avvia monitoraggio
    health_monitor.start_monitoring()
    
    try:
        # Test per 30 secondi
        for i in range(6):
            time.sleep(5)
            health = health_monitor.check_system_health()
            print(f"\n📊 Check {i+1}/6:")
            print(f"   Status: {health.overall_status}")
            print(f"   Uptime: {health.uptime_seconds:.1f}s")
            
            for metric in health.metrics:
                print(f"   {metric.name}: {metric.value:.1f}{metric.unit} ({metric.status})")
            
            if health.issues:
                print(f"   Issues: {', '.join(health.issues)}")
        
        # Report finale
        report = health_monitor.get_health_report()
        print(f"\n📋 Report finale:")
        print(f"   Status: {report['current_status']}")
        print(f"   Uptime: {report['uptime_hours']:.2f}h")
        print(f"   Monitoring: {report['monitoring_active']}")
        
        # Esporta metriche
        health_monitor.export_metrics('health_metrics.json')
        
    finally:
        health_monitor.stop_monitoring()
    
    print("\n✅ Test Health Monitor completato")

if __name__ == "__main__":
    main()
