@echo off
echo ===================================
echo Avvio dell'applicazione app-roberto
echo ===================================
echo.

REM Verifica se l'ambiente virtuale esiste
if not exist venv (
    echo Creazione dell'ambiente virtuale...
    python -m venv venv
    echo Ambiente virtuale creato.
    echo.
)

REM Attiva l'ambiente virtuale
echo Attivazione dell'ambiente virtuale...
call venv\Scripts\activate
echo Ambiente virtuale attivato.
echo.

REM Verifica se le dipendenze sono installate
echo Verifica delle dipendenze...
pip freeze 2>nul > temp_requirements.txt
findstr /i "flask pandas plotly" temp_requirements.txt > nul 2>nul
if %errorlevel% neq 0 (
    echo Installazione delle dipendenze...
    pip install -r requirements.txt 2>nul
    echo Dipendenze installate.
) else (
    echo Le dipendenze sono già installate.
)
del temp_requirements.txt
echo.

REM Rimuovi pacchetti problematici e ripara l'installazione di plotly
echo Pulizia pacchetti problematici...

REM Rimuovi manualmente la directory del pacchetto problematico ~lotly
if exist venv\Lib\site-packages\~lotly (
    echo Rimozione manuale del pacchetto ~lotly...
    rd /s /q venv\Lib\site-packages\~lotly 2>nul
)

REM Rimuovi anche eventuali file di metadati associati
if exist venv\Lib\site-packages\~lotly-* (
    echo Rimozione file di metadati ~lotly...
    del /q venv\Lib\site-packages\~lotly-* 2>nul
)

REM Pulisci la cache di pip per evitare problemi di installazione
echo Pulizia della cache di pip...
pip cache purge 2>nul

REM Reinstalla plotly per assicurarsi che sia correttamente installato
echo Reinstallazione del pacchetto plotly...
pip uninstall -y plotly 2>nul
pip install plotly==5.16.1 2>nul

REM Verifica che plotly sia stato installato correttamente
pip show plotly > nul 2>&1
if %errorlevel% neq 0 (
    echo ATTENZIONE: Problemi con l'installazione di plotly. Tentativo di riparazione...
    pip install --force-reinstall plotly==5.16.1 2>nul
) else (
    echo Pacchetto plotly installato correttamente.
)
echo.

REM Crea la cartella uploads se non esiste
if not exist uploads (
    echo Creazione della cartella uploads...
    mkdir uploads
    echo Cartella uploads creata.
    echo.
)

REM Avvia il server MCP in una nuova finestra
echo Avvio del server MCP...
start "MCP Server" cmd /c "call venv\Scripts\activate && cd mcp_server && python run_server.py"
echo Server MCP avviato in una nuova finestra.
echo.

REM Attendi 5 secondi per dare tempo al server MCP di avviarsi
echo Attesa di 5 secondi per l'avvio del server MCP...
ping 127.0.0.1 -n 6 > nul
echo.

REM Avvia l'applicazione principale
echo Avvio dell'applicazione principale...
python app.py
echo.

REM Disattiva l'ambiente virtuale (questo punto viene raggiunto solo quando l'app principale viene chiusa)
call venv\Scripts\deactivate
echo Ambiente virtuale disattivato.
echo.

echo ===================================
echo Applicazione terminata
echo ===================================
pause



