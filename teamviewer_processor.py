#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from datetime import datetime
import re
from data_processor import DataProcessor
from universal_file_reader import universal_reader

class TeamViewerProcessor:
    """
    Classe specifica per l'elaborazione dei dati TeamViewer.
    Estende le funzionalità di DataProcessor per gestire le specificità dei file TeamViewer.
    """

    def __init__(self):
        self.data_processor = DataProcessor()

        # Mappatura specifica per i campi TeamViewer
        self.teamviewer_fields = {
            'Utente': 'tecnico',
            'Computer': 'cliente',
            'ID': 'id_sessione',
            'Tipo di sessione': 'tipo_sessione',
            'Gruppo': 'gruppo',
            'Inizio': 'data_inizio',
            'Fine': 'data_fine',
            'Durata': 'durata',
            'Note': 'note',
            'Classificazione': 'valutazione',
            'Commenti del cliente': 'commenti'
        }

        # Mappatura dei tipi di sessione
        self.session_types = {
            'Accesso completo': 'accesso_completo',
            'Trasferimento file': 'trasferimento_file',
            'VPN': 'vpn',
            'Gestione': 'gestione',
            'Riunione': 'riunione'
        }

    def process_teamviewer_file(self, file_path):
        """
        Elabora un file TeamViewer e restituisce un DataFrame standardizzato
        """
        # Usa Universal File Reader per lettura automatica
        df, file_info = universal_reader.read_file(file_path)

        if not file_info['success']:
            raise ValueError(f"Errore lettura file: {file_info.get('error', 'Errore sconosciuto')}")

        print(f"📊 File TeamViewer letto: {file_info['rows']} righe, {file_info['columns']} colonne")
        print(f"📋 Colonne rilevate: {df.columns.tolist()}")

        # Standardizza i nomi delle colonne
        df = self._standardize_teamviewer_columns(df)

        # Elabora le date
        df = self._process_teamviewer_dates(df)

        # Elabora le durate
        df = self._process_teamviewer_durations(df)

        # Calcola metriche aggiuntive
        df = self._calculate_additional_metrics(df)

        return df

    def _standardize_teamviewer_columns(self, df):
        """
        Standardizza i nomi delle colonne specifiche di TeamViewer
        """
        renamed_columns = {}

        for col in df.columns:
            if col in self.teamviewer_fields:
                renamed_columns[col] = self.teamviewer_fields[col]

        if renamed_columns:
            return df.rename(columns=renamed_columns)

        return df

    def _process_teamviewer_dates(self, df):
        """
        Elabora le colonne di date specifiche di TeamViewer
        """
        date_columns = ['data_inizio', 'data_fine']

        for col in date_columns:
            if col in df.columns:
                df[col] = df[col].apply(self.data_processor.parse_italian_date)

        # Aggiungi colonna per la data (solo giorno)
        if 'data_inizio' in df.columns:
            # Salva la data come stringa in formato ISO
            df['data'] = df['data_inizio'].dt.date.apply(lambda x: x.isoformat() if pd.notna(x) else None)

            # Salva gli oggetti time in colonne temporanee per i calcoli
            time_series = df['data_inizio'].dt.time
            df['ora_inizio_obj'] = time_series

            # Converti gli oggetti time in stringhe per la serializzazione JSON
            df['ora_inizio'] = time_series.apply(lambda x: x.strftime('%H:%M') if pd.notna(x) else None)

        if 'data_fine' in df.columns:
            # Salva gli oggetti time in colonne temporanee per i calcoli
            time_series = df['data_fine'].dt.time
            df['ora_fine_obj'] = time_series

            # Converti gli oggetti time in stringhe per la serializzazione JSON
            df['ora_fine'] = time_series.apply(lambda x: x.strftime('%H:%M') if pd.notna(x) else None)

        # Rimuovi le colonne temporanee dopo aver completato i calcoli
        temp_columns = ['ora_inizio_obj', 'ora_fine_obj']
        for col in temp_columns:
            if col in df.columns:
                df.drop(col, axis=1, inplace=True)

        return df

    def _process_teamviewer_durations(self, df):
        """
        Elabora le durate delle sessioni TeamViewer
        """
        if 'durata' in df.columns:
            df['durata_minuti'] = df['durata'].apply(self.data_processor.parse_duration)

            # Calcola durata in formato ore:minuti
            df['durata_formattata'] = df['durata_minuti'].apply(
                lambda x: f"{x // 60}h {x % 60}m" if x >= 60 else f"{x}m"
            )

        return df

    def _calculate_additional_metrics(self, df):
        """
        Calcola metriche aggiuntive utili per l'analisi
        """
        # Giorno della settimana
        if 'data_inizio' in df.columns:
            df['giorno_settimana'] = df['data_inizio'].dt.day_name()
            df['ora_del_giorno'] = df['data_inizio'].dt.hour
            df['mese'] = df['data_inizio'].dt.month
            df['anno'] = df['data_inizio'].dt.year
            df['settimana_anno'] = df['data_inizio'].dt.isocalendar().week

        # Categorizza le sessioni per durata
        if 'durata_minuti' in df.columns:
            df['categoria_durata'] = pd.cut(
                df['durata_minuti'],
                bins=[0, 5, 15, 30, 60, 120, float('inf')],
                labels=['Molto breve (<5m)', 'Breve (5-15m)', 'Media (15-30m)',
                        'Lunga (30-60m)', 'Molto lunga (1-2h)', 'Estesa (>2h)']
            )

        return df

    def generate_summary_stats(self, df):
        """
        Genera statistiche di riepilogo per i dati TeamViewer
        """
        stats = {}

        # Conteggio totale sessioni
        stats['total_sessions'] = len(df)

        # Durata media
        if 'durata_minuti' in df.columns:
            stats['avg_duration'] = df['durata_minuti'].mean()
            stats['total_duration'] = df['durata_minuti'].sum()
            stats['max_duration'] = df['durata_minuti'].max()

        # Tecnici unici
        if 'tecnico' in df.columns:
            stats['unique_technicians'] = df['tecnico'].nunique()
            stats['sessions_by_technician'] = df.groupby('tecnico').size().to_dict()

        # Clienti unici
        if 'cliente' in df.columns:
            stats['unique_clients'] = df['cliente'].nunique()
            stats['sessions_by_client'] = df.groupby('cliente').size().to_dict()

        # Distribuzione per giorno della settimana
        if 'giorno_settimana' in df.columns:
            stats['sessions_by_weekday'] = df.groupby('giorno_settimana').size().to_dict()

        # Distribuzione per ora del giorno
        if 'ora_del_giorno' in df.columns:
            stats['sessions_by_hour'] = df.groupby('ora_del_giorno').size().to_dict()

        return stats
