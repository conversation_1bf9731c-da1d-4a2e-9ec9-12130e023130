#!/usr/bin/env python3
"""
Test diretto dell'endpoint wizard con debug completo
"""

import requests
import json
import sys

def test_wizard_direct():
    """Test diretto dell'endpoint wizard"""
    
    print("🧪 TEST DIRETTO ENDPOINT WIZARD")
    print("=" * 50)
    
    # Test 1: Verifica OPTIONS (CORS)
    print("1️⃣ Test OPTIONS (CORS)...")
    try:
        response = requests.options("http://127.0.0.1:5000/api/wizard/complete", timeout=10)
        print(f"   Status: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        if response.status_code == 200:
            print("   ✅ CORS OK")
        else:
            print(f"   ❌ CORS FAILED: {response.text}")
    except Exception as e:
        print(f"   ❌ ERRORE OPTIONS: {str(e)}")
    
    print()
    
    # Test 2: Verifica POST con dati minimi
    print("2️⃣ Test POST con dati minimi...")
    minimal_data = {
        "files": [],
        "employees": [],
        "vehicles": [],
        "configuration": {}
    }
    
    try:
        response = requests.post(
            "http://127.0.0.1:5000/api/wizard/complete",
            json=minimal_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"   Status: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        print(f"   Content-Type: {response.headers.get('content-type', 'N/A')}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("   ✅ RISPOSTA JSON VALIDA")
                print(f"   Success: {result.get('success', 'N/A')}")
                print(f"   Message: {result.get('message', 'N/A')}")
                if 'data' in result:
                    print(f"   Redirect: {result['data'].get('redirect', 'N/A')}")
                    print(f"   Supabase: {result['data'].get('supabase_integration', 'N/A')}")
            except json.JSONDecodeError:
                print(f"   ❌ RISPOSTA NON JSON: {response.text[:200]}...")
        else:
            print(f"   ❌ ERRORE HTTP: {response.text[:200]}...")
            
    except requests.exceptions.Timeout:
        print("   ❌ TIMEOUT")
    except requests.exceptions.ConnectionError:
        print("   ❌ CONNECTION ERROR")
    except Exception as e:
        print(f"   ❌ ERRORE GENERICO: {str(e)}")
    
    print()
    
    # Test 3: Verifica altri endpoint per confronto
    print("3️⃣ Test endpoint di confronto...")
    
    # Test /api/health
    try:
        response = requests.get("http://127.0.0.1:5000/api/health", timeout=10)
        print(f"   /api/health: {response.status_code} ({'✅' if response.status_code == 200 else '❌'})")
    except:
        print("   /api/health: ❌ ERRORE")
    
    # Test /api/endpoints
    try:
        response = requests.get("http://127.0.0.1:5000/api/endpoints", timeout=10)
        print(f"   /api/endpoints: {response.status_code} ({'✅' if response.status_code == 200 else '❌'})")
    except:
        print("   /api/endpoints: ❌ ERRORE")
    
    # Test /api/config/employees
    try:
        response = requests.get("http://127.0.0.1:5000/api/config/employees", timeout=10)
        print(f"   /api/config/employees: {response.status_code} ({'✅' if response.status_code == 200 else '❌'})")
    except:
        print("   /api/config/employees: ❌ ERRORE")
    
    print()
    
    # Test 4: Verifica con curl simulato
    print("4️⃣ Test con richiesta raw...")
    try:
        import urllib.request
        import urllib.parse
        
        data = json.dumps(minimal_data).encode('utf-8')
        req = urllib.request.Request(
            'http://127.0.0.1:5000/api/wizard/complete',
            data=data,
            headers={'Content-Type': 'application/json'}
        )
        
        with urllib.request.urlopen(req, timeout=30) as response:
            status = response.getcode()
            content = response.read().decode('utf-8')
            print(f"   Status: {status}")
            print(f"   Content: {content[:200]}...")
            
    except urllib.error.HTTPError as e:
        print(f"   ❌ HTTP ERROR: {e.code} - {e.reason}")
        print(f"   Content: {e.read().decode('utf-8')[:200]}...")
    except Exception as e:
        print(f"   ❌ ERRORE: {str(e)}")

if __name__ == "__main__":
    test_wizard_direct()
