<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧙‍♂️ Wizard Interattivo - Caricamento File</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        .wizard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .wizard-step {
            display: none;
            animation: fadeIn 0.5s ease-in;
        }

        .wizard-step.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .step-item {
            display: flex;
            align-items: center;
            margin: 0 10px;
        }

        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .step-circle.active {
            background: #007bff;
            color: white;
        }

        .step-circle.completed {
            background: #28a745;
            color: white;
        }

        .file-drop-zone {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .file-drop-zone:hover {
            background: #e3f2fd;
            border-color: #0056b3;
        }

        .file-drop-zone.dragover {
            background: #bbdefb;
            border-color: #0056b3;
        }

        .column-mapping-table {
            max-height: 400px;
            overflow-y: auto;
        }

        .sample-data-table {
            max-height: 300px;
            overflow: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }

        .confidence-badge {
            font-size: 0.8em;
        }

        .entity-mapping-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
        }

        .entity-new {
            border-left: 4px solid #28a745;
        }

        .entity-existing {
            border-left: 4px solid #007bff;
        }

        .progress-container {
            margin: 20px 0;
        }

        .loading-spinner {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .wizard-navigation {
            display: flex;
            justify-content: between;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
        }

        .btn-wizard {
            min-width: 120px;
        }

        .alert-wizard {
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="wizard-container">
        <!-- Header -->
        <div class="text-center mb-4">
            <h1 class="h2">
                <i class="fas fa-magic text-primary"></i>
                Wizard Interattivo di Caricamento File
            </h1>
            <p class="text-muted">
                Carica e valida i tuoi file passo dopo passo con controllo completo sui dati
            </p>
        </div>

        <!-- Step Indicator -->
        <div class="step-indicator">
            <div class="step-item">
                <div class="step-circle active" id="step-circle-1">1</div>
                <span>Caricamento File</span>
            </div>
            <div class="step-item">
                <div class="step-circle" id="step-circle-2">2</div>
                <span>Validazione Colonne</span>
            </div>
            <div class="step-item">
                <div class="step-circle" id="step-circle-3">3</div>
                <span>Normalizzazione Entità</span>
            </div>
            <div class="step-item">
                <div class="step-circle" id="step-circle-4">4</div>
                <span>Inserimento Dati</span>
            </div>
        </div>

        <!-- STEP 1: Caricamento File -->
        <div class="wizard-step active" id="step-1">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-upload"></i>
                        Step 1: Caricamento File
                    </h4>
                </div>
                <div class="card-body">
                    <div class="file-drop-zone" id="file-drop-zone">
                        <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                        <h5>Trascina il file qui o clicca per selezionare</h5>
                        <p class="text-muted">
                            Formati supportati: CSV, Excel (.xlsx, .xls)<br>
                            Dimensione massima: 50MB
                        </p>
                        <label for="file-input" class="visually-hidden">Seleziona file da caricare</label>
                        <input type="file" id="file-input" class="d-none" accept=".csv,.xlsx,.xls" title="Seleziona file CSV o Excel" placeholder="Seleziona un file">
                        <button type="button" class="btn btn-primary" onclick="document.getElementById('file-input').click()">
                            <i class="fas fa-folder-open"></i>
                            Seleziona File
                        </button>
                    </div>

                    <div id="file-info" class="mt-3 wizard-hidden">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle"></i> File Selezionato:</h6>
                            <div id="file-details"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- STEP 2: Validazione Colonne -->
        <div class="wizard-step" id="step-2">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-table"></i>
                        Step 2: Validazione Colonne
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>📊 Anteprima Dati</h6>
                            <div class="sample-data-table" id="sample-data-container">
                                <!-- Dati di anteprima verranno caricati qui -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>🔍 Colonne Rilevate</h6>
                            <div class="column-mapping-table" id="column-mapping-container">
                                <!-- Mappatura colonne verrà caricata qui -->
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h6>❓ Domande di Validazione</h6>
                        <div id="validation-questions">
                            <!-- Domande di validazione verranno caricate qui -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- STEP 3: Normalizzazione Entità -->
        <div class="wizard-step" id="step-3">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-sitemap"></i>
                        Step 3: Normalizzazione Entità
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Normalizzazione Entità:</strong>
                        Mappa i nomi trovati nel file alle entità esistenti nel database o crea nuove entità.
                    </div>

                    <div id="entity-mapping-container">
                        <!-- Mappature entità verranno caricate qui -->
                    </div>
                </div>
            </div>
        </div>

        <!-- STEP 4: Inserimento Dati -->
        <div class="wizard-step" id="step-4">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-database"></i>
                        Step 4: Inserimento Dati
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Conferma Inserimento:</strong>
                        I dati sono stati validati e normalizzati. Conferma per inserirli nel database Supabase.
                    </div>

                    <div id="insertion-summary">
                        <!-- Riepilogo inserimento verrà caricato qui -->
                    </div>

                    <div class="progress-container wizard-hidden" id="insertion-progress">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated wizard-progress-bar"
                                 role="progressbar" id="progress-bar">
                            </div>
                        </div>
                        <div class="text-center mt-2">
                            <small id="progress-text">Preparazione inserimento...</small>
                        </div>
                    </div>

                    <div id="insertion-results" class="wizard-hidden">
                        <!-- Risultati inserimento verranno mostrati qui -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Spinner -->
        <div class="loading-spinner" id="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Caricamento...</span>
            </div>
            <p class="mt-2">Elaborazione in corso...</p>
        </div>

        <!-- Navigation -->
        <div class="wizard-navigation">
            <button type="button" class="btn btn-secondary btn-wizard" id="btn-previous" onclick="previousStep()" disabled>
                <i class="fas fa-arrow-left"></i>
                Indietro
            </button>

            <div class="ms-auto">
                <button type="button" class="btn btn-primary btn-wizard" id="btn-next" onclick="nextStep()" disabled>
                    Avanti
                    <i class="fas fa-arrow-right"></i>
                </button>
                <button type="button" class="btn btn-success btn-wizard wizard-hidden" id="btn-finish" onclick="finishWizard()">
                    <i class="fas fa-check"></i>
                    Completa
                </button>
            </div>
        </div>

        <!-- Alert Container -->
        <div id="alert-container"></div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Stato globale del wizard
        let wizardState = {
            currentStep: 1,
            totalSteps: 4,
            fileId: null,
            analysisResult: null,
            validationResult: null,
            normalizationResult: null,
            insertionResult: null
        };

        // Inizializzazione
        document.addEventListener('DOMContentLoaded', function() {
            setupFileUpload();
            updateNavigationButtons();
        });

        // Setup file upload
        function setupFileUpload() {
            const dropZone = document.getElementById('file-drop-zone');
            const fileInput = document.getElementById('file-input');

            // Drag and drop events
            dropZone.addEventListener('dragover', function(e) {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });

            dropZone.addEventListener('dragleave', function(e) {
                e.preventDefault();
                dropZone.classList.remove('dragover');
            });

            dropZone.addEventListener('drop', function(e) {
                e.preventDefault();
                dropZone.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileSelection(files[0]);
                }
            });

            // File input change
            fileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    handleFileSelection(e.target.files[0]);
                }
            });

            // Click to select
            dropZone.addEventListener('click', function() {
                fileInput.click();
            });
        }

        // Gestione selezione file
        function handleFileSelection(file) {
            // Validazione file
            const maxSize = 50 * 1024 * 1024; // 50MB
            const allowedTypes = ['.csv', '.xlsx', '.xls'];

            if (file.size > maxSize) {
                showAlert('Errore: Il file è troppo grande (max 50MB)', 'danger');
                return;
            }

            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
            if (!allowedTypes.includes(fileExtension)) {
                showAlert('Errore: Formato file non supportato', 'danger');
                return;
            }

            // Mostra info file
            showFileInfo(file);

            // Avvia analisi
            startFileAnalysis(file);
        }

        // Mostra informazioni file
        function showFileInfo(file) {
            const fileInfo = document.getElementById('file-info');
            const fileDetails = document.getElementById('file-details');

            fileDetails.innerHTML = `
                <strong>Nome:</strong> ${file.name}<br>
                <strong>Dimensione:</strong> ${formatFileSize(file.size)}<br>
                <strong>Tipo:</strong> ${file.type || 'Non specificato'}
            `;

            fileInfo.classList.remove('wizard-hidden');
        }

        // Avvia analisi file
        async function startFileAnalysis(file) {
            showLoading(true);

            try {
                const formData = new FormData();
                formData.append('file', file);

                const response = await fetch('/api/wizard/interactive/start', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    wizardState.fileId = result.data.file_id;
                    wizardState.analysisResult = result.data;

                    showAlert('File analizzato con successo!', 'success');
                    enableNextStep();
                } else {
                    showAlert('Errore analisi file: ' + result.error, 'danger');
                }

            } catch (error) {
                console.error('Errore analisi file:', error);
                showAlert('Errore di connessione durante l\'analisi del file', 'danger');
            } finally {
                showLoading(false);
            }
        }

        // Utility functions
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showLoading(show) {
            const spinner = document.getElementById('loading-spinner');
            if (show) {
                spinner.classList.remove('wizard-hidden');
            } else {
                spinner.classList.add('wizard-hidden');
            }
        }

        function showAlert(message, type) {
            const alertContainer = document.getElementById('alert-container');
            const alertId = 'alert-' + Date.now();

            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show alert-wizard" id="${alertId}">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            alertContainer.innerHTML = alertHtml;

            // Auto-remove dopo 5 secondi
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    alertElement.remove();
                }
            }, 5000);
        }

        function enableNextStep() {
            document.getElementById('btn-next').disabled = false;
        }

        function updateNavigationButtons() {
            const btnPrevious = document.getElementById('btn-previous');
            const btnNext = document.getElementById('btn-next');
            const btnFinish = document.getElementById('btn-finish');

            btnPrevious.disabled = wizardState.currentStep === 1;

            if (wizardState.currentStep === wizardState.totalSteps) {
                btnNext.classList.add('wizard-hidden');
                btnFinish.classList.remove('wizard-hidden');
            } else {
                btnNext.classList.remove('wizard-hidden');
                btnFinish.classList.add('wizard-hidden');
            }
        }

        // Navigation functions
        function nextStep() {
            if (wizardState.currentStep < wizardState.totalSteps) {
                wizardState.currentStep++;
                showStep(wizardState.currentStep);
                updateStepIndicator();
                updateNavigationButtons();

                // Carica contenuto step
                loadStepContent(wizardState.currentStep);
            }
        }

        function previousStep() {
            if (wizardState.currentStep > 1) {
                wizardState.currentStep--;
                showStep(wizardState.currentStep);
                updateStepIndicator();
                updateNavigationButtons();
            }
        }

        function showStep(stepNumber) {
            // Nascondi tutti gli step
            document.querySelectorAll('.wizard-step').forEach(step => {
                step.classList.remove('active');
            });

            // Mostra step corrente
            document.getElementById(`step-${stepNumber}`).classList.add('active');
        }

        function updateStepIndicator() {
            for (let i = 1; i <= wizardState.totalSteps; i++) {
                const circle = document.getElementById(`step-circle-${i}`);
                circle.classList.remove('active', 'completed');

                if (i < wizardState.currentStep) {
                    circle.classList.add('completed');
                } else if (i === wizardState.currentStep) {
                    circle.classList.add('active');
                }
            }
        }

        function loadStepContent(stepNumber) {
            switch (stepNumber) {
                case 2:
                    loadValidationStep();
                    break;
                case 3:
                    loadNormalizationStep();
                    break;
                case 4:
                    loadInsertionStep();
                    break;
            }
        }

        // Step-specific functions
        function loadValidationStep() {
            if (!wizardState.analysisResult) return;

            // Carica anteprima dati
            loadSampleData();

            // Carica mappatura colonne
            loadColumnMapping();

            // Carica domande validazione
            loadValidationQuestions();
        }

        function loadSampleData() {
            const container = document.getElementById('sample-data-container');
            const sampleData = wizardState.analysisResult.feedback_data?.sample_data || [];

            if (sampleData.length === 0) {
                container.innerHTML = '<p class="text-muted">Nessun dato di anteprima disponibile</p>';
                return;
            }

            // Crea tabella HTML
            const headers = Object.keys(sampleData[0]);
            let tableHtml = '<table class="table table-sm table-striped">';

            // Header
            tableHtml += '<thead><tr>';
            headers.forEach(header => {
                tableHtml += `<th>${header}</th>`;
            });
            tableHtml += '</tr></thead>';

            // Righe
            tableHtml += '<tbody>';
            sampleData.slice(0, 5).forEach(row => {
                tableHtml += '<tr>';
                headers.forEach(header => {
                    const value = row[header] || '';
                    tableHtml += `<td>${String(value).substring(0, 50)}</td>`;
                });
                tableHtml += '</tr>';
            });
            tableHtml += '</tbody></table>';

            container.innerHTML = tableHtml;
        }

        function loadColumnMapping() {
            const container = document.getElementById('column-mapping-container');
            const detectedColumns = wizardState.analysisResult.feedback_data?.detected_columns || {};

            if (Object.keys(detectedColumns).length === 0) {
                container.innerHTML = '<p class="text-muted">Nessuna colonna rilevata</p>';
                return;
            }

            let mappingHtml = '';

            Object.entries(detectedColumns).forEach(([colName, colInfo]) => {
                const confidenceClass = colInfo.confidence > 0.7 ? 'success' :
                                      colInfo.confidence > 0.4 ? 'warning' : 'danger';

                mappingHtml += `
                    <div class="card mb-2">
                        <div class="card-body p-3">
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <strong>${colName}</strong>
                                </div>
                                <div class="col-md-4">
                                    <select class="form-select form-select-sm"
                                            id="col-type-${colName.replace(/\s+/g, '_')}"
                                            onchange="updateColumnType('${colName}', this.value)">
                                        <option value="${colInfo.detected_type}" selected>
                                            ${colInfo.detected_type}
                                        </option>
                                        <option value="employee_name">Nome Dipendente</option>
                                        <option value="client_name">Nome Cliente</option>
                                        <option value="project_name">Nome Progetto</option>
                                        <option value="vehicle_plate">Targa Veicolo</option>
                                        <option value="date">Data</option>
                                        <option value="time_start">Ora Inizio</option>
                                        <option value="time_end">Ora Fine</option>
                                        <option value="duration">Durata</option>
                                        <option value="description">Descrizione</option>
                                        <option value="text">Testo</option>
                                        <option value="numeric">Numerico</option>
                                        <option value="ignore">Ignora</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <span class="badge bg-${confidenceClass} confidence-badge">
                                        ${Math.round(colInfo.confidence * 100)}% confidenza
                                    </span>
                                    <br>
                                    <small class="text-muted">
                                        ${colInfo.sample_values?.slice(0, 2).join(', ') || 'Nessun esempio'}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = mappingHtml;
        }

        function loadValidationQuestions() {
            const container = document.getElementById('validation-questions');
            const questions = wizardState.analysisResult.feedback_data?.validation_questions || [];

            let questionsHtml = '';

            questions.forEach((question, index) => {
                questionsHtml += `
                    <div class="card mb-3">
                        <div class="card-body">
                            <h6>${question.question}</h6>
                            <p class="text-muted small">${question.context}</p>

                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio"
                                       name="question_${question.id}"
                                       id="q${index}_yes" value="true">
                                <label class="form-check-label" for="q${index}_yes">Sì</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio"
                                       name="question_${question.id}"
                                       id="q${index}_no" value="false">
                                <label class="form-check-label" for="q${index}_no">No</label>
                            </div>
                        </div>
                    </div>
                `;
            });

            questionsHtml += `
                <div class="text-center mt-3">
                    <button type="button" class="btn btn-primary" onclick="submitValidation()">
                        <i class="fas fa-check"></i>
                        Conferma Validazione
                    </button>
                </div>
            `;

            container.innerHTML = questionsHtml;
        }

        function updateColumnType(columnName, newType) {
            // Aggiorna il tipo di colonna nello stato
            if (!wizardState.columnModifications) {
                wizardState.columnModifications = {};
            }

            if (!wizardState.columnModifications.column_types) {
                wizardState.columnModifications.column_types = {};
            }

            wizardState.columnModifications.column_types[columnName] = newType;

            console.log(`Colonna ${columnName} aggiornata a tipo: ${newType}`);
        }

        async function submitValidation() {
            showLoading(true);

            try {
                // Raccogli risposte alle domande
                const feedback = {
                    format_correct: getQuestionAnswer('format_correct'),
                    columns_correct: getQuestionAnswer('columns_correct'),
                    need_modifications: getQuestionAnswer('need_modifications'),
                    modifications: wizardState.columnModifications || {}
                };

                const response = await fetch('/api/wizard/interactive/validate', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        file_id: wizardState.fileId,
                        feedback: feedback
                    })
                });

                const result = await response.json();

                if (result.success) {
                    wizardState.validationResult = result.data;

                    if (result.data.validation_accepted) {
                        showAlert('Validazione completata con successo!', 'success');
                        enableNextStep();
                    } else {
                        showAlert('Validazione non accettata. Modifica i parametri e riprova.', 'warning');
                    }
                } else {
                    showAlert('Errore durante la validazione: ' + result.error, 'danger');
                }

            } catch (error) {
                console.error('Errore validazione:', error);
                showAlert('Errore di connessione durante la validazione', 'danger');
            } finally {
                showLoading(false);
            }
        }

        function getQuestionAnswer(questionId) {
            const radio = document.querySelector(`input[name="question_${questionId}"]:checked`);
            return radio ? radio.value === 'true' : null;
        }

        function loadNormalizationStep() {
            if (!wizardState.validationResult?.entity_analysis) return;

            const container = document.getElementById('entity-mapping-container');
            const entityAnalysis = wizardState.validationResult.entity_analysis;

            let mappingHtml = '';

            // Per ogni tipo di entità
            Object.entries(entityAnalysis.entities_found).forEach(([entityType, entities]) => {
                if (entities.length === 0) return;

                const entityTypeNames = {
                    'employees': 'Dipendenti',
                    'clients': 'Clienti',
                    'projects': 'Progetti',
                    'vehicles': 'Veicoli'
                };

                mappingHtml += `
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-users"></i>
                                ${entityTypeNames[entityType] || entityType}
                            </h5>
                        </div>
                        <div class="card-body">
                `;

                entities.forEach(entityName => {
                    const isExisting = entityAnalysis.supabase_entities?.[entityType]?.existing?.includes(entityName);
                    const cardClass = isExisting ? 'entity-existing' : 'entity-new';
                    const statusText = isExisting ? 'Esistente' : 'Nuovo';
                    const statusIcon = isExisting ? 'fas fa-check-circle text-primary' : 'fas fa-plus-circle text-success';

                    mappingHtml += `
                        <div class="entity-mapping-card ${cardClass}">
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <strong>${entityName}</strong>
                                </div>
                                <div class="col-md-3">
                                    <i class="${statusIcon}"></i>
                                    ${statusText}
                                </div>
                                <div class="col-md-5">
                                    <select class="form-select form-select-sm"
                                            id="entity-${entityType}-${entityName.replace(/\s+/g, '_')}"
                                            onchange="updateEntityMapping('${entityType}', '${entityName}', this.value)">
                                        ${isExisting ?
                                            '<option value="map" selected>Mappa a esistente</option>' :
                                            '<option value="create" selected>Crea nuovo</option>'
                                        }
                                        <option value="create">Crea nuovo</option>
                                        <option value="map">Mappa a esistente</option>
                                        <option value="ignore">Ignora</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    `;
                });

                mappingHtml += `
                        </div>
                    </div>
                `;
            });

            mappingHtml += `
                <div class="text-center mt-3">
                    <button type="button" class="btn btn-primary" onclick="submitNormalization()">
                        <i class="fas fa-sitemap"></i>
                        Conferma Normalizzazione
                    </button>
                </div>
            `;

            container.innerHTML = mappingHtml;
        }

        function updateEntityMapping(entityType, entityName, action) {
            if (!wizardState.entityMappings) {
                wizardState.entityMappings = {};
            }

            if (!wizardState.entityMappings[entityType]) {
                wizardState.entityMappings[entityType] = {};
            }

            wizardState.entityMappings[entityType][entityName] = {
                action: action,
                original_name: entityName
            };

            console.log(`Entità ${entityName} (${entityType}) mappata come: ${action}`);
        }

        async function submitNormalization() {
            showLoading(true);

            try {
                const response = await fetch('/api/wizard/interactive/normalize', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        file_id: wizardState.fileId,
                        entity_mappings: wizardState.entityMappings || {}
                    })
                });

                const result = await response.json();

                if (result.success) {
                    wizardState.normalizationResult = result.data;
                    showAlert('Normalizzazione completata con successo!', 'success');
                    enableNextStep();
                } else {
                    showAlert('Errore durante la normalizzazione: ' + result.error, 'danger');
                }

            } catch (error) {
                console.error('Errore normalizzazione:', error);
                showAlert('Errore di connessione durante la normalizzazione', 'danger');
            } finally {
                showLoading(false);
            }
        }

        function loadInsertionStep() {
            const container = document.getElementById('insertion-summary');

            let summaryHtml = `
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Riepilogo Inserimento</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>📊 File Processato</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Nome:</strong> ${wizardState.analysisResult?.file_info?.original_name || 'N/A'}</li>
                                    <li><strong>Righe:</strong> ${wizardState.analysisResult?.format_analysis?.total_rows || 'N/A'}</li>
                                    <li><strong>Colonne:</strong> ${wizardState.analysisResult?.format_analysis?.total_columns || 'N/A'}</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>🔄 Entità Processate</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Create:</strong> ${wizardState.normalizationResult?.entities_created?.length || 0}</li>
                                    <li><strong>Mappate:</strong> ${wizardState.normalizationResult?.entities_mapped?.length || 0}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button type="button" class="btn btn-success btn-lg" onclick="startInsertion()">
                        <i class="fas fa-database"></i>
                        Conferma Inserimento in Supabase
                    </button>
                </div>
            `;

            container.innerHTML = summaryHtml;
        }

        async function startInsertion() {
            const progressContainer = document.getElementById('insertion-progress');
            const resultsContainer = document.getElementById('insertion-results');
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');

            progressContainer.style.display = 'block';
            resultsContainer.style.display = 'none';

            // Simula progresso
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += 10;
                progressBar.style.width = progress + '%';

                if (progress <= 30) {
                    progressText.textContent = 'Preparazione dati...';
                } else if (progress <= 70) {
                    progressText.textContent = 'Inserimento in corso...';
                } else if (progress < 100) {
                    progressText.textContent = 'Finalizzazione...';
                }

                if (progress >= 100) {
                    clearInterval(progressInterval);
                    progressText.textContent = 'Completato!';
                }
            }, 200);

            try {
                const response = await fetch('/api/wizard/interactive/insert', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        file_id: wizardState.fileId,
                        confirm_insert: true
                    })
                });

                const result = await response.json();

                clearInterval(progressInterval);
                progressBar.style.width = '100%';
                progressText.textContent = 'Completato!';

                setTimeout(() => {
                    progressContainer.style.display = 'none';

                    if (result.success) {
                        wizardState.insertionResult = result.data;
                        showInsertionResults(result.data);
                        enableFinishButton();
                    } else {
                        showAlert('Errore durante l\'inserimento: ' + result.error, 'danger');
                    }
                }, 1000);

            } catch (error) {
                clearInterval(progressInterval);
                console.error('Errore inserimento:', error);
                showAlert('Errore di connessione durante l\'inserimento', 'danger');
                progressContainer.style.display = 'none';
            }
        }

        function showInsertionResults(results) {
            const container = document.getElementById('insertion-results');

            const successClass = results.success ? 'success' : 'danger';
            const successIcon = results.success ? 'fas fa-check-circle' : 'fas fa-times-circle';

            let resultsHtml = `
                <div class="alert alert-${successClass}">
                    <h5>
                        <i class="${successIcon}"></i>
                        ${results.success ? 'Inserimento Completato!' : 'Inserimento Fallito'}
                    </h5>
                    <p>
                        <strong>Record inseriti:</strong> ${results.records_inserted || 0}<br>
                        <strong>Tabella destinazione:</strong> ${results.target_table || 'N/A'}
                    </p>
                </div>
            `;

            if (results.success) {
                resultsHtml += `
                    <div class="text-center">
                        <p class="text-success">
                            <i class="fas fa-thumbs-up"></i>
                            I tuoi dati sono stati inseriti con successo nel database!
                        </p>
                    </div>
                `;
            }

            container.innerHTML = resultsHtml;
            container.style.display = 'block';
        }

        function enableFinishButton() {
            document.getElementById('btn-finish').disabled = false;
        }

        function finishWizard() {
            showAlert('Wizard completato con successo! Reindirizzamento alla dashboard...', 'success');

            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 2000);
        }
    </script>
</body>
</html>
