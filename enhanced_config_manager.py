#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced Configuration Manager con integrazione Supabase.
Estende il ConfigManager esistente con funzionalità cloud e sincronizzazione.
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime
from config_manager import ConfigManager

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedConfigManager(ConfigManager):
    """
    Gestore di configurazione avanzato con supporto Supabase.
    Estende ConfigManager con funzionalità cloud e sincronizzazione.
    """
    
    def __init__(self, config_file: str = "app_config.json", use_supabase: bool = True):
        """
        Inizializza il gestore di configurazione avanzato.
        
        Args:
            config_file: Percorso del file di configurazione locale
            use_supabase: Se utilizzare Supabase per le configurazioni cloud
        """
        self.use_supabase = use_supabase
        self.supabase_manager = None
        
        # Inizializza Supabase se richiesto
        if self.use_supabase:
            try:
                from supabase_integration import supabase_manager
                self.supabase_manager = supabase_manager
                if not self.supabase_manager.is_connected:
                    logger.warning("Supabase non connesso, utilizzo solo storage locale")
                    self.use_supabase = False
            except ImportError:
                logger.warning("Supabase non disponibile, utilizzo solo storage locale")
                self.use_supabase = False
        
        # Inizializza il ConfigManager base
        super().__init__(config_file)
        
        # Carica configurazioni da cloud se disponibile
        if self.use_supabase:
            self._sync_from_cloud()
    
    def _sync_from_cloud(self) -> bool:
        """
        Sincronizza le configurazioni dal cloud.
        
        Returns:
            bool: True se la sincronizzazione è riuscita, False altrimenti
        """
        if not self.use_supabase or not self.supabase_manager or not self.supabase_manager.is_connected:
            return False
            
        try:
            # Recupera configurazioni da Supabase
            result = self.supabase_manager.client.table("system_config").select("*").execute()
            if result.data:
                cloud_config = {}
                for item in result.data:
                    cloud_config[item['config_key']] = item['config_value']
                
                # Merge con configurazione locale (cloud ha priorità)
                self._deep_merge(self.config_data, cloud_config)
                logger.info("✅ Configurazione sincronizzata dal cloud")
                return True
        except Exception as e:
            logger.warning(f"⚠️ Errore sincronizzazione cloud: {str(e)}")
        
        return False
    
    def _sync_to_cloud(self) -> bool:
        """
        Sincronizza le configurazioni verso il cloud.
        
        Returns:
            bool: True se la sincronizzazione è riuscita, False altrimenti
        """
        if not self.use_supabase or not self.supabase_manager or not self.supabase_manager.is_connected:
            return False
            
        try:
            # Salva ogni sezione di configurazione come record separato
            for key, value in self.config_data.items():
                if key not in ['version', 'created', 'last_updated']:  # Skip metadata
                    config_record = {
                        'config_key': key,
                        'config_value': value,
                        'description': f'Configurazione per {key}',
                        'updated_at': datetime.now().isoformat()
                    }
                    
                    # Upsert (insert or update)
                    self.supabase_manager.client.table("system_config").upsert(
                        config_record, 
                        on_conflict='config_key'
                    ).execute()
            
            logger.info("☁️ Configurazione sincronizzata verso il cloud")
            return True
            
        except Exception as e:
            logger.warning(f"⚠️ Errore sincronizzazione verso cloud: {str(e)}")
            return False
    
    def _deep_merge(self, target: Dict, source: Dict) -> None:
        """
        Merge ricorsivo di due dizionari.
        
        Args:
            target: Dizionario target
            source: Dizionario sorgente
        """
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._deep_merge(target[key], value)
            else:
                target[key] = value
    
    def save_config(self, sync_to_cloud: bool = True):
        """
        Salva la configurazione localmente e opzionalmente su cloud.
        
        Args:
            sync_to_cloud: Se sincronizzare anche verso il cloud
            
        Returns:
            bool: True se il salvataggio è riuscito, False altrimenti
        """
        # Salva localmente usando il metodo del parent
        success = super().save_config()
        
        # Sincronizza verso cloud se richiesto
        if success and sync_to_cloud and self.use_supabase:
            cloud_success = self._sync_to_cloud()
            return success and cloud_success
        
        return success
    
    def get_advanced(self, key: str, default: Any = None, from_cloud: bool = False) -> Any:
        """
        Ottiene un valore di configurazione con opzioni avanzate.
        
        Args:
            key: Chiave di configurazione (supporta notazione punto)
            default: Valore di default se la chiave non esiste
            from_cloud: Se recuperare direttamente dal cloud
            
        Returns:
            Any: Valore di configurazione
        """
        if from_cloud and self.use_supabase and self.supabase_manager and self.supabase_manager.is_connected:
            try:
                result = self.supabase_manager.client.table("system_config").select("config_value").eq("config_key", key).execute()
                if result.data:
                    return result.data[0]['config_value']
            except Exception as e:
                logger.warning(f"⚠️ Errore recupero da cloud per {key}: {str(e)}")
        
        # Fallback al metodo locale
        try:
            keys = key.split('.')
            value = self.config_data
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set_advanced(self, key: str, value: Any, save_immediately: bool = True, 
                    sync_to_cloud: bool = True) -> bool:
        """
        Imposta un valore di configurazione con opzioni avanzate.
        
        Args:
            key: Chiave di configurazione (supporta notazione punto)
            value: Valore da impostare
            save_immediately: Se salvare immediatamente
            sync_to_cloud: Se sincronizzare verso il cloud
            
        Returns:
            bool: True se l'impostazione è riuscita, False altrimenti
        """
        try:
            keys = key.split('.')
            config = self.config_data
            
            # Naviga fino al penultimo livello
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # Imposta il valore
            config[keys[-1]] = value
            
            # Salva se richiesto
            if save_immediately:
                return self.save_config(sync_to_cloud=sync_to_cloud)
            return True
            
        except Exception as e:
            logger.error(f"❌ Errore impostazione configurazione {key}: {str(e)}")
            return False
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        Ottiene informazioni di sistema e stato delle integrazioni.
        
        Returns:
            Dict: Informazioni di sistema
        """
        info = {
            "config_version": self.config_data.get("version", "unknown"),
            "last_updated": self.config_data.get("last_updated", "unknown"),
            "local_config_exists": os.path.exists(self.config_file),
            "supabase_enabled": self.use_supabase,
            "supabase_connected": False,
            "total_config_keys": len(self.config_data),
            "employee_count": len(self.config_data.get("employee_costs", {}))
        }
        
        if self.supabase_manager:
            info["supabase_connected"] = self.supabase_manager.is_connected
            if self.supabase_manager.is_connected:
                info["supabase_test"] = self.supabase_manager.test_connection()
        
        return info
    
    def backup_config(self, backup_path: Optional[str] = None) -> bool:
        """
        Crea un backup della configurazione.
        
        Args:
            backup_path: Percorso del backup (opzionale)
            
        Returns:
            bool: True se il backup è riuscito, False altrimenti
        """
        if not backup_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"backup_config_{timestamp}.json"
        
        try:
            backup_data = {
                "backup_timestamp": datetime.now().isoformat(),
                "original_config": self.config_data.copy()
            }
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 Backup configurazione creato: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Errore creazione backup: {str(e)}")
            return False
    
    def restore_config(self, backup_path: str) -> bool:
        """
        Ripristina la configurazione da un backup.
        
        Args:
            backup_path: Percorso del file di backup
            
        Returns:
            bool: True se il ripristino è riuscito, False altrimenti
        """
        try:
            with open(backup_path, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            if 'original_config' in backup_data:
                self.config_data = backup_data['original_config']
                success = self.save_config(sync_to_cloud=True)
                if success:
                    logger.info(f"🔄 Configurazione ripristinata da: {backup_path}")
                return success
            else:
                logger.error("❌ File di backup non valido")
                return False
                
        except Exception as e:
            logger.error(f"❌ Errore ripristino configurazione: {str(e)}")
            return False

# Istanza globale del manager avanzato
enhanced_config_manager = EnhancedConfigManager()
