/**
 * Setup Wizard JavaScript - App Roberto
 * Logica per il wizard di configurazione guidata
 * Versione: 1.1.0
 */

class SetupWizard {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 5;
        this.uploadedFiles = [];
        this.detectedData = {
            employees: [],
            vehicles: [],
            analysis: [],
            automations: []
        };
        this.configuration = {
            timezone: 'Europe/Rome',
            currency: 'EUR',
            dateFormat: 'DD/MM/YYYY'
        };

        this.init();
    }

    init() {
        console.log('🚀 Inizializzazione Setup Wizard...');
        
        // Inizializza elementi DOM
        this.steps = document.querySelectorAll(".wizard-step");
        this.nextButton = document.getElementById("next-btn") || document.getElementById("nextButton");
        this.prevButton = document.getElementById("prev-btn") || document.getElementById("prevButton");
        
        console.log(`📊 Steps trovati: ${this.steps.length}`);
        console.log(`🔘 Next button:`, this.nextButton);
        console.log(`🔘 Prev button:`, this.prevButton);

        if (this.steps.length > 0) {
            this.showStep(0);
        }

        // Aggiungi event listeners se i pulsanti esistono
        if (this.nextButton) {
            this.nextButton.addEventListener("click", this.nextStep.bind(this));
        }
        if (this.prevButton) {
            this.prevButton.addEventListener("click", this.prevStep.bind(this));
        }

        // Carica dati di test
        this.loadTestData();
    }

    showStep(stepIndex) {
        console.log(`📍 Mostrando step: ${stepIndex + 1}`);
        
        // Controlla se gli elementi esistono prima di usarli
        if (!this.steps || this.steps.length === 0) {
            console.warn('⚠️ Nessun step trovato');
            return;
        }

        this.steps.forEach((step, index) => {
            if (step && step.style) {
                if (index === stepIndex) {
                    step.style.display = "block";
                    step.classList.add('active');
                } else {
                    step.style.display = "none";
                    step.classList.remove('active');
                }
            }
        });

        // Aggiorna il currentStep
        this.currentStep = stepIndex + 1;

        // Controlla se i pulsanti esistono
        if (this.prevButton && this.prevButton.style) {
            if (stepIndex === 0) {
                this.prevButton.style.display = "none";
                this.prevButton.disabled = true;
            } else {
                this.prevButton.style.display = "inline-block";
                this.prevButton.disabled = false;
            }
        }

        if (this.nextButton) {
            if (stepIndex === this.steps.length - 1) {
                this.nextButton.textContent = "Completa";
                this.nextButton.innerHTML = '<i class="fas fa-check me-2"></i>Completa';
            } else {
                this.nextButton.textContent = "Avanti";
                this.nextButton.innerHTML = 'Avanti <i class="fas fa-arrow-right ms-2"></i>';
            }
        }

        // Aggiorna progress bar
        this.updateProgressBar();
        
        // Aggiorna step indicator
        this.updateStepIndicator();
    }

    nextStep() {
        console.log('➡️ Next step clicked');
        
        if (this.validateStep(this.currentStep - 1)) {
            if (this.currentStep < this.steps.length) {
                this.showStep(this.currentStep);
            } else {
                this.complete();
            }
        } else {
            alert("Per favore, compila tutti i campi obbligatori.");
        }
    }

    prevStep() {
        console.log('⬅️ Previous step clicked');
        
        if (this.currentStep > 1) {
            this.showStep(this.currentStep - 2);
        }
    }

    validateStep(stepIndex) {
        console.log(`✅ Validazione step: ${stepIndex + 1}`);
        
        if (!this.steps[stepIndex]) {
            return true;
        }

        const step = this.steps[stepIndex];
        const inputs = step.querySelectorAll("input, select, textarea");
        let isValid = true;

        inputs.forEach((input) => {
            if (input.hasAttribute("required") && !input.value.trim()) {
                isValid = false;
                input.classList.add('is-invalid');
            } else {
                input.classList.remove('is-invalid');
            }
        });

        return isValid;
    }

    complete() {
        console.log('🎉 Setup completato!');
        
        const form = document.querySelector("#setupForm");
        if (form) {
            const formData = new FormData(form);

            fetch("/save_wizard_config", {
                method: "POST",
                body: formData,
            })
            .then((response) => response.json())
            .then((data) => {
                if (data.success) {
                    window.location.href = "/dashboard";
                } else {
                    alert("Errore nel salvataggio del setup. Riprova.");
                }
            })
            .catch((error) => {
                console.error('Errore:', error);
                alert("Errore di connessione. Riprova.");
            });
        } else {
            // Fallback se non c'è form
            window.location.href = "/dashboard";
        }
    }

    updateProgressBar() {
        const progressBar = document.getElementById('wizard-progress-bar');
        if (progressBar) {
            const percentage = (this.currentStep / this.totalSteps) * 100;
            progressBar.style.width = percentage + '%';
            progressBar.setAttribute('aria-valuenow', percentage);
        }
    }

    updateStepIndicator() {
        const steps = document.querySelectorAll('.progress-steps .step');
        steps.forEach((step, index) => {
            const stepNumber = index + 1;
            step.classList.remove('active', 'completed');
            
            if (stepNumber < this.currentStep) {
                step.classList.add('completed');
            } else if (stepNumber === this.currentStep) {
                step.classList.add('active');
            }
        });
    }

    loadTestData() {
        // Carica alcuni dati di test
        this.detectedData.employees = [
            { name: 'Mario Rossi', role: 'Tecnico', active: true },
            { name: 'Luigi Verdi', role: 'Manager', active: true }
        ];
        
        this.detectedData.vehicles = [
            { plate: 'AB123CD', model: 'Fiat Ducato', active: true },
            { plate: 'EF456GH', model: 'Ford Transit', active: true }
        ];

        // Popola le sezioni se esistono
        this.loadTeamData();
        this.loadVehicleData();
    }

    loadTeamData() {
        const container = document.getElementById('detected-employees');
        if (!container) return;

        let html = '';
        this.detectedData.employees.forEach(emp => {
            html += `
                <div class="employee-item d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <strong>${emp.name}</strong>
                        <small class="text-muted d-block">${emp.role}</small>
                    </div>
                    <span class="badge ${emp.active ? 'bg-success' : 'bg-secondary'}">
                        ${emp.active ? 'Attivo' : 'Inattivo'}
                    </span>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }

    loadVehicleData() {
        const container = document.getElementById('detected-vehicles');
        if (!container) return;

        let html = '';
        this.detectedData.vehicles.forEach(vehicle => {
            html += `
                <div class="vehicle-item d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <strong>${vehicle.plate}</strong>
                        <small class="text-muted d-block">${vehicle.model}</small>
                    </div>
                    <span class="badge ${vehicle.active ? 'bg-success' : 'bg-secondary'}">
                        ${vehicle.active ? 'Attivo' : 'Inattivo'}
                    </span>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }

    saveWizardState() {
        try {
            const state = {
                currentStep: this.currentStep,
                uploadedFiles: this.uploadedFiles,
                detectedData: this.detectedData,
                configuration: this.configuration
            };
            
            localStorage.setItem('setupWizardState', JSON.stringify(state));
            console.log('💾 Stato wizard salvato');
        } catch (error) {
            console.warn('⚠️ Impossibile salvare stato wizard:', error);
        }
    }

    completeSetup() {
        console.log('🚀 Completamento setup avanzato...');
        this.complete();
    }

    restartWizard() {
        console.log('🔄 Riavvio wizard...');
        
        // Reset stato
        this.currentStep = 1;
        this.uploadedFiles = [];
        
        // Mostra primo step
        this.showStep(0);
        
        // Pulisci localStorage
        try {
            localStorage.removeItem('setupWizardState');
        } catch (error) {
            console.warn('⚠️ Impossibile pulire localStorage:', error);
        }
    }

    generateReport() {
        try {
            console.log('📋 Generazione report setup...');

            // Prepara dati per il report
            const reportData = {
                wizard_progress: {
                    current_step: this.currentStep,
                    total_steps: this.totalSteps
                },
                uploaded_files: this.uploadedFiles.map(file => ({
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    processed: file.processed
                })),
                detected_data: {
                    employees_count: this.detectedData.employees.length,
                    vehicles_count: this.detectedData.vehicles.length,
                    business_type: this.detectedData.business_type,
                    confidence_score: this.detectedData.confidence_score,
                    data_quality_score: this.detectedData.data_quality_score
                },
                configuration: this.configuration,
                timestamp: new Date().toISOString()
            };

            // Mostra report in un modal
            const modalHtml = `
                <div class="modal fade" id="setupReportModal" tabindex="-1" aria-labelledby="setupReportModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="setupReportModalLabel">
                                    <i class="fas fa-file-alt me-2"></i>
                                    Report Setup Wizard
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Stato Wizard</h6>
                                        <p>Step corrente: ${reportData.wizard_progress.current_step}/${reportData.wizard_progress.total_steps}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Configurazione</h6>
                                        <ul>
                                            <li>Fuso Orario: ${reportData.configuration.timezone}</li>
                                            <li>Valuta: ${reportData.configuration.currency}</li>
                                            <li>Formato Data: ${reportData.configuration.dateFormat}</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <h6>File Caricati</h6>
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Nome</th>
                                                <th>Dimensione</th>
                                                <th>Tipo</th>
                                                <th>Stato</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${reportData.uploaded_files.map(file => `
                                                <tr>
                                                    <td>${file.name}</td>
                                                    <td>${(file.size / 1024).toFixed(2)} KB</td>
                                                    <td>${file.type}</td>
                                                    <td>${file.processed ? '✅ Elaborato' : '❌ Non elaborato'}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                    <i class="fas fa-times me-1"></i>
                                    Chiudi
                                </button>
                                <button type="button" class="btn btn-primary" onclick="downloadReport()">
                                    <i class="fas fa-download me-1"></i>
                                    Scarica Report
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Rimuovi modal esistente se presente
            const existingModal = document.getElementById('setupReportModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Aggiungi modal al DOM
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Mostra modal
            const modal = new bootstrap.Modal(document.getElementById('setupReportModal'));
            modal.show();

            // Salva report per download
            window.currentReport = reportData;

            console.log('✅ Report setup generato con successo');

        } catch (error) {
            console.error('❌ Errore generazione report:', error);
            showError('Errore durante la generazione del report: ' + error.message);
        }
    }
}

// Funzione per scaricare il report
function downloadReport() {
    try {
        if (window.currentReport) {
            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(window.currentReport, null, 2));
            const downloadAnchorNode = document.createElement('a');
            downloadAnchorNode.setAttribute("href", dataStr);
            downloadAnchorNode.setAttribute("download", `setup-report-${new Date().toISOString().split('T')[0]}.json`);
            document.body.appendChild(downloadAnchorNode);
            downloadAnchorNode.click();
            downloadAnchorNode.remove();
            
            console.log('📥 Report scaricato');
        }
    } catch (error) {
        console.error('❌ Errore download report:', error);
    }
}

// Inizializza il wizard quando il DOM è pronto
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 Inizializzazione Setup Wizard...');
    window.wizard = new SetupWizard();
});

// Funzioni globali per compatibilità template
function nextStep() {
    console.log('🔗 nextStep() globale chiamata');
    if (window.wizard) {
        window.wizard.nextStep();
    } else {
        console.error('❌ Wizard non inizializzato');
    }
}

function prevStep() {
    console.log('🔗 prevStep() globale chiamata');
    if (window.wizard) {
        window.wizard.prevStep();
    } else {
        console.error('❌ Wizard non inizializzato');
    }
}

function completeSetup() {
    console.log('🔗 completeSetup() globale chiamata');
    if (window.wizard) {
        window.wizard.completeSetup();
    } else {
        console.error('❌ Wizard non inizializzato');
    }
}

function generateReport() {
    console.log('🔗 generateReport() globale chiamata');
    if (window.wizard) {
        window.wizard.generateReport();
    } else {
        console.error('❌ Wizard non inizializzato');
    }
}

function restartWizard() {
    console.log('🔗 restartWizard() globale chiamata');
    if (window.wizard) {
        window.wizard.restartWizard();
    } else {
        console.error('❌ Wizard non inizializzato');
    }
}

function addEmployee() {
    console.log('👥 Aggiungi dipendente');
    
    try {
        // Crea modal per aggiunta dipendente
        const modalHtml = `
            <div class="modal fade" id="addEmployeeModal" tabindex="-1" aria-labelledby="addEmployeeModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="addEmployeeModalLabel">
                                <i class="fas fa-user-plus me-2"></i>
                                Aggiungi Dipendente
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="addEmployeeForm">
                                <div class="mb-3">
                                    <label for="employeeName" class="form-label">Nome</label>
                                    <input type="text" class="form-control" id="employeeName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="employeeRole" class="form-label">Ruolo</label>
                                    <select class="form-select" id="employeeRole">
                                        <option value="Tecnico">Tecnico</option>
                                        <option value="Tecnico Senior">Tecnico Senior</option>
                                        <option value="Tecnico Specializzato">Tecnico Specializzato</option>
                                        <option value="Responsabile">Responsabile</option>
                                        <option value="Manager">Manager</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="employeeEmail" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="employeeEmail" placeholder="<EMAIL>">
                                </div>
                                <div class="mb-3">
                                    <label for="employeePhone" class="form-label">Telefono</label>
                                    <input type="tel" class="form-control" id="employeePhone" placeholder="+39 ************">
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="employeeActive" checked>
                                        <label class="form-check-label" for="employeeActive">
                                            Dipendente attivo
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>
                                Annulla
                            </button>
                            <button type="button" class="btn btn-primary" onclick="saveNewEmployee()">
                                <i class="fas fa-save me-1"></i>
                                Salva Dipendente
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Rimuovi modal esistente se presente
        const existingModal = document.getElementById('addEmployeeModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Aggiungi modal al DOM
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Mostra modal
        const modal = new bootstrap.Modal(document.getElementById('addEmployeeModal'));
        modal.show();

        console.log('✅ Modal aggiunta dipendente aperto');

    } catch (error) {
        console.error('❌ Errore apertura modal aggiunta dipendente:', error);
        showError('Errore durante l\'apertura del modal di aggiunta: ' + error.message);
    }
}

function saveNewEmployee() {
    console.log('💾 Salvataggio nuovo dipendente');

    try {
        // Ottieni valori dal form
        const name = document.getElementById('employeeName').value.trim();
        const role = document.getElementById('employeeRole').value;
        const email = document.getElementById('employeeEmail').value.trim();
        const phone = document.getElementById('employeePhone').value.trim();
        const active = document.getElementById('employeeActive').checked;

        // Validazione
        if (!name) {
            showError('Il nome del dipendente è obbligatorio');
            return;
        }

        // Aggiungi nuovo dipendente
        if (window.wizard && window.wizard.detectedData && window.wizard.detectedData.employees) {
            const newEmployee = {
                name: name,
                role: role,
                email: email,
                phone: phone,
                active: active,
                created_at: new Date().toISOString()
            };

            window.wizard.detectedData.employees.push(newEmployee);

            console.log('✅ Nuovo dipendente aggiunto:', newEmployee);

            // Aggiorna UI
            window.wizard.loadTeamData();

            // Salva stato
            window.wizard.saveWizardState();

            // Chiudi modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addEmployeeModal'));
            modal.hide();

            // Mostra messaggio di successo
            showSuccess('Dipendente aggiunto con successo!');

        } else {
            console.error('❌ Dati wizard non disponibili');
            showError('Dati wizard non disponibili');
        }

    } catch (error) {
        console.error('❌ Errore salvataggio nuovo dipendente:', error);
        showError('Errore durante il salvataggio: ' + error.message);
    }
}

function addVehicle() {
    console.log('🚗 Aggiungi veicolo');
    
    try {
        // Crea modal per aggiunta veicolo
        const modalHtml = `
            <div class="modal fade" id="addVehicleModal" tabindex="-1" aria-labelledby="addVehicleModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="addVehicleModalLabel">
                                <i class="fas fa-car me-2"></i>
                                Aggiungi Veicolo
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="addVehicleForm">
                                <div class="mb-3">
                                    <label for="vehiclePlate" class="form-label">Targa</label>
                                    <input type="text" class="form-control" id="vehiclePlate" required placeholder="Es. AB123CD">
                                </div>
                                <div class="mb-3">
                                    <label for="vehicleModel" class="form-label">Modello</label>
                                    <input type="text" class="form-control" id="vehicleModel" placeholder="Es. Fiat Ducato">
                                </div>
                                <div class="mb-3">
                                    <label for="vehicleBrand" class="form-label">Marca</label>
                                    <input type="text" class="form-control" id="vehicleBrand" placeholder="Es. Fiat">
                                </div>
                                <div class="mb-3">
                                    <label for="vehicleYear" class="form-label">Anno</label>
                                    <input type="number" class="form-control" id="vehicleYear" min="1990" max="2030" placeholder="2020">
                                </div>
                                <div class="mb-3">
                                    <label for="vehicleFuelType" class="form-label">Tipo Carburante</label>
                                    <select class="form-select" id="vehicleFuelType">
                                        <option value="Benzina">Benzina</option>
                                        <option value="Diesel">Diesel</option>
                                        <option value="GPL">GPL</option>
                                        <option value="Metano">Metano</option>
                                        <option value="Elettrico">Elettrico</option>
                                        <option value="Ibrido">Ibrido</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="vehicleActive" checked>
                                        <label class="form-check-label" for="vehicleActive">
                                            Veicolo attivo
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>
                                Annulla
                            </button>
                            <button type="button" class="btn btn-success" onclick="saveNewVehicle()">
                                <i class="fas fa-save me-1"></i>
                                Salva Veicolo
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Rimuovi modal esistente se presente
        const existingModal = document.getElementById('addVehicleModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Aggiungi modal al DOM
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Mostra modal
        const modal = new bootstrap.Modal(document.getElementById('addVehicleModal'));
        modal.show();

        console.log('✅ Modal aggiunta veicolo aperto');

    } catch (error) {
        console.error('❌ Errore apertura modal aggiunta veicolo:', error);
        showError('Errore durante l\'apertura del modal di aggiunta: ' + error.message);
    }
}

function saveNewVehicle() {
    console.log('💾 Salvataggio nuovo veicolo');

    try {
        // Ottieni valori dal form
        const plate = document.getElementById('vehiclePlate').value.trim().toUpperCase();
        const model = document.getElementById('vehicleModel').value.trim();
        const brand = document.getElementById('vehicleBrand').value.trim();
        const year = document.getElementById('vehicleYear').value.trim();
        const fuelType = document.getElementById('vehicleFuelType').value;
        const active = document.getElementById('vehicleActive').checked;

        // Validazione
        if (!plate) {
            showError('La targa del veicolo è obbligatoria');
            return;
        }

        // Aggiungi nuovo veicolo
        if (window.wizard && window.wizard.detectedData && window.wizard.detectedData.vehicles) {
            const newVehicle = {
                plate: plate,
                model: model,
                brand: brand,
                year: year,
                fuel_type: fuelType,
                active: active,
                created_at: new Date().toISOString()
            };

            window.wizard.detectedData.vehicles.push(newVehicle);

            console.log('✅ Nuovo veicolo aggiunto:', newVehicle);

            // Aggiorna UI
            window.wizard.loadVehicleData();

            // Salva stato
            window.wizard.saveWizardState();

            // Chiudi modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addVehicleModal'));
            modal.hide();

            // Mostra messaggio di successo
            showSuccess('Veicolo aggiunto con successo!');

        } else {
            console.error('❌ Dati wizard non disponibili');
            showError('Dati wizard non disponibili');
        }

    } catch (error) {
        console.error('❌ Errore salvataggio nuovo veicolo:', error);
        showError('Errore durante il salvataggio: ' + error.message);
    }
}

// Funzioni di utilità per messaggi
function showSuccess(message) {
    console.log('✅ SUCCESS:', message);

    // Cerca un container per toast o usa alert
    const toastContainer = document.querySelector('.toast-container');
    if (toastContainer) {
        // Usa sistema toast se disponibile
        const toastHtml = `
            <div class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-check-circle me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        const toastElement = toastContainer.lastElementChild;
        const toast = new bootstrap.Toast(toastElement);
        toast.show();

        // Rimuovi toast dopo che si nasconde
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    } else {
        // Fallback con alert
        alert('✅ ' + message);
    }
}

function showError(message) {
    console.error('❌ ERROR:', message);

    // Cerca un container per toast o usa alert
    const toastContainer = document.querySelector('.toast-container');
    if (toastContainer) {
        // Usa sistema toast se disponibile
        const toastHtml = `
            <div class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        const toastElement = toastContainer.lastElementChild;
        const toast = new bootstrap.Toast(toastElement);
        toast.show();

        // Rimuovi toast dopo che si nasconde
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    } else {
        // Fallback con alert
        alert('❌ ' + message);
    }
}
