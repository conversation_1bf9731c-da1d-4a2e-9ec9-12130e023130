#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test dello schema database Supabase per app-roberto
"""

from supabase_integration import <PERSON><PERSON>baseMana<PERSON>

def test_database_schema():
    """Test dello schema database"""
    print('🗄️ Test Schema Database Supabase')
    print('=' * 40)

    # Usa le chiavi aggiornate direttamente
    url = "https://zqjllwxqjxjhdkbcawfr.supabase.co"
    key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxamxsd3hxanhqaGRrYmNhd2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzc1MDA1NTQsImV4cCI6MjA1MzA3NjU1NH0.WxbX4nvlen-3WixZ7D9flaQwTfLtA5V3PvD0guA5hdo"

    manager = SupabaseManager(url=url, key=key)
    
    if not manager.is_connected:
        print('❌ Connessione fallita')
        return False

    # Lista delle tabelle da testare
    tables_to_test = [
        'file_uploads',
        'processed_data', 
        'user_sessions',
        'system_config',
        'employee_costs',
        'system_logs',
        'ai_analyses'
    ]

    print('Test esistenza tabelle:')
    all_tables_exist = True
    
    for table in tables_to_test:
        try:
            result = manager.client.table(table).select("*").limit(1).execute()
            print(f'  ✅ {table}: OK')
        except Exception as e:
            print(f'  ❌ {table}: ERRORE - {str(e)}')
            all_tables_exist = False

    print()
    
    # Test configurazioni di default
    print('Test configurazioni di default:')
    try:
        result = manager.client.table("system_config").select("*").execute()
        configs = result.data if result.data else []
        print(f'  📊 Configurazioni trovate: {len(configs)}')
        for config in configs:
            print(f'    - {config["config_key"]}: {config["config_value"]}')
    except Exception as e:
        print(f'  ❌ Errore nel recuperare configurazioni: {str(e)}')
        all_tables_exist = False

    if all_tables_exist:
        print('\n✅ Schema database completamente configurato!')
        return True
    else:
        print('\n❌ Alcuni problemi rilevati nello schema')
        return False

if __name__ == "__main__":
    test_database_schema()
