#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sync Monitor - Sistema di monitoraggio e logging avanzato per app-roberto.
Monitora le operazioni di sincronizzazione, performance e salute del sistema.
"""

import logging
import json
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import threading
from collections import deque, defaultdict

# Import dei moduli esistenti
try:
    from supabase_integration import SupabaseManager
    from data_synchronizer import DataSynchronizer
    from incremental_updater import IncrementalUpdater
    from conflict_resolver import ConflictResolver
except ImportError as e:
    logging.warning(f"Alcuni moduli non disponibili: {e}")

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AlertLevel(Enum):
    """Livelli di allerta."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class MetricType(Enum):
    """Tipi di metriche."""
    PERFORMANCE = "performance"
    SYNC_OPERATION = "sync_operation"
    ERROR_RATE = "error_rate"
    SYSTEM_HEALTH = "system_health"

@dataclass
class SyncMetric:
    """Metrica di sincronizzazione."""
    timestamp: datetime
    metric_type: MetricType
    name: str
    value: float
    unit: str
    tags: Dict[str, str] = None

    def __post_init__(self):
        if self.tags is None:
            self.tags = {}

@dataclass
class SyncAlert:
    """Allerta di sincronizzazione."""
    alert_id: str
    level: AlertLevel
    title: str
    message: str
    timestamp: datetime
    component: str
    resolved: bool = False
    resolved_at: Optional[datetime] = None

class SyncMonitor:
    """
    Sistema di monitoraggio avanzato che:
    - Monitora performance delle sincronizzazioni
    - Traccia metriche di sistema in tempo reale
    - Genera allerte per anomalie
    - Mantiene cronologia delle operazioni
    - Fornisce dashboard di monitoraggio
    """

    def __init__(self, supabase_manager: Optional[SupabaseManager] = None):
        self.supabase_manager = supabase_manager or SupabaseManager()

        # Configurazione monitoraggio
        self.METRIC_RETENTION_HOURS = 24 * 7  # 7 giorni
        self.ALERT_RETENTION_HOURS = 24 * 30  # 30 giorni
        self.PERFORMANCE_THRESHOLD_MS = 5000   # 5 secondi
        self.ERROR_RATE_THRESHOLD = 0.1        # 10%
        self.MEMORY_USAGE_THRESHOLD = 0.8      # 80%

        # Storage metriche e allerte
        self.metrics_buffer = deque(maxlen=10000)
        self.active_alerts = {}
        self.alert_history = deque(maxlen=1000)

        # Contatori performance
        self.performance_counters = defaultdict(list)
        self.error_counters = defaultdict(int)
        self.operation_counters = defaultdict(int)

        # Thread per monitoraggio continuo
        self.monitoring_active = False
        self.monitoring_thread = None

        # Dashboard data
        self.dashboard_data = {
            "last_updated": datetime.now(),
            "system_status": "healthy",
            "active_syncs": 0,
            "total_operations": 0,
            "error_rate": 0.0,
            "avg_response_time": 0.0
        }

        logger.info("SyncMonitor inizializzato")

    def start_monitoring(self):
        """Avvia il monitoraggio continuo."""
        if self.monitoring_active:
            logger.warning("Monitoraggio già attivo")
            return

        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()

        logger.info("🔍 Monitoraggio avviato")

    def stop_monitoring(self):
        """Ferma il monitoraggio continuo."""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)

        logger.info("⏹️ Monitoraggio fermato")

    def record_sync_operation(self, operation_type: str, duration_ms: float,
                            success: bool, details: Dict[str, Any] = None):
        """
        Registra un'operazione di sincronizzazione.

        Args:
            operation_type: Tipo di operazione
            duration_ms: Durata in millisecondi
            success: Se l'operazione è riuscita
            details: Dettagli aggiuntivi
        """
        timestamp = datetime.now()

        # Registra metrica performance
        self.record_metric(
            MetricType.PERFORMANCE,
            f"{operation_type}_duration",
            duration_ms,
            "ms",
            {"operation": operation_type, "success": str(success)}
        )

        # Aggiorna contatori
        self.operation_counters[operation_type] += 1
        if not success:
            self.error_counters[operation_type] += 1

        # Verifica soglie performance
        if duration_ms > self.PERFORMANCE_THRESHOLD_MS:
            self._create_alert(
                AlertLevel.WARNING,
                "Performance Degradata",
                f"Operazione {operation_type} ha impiegato {duration_ms:.0f}ms (soglia: {self.PERFORMANCE_THRESHOLD_MS}ms)",
                "sync_monitor"
            )

        # Verifica tasso di errore
        self._check_error_rate(operation_type)

        logger.debug(f"📊 Operazione registrata: {operation_type} ({duration_ms:.0f}ms, {'✅' if success else '❌'})")

    def record_metric(self, metric_type: MetricType, name: str, value: float,
                     unit: str, tags: Dict[str, str] = None):
        """
        Registra una metrica.

        Args:
            metric_type: Tipo di metrica
            name: Nome della metrica
            value: Valore
            unit: Unità di misura
            tags: Tag aggiuntivi
        """
        metric = SyncMetric(
            timestamp=datetime.now(),
            metric_type=metric_type,
            name=name,
            value=value,
            unit=unit,
            tags=tags or {}
        )

        self.metrics_buffer.append(metric)

        # Aggiorna contatori performance
        if metric_type == MetricType.PERFORMANCE:
            self.performance_counters[name].append(value)
            # Mantieni solo ultimi 100 valori
            if len(self.performance_counters[name]) > 100:
                self.performance_counters[name] = self.performance_counters[name][-100:]

    def create_alert(self, level: AlertLevel, title: str, message: str, component: str):
        """Crea un'allerta."""
        self._create_alert(level, title, message, component)

    def _create_alert(self, level: AlertLevel, title: str, message: str, component: str):
        """Crea un'allerta interna."""
        alert_id = f"{component}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        alert = SyncAlert(
            alert_id=alert_id,
            level=level,
            title=title,
            message=message,
            timestamp=datetime.now(),
            component=component
        )

        self.active_alerts[alert_id] = alert
        self.alert_history.append(alert)

        # Log allerta
        log_level = {
            AlertLevel.INFO: logging.INFO,
            AlertLevel.WARNING: logging.WARNING,
            AlertLevel.ERROR: logging.ERROR,
            AlertLevel.CRITICAL: logging.CRITICAL
        }.get(level, logging.INFO)

        logger.log(log_level, f"🚨 {level.value.upper()}: {title} - {message}")

        # Salva allerta nel database se critica
        if level in [AlertLevel.ERROR, AlertLevel.CRITICAL]:
            self._save_alert_to_db(alert)

    def resolve_alert(self, alert_id: str):
        """Risolve un'allerta."""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.resolved = True
            alert.resolved_at = datetime.now()

            del self.active_alerts[alert_id]
            logger.info(f"✅ Allerta risolta: {alert_id}")

    def get_system_health(self) -> Dict[str, Any]:
        """Restituisce lo stato di salute del sistema."""
        now = datetime.now()

        # Calcola metriche recenti (ultima ora)
        recent_metrics = [m for m in self.metrics_buffer
                         if (now - m.timestamp).total_seconds() < 3600]

        # Calcola statistiche performance
        recent_performance = [m for m in recent_metrics
                            if m.metric_type == MetricType.PERFORMANCE]

        avg_response_time = 0.0
        if recent_performance:
            avg_response_time = sum(m.value for m in recent_performance) / len(recent_performance)

        # Calcola tasso di errore
        total_ops = sum(self.operation_counters.values())
        total_errors = sum(self.error_counters.values())
        error_rate = (total_errors / total_ops) if total_ops > 0 else 0.0

        # Determina stato sistema
        system_status = "healthy"
        if error_rate > self.ERROR_RATE_THRESHOLD:
            system_status = "degraded"
        if len(self.active_alerts) > 0:
            critical_alerts = [a for a in self.active_alerts.values()
                             if a.level == AlertLevel.CRITICAL]
            if critical_alerts:
                system_status = "critical"
            elif system_status == "healthy":
                system_status = "warning"

        health_data = {
            "timestamp": now.isoformat(),
            "system_status": system_status,
            "metrics": {
                "total_operations": total_ops,
                "error_rate": error_rate * 100,
                "avg_response_time_ms": avg_response_time,
                "active_alerts": len(self.active_alerts),
                "metrics_collected": len(recent_metrics)
            },
            "alerts": {
                "active": len(self.active_alerts),
                "critical": len([a for a in self.active_alerts.values()
                               if a.level == AlertLevel.CRITICAL]),
                "warnings": len([a for a in self.active_alerts.values()
                               if a.level == AlertLevel.WARNING])
            },
            "performance": {
                "sync_operations": self.operation_counters.copy(),
                "error_counts": self.error_counters.copy(),
                "avg_durations": {
                    name: sum(values) / len(values) if values else 0.0
                    for name, values in self.performance_counters.items()
                }
            }
        }

        # Aggiorna dashboard
        self.dashboard_data.update({
            "last_updated": now,
            "system_status": system_status,
            "total_operations": total_ops,
            "error_rate": error_rate * 100,
            "avg_response_time": avg_response_time
        })

        return health_data

    def get_dashboard_data(self) -> Dict[str, Any]:
        """Restituisce dati per dashboard di monitoraggio."""
        health = self.get_system_health()

        # Prepara dati per grafici
        now = datetime.now()
        last_hour = now - timedelta(hours=1)

        # Metriche ultima ora
        hourly_metrics = [m for m in self.metrics_buffer
                         if m.timestamp >= last_hour]

        # Raggruppa per tipo
        performance_metrics = [m for m in hourly_metrics
                             if m.metric_type == MetricType.PERFORMANCE]

        # Crea timeline (ultimi 60 minuti, 1 punto ogni 5 minuti)
        timeline = []
        for i in range(12):  # 12 * 5 = 60 minuti
            time_point = now - timedelta(minutes=i * 5)

            # Metriche in questo intervallo di 5 minuti
            interval_start = time_point - timedelta(minutes=5)
            interval_metrics = [m for m in performance_metrics
                              if interval_start <= m.timestamp < time_point]

            avg_duration = 0.0
            if interval_metrics:
                avg_duration = sum(m.value for m in interval_metrics) / len(interval_metrics)

            timeline.append({
                "timestamp": time_point.isoformat(),
                "avg_duration_ms": avg_duration,
                "operation_count": len(interval_metrics)
            })

        timeline.reverse()  # Ordine cronologico

        dashboard = {
            "system_health": health,
            "timeline": timeline,
            "active_alerts": [
                {
                    "id": alert.alert_id,
                    "level": alert.level.value,
                    "title": alert.title,
                    "message": alert.message,
                    "timestamp": alert.timestamp.isoformat(),
                    "component": alert.component
                }
                for alert in self.active_alerts.values()
            ],
            "recent_operations": self._get_recent_operations_summary(),
            "performance_trends": self._calculate_performance_trends()
        }

        return dashboard

    def _get_recent_operations_summary(self) -> Dict[str, Any]:
        """Riassunto operazioni recenti."""
        now = datetime.now()
        last_hour = now - timedelta(hours=1)

        recent_metrics = [m for m in self.metrics_buffer
                         if m.timestamp >= last_hour and
                         m.metric_type == MetricType.PERFORMANCE]

        # Raggruppa per tipo operazione
        operations = defaultdict(list)
        for metric in recent_metrics:
            op_type = metric.tags.get("operation", "unknown")
            operations[op_type].append(metric.value)

        summary = {}
        for op_type, durations in operations.items():
            summary[op_type] = {
                "count": len(durations),
                "avg_duration_ms": sum(durations) / len(durations),
                "min_duration_ms": min(durations),
                "max_duration_ms": max(durations)
            }

        return summary

    def _calculate_performance_trends(self) -> Dict[str, Any]:
        """Calcola trend di performance."""
        trends = {}

        for name, values in self.performance_counters.items():
            if len(values) < 2:
                continue

            # Calcola trend semplice (ultimi vs primi valori)
            recent_avg = sum(values[-10:]) / len(values[-10:]) if len(values) >= 10 else sum(values) / len(values)
            older_avg = sum(values[:10]) / len(values[:10]) if len(values) >= 20 else recent_avg

            trend_direction = "stable"
            if recent_avg > older_avg * 1.1:
                trend_direction = "increasing"
            elif recent_avg < older_avg * 0.9:
                trend_direction = "decreasing"

            trends[name] = {
                "direction": trend_direction,
                "recent_avg": recent_avg,
                "change_percent": ((recent_avg - older_avg) / older_avg * 100) if older_avg > 0 else 0.0
            }

        return trends

    def _check_error_rate(self, operation_type: str):
        """Verifica tasso di errore per tipo operazione."""
        total_ops = self.operation_counters[operation_type]
        errors = self.error_counters[operation_type]

        if total_ops >= 10:  # Verifica solo dopo almeno 10 operazioni
            error_rate = errors / total_ops

            if error_rate > self.ERROR_RATE_THRESHOLD:
                self._create_alert(
                    AlertLevel.ERROR,
                    "Tasso di Errore Elevato",
                    f"Operazione {operation_type}: {error_rate:.1%} di errori (soglia: {self.ERROR_RATE_THRESHOLD:.1%})",
                    "sync_monitor"
                )

    def _monitoring_loop(self):
        """Loop principale di monitoraggio."""
        logger.info("🔄 Loop monitoraggio avviato")

        while self.monitoring_active:
            try:
                # Pulizia metriche vecchie
                self._cleanup_old_metrics()

                # Pulizia allerte vecchie
                self._cleanup_old_alerts()

                # Verifica salute sistema
                health = self.get_system_health()

                # Salva metriche nel database ogni 5 minuti
                if datetime.now().minute % 5 == 0:
                    self._save_metrics_to_db()

                # Attendi 60 secondi
                time.sleep(60)

            except Exception as e:
                logger.error(f"Errore nel loop monitoraggio: {e}")
                time.sleep(60)

        logger.info("🔄 Loop monitoraggio terminato")

    def _cleanup_old_metrics(self):
        """Rimuove metriche vecchie."""
        cutoff_time = datetime.now() - timedelta(hours=self.METRIC_RETENTION_HOURS)

        # Filtra metriche recenti
        recent_metrics = deque()
        for metric in self.metrics_buffer:
            if metric.timestamp >= cutoff_time:
                recent_metrics.append(metric)

        self.metrics_buffer = recent_metrics

    def _cleanup_old_alerts(self):
        """Rimuove allerte vecchie."""
        cutoff_time = datetime.now() - timedelta(hours=self.ALERT_RETENTION_HOURS)

        # Filtra cronologia allerte
        recent_alerts = deque()
        for alert in self.alert_history:
            if alert.timestamp >= cutoff_time:
                recent_alerts.append(alert)

        self.alert_history = recent_alerts

    def _save_metrics_to_db(self):
        """Salva metriche aggregate nel database."""
        try:
            if not self.supabase_manager.is_connected:
                return

            # Prepara dati aggregati
            now = datetime.now()
            health_data = self.get_system_health()

            metric_data = {
                "timestamp": now.isoformat(),
                "system_status": health_data["system_status"],
                "total_operations": health_data["metrics"]["total_operations"],
                "error_rate": health_data["metrics"]["error_rate"],
                "avg_response_time": health_data["metrics"]["avg_response_time_ms"],
                "active_alerts": health_data["alerts"]["active"],
                "performance_data": health_data["performance"]
            }

            # Salva nel database
            self.supabase_manager.client.table("sync_metrics").insert(metric_data).execute()

        except Exception as e:
            logger.error(f"Errore salvataggio metriche: {e}")

    def _save_alert_to_db(self, alert: SyncAlert):
        """Salva allerta nel database."""
        try:
            if not self.supabase_manager.is_connected:
                return

            alert_data = {
                "alert_id": alert.alert_id,
                "level": alert.level.value,
                "title": alert.title,
                "message": alert.message,
                "component": alert.component,
                "timestamp": alert.timestamp.isoformat(),
                "resolved": alert.resolved
            }

            self.supabase_manager.client.table("sync_alerts").insert(alert_data).execute()

        except Exception as e:
            logger.error(f"Errore salvataggio allerta: {e}")

    def export_metrics(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """Esporta metriche per un periodo specifico."""
        filtered_metrics = [
            asdict(metric) for metric in self.metrics_buffer
            if start_time <= metric.timestamp <= end_time
        ]

        # Converti datetime in string per JSON
        for metric in filtered_metrics:
            metric["timestamp"] = metric["timestamp"].isoformat()

        return filtered_metrics

    def get_monitoring_statistics(self) -> Dict[str, Any]:
        """Restituisce statistiche del monitoraggio."""
        return {
            "monitoring_active": self.monitoring_active,
            "metrics_buffer_size": len(self.metrics_buffer),
            "active_alerts_count": len(self.active_alerts),
            "alert_history_size": len(self.alert_history),
            "operation_counters": dict(self.operation_counters),
            "error_counters": dict(self.error_counters),
            "performance_counters_size": {
                name: len(values) for name, values in self.performance_counters.items()
            },
            "dashboard_last_updated": self.dashboard_data["last_updated"].isoformat()
        }

# Istanza globale
sync_monitor = SyncMonitor()
