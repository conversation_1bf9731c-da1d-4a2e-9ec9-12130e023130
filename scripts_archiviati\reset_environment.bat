@echo off
setlocal enabledelayedexpansion
echo ===================================
echo Risoluzione radicale del problema plotly
echo Ricreazione completa dell'ambiente virtuale
echo ===================================
echo.

REM Verifica se l'utente vuole procedere
echo ATTENZIONE: Questo script eliminerà completamente l'ambiente virtuale esistente
echo e lo ricreerà da zero. Tutte le dipendenze verranno reinstallate.
echo.
set /p CONFIRM=Sei sicuro di voler procedere? (S/N): 
if /i not "%CONFIRM%"=="S" (
    echo Operazione annullata.
    goto :EOF
)

echo.
echo Procedendo con la ricreazione dell'ambiente virtuale...
echo.

REM Disattiva l'ambiente virtuale se attivo
if defined VIRTUAL_ENV (
    echo Disattivazione dell'ambiente virtuale corrente...
    call venv\Scripts\deactivate.bat
    echo Ambiente virtuale disattivato.
    echo.
)

REM Elimina l'ambiente virtuale esistente
if exist venv (
    echo Eliminazione dell'ambiente virtuale esistente...
    rd /s /q venv
    echo Ambiente virtuale eliminato.
    echo.
)

REM Crea un nuovo ambiente virtuale
echo Creazione di un nuovo ambiente virtuale pulito...
python -m venv venv
if %errorlevel% neq 0 (
    echo ERRORE: Impossibile creare l'ambiente virtuale.
    echo Verifica che Python sia installato correttamente.
    goto :EOF
)
echo Nuovo ambiente virtuale creato.
echo.

REM Attiva il nuovo ambiente virtuale
echo Attivazione del nuovo ambiente virtuale...
call venv\Scripts\activate
if %errorlevel% neq 0 (
    echo ERRORE: Impossibile attivare l'ambiente virtuale.
    goto :EOF
)
echo Ambiente virtuale attivato.
echo.

REM Aggiorna pip all'ultima versione
echo Aggiornamento di pip all'ultima versione...
python -m pip install --upgrade pip
if %errorlevel% neq 0 (
    echo AVVISO: Impossibile aggiornare pip, ma continuo comunque.
)
echo.

REM Installa le dipendenze di base
echo Installazione delle dipendenze di base...
pip install python-dotenv fastapi
if %errorlevel% neq 0 (
    echo ERRORE: Impossibile installare le dipendenze di base.
    goto :EOF
)
echo Dipendenze di base installate.
echo.

REM Installa plotly in modo pulito
echo Installazione pulita di plotly...
pip install --no-cache-dir plotly==5.16.1
if %errorlevel% neq 0 (
    echo ERRORE: Impossibile installare plotly.
    goto :EOF
)
echo Plotly installato correttamente.
echo.

REM Installa tutte le altre dipendenze
echo Installazione di tutte le altre dipendenze...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo AVVISO: Alcune dipendenze potrebbero non essere state installate correttamente.
    echo Controlla il messaggio di errore sopra.
) else (
    echo Tutte le dipendenze sono state installate correttamente.
)
echo.

REM Verifica che non ci siano pacchetti problematici
echo Verifica dei pacchetti installati...
pip list | findstr "~" > temp_packages.txt
set PROBLEM_FOUND=0
for /f "tokens=*" %%a in (temp_packages.txt) do (
    echo AVVISO: Trovato pacchetto problematico: %%a
    set PROBLEM_FOUND=1
)
del temp_packages.txt

if %PROBLEM_FOUND% equ 0 (
    echo Nessun pacchetto problematico trovato. L'ambiente è pulito!
) else (
    echo Sono stati trovati pacchetti problematici.
    echo Tentativo di rimozione manuale dei file problematici...
    
    REM Rimozione manuale più aggressiva dei file problematici
    if exist venv\Lib\site-packages\~lotly (
        echo Rimozione manuale della directory ~lotly...
        rd /s /q venv\Lib\site-packages\~lotly 2>nul
    )
    if exist venv\Lib\site-packages\~lotly-* (
        echo Rimozione dei file di metadati ~lotly...
        del /q venv\Lib\site-packages\~lotly-* 2>nul
    )
    
    REM Rimuovi anche dalla directory dist-info se presente
    if exist venv\Lib\site-packages\~lotly*.dist-info (
        echo Rimozione della directory dist-info di ~lotly...
        rd /s /q venv\Lib\site-packages\~lotly*.dist-info 2>nul
    )
    
    REM Verifica se il problema persiste
    pip list | findstr "~" > nul
    if %errorlevel% equ 0 (
        echo Il problema persiste. Potrebbe essere necessario un intervento manuale.
    ) else (
        echo Pacchetti problematici rimossi con successo!
    )
) else (
    echo Sono stati trovati pacchetti problematici.
    echo Potrebbe essere necessario un intervento manuale.
)
echo.

REM Verifica che plotly sia installato correttamente
echo Verifica dell'installazione di plotly...
pip show plotly > nul 2>&1
if %errorlevel% neq 0 (
    echo ERRORE: Plotly non risulta installato correttamente.
) else (
    echo Plotly è installato correttamente.
)
echo.

echo ===================================
echo Operazione completata!
echo.
echo Se non ci sono stati errori, l'ambiente virtuale è stato
echo ricreato correttamente e tutte le dipendenze sono state installate.
echo.
echo Per avviare l'applicazione, usa il comando:
echo   start_app_with_env.bat
echo ===================================
echo.
echo Premi un tasto per chiudere questa finestra...
pause > nul



