#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re

def find_duplicate_routes():
    """Trova tutte le route duplicate in app.py"""
    
    with open('app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Trova tutte le route con i loro numeri di riga
    lines = content.split('\n')
    routes = {}
    
    for i, line in enumerate(lines, 1):
        # Cerca pattern @app.route
        route_match = re.search(r'@app\.route\([\'\"](.*?)[\'\"]', line)
        if route_match:
            route_path = route_match.group(1)
            
            # Cerca la funzione nella riga successiva
            if i < len(lines):
                next_line = lines[i]
                func_match = re.search(r'def\s+(\w+)', next_line)
                if func_match:
                    func_name = func_match.group(1)
                    
                    if route_path not in routes:
                        routes[route_path] = []
                    routes[route_path].append({
                        'function': func_name,
                        'line': i
                    })
    
    # Trova duplicati
    duplicates = {route: funcs for route, funcs in routes.items() if len(funcs) > 1}
    
    print('Route duplicate trovate:')
    for route, funcs in duplicates.items():
        print(f'\n  Route: {route}')
        for func_info in funcs:
            print(f'    - Funzione: {func_info["function"]} (riga {func_info["line"]})')
    
    return duplicates

if __name__ == '__main__':
    find_duplicate_routes()
