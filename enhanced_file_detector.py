#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced File Detector per il rilevamento content-based dei tipi di file.
Supporta varianti, sinonimi e fuzzy matching per gestire file grezzi.
"""

import pandas as pd
import logging
import re
from typing import Dict, List, Tuple, Any, Set
from difflib import SequenceMatcher

# Configurazione del logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedFileDetector:
    """
    Rilevatore di tipo file avanzato che supporta:
    - Varianti linguistiche (italiano/inglese)
    - Sinonimi e abbreviazioni
    - Fuzzy matching per colonne simili
    - Pattern content-based
    """

    def __init__(self):
        # Definizione delle firme avanzate per i diversi tipi di file
        self.FILE_SIGNATURES = {
            "teamviewer": {
                "required_patterns": [
                    # Utente/User/Tecnico
                    ["utente", "user", "tecnico", "technician", "operator", "operatore"],
                    # Computer/PC/Client
                    ["computer", "pc", "client", "cliente", "machine", "host", "endpoint"],
                    # Inizio/Start/Begin
                    ["inizio", "start", "begin", "started", "iniziato", "avvio", "connection start"],
                    # Fine/End/Stop
                    ["fine", "end", "stop", "ended", "finito", "terminato", "connection end"]
                ],
                "optional_patterns": [
                    ["durata", "duration", "time", "tempo", "elapsed"],
                    ["id", "session id", "sessione", "connection id"],
                    ["tipo", "type", "session type", "connection type"],
                    ["gruppo", "group", "team"],
                    ["note", "notes", "comment", "commenti"]
                ],
                "weight": {
                    "user_pattern": 1.5,
                    "computer_pattern": 1.5,
                    "start_pattern": 1.5,
                    "end_pattern": 1.5,
                    "duration_pattern": 1.0,
                    "id_pattern": 0.8,
                    "type_pattern": 0.8,
                    "group_pattern": 0.5,
                    "notes_pattern": 0.3
                }
            },
            "calendario": {
                "required_patterns": [
                    # Data/Date
                    ["data", "date", "giorno", "day"],
                    # Ora inizio/Start time
                    ["ora inizio", "start time", "begin time", "inizio", "start", "from"],
                    # Ora fine/End time
                    ["ora fine", "end time", "stop time", "fine", "end", "to"],
                    # Titolo/Subject/Event
                    ["titolo", "title", "subject", "evento", "event", "summary", "oggetto"]
                ],
                "optional_patterns": [
                    ["descrizione", "description", "details", "dettagli"],
                    ["luogo", "location", "place", "posto", "where"],
                    ["partecipanti", "attendees", "participants", "invitati"]
                ],
                "weight": {
                    "date_pattern": 1.5,
                    "start_pattern": 1.5,
                    "end_pattern": 1.5,
                    "title_pattern": 1.2,
                    "description_pattern": 0.5,
                    "location_pattern": 0.5,
                    "attendees_pattern": 0.5
                }
            },
            "timbrature": {
                "required_patterns": [
                    # Data/Date
                    ["data", "date", "giorno", "day"],
                    # Dipendente/Employee
                    ["dipendente", "employee", "worker", "nome", "name", "person"],
                    # Entrata/Clock in
                    ["entrata", "clock in", "check in", "arrival", "arrivo", "in"],
                    # Uscita/Clock out
                    ["uscita", "clock out", "check out", "departure", "partenza", "out"]
                ],
                "optional_patterns": [
                    ["ore lavorate", "hours worked", "working hours", "ore", "hours"],
                    ["straordinario", "overtime", "extra hours"],
                    ["note", "notes", "comment", "commenti"]
                ],
                "weight": {
                    "date_pattern": 1.5,
                    "employee_pattern": 1.2,
                    "checkin_pattern": 1.5,
                    "checkout_pattern": 1.5,
                    "hours_pattern": 0.8,
                    "overtime_pattern": 0.5,
                    "notes_pattern": 0.3
                }
            },
            "permessi": {
                "required_patterns": [
                    # Dipendente/Employee
                    ["dipendente", "employee", "worker", "nome", "name", "person"],
                    # Data inizio/Start date
                    ["data inizio", "start date", "from date", "inizio", "da"],
                    # Data fine/End date
                    ["data fine", "end date", "to date", "fine", "a"],
                    # Tipo permesso/Leave type
                    ["tipo permesso", "leave type", "permission type", "tipo", "type"]
                ],
                "optional_patterns": [
                    ["ore", "hours", "giorni", "days"],
                    ["approvato", "approved", "autorizzato", "authorized"],
                    ["note", "notes", "reason", "motivo"]
                ],
                "weight": {
                    "employee_pattern": 1.2,
                    "start_date_pattern": 1.5,
                    "end_date_pattern": 1.5,
                    "type_pattern": 1.5,
                    "hours_pattern": 0.8,
                    "approved_pattern": 0.5,
                    "notes_pattern": 0.3
                }
            },
            "attivita": {
                "required_patterns": [
                    # Cliente/Contract/Customer
                    ["contratto", "cliente", "customer", "client", "contract"],
                    # ID Ticket/Task ID
                    ["id ticket", "ticket id", "task id", "id", "numero", "number"],
                    # Data inizio/Started
                    ["iniziata", "started", "inizio", "start", "created"],
                    # Data fine/Completed
                    ["conclusa", "completed", "finished", "fine", "end"],
                    # Durata/Duration
                    ["durata", "duration", "time", "tempo", "hours", "ore"]
                ],
                "optional_patterns": [
                    ["tecnico", "technician", "assigned", "assegnato", "creato da"],
                    ["descrizione", "description", "summary", "details"],
                    ["stato", "status", "state"],
                    ["tipo", "type", "category", "categoria"]
                ],
                "weight": {
                    "client_pattern": 1.2,
                    "ticket_pattern": 1.5,
                    "start_pattern": 1.5,
                    "end_pattern": 1.5,
                    "duration_pattern": 1.2,
                    "technician_pattern": 0.8,
                    "description_pattern": 0.5,
                    "status_pattern": 0.5,
                    "type_pattern": 0.5
                }
            },
            "registro_auto": {
                "required_patterns": [
                    # Dipendente/Employee
                    ["dipendente", "employee", "driver", "conducente", "nome"],
                    # Data utilizzo
                    ["data", "date", "giorno", "day"],
                    # Auto/Vehicle
                    ["auto", "vehicle", "car", "veicolo", "macchina"],
                    # Presa/Pickup
                    ["presa", "pickup", "taken", "ritiro", "presa data", "start time"],
                    # Cliente/Customer
                    ["cliente", "customer", "client", "destinazione", "destination"]
                ],
                "optional_patterns": [
                    # Riconsegna/Return
                    ["riconsegna", "return", "returned", "consegna", "riconsegna data"],
                    # Ore/Hours
                    ["ore", "hours", "durata", "duration", "tempo"],
                    # Note/Comments
                    ["note", "notes", "comment", "commenti", "osservazioni"]
                ],
                "weight": {
                    "employee_pattern": 1.5,
                    "date_pattern": 1.5,
                    "vehicle_pattern": 1.5,
                    "pickup_pattern": 1.2,
                    "client_pattern": 1.2,
                    "return_pattern": 1.0,
                    "hours_pattern": 0.8,
                    "notes_pattern": 0.3
                }
            },
            "dipendenti": {
                "required_patterns": [
                    # Dipendente/Employee/Nome
                    ["dipendente", "employee", "nome", "name", "worker", "person"],
                    # Data/Date
                    ["data", "date", "giorno", "day"],
                    # Ore lavorate/Hours worked
                    ["ore lavorate", "hours worked", "ore", "hours", "tempo", "time"]
                ],
                "optional_patterns": [
                    # Progetto (ma non come pattern principale)
                    ["progetto", "project", "task", "attivita"],
                    # Straordinario/Overtime
                    ["straordinario", "overtime", "extra"],
                    # Note/Comments
                    ["note", "notes", "comment", "commenti"],
                    # Reparto/Department
                    ["reparto", "department", "team", "gruppo"]
                ],
                "weight": {
                    "employee_pattern": 2.0,          # Peso alto per dipendente
                    "date_pattern": 1.5,              # Peso alto per data
                    "hours_pattern": 2.0,             # Peso alto per ore lavorate
                    "project_pattern": 0.8,           # Peso basso per progetto (opzionale)
                    "overtime_pattern": 0.6,
                    "notes_pattern": 0.3,
                    "department_pattern": 0.5
                }
            },
            "progetti": {
                "required_patterns": [
                    # Codice Progetto (pattern più specifico basato sull'analisi)
                    ["codice progetto", "project code", "codice", "code"],
                    # Nome/Descrizione progetto
                    ["nome", "name", "progetto", "project", "nome progetto", "project name"],
                    # Stato/Status
                    ["stato", "status", "phase", "fase"]
                ],
                "optional_patterns": [
                    # Capo Progetto/Manager (pattern trovato nell'analisi)
                    ["capo progetto", "project manager", "responsabile", "manager", "capo", "pm"],
                    # Cliente/Customer
                    ["cliente", "customer", "client", "azienda", "company"],
                    # Date
                    ["data inizio", "start date", "inizio", "started", "data", "date"],
                    ["data fine", "end date", "fine", "completed", "concluso"],
                    # Altri campi
                    ["priorita", "priority", "urgenza", "urgency"],
                    ["descrizione", "description", "details", "dettagli"]
                ],
                "weight": {
                    "project_code_pattern": 2.5,      # Peso molto alto per "Codice Progetto"
                    "project_name_pattern": 1.8,      # Peso alto per nome progetto
                    "status_pattern": 1.5,            # Peso alto per stato
                    "manager_pattern": 1.2,           # Peso per capo progetto
                    "client_pattern": 1.0,
                    "start_date_pattern": 0.8,
                    "end_date_pattern": 0.8,
                    "priority_pattern": 0.6,
                    "description_pattern": 0.5
                }
            }
        }

        # Soglia minima di punteggio per considerare un match valido
        self.MIN_MATCH_THRESHOLD = 0.5  # Ridotta per essere più permissiva

        # Soglia per fuzzy matching
        self.FUZZY_THRESHOLD = 0.6

    def detect_file_type(self, df: pd.DataFrame, filename: str = None) -> Tuple[str, float, Dict[str, float]]:
        """
        Analizza un DataFrame e determina il tipo di file usando pattern avanzati.

        Args:
            df: DataFrame pandas da analizzare
            filename: Nome del file (opzionale, per migliorare la rilevazione)

        Returns:
            Tuple contenente:
            - Il tipo di file rilevato (o "unknown" se non riconosciuto)
            - Il punteggio di confidenza (da 0 a 1)
            - Un dizionario con i punteggi per ogni tipo di file
        """
        if df is None or df.empty:
            logger.warning("DataFrame vuoto o None fornito al rilevatore di tipo file")
            return "unknown", 0.0, {}

        logger.info(f"Analizzando file con colonne: {df.columns.tolist()}")

        # CONTROLLO PRIORITARIO SUL NOME FILE
        if filename:
            filename_lower = filename.lower()

            # Controllo specifico per file di permessi
            if "richieste" in filename_lower and "apprilevazionepresenze" in filename_lower:
                logger.info(f"FILENAME MATCH: File riconosciuto come PERMESSI dal nome: {filename}")
                # Calcola comunque i punteggi ma forza il tipo permessi se ha le colonne giuste
                df_columns = [self._normalize_column_name(col) for col in df.columns]
                scores = {}
                for file_type, signature in self.FILE_SIGNATURES.items():
                    score = self._calculate_pattern_score(df_columns, signature)
                    scores[file_type] = score

                # Forza permessi con confidenza alta se ha le colonne giuste
                permessi_score = scores.get('permessi', 0.0)
                if permessi_score > 0.5:  # Se ha almeno alcune colonne di permessi
                    logger.info(f"CORREZIONE: Forzato tipo 'permessi' per filename match (score originale: {permessi_score:.3f})")
                    return "permessi", 0.95, scores  # Confidenza alta per filename match

            # Controllo specifico per file di timbrature
            elif "timbrature" in filename_lower and "apprilevazionepresenze" in filename_lower:
                logger.info(f"FILENAME MATCH: File riconosciuto come TIMBRATURE dal nome: {filename}")
                # Calcola comunque i punteggi ma forza il tipo timbrature se ha le colonne giuste
                df_columns = [self._normalize_column_name(col) for col in df.columns]
                scores = {}
                for file_type, signature in self.FILE_SIGNATURES.items():
                    score = self._calculate_pattern_score(df_columns, signature)
                    scores[file_type] = score

                # Forza timbrature con confidenza alta se ha le colonne giuste
                timbrature_score = scores.get('timbrature', 0.0)
                if timbrature_score > 0.3:  # Soglia più bassa per timbrature
                    logger.info(f"CORREZIONE: Forzato tipo 'timbrature' per filename match (score originale: {timbrature_score:.3f})")
                    return "timbrature", 0.95, scores  # Confidenza alta per filename match

            # Altri controlli filename specifici
            elif "connectionreport" in filename_lower:
                logger.info(f"FILENAME MATCH: File riconosciuto come TEAMVIEWER dal nome: {filename}")
            elif ("export_" in filename_lower or "attivita" in filename_lower) and filename_lower.endswith('.xlsx'):
                logger.info(f"FILENAME MATCH: File riconosciuto come ATTIVITA dal nome: {filename}")
                # Per file attività, forza il tipo solo se ha pattern appropriati
                df_columns = [self._normalize_column_name(col) for col in df.columns]
                scores = {}
                for file_type, signature in self.FILE_SIGNATURES.items():
                    score = self._calculate_pattern_score(df_columns, signature)
                    scores[file_type] = score

                attivita_score = scores.get('attivita', 0.0)
                timbrature_score = scores.get('timbrature', 0.0)

                # Se ha pattern timbrature molto forti, non forzare attività
                if timbrature_score > 0.8 and attivita_score < 0.5:
                    logger.info(f"SKIP: File ha pattern timbrature troppo forti ({timbrature_score:.3f}) per essere forzato come attività")
                elif attivita_score > 0.2:  # Soglia bassa per attività
                    logger.info(f"CORREZIONE: Forzato tipo 'attivita' per filename match (score originale: {attivita_score:.3f})")
                    return "attivita", 0.95, scores
            elif "timbrature" in filename_lower and "apprilevazionepresenze" not in filename_lower:
                logger.info(f"FILENAME MATCH: File riconosciuto come TIMBRATURE dal nome: {filename}")
                # Per timbrature generiche, verifica che abbia senso
                df_columns = [self._normalize_column_name(col) for col in df.columns]
                scores = {}
                for file_type, signature in self.FILE_SIGNATURES.items():
                    score = self._calculate_pattern_score(df_columns, signature)
                    scores[file_type] = score

                timbrature_score = scores.get('timbrature', 0.0)
                if timbrature_score > 0.5:  # Se ha pattern timbrature decenti
                    logger.info(f"CORREZIONE: Forzato tipo 'timbrature' per filename match (score originale: {timbrature_score:.3f})")
                    return "timbrature", 0.95, scores
            elif "dipendenti" in filename_lower:
                logger.info(f"FILENAME MATCH: File riconosciuto come DIPENDENTI dal nome: {filename}")
                # Per file dipendenti, verifica che abbia senso
                df_columns = [self._normalize_column_name(col) for col in df.columns]
                scores = {}
                for file_type, signature in self.FILE_SIGNATURES.items():
                    score = self._calculate_pattern_score(df_columns, signature)
                    scores[file_type] = score

                dipendenti_score = scores.get('dipendenti', 0.0)
                if dipendenti_score > 0.3:  # Soglia più bassa per dipendenti
                    logger.info(f"CORREZIONE: Forzato tipo 'dipendenti' per filename match (score originale: {dipendenti_score:.3f})")
                    return "dipendenti", 0.95, scores

        # Normalizza i nomi delle colonne
        df_columns = [self._normalize_column_name(col) for col in df.columns]
        logger.info(f"Colonne normalizzate: {df_columns}")

        # Calcola i punteggi per ogni tipo di file
        scores = {}
        for file_type, signature in self.FILE_SIGNATURES.items():
            score = self._calculate_pattern_score(df_columns, signature)
            scores[file_type] = score
            logger.info(f"Punteggio per {file_type}: {score:.3f}")

        # Trova il tipo di file con il punteggio più alto
        if not scores:
            return "unknown", 0.0, {}

        best_match = max(scores.items(), key=lambda x: x[1])
        file_type, score = best_match

        # Verifica se il punteggio supera la soglia minima
        if score < self.MIN_MATCH_THRESHOLD:
            logger.info(f"Nessun tipo di file riconosciuto con sufficiente confidenza. Punteggio massimo: {score:.3f} per {file_type}")
            return "unknown", score, scores

        logger.info(f"Tipo di file rilevato: {file_type} con confidenza {score:.3f}")
        return file_type, score, scores

    def _normalize_column_name(self, column_name: str) -> str:
        """Normalizza il nome di una colonna per il confronto"""
        if not isinstance(column_name, str):
            return str(column_name).lower().strip()

        # Rimuovi caratteri speciali e normalizza
        normalized = re.sub(r'[^\w\s]', ' ', column_name.lower())
        normalized = re.sub(r'\s+', ' ', normalized).strip()
        return normalized

    def _calculate_pattern_score(self, df_columns: List[str], signature: Dict[str, Any]) -> float:
        """
        Calcola un punteggio basato sui pattern di colonne.

        Args:
            df_columns: Lista dei nomi delle colonne normalizzati
            signature: Dizionario contenente i pattern del tipo di file

        Returns:
            Punteggio di corrispondenza (da 0 a 1)
        """
        required_patterns = signature["required_patterns"]
        optional_patterns = signature.get("optional_patterns", [])
        weights = signature["weight"]

        # Calcola il punteggio massimo possibile
        max_score = sum(weights.values())

        # Calcola il punteggio effettivo
        actual_score = 0.0
        matched_patterns = []

        # Verifica pattern richiesti
        for i, pattern_list in enumerate(required_patterns):
            pattern_key = list(weights.keys())[i]
            if self._match_pattern_in_columns(pattern_list, df_columns):
                actual_score += weights[pattern_key]
                matched_patterns.append(pattern_key)
                logger.debug(f"Pattern richiesto trovato: {pattern_key} -> {pattern_list}")
            else:
                # Penalizza la mancanza di pattern richiesti
                actual_score -= weights[pattern_key] * 0.5
                logger.debug(f"Pattern richiesto mancante: {pattern_key} -> {pattern_list}")

        # Verifica pattern opzionali
        required_count = len(required_patterns)
        for i, pattern_list in enumerate(optional_patterns):
            pattern_key = list(weights.keys())[required_count + i]
            if self._match_pattern_in_columns(pattern_list, df_columns):
                actual_score += weights[pattern_key]
                matched_patterns.append(pattern_key)
                logger.debug(f"Pattern opzionale trovato: {pattern_key} -> {pattern_list}")

        # Normalizza il punteggio (da 0 a 1)
        if max_score > 0:
            normalized_score = max(0.0, min(1.0, actual_score / max_score))
        else:
            normalized_score = 0.0

        logger.debug(f"Pattern matched: {matched_patterns}, Score: {actual_score:.3f}/{max_score:.3f} = {normalized_score:.3f}")
        return normalized_score

    def _match_pattern_in_columns(self, pattern_list: List[str], df_columns: List[str]) -> bool:
        """
        Verifica se almeno uno dei pattern nella lista corrisponde a una colonna.

        Args:
            pattern_list: Lista di pattern da cercare
            df_columns: Lista delle colonne del DataFrame

        Returns:
            True se almeno un pattern corrisponde
        """
        for pattern in pattern_list:
            pattern_normalized = self._normalize_column_name(pattern)

            # Controllo esatto
            if pattern_normalized in df_columns:
                logger.debug(f"Match esatto: '{pattern_normalized}' in {df_columns}")
                return True

            # Controllo fuzzy
            for col in df_columns:
                if self._fuzzy_match(pattern_normalized, col):
                    logger.debug(f"Fuzzy match: '{pattern_normalized}' ~ '{col}'")
                    return True

                # Controllo contenimento
                if pattern_normalized in col or col in pattern_normalized:
                    logger.debug(f"Containment match: '{pattern_normalized}' <-> '{col}'")
                    return True

        return False

    def _fuzzy_match(self, pattern: str, column: str) -> bool:
        """
        Verifica se due stringhe sono simili usando fuzzy matching.

        Args:
            pattern: Pattern da cercare
            column: Nome della colonna

        Returns:
            True se le stringhe sono sufficientemente simili
        """
        similarity = SequenceMatcher(None, pattern, column).ratio()
        return similarity >= self.FUZZY_THRESHOLD

    def get_column_mapping_suggestions(self, df: pd.DataFrame, detected_type: str) -> Dict[str, str]:
        """
        Suggerisce una mappatura tra le colonne del DataFrame e i pattern standard.

        Args:
            df: DataFrame pandas da analizzare
            detected_type: Tipo di file rilevato

        Returns:
            Dizionario con la mappatura suggerita {colonna_df: pattern_standard}
        """
        if detected_type not in self.FILE_SIGNATURES or detected_type == "unknown":
            return {}

        signature = self.FILE_SIGNATURES[detected_type]
        all_patterns = signature["required_patterns"] + signature.get("optional_patterns", [])

        mapping = {}
        df_columns = [self._normalize_column_name(col) for col in df.columns]

        # Per ogni pattern, trova la migliore corrispondenza
        for i, pattern_list in enumerate(all_patterns):
            best_match = None
            best_score = 0.0

            for original_col, normalized_col in zip(df.columns, df_columns):
                for pattern in pattern_list:
                    pattern_normalized = self._normalize_column_name(pattern)

                    # Calcola similarità
                    if pattern_normalized == normalized_col:
                        score = 1.0
                    elif pattern_normalized in normalized_col or normalized_col in pattern_normalized:
                        score = 0.8
                    else:
                        score = SequenceMatcher(None, pattern_normalized, normalized_col).ratio()

                    if score > best_score and score >= self.FUZZY_THRESHOLD:
                        best_match = original_col
                        best_score = score

            if best_match:
                # Usa il primo pattern come nome standard
                standard_name = pattern_list[0]
                mapping[best_match] = standard_name

        return mapping
