{% extends "base.html" %}

{% block title %}Dashboard - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<main class="row mb-4">
    <section class="col-12">
        <article class="card shadow">
            <header class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h4>
            </header>
            <div class="card-body">
                {% if not data %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Questa è una versione preliminare della dashboard. Carica un file per visualizzare i dati.
                </div>
                {% else %}
                <div class="alert alert-info d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-info-circle me-2"></i>
                        Visualizzazione dati: <strong>{{ filename }}</strong>
                        <span class="badge {% if file_type == 'unknown' or file_type == 'generico' %}bg-warning{% else %}bg-success{% endif %} text-white ms-2">
                            <i class="fas {% if file_type == 'attivita' %}fa-tasks{% elif 'teamviewer' in file_type %}fa-desktop{% elif file_type == 'calendario' %}fa-calendar{% elif file_type == 'timbrature' %}fa-clock{% elif file_type == 'permessi' %}fa-calendar-check{% elif file_type == 'normalized' %}fa-database{% elif file_type == 'wizard' %}fa-magic{% else %}fa-file{% endif %} me-1"></i>
                            Tipo: {{ file_type }}
                        </span>
                        {% if data_source %}
                        <span class="badge {% if data_source == 'supabase' %}bg-primary{% elif data_source == 'wizard' %}bg-info{% else %}bg-secondary{% endif %} text-white ms-2" title="Fonte dati: {{ data_source }}">
                            <i class="fas {% if data_source == 'supabase' %}fa-database{% elif data_source == 'wizard' %}fa-magic{% elif data_source == 'processed_file' %}fa-cogs{% else %}fa-file{% endif %} me-1"></i>
                            {% if data_source == 'supabase' %}Supabase{% elif data_source == 'wizard' %}Wizard{% elif data_source == 'processed_file' %}Elaborato{% else %}Sessione{% endif %}
                        </span>
                        {% endif %}
                        {% if session.get('mcp_file_id') %}
                        <span class="badge bg-info text-white ms-2" title="Elaborato con MCP">
                            <i class="fas fa-server me-1"></i>MCP
                        </span>
                        {% endif %}
                    </div>
                    <div>
                        <a href="{{ url_for('interactive_charts') }}" class="btn btn-outline-primary btn-sm me-2">
                            <i class="fas fa-chart-bar me-1"></i>Grafici Interattivi
                        </a>
                        <a href="{{ url_for('advanced_dashboard') }}" class="btn btn-outline-primary btn-sm me-2">
                            <i class="fas fa-chart-line me-1"></i>Dashboard Avanzata
                        </a>
                        <a href="{{ url_for('chat') }}" class="btn btn-outline-primary btn-sm me-2">
                            <i class="fas fa-comments me-1"></i>Chat AI
                        </a>
                        <a href="/agents/dashboard" class="btn btn-outline-primary btn-sm me-2">
                            <i class="fas fa-robot me-1"></i>Agenti AI
                        </a>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success btn-sm dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-file-export me-1"></i>Esporta
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="{{ url_for('export_excel') }}"><i class="fas fa-file-excel me-2 text-success"></i>Excel</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('export_csv') }}"><i class="fas fa-file-csv me-2 text-primary"></i>CSV</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('export_json') }}"><i class="fas fa-file-code me-2 text-warning"></i>JSON</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('export_pdf') }}"><i class="fas fa-file-pdf me-2 text-danger"></i>PDF</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                {% endif %}

                <div class="row">
                    <div class="col-md-3 mb-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">
                                            {% if 'teamviewer' in file_type %}
                                                Totale Sessioni
                                            {% elif file_type == 'attivita' %}
                                                Totale Attività
                                            {% elif 'calendario' in file_type %}
                                                Totale Eventi
                                            {% else %}
                                                Totale Elementi
                                            {% endif %}
                                        </h6>
                                        <h2 class="mb-0" id="total-sessions">
                                            {% if stats.total_sessions is defined %}
                                                {{ stats.total_sessions }}
                                            {% elif stats.total_events is defined %}
                                                {{ stats.total_events }}
                                            {% else %}
                                                --
                                            {% endif %}
                                        </h2>
                                    </div>
                                    <i class="fas fa-headset fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">Durata Media</h6>
                                        <h2 class="mb-0" id="avg-duration">
                                            {% if stats.avg_duration is defined %}
                                                {{ "%.1f"|format(stats.avg_duration) }} min
                                            {% else %}
                                                --
                                            {% endif %}
                                        </h2>
                                    </div>
                                    <i class="fas fa-clock fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">
                                            {% if 'teamviewer' in file_type %}
                                                Tecnici Attivi
                                            {% elif 'timbrature' in file_type or 'permessi' in file_type %}
                                                Dipendenti
                                            {% elif file_type == 'attivita' %}
                                                Tecnici
                                            {% else %}
                                                Elementi Unici
                                            {% endif %}
                                        </h6>
                                        <h2 class="mb-0" id="active-technicians">
                                            {% if stats.unique_technicians is defined %}
                                                {{ stats.unique_technicians }}
                                            {% elif stats.unique_clients is defined %}
                                                {{ stats.unique_clients }}
                                            {% else %}
                                                --
                                            {% endif %}
                                        </h2>
                                    </div>
                                    <i class="fas fa-users fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">
                                            {% if 'teamviewer' in file_type %}
                                                Soddisfazione
                                            {% elif 'timbrature' in file_type %}
                                                Ore Totali
                                            {% elif file_type == 'attivita' %}
                                                Ore Totali
                                            {% elif 'calendario' in file_type %}
                                                Durata Totale
                                            {% else %}
                                                Totale
                                            {% endif %}
                                        </h6>
                                        <h2 class="mb-0" id="satisfaction-rate">
                                            {% if stats.total_hours is defined %}
                                                {{ "%.1f"|format(stats.total_hours) }}h
                                            {% elif stats.total_duration is defined %}
                                                {{ "%.1f"|format(stats.total_duration / 60) }}h
                                            {% else %}
                                                --
                                            {% endif %}
                                        </h2>
                                    </div>
                                    <i class="fas fa-chart-line fa-3x opacity-50"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8 mb-4">
                        <div class="card shadow">
                            <div class="card-header">
                                <h5 class="mb-0">Attività nel Tempo</h5>
                            </div>
                            <div class="card-body">
                                <div id="time-chart" class="chart-container">
                                    {% if data %}
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Caricamento...</span>
                                        </div>
                                        <p class="mt-2">Caricamento grafico...</p>
                                    </div>
                                    {% else %}
                                    <div class="d-flex justify-content-center align-items-center h-100">
                                        <p class="text-muted">Carica un file per visualizzare il grafico</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-4">
                        <div class="card shadow">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    {% if 'teamviewer' in file_type %}
                                        Distribuzione Tecnici
                                    {% elif 'timbrature' in file_type or 'permessi' in file_type %}
                                        Distribuzione Dipendenti
                                    {% elif 'calendario' in file_type or file_type == 'attivita' %}
                                        Distribuzione Eventi
                                    {% else %}
                                        Distribuzione
                                    {% endif %}
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="technician-chart" class="chart-container">
                                    {% if data %}
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Caricamento...</span>
                                        </div>
                                        <p class="mt-2">Caricamento grafico...</p>
                                    </div>
                                    {% else %}
                                    <div class="d-flex justify-content-center align-items-center h-100">
                                        <p class="text-muted">Carica un file per visualizzare il grafico</p>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- KPI Cards -->
                <div class="row">
                    <div class="col-12 mb-4">
                        <div class="card shadow">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">KPI - Indicatori Chiave di Performance</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <!-- KPI 1: Ore Totali -->
                                    <div class="col-md-4 mb-4">
                                        <div class="card border-0 shadow-sm">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between mb-3">
                                                    <div>
                                                        <h6 class="card-title text-muted mb-0">Ore Totali</h6>
                                                        <h4 class="mt-2 mb-0">{{ stats.get('total_hours', 0) }}</h4>
                                                    </div>
                                                    <div class="rounded-circle bg-light p-3">
                                                        <i class="fas fa-clock text-primary fa-2x"></i>
                                                    </div>
                                                </div>
                                                <div class="progress" role="progressbar" aria-label="Ore totali" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                                    {% set hours_percentage = stats.get('total_hours', 0) / 100 * 100 %}
                                                    {% if hours_percentage > 100 %}
                                                        {% set hours_percentage = 100 %}
                                                    {% endif %}
                                                    {% set hours_percentage_int = hours_percentage|round(0)|int %}
                                                    <div class="progress-bar bg-primary progress-width-dynamic" data-width="{{ hours_percentage_int }}"></div>
                                                </div>
                                                <p class="text-muted small mt-2">{{ hours_percentage_int }}% dell'obiettivo</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- KPI 2: Durata Media -->
                                    <div class="col-md-4 mb-4">
                                        <div class="card border-0 shadow-sm">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between mb-3">
                                                    <div>
                                                        <h6 class="card-title text-muted mb-0">Durata Media</h6>
                                                        <h4 class="mt-2 mb-0">{{ stats.get('average_duration', 0) }} min</h4>
                                                    </div>
                                                    <div class="rounded-circle bg-light p-3">
                                                        <i class="fas fa-hourglass-half text-success fa-2x"></i>
                                                    </div>
                                                </div>
                                                <div class="progress" role="progressbar" aria-label="Durata media" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                                    {% set duration_percentage = stats.get('average_duration', 0) / 60 * 100 %}
                                                    {% if duration_percentage > 100 %}
                                                        {% set duration_percentage = 100 %}
                                                    {% endif %}
                                                    {% set duration_percentage_int = duration_percentage|round(0)|int %}
                                                    <div class="progress-bar bg-success progress-width-dynamic" data-width="{{ duration_percentage_int }}"></div>
                                                </div>
                                                <p class="text-muted small mt-2">{{ duration_percentage_int }}% dell'obiettivo (60 min)</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- KPI 3: Tecnici Attivi -->
                                    <div class="col-md-4 mb-4">
                                        <div class="card border-0 shadow-sm">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between mb-3">
                                                    <div>
                                                        <h6 class="card-title text-muted mb-0">Tecnici Attivi</h6>
                                                        <h4 class="mt-2 mb-0">{{ stats.get('technicians_count', 0) }}</h4>
                                                    </div>
                                                    <div class="rounded-circle bg-light p-3">
                                                        <i class="fas fa-users text-info fa-2x"></i>
                                                    </div>
                                                </div>
                                                <div class="progress" role="progressbar" aria-label="Numero tecnici" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                                    {% set technicians_percentage = stats.get('technicians_count', 0) / 10 * 100 %}
                                                    {% if technicians_percentage > 100 %}
                                                        {% set technicians_percentage = 100 %}
                                                    {% endif %}
                                                    {% set technicians_percentage_int = technicians_percentage|round(0)|int %}
                                                    <div class="progress-bar bg-info progress-width-dynamic" data-width="{{ technicians_percentage_int }}"></div>
                                                </div>
                                                <p class="text-muted small mt-2">{{ technicians_percentage_int }}% dell'obiettivo (10)</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- KPI 4: Clienti Serviti -->
                                    <div class="col-md-4 mb-4">
                                        <div class="card border-0 shadow-sm">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between mb-3">
                                                    <div>
                                                        <h6 class="card-title text-muted mb-0">Clienti Serviti</h6>
                                                        <h4 class="mt-2 mb-0">{{ stats.get('clients_count', 0) }}</h4>
                                                    </div>
                                                    <div class="rounded-circle bg-light p-3">
                                                        <i class="fas fa-building text-warning fa-2x"></i>
                                                    </div>
                                                </div>
                                                <div class="progress" role="progressbar" aria-label="Numero clienti" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                                    {% set clients_percentage = stats.get('clients_count', 0) / 20 * 100 %}
                                                    {% if clients_percentage > 100 %}
                                                        {% set clients_percentage = 100 %}
                                                    {% endif %}
                                                    {% set clients_percentage_int = clients_percentage|round(0)|int %}
                                                    <div class="progress-bar bg-warning progress-width-dynamic" data-width="{{ clients_percentage_int }}"></div>
                                                </div>
                                                <p class="text-muted small mt-2">{{ clients_percentage_int }}% dell'obiettivo (20)</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- KPI 5: Efficienza -->
                                    <div class="col-md-4 mb-4">
                                        <div class="card border-0 shadow-sm">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between mb-3">
                                                    <div>
                                                        <h6 class="card-title text-muted mb-0">Efficienza</h6>
                                                        {% set efficiency = stats.get('efficiency', 1) %}
                                                        <h4 class="mt-2 mb-0">{{ "%.1f"|format(efficiency) }}</h4>
                                                    </div>
                                                    <div class="rounded-circle bg-light p-3">
                                                        <i class="fas fa-tachometer-alt text-danger fa-2x"></i>
                                                    </div>
                                                </div>
                                                <div class="progress" role="progressbar" aria-label="Efficienza" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                                    {% set efficiency_percentage = efficiency / 2 * 100 %}
                                                    {% if efficiency_percentage > 100 %}
                                                        {% set efficiency_percentage = 100 %}
                                                    {% endif %}
                                                    {% set efficiency_percentage_int = efficiency_percentage|round(0)|int %}
                                                    <div class="progress-bar bg-danger progress-width-dynamic" data-width="{{ efficiency_percentage_int }}"></div>
                                                </div>
                                                <p class="text-muted small mt-2">{{ efficiency_percentage_int }}% dell'obiettivo (2.0)</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- KPI 6: Carico di Lavoro -->
                                    <div class="col-md-4 mb-4">
                                        <div class="card border-0 shadow-sm">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between mb-3">
                                                    <div>
                                                        <h6 class="card-title text-muted mb-0">Carico di Lavoro</h6>
                                                        {% set workload = stats.get('workload', 0) %}
                                                        <h4 class="mt-2 mb-0">{{ "%.1f"|format(workload) }}</h4>
                                                    </div>
                                                    <div class="rounded-circle bg-light p-3">
                                                        <i class="fas fa-weight-hanging text-secondary fa-2x"></i>
                                                    </div>
                                                </div>
                                                <div class="progress" role="progressbar" aria-label="Carico di lavoro" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                                    {% set workload_percentage = workload / 20 * 100 %}
                                                    {% if workload_percentage > 100 %}
                                                        {% set workload_percentage = 100 %}
                                                    {% endif %}
                                                    {% set workload_percentage_int = workload_percentage|round(0)|int %}
                                                    <div class="progress-bar bg-secondary progress-width-dynamic" data-width="{{ workload_percentage_int }}"></div>
                                                </div>
                                                <p class="text-muted small mt-2">{{ workload_percentage_int }}% dell'obiettivo (20)</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </article>
    </section>
</main>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
{% endblock %}

{% block extra_js %}
<!-- Carica Plotly (versione completa per maggiore compatibilità) -->
<script src="https://cdn.plot.ly/plotly-2.24.1.min.js"></script>

<!-- Carica moment.js prima di daterangepicker (necessario per il funzionamento) -->
<script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>

<!-- Carica daterangepicker dopo moment.js -->
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

<!-- Carica lo script ottimizzato della dashboard con versione per evitare caching -->
<script src="{{ url_for('static', filename='js/dashboard.js') }}?v=2.2.0"></script>
{% endblock %}













