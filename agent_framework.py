#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Framework per la gestione degli agenti AI.
Questo modulo fornisce le classi e le funzioni di base per implementare
e orchestrare agenti AI che utilizzano il server MCP (MODEL CONTEXT PROTOCOL).
"""

import os
import json
import logging
import asyncio
import uuid
from typing import Dict, List, Any, Optional, Callable, Union, Tuple
from abc import ABC, abstractmethod
from datetime import datetime

import httpx

# Configurazione del logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("agent_framework")

# Carica le configurazioni da .env se disponibile
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    logger.warning("dotenv non installato. Le variabili d'ambiente devono essere impostate manualmente.")

# URL base del server MCP (MODEL CONTEXT PROTOCOL)
MCP_BASE_URL = os.getenv("MCP_URL", "http://localhost:8000")


class AgentTool:
    """
    Rappresenta uno strumento che un agente può utilizzare.
    Uno strumento è essenzialmente un wrapper attorno a una chiamata API al server MCP (MODEL CONTEXT PROTOCOL).
    """

    def __init__(self, name: str, description: str, endpoint: str, method: str = "POST"):
        """
        Inizializza un nuovo strumento.

        Args:
            name: Nome dello strumento
            description: Descrizione dello strumento
            endpoint: Endpoint API del server MCP (MODEL CONTEXT PROTOCOL) (senza il base URL)
            method: Metodo HTTP da utilizzare (default: POST)
        """
        self.name = name
        self.description = description
        self.endpoint = endpoint
        self.method = method

    async def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Esegue lo strumento chiamando l'endpoint API corrispondente.

        Args:
            params: Parametri da passare all'endpoint

        Returns:
            Risposta dell'endpoint
        """
        url = f"{MCP_BASE_URL}{self.endpoint}"

        async with httpx.AsyncClient() as client:
            if self.method == "GET":
                response = await client.get(url, params=params)
            else:
                response = await client.post(url, json=params)

            if response.status_code >= 400:
                logger.error(f"Errore nell'esecuzione dello strumento {self.name}: {response.text}")
                raise Exception(f"Errore nell'esecuzione dello strumento {self.name}: {response.status_code}")

            return response.json()


class Agent(ABC):
    """
    Classe base astratta per tutti gli agenti.
    Gli agenti concreti devono estendere questa classe e implementare il metodo run().
    """

    def __init__(self, name: str, description: str, tools: List[AgentTool] = None):
        """
        Inizializza un nuovo agente.

        Args:
            name: Nome dell'agente
            description: Descrizione dell'agente
            tools: Lista di strumenti disponibili per l'agente
        """
        self.name = name
        self.description = description
        self.tools = tools or []
        self.state = {
            "status": "idle",
            "progress": 0,
            "start_time": None,
            "end_time": None,
            "error": None,
            "result": None
        }
        self.id = str(uuid.uuid4())

    def add_tool(self, tool: AgentTool) -> None:
        """
        Aggiunge uno strumento all'agente.

        Args:
            tool: Strumento da aggiungere
        """
        self.tools.append(tool)

    def get_tool_by_name(self, name: str) -> Optional[AgentTool]:
        """
        Ottiene uno strumento per nome.

        Args:
            name: Nome dello strumento

        Returns:
            Strumento corrispondente o None se non trovato
        """
        for tool in self.tools:
            if tool.name == name:
                return tool
        return None

    def update_state(self, **kwargs) -> None:
        """
        Aggiorna lo stato dell'agente.

        Args:
            **kwargs: Coppie chiave-valore da aggiornare nello stato
        """
        self.state.update(kwargs)

    def get_state(self) -> Dict[str, Any]:
        """
        Ottiene lo stato corrente dell'agente.

        Returns:
            Stato corrente dell'agente
        """
        return self.state

    @abstractmethod
    async def run(self, **kwargs) -> Dict[str, Any]:
        """
        Esegue l'agente.
        Questo metodo deve essere implementato dalle sottoclassi.

        Args:
            **kwargs: Parametri specifici dell'agente

        Returns:
            Risultato dell'esecuzione dell'agente
        """
        pass


class AgentRegistry:
    """
    Registro degli agenti disponibili.
    Questa classe gestisce il caricamento e l'accesso agli agenti.
    """

    def __init__(self):
        """
        Inizializza un nuovo registro degli agenti.
        """
        self.agents = {}

    def register(self, agent_class: type) -> None:
        """
        Registra una classe di agente.

        Args:
            agent_class: Classe dell'agente da registrare
        """
        # Crea un'istanza temporanea per ottenere il nome
        temp_instance = agent_class(name="temp", description="temp")
        self.agents[temp_instance.name] = agent_class

    def get_agent_class(self, name: str) -> Optional[type]:
        """
        Ottiene una classe di agente per nome.

        Args:
            name: Nome dell'agente

        Returns:
            Classe dell'agente o None se non trovata
        """
        return self.agents.get(name)

    def create_agent(self, name: str, **kwargs) -> Optional[Agent]:
        """
        Crea un'istanza di un agente.

        Args:
            name: Nome dell'agente
            **kwargs: Parametri da passare al costruttore dell'agente

        Returns:
            Istanza dell'agente o None se la classe non è registrata
        """
        agent_class = self.get_agent_class(name)
        if agent_class:
            return agent_class(**kwargs)
        return None

    def list_agents(self) -> List[Dict[str, str]]:
        """
        Elenca tutti gli agenti registrati.

        Returns:
            Lista di dizionari con nome e descrizione degli agenti
        """
        result = []
        for name, agent_class in self.agents.items():
            # Crea un'istanza temporanea per ottenere la descrizione
            temp_instance = agent_class(name=name, description="")
            result.append({
                "name": name,
                "description": temp_instance.description
            })
        return result


class AgentExecutor:
    """
    Esecutore di agenti.
    Questa classe gestisce l'esecuzione degli agenti e il tracciamento del loro stato.
    """

    def __init__(self, registry: AgentRegistry):
        """
        Inizializza un nuovo esecutore di agenti.

        Args:
            registry: Registro degli agenti
        """
        self.registry = registry
        self.running_agents = {}
        self.completed_agents = {}

    async def run_agent(self, agent_name: str, **kwargs) -> str:
        """
        Esegue un agente in modo asincrono.

        Args:
            agent_name: Nome dell'agente da eseguire
            **kwargs: Parametri da passare all'agente

        Returns:
            ID dell'esecuzione dell'agente
        """
        agent = self.registry.create_agent(agent_name, name=agent_name, description="")
        if not agent:
            raise ValueError(f"Agente non trovato: {agent_name}")

        agent.update_state(status="running", start_time=datetime.now().isoformat())
        self.running_agents[agent.id] = agent

        # Esegui l'agente in un task separato
        asyncio.create_task(self._execute_agent(agent, **kwargs))

        return agent.id

    async def _execute_agent(self, agent: Agent, **kwargs) -> None:
        """
        Esegue un agente e gestisce il suo stato.

        Args:
            agent: Istanza dell'agente da eseguire
            **kwargs: Parametri da passare all'agente
        """
        try:
            result = await agent.run(**kwargs)
            agent.update_state(
                status="completed",
                end_time=datetime.now().isoformat(),
                result=result
            )
        except Exception as e:
            logger.exception(f"Errore nell'esecuzione dell'agente {agent.name}")
            agent.update_state(
                status="error",
                end_time=datetime.now().isoformat(),
                error=str(e)
            )

        # Sposta l'agente dalla lista dei running a quella dei completed
        self.completed_agents[agent.id] = agent
        del self.running_agents[agent.id]

    def get_agent_state(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """
        Ottiene lo stato di un agente.

        Args:
            agent_id: ID dell'agente

        Returns:
            Stato dell'agente o None se non trovato
        """
        agent = self.running_agents.get(agent_id) or self.completed_agents.get(agent_id)
        if agent:
            return agent.get_state()
        return None

    def get_agent_result(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """
        Ottiene il risultato di un agente completato.

        Args:
            agent_id: ID dell'agente

        Returns:
            Risultato dell'agente o None se non trovato o non completato
        """
        agent = self.completed_agents.get(agent_id)
        if agent and agent.state["status"] == "completed":
            return agent.state["result"]
        return None


# Crea un'istanza globale del registro degli agenti
agent_registry = AgentRegistry()

# Crea un'istanza globale dell'esecutore degli agenti
agent_executor = AgentExecutor(agent_registry)
