@echo off
setlocal enabledelayedexpansion
echo ===================================
echo Avvio definitivo dell'applicazione app-roberto
echo con ambiente virtuale pulito
echo ===================================
echo.

REM Definisci il nome dell'ambiente pulito
set CLEAN_ENV=clean_env

REM Verifica se l'ambiente pulito esiste
if not exist %CLEAN_ENV% (
    echo L'ambiente %CLEAN_ENV% non esiste.
    echo Creazione di un nuovo ambiente virtuale pulito...
    echo.
    
    REM Crea il nuovo ambiente virtuale
    python -m venv %CLEAN_ENV%
    if %errorlevel% neq 0 (
        echo ERRORE: Impossibile creare il nuovo ambiente virtuale.
        goto :EOF
    )
    echo Nuovo ambiente virtuale creato con successo.
    echo.
    
    REM Attiva il nuovo ambiente
    echo Attivazione del nuovo ambiente...
    call %CLEAN_ENV%\Scripts\activate
    if %errorlevel% neq 0 (
        echo ERRORE: Impossibile attivare il nuovo ambiente.
        goto :EOF
    )
    echo Ambiente attivato.
    echo.
    
    REM Aggiorna pip all'ultima versione
    echo Aggiornamento di pip...
    python -m pip install --upgrade pip
    echo.
    
    REM Installa le dipendenze di base direttamente
    echo Installazione delle dipendenze di base...
    pip install python-dotenv fastapi flask pandas cachelib
    echo.
    
    REM Installa plotly con un approccio diverso
    echo Installazione di plotly con approccio alternativo...
    pip install --no-deps plotly==5.16.1
    pip install tenacity packaging
    echo.
    
    REM Verifica l'installazione di plotly
    echo Verifica dell'installazione di plotly...
    python -c "import plotly; print(f'Plotly {plotly.__version__} installato correttamente')"
    if %errorlevel% neq 0 (
        echo ERRORE: Verifica di plotly fallita.
    ) else (
        echo Plotly verificato con successo.
    )
    echo.
    
    REM Installa le altre dipendenze dal requirements.txt
    echo Installazione delle altre dipendenze...
    pip install -r requirements.txt
    echo.
    
    echo Ambiente virtuale pulito creato e configurato con successo.
    echo.
) else (
    REM Attiva l'ambiente esistente
    echo Attivazione dell'ambiente virtuale esistente...
    call %CLEAN_ENV%\Scripts\activate
    echo Ambiente attivato.
    echo.
    
    REM Verifica se cachelib è installato
    echo Verifica delle dipendenze necessarie...
    pip show cachelib > nul 2>&1
    if %errorlevel% neq 0 (
        echo Installazione di cachelib (necessario per Flask-Session)...
        pip install cachelib
        echo Cachelib installato.
        echo.
    ) else (
        echo Cachelib già installato.
        echo.
    )
    
    REM Verifica se plotly è installato correttamente
    echo Verifica dell'installazione di plotly...
    python -c "import plotly" > nul 2>&1
    if %errorlevel% neq 0 (
        echo ATTENZIONE: Plotly non è installato correttamente.
        echo Reinstallazione di plotly...
        pip install --no-deps plotly==5.16.1
        pip install tenacity packaging
        echo.
    ) else (
        echo Plotly è installato correttamente.
        echo.
    )
)

REM Imposta le variabili d'ambiente
echo Impostazione delle variabili d'ambiente...
set OPENROUTER_API_KEY=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
set APP_URL=http://localhost:5000
set MCP_URL=http://localhost:8000

REM Leggi anche le variabili dal file .env se esiste
if exist .env (
    echo Lettura delle variabili d'ambiente dal file .env...
    for /f "tokens=*" %%a in (.env) do (
        set "line=%%a"
        if not "!line:~0,1!"=="#" (
            if not "!line!"=="" (
                set "%%a"
            )
        )
    )
)

echo Variabili d'ambiente impostate:
echo OPENROUTER_API_KEY: %OPENROUTER_API_KEY%
echo APP_URL: %APP_URL%
echo MCP_URL: %MCP_URL%
echo.

REM Crea la cartella uploads se non esiste
if not exist uploads (
    echo Creazione della cartella uploads...
    mkdir uploads
    echo Cartella uploads creata.
    echo.
)

REM Avvia il server MCP in una nuova finestra
echo Avvio del server MCP...
start "MCP Server" cmd /k "call %CLEAN_ENV%\Scripts\activate && cd mcp_server && python run_server.py"
echo Server MCP avviato in una nuova finestra.
echo.

REM Attendi 5 secondi per dare tempo al server MCP di avviarsi
echo Attesa di 5 secondi per l'avvio del server MCP...
ping 127.0.0.1 -n 6 > nul
echo.

REM Avvia l'applicazione principale
echo Avvio dell'applicazione principale...
start "App Roberto" cmd /k "call %CLEAN_ENV%\Scripts\activate && python app.py"
echo Applicazione principale avviata in una nuova finestra.
echo.

echo ===================================
echo Applicazione avviata con successo!
echo - Flask: http://localhost:5000
echo - MCP: http://localhost:8000
echo ===================================
echo.
echo NOTA: Il messaggio "WARNING:root:Librerie COM non disponibili. Alcune funzionalità Excel potrebbero non funzionare"
echo è solo un avviso e non un errore critico. L'applicazione dovrebbe funzionare correttamente.
echo.
echo Premi un tasto per chiudere questa finestra...
pause > nul
