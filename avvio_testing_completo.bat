@echo off
echo.
echo ========================================
echo 🧪 SISTEMA TESTING COMPLETO APP-ROBERTO
echo ========================================
echo.
echo 🎯 OBIETTIVO: Completare debugging per lavoro produttivo
echo 📋 TESTING: Tutti endpoint, funzionalità, UI, workflow
echo ⚡ MODALITÀ: Automatizzata e completa
echo.

REM Verifica se Python è disponibile
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERRORE: Python non trovato
    echo 🔧 Installa Python e riprova
    pause
    exit /b 1
)

REM Verifica se le dipendenze sono installate
echo 📦 Verificando dipendenze...
python -c "import aiohttp, requests" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Installando dipendenze mancanti...
    pip install aiohttp requests
)

REM Crea directory per i report se non esiste
if not exist "reports" mkdir reports

REM Backup dei log precedenti
if exist "TESTING_REPORT_FINALE.md" (
    echo 📁 Backup report precedente...
    move "TESTING_REPORT_FINALE.md" "reports\TESTING_REPORT_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%.md" >nul 2>&1
)

echo.
echo 🚀 AVVIO TESTING SUITE COMPLETA...
echo ⏱️ Tempo stimato: 2-3 minuti
echo 📊 Endpoint da testare: 50+
echo 🎯 Funzionalità da testare: 8
echo.

REM Esegui il testing completo
python sistema_testing_completo.py

REM Verifica se il report è stato generato
if exist "TESTING_REPORT_FINALE.md" (
    echo.
    echo ✅ TESTING COMPLETATO CON SUCCESSO!
    echo 📄 Report generato: TESTING_REPORT_FINALE.md
    echo.
    echo 📋 Vuoi aprire il report? (S/N)
    set /p choice=
    if /i "%choice%"=="S" (
        start notepad "TESTING_REPORT_FINALE.md"
    )
) else (
    echo.
    echo ❌ TESTING FALLITO - Report non generato
    echo 🔧 Controlla i log per errori
)

echo.
echo 🎯 PROSSIMI PASSI:
echo 1. Controlla il report per errori critici
echo 2. Risolvi eventuali problemi identificati
echo 3. Procedi con il lavoro produttivo sul database
echo.
pause
