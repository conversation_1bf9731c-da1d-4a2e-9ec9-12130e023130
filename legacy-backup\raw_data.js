/**
 * Script per la gestione della visualizzazione dei dati grezzi
 * Versione: 2.0.0 - Supporto per dati elaborati dall'MCP e miglioramenti UI
 */

// Configurazione globale
const config = {
    pageSize: 10,
    currentPage: 1,
    totalPages: 1,
    sortColumn: null,
    sortDirection: 'asc',
    filters: {},
    dataSource: 'raw', // 'raw' o 'processed'
    highlightAnomalies: true
};

// Cache per i dati
const cache = {
    rawData: null,
    filteredData: null,
    processedData: null,
    metadata: null
};

// Elementi DOM
let dataTable, pagination, pageSize, columnFilter, operatorFilter, valueFilter;
let applyFilterBtn, resetFilterBtn;
let exportExcelBtn, exportCsvBtn, exportJsonBtn, exportPdfBtn;
let dataSourceSelector, highlightAnomaliesToggle, dataSourceInfo;

/**
 * Inizializza la pagina dei dati grezzi
 */
function initRawDataPage() {
    console.log('Inizializzazione pagina dati grezzi...');

    // Ottieni riferimenti agli elementi DOM
    dataTable = document.getElementById('data-table');
    pagination = document.getElementById('pagination');
    pageSize = document.getElementById('page-size');
    columnFilter = document.getElementById('column-filter');
    operatorFilter = document.getElementById('operator-filter');
    valueFilter = document.getElementById('value-filter');
    applyFilterBtn = document.getElementById('apply-column-filter');
    resetFilterBtn = document.getElementById('reset-filters');
    exportExcelBtn = document.getElementById('export-excel');
    exportCsvBtn = document.getElementById('export-csv');
    exportJsonBtn = document.getElementById('export-json');
    exportPdfBtn = document.getElementById('export-pdf');
    dataSourceSelector = document.getElementById('data-source');
    highlightAnomaliesToggle = document.getElementById('highlight-anomalies');
    dataSourceInfo = document.getElementById('data-source-info');

    // Aggiungi event listeners
    if (pageSize) {
        pageSize.addEventListener('change', handlePageSizeChange);
    }

    if (applyFilterBtn) {
        applyFilterBtn.addEventListener('click', applyFilters);
    }

    if (resetFilterBtn) {
        resetFilterBtn.addEventListener('click', resetFilters);
    }

    if (exportExcelBtn) {
        exportExcelBtn.addEventListener('click', () => exportData('excel'));
    }

    if (exportCsvBtn) {
        exportCsvBtn.addEventListener('click', () => exportData('csv'));
    }

    if (exportJsonBtn) {
        exportJsonBtn.addEventListener('click', () => exportData('json'));
    }

    if (exportPdfBtn) {
        exportPdfBtn.addEventListener('click', () => exportData('pdf'));
    }

    // Aggiungi event listeners per il selettore di origine dati
    if (dataSourceSelector) {
        dataSourceSelector.addEventListener('change', handleDataSourceChange);
    }

    // Aggiungi event listeners per il toggle di evidenziazione anomalie
    if (highlightAnomaliesToggle) {
        highlightAnomaliesToggle.addEventListener('change', handleHighlightAnomaliesToggle);
    }

    // Aggiungi event listeners per l'ordinamento
    const sortableHeaders = document.querySelectorAll('.sortable');
    sortableHeaders.forEach(header => {
        header.addEventListener('click', () => {
            const column = header.getAttribute('data-column');
            handleSort(column);
        });
    });

    // Carica i dati grezzi
    loadData();

    // Carica anche i dati elaborati se disponibili
    loadProcessedData();
}

/**
 * Carica i dati grezzi dal server
 */
async function loadData() {
    try {
        console.log('Caricamento dati grezzi dal server...');

        // Aggiungi un parametro casuale per evitare problemi di cache
        const nocache = Date.now() + Math.random().toString(36).substring(2, 15);
        const response = await fetch(`/api/raw_data?nocache=${nocache}`);

        if (!response.ok) {
            throw new Error(`Errore HTTP: ${response.status}`);
        }

        const data = await response.json();

        if (data.error) {
            showError(data.error);
            return;
        }

        cache.rawData = data;

        // Se la fonte dati corrente è 'raw', aggiorna i dati filtrati
        if (config.dataSource === 'raw') {
            cache.filteredData = [...data];

            // Imposta il numero totale di pagine
            config.totalPages = Math.ceil(cache.filteredData.length / config.pageSize);

            // Renderizza i dati
            renderData();
        }

    } catch (error) {
        console.error('Errore nel caricamento dei dati grezzi:', error);
        showError('Errore nel caricamento dei dati grezzi. Riprova più tardi.');
    }
}

/**
 * Carica i dati elaborati dal server MCP
 */
async function loadProcessedData() {
    try {
        console.log('Caricamento dati elaborati dal server MCP...');

        // Aggiungi un parametro casuale per evitare problemi di cache
        const nocache = Date.now() + Math.random().toString(36).substring(2, 15);
        const response = await fetch(`/api/processed_data?nocache=${nocache}`);

        if (!response.ok) {
            throw new Error(`Errore HTTP: ${response.status}`);
        }

        const result = await response.json();

        if (result.error) {
            console.warn('Dati elaborati non disponibili:', result.error);

            // Abilita/disabilita il selettore di origine dati
            if (dataSourceSelector) {
                const option = dataSourceSelector.querySelector('option[value="processed"]');
                if (option) {
                    option.disabled = true;
                    option.textContent = 'Dati elaborati (non disponibili)';
                }
            }

            // Aggiorna l'informazione sulla fonte dati
            updateDataSourceInfo('raw', 'Dati elaborati non disponibili');

            return;
        }

        // Salva i dati elaborati e i metadati
        cache.processedData = result.data || [];
        cache.metadata = result.metadata || {};

        // Abilita il selettore di origine dati
        if (dataSourceSelector) {
            const option = dataSourceSelector.querySelector('option[value="processed"]');
            if (option) {
                option.disabled = false;
                option.textContent = 'Dati elaborati (MCP)';
            }
        }

        // Se la fonte dati corrente è 'processed', aggiorna i dati filtrati
        if (config.dataSource === 'processed') {
            cache.filteredData = [...cache.processedData];

            // Imposta il numero totale di pagine
            config.totalPages = Math.ceil(cache.filteredData.length / config.pageSize);

            // Renderizza i dati
            renderData();
        }

        // Aggiorna l'informazione sulla fonte dati
        updateDataSourceInfo(config.dataSource);

    } catch (error) {
        console.error('Errore nel caricamento dei dati elaborati:', error);

        // Abilita/disabilita il selettore di origine dati
        if (dataSourceSelector) {
            const option = dataSourceSelector.querySelector('option[value="processed"]');
            if (option) {
                option.disabled = true;
                option.textContent = 'Dati elaborati (errore)';
            }
        }

        // Se la fonte dati corrente è 'processed', passa a 'raw'
        if (config.dataSource === 'processed') {
            config.dataSource = 'raw';
            if (dataSourceSelector) {
                dataSourceSelector.value = 'raw';
            }

            // Aggiorna i dati filtrati
            if (cache.rawData) {
                cache.filteredData = [...cache.rawData];

                // Imposta il numero totale di pagine
                config.totalPages = Math.ceil(cache.filteredData.length / config.pageSize);

                // Renderizza i dati
                renderData();
            }
        }

        // Aggiorna l'informazione sulla fonte dati
        updateDataSourceInfo('raw', 'Errore nel caricamento dei dati elaborati');
    }
}

/**
 * Gestisce il cambio di origine dati
 */
function handleDataSourceChange() {
    const newSource = dataSourceSelector.value;

    // Se la fonte dati è cambiata
    if (newSource !== config.dataSource) {
        config.dataSource = newSource;

        // Aggiorna i dati filtrati in base alla fonte dati
        if (newSource === 'raw' && cache.rawData) {
            cache.filteredData = [...cache.rawData];
        } else if (newSource === 'processed' && cache.processedData) {
            cache.filteredData = [...cache.processedData];
        }

        // Resetta la paginazione
        config.currentPage = 1;
        config.totalPages = Math.ceil(cache.filteredData.length / config.pageSize);

        // Mostra o nascondi la legenda delle anomalie
        const anomalyLegend = document.getElementById('anomaly-legend');
        if (anomalyLegend) {
            if (config.highlightAnomalies && newSource === 'processed') {
                anomalyLegend.style.display = 'block';
            } else {
                anomalyLegend.style.display = 'none';
            }
        }

        // Renderizza i dati
        renderData();

        // Aggiorna l'informazione sulla fonte dati
        updateDataSourceInfo(newSource);
    }
}

/**
 * Gestisce il toggle di evidenziazione anomalie
 */
function handleHighlightAnomaliesToggle() {
    config.highlightAnomalies = highlightAnomaliesToggle.checked;

    // Mostra o nascondi la legenda delle anomalie
    const anomalyLegend = document.getElementById('anomaly-legend');
    if (anomalyLegend) {
        if (config.highlightAnomalies && config.dataSource === 'processed') {
            anomalyLegend.style.display = 'block';
        } else {
            anomalyLegend.style.display = 'none';
        }
    }

    // Renderizza i dati per applicare o rimuovere l'evidenziazione
    renderData();
}

/**
 * Aggiorna l'informazione sulla fonte dati
 */
function updateDataSourceInfo(source, message = null) {
    if (!dataSourceInfo) return;

    let html = '';

    if (source === 'raw') {
        html = '<i class="fas fa-info-circle me-2"></i>Visualizzazione dati grezzi';
        if (message) {
            html += ` <span class="text-muted">- ${message}</span>`;
        }
    } else if (source === 'processed') {
        html = '<i class="fas fa-check-circle me-2 text-success"></i>Visualizzazione dati elaborati da MCP';

        // Aggiungi informazioni sui metadati se disponibili
        if (cache.metadata) {
            const totalRows = cache.metadata.total_rows || 0;
            const fileType = cache.metadata.file_type || 'sconosciuto';

            html += ` <span class="text-muted">- Tipo: ${fileType}, Righe totali: ${totalRows}</span>`;
        }

        if (message) {
            html += ` <span class="text-muted">- ${message}</span>`;
        }
    }

    dataSourceInfo.innerHTML = html;
}

/**
 * Renderizza i dati nella tabella
 */
function renderData() {
    if (!cache.filteredData || !dataTable) return;

    const tbody = dataTable.querySelector('tbody');
    tbody.innerHTML = '';

    // Calcola l'indice di inizio e fine per la paginazione
    const startIndex = (config.currentPage - 1) * config.pageSize;
    const endIndex = Math.min(startIndex + config.pageSize, cache.filteredData.length);

    // Se non ci sono dati, mostra un messaggio
    if (cache.filteredData.length === 0) {
        const row = document.createElement('tr');
        const cell = document.createElement('td');
        cell.colSpan = dataTable.querySelectorAll('th').length;
        cell.className = 'text-center';
        cell.innerHTML = 'Nessun dato trovato.';
        row.appendChild(cell);
        tbody.appendChild(row);

        // Aggiorna la paginazione
        renderPagination();
        return;
    }

    // Ottieni le anomalie se disponibili e se l'evidenziazione è attiva
    const anomalies = getAnomalies();

    // Renderizza i dati per la pagina corrente
    for (let i = startIndex; i < endIndex; i++) {
        const row = document.createElement('tr');
        const item = cache.filteredData[i];

        // Crea una cella per ogni colonna
        for (const column of Object.keys(item)) {
            const cell = document.createElement('td');
            const value = item[column] !== null && item[column] !== undefined ? item[column] : '';
            cell.textContent = value;

            // Applica l'evidenziazione se necessario
            if (config.highlightAnomalies && anomalies && anomalies[i] && anomalies[i][column]) {
                const anomaly = anomalies[i][column];
                cell.classList.add('anomaly');

                // Aggiungi una classe specifica in base al tipo di anomalia
                if (anomaly.type === 'missing') {
                    cell.classList.add('anomaly-missing');
                } else if (anomaly.type === 'format') {
                    cell.classList.add('anomaly-format');
                } else if (anomaly.type === 'outlier') {
                    cell.classList.add('anomaly-outlier');
                }

                // Aggiungi un tooltip con la descrizione dell'anomalia
                cell.setAttribute('data-bs-toggle', 'tooltip');
                cell.setAttribute('data-bs-placement', 'top');
                cell.setAttribute('title', anomaly.description || 'Anomalia rilevata');

                // Aggiungi un'icona per indicare l'anomalia
                const icon = document.createElement('i');
                icon.className = 'fas fa-exclamation-circle text-warning ms-1';
                cell.appendChild(icon);
            }

            row.appendChild(cell);
        }

        tbody.appendChild(row);
    }

    // Inizializza i tooltip di Bootstrap
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltips.forEach(tooltip => {
            new bootstrap.Tooltip(tooltip);
        });
    }

    // Aggiorna la paginazione
    renderPagination();
}

/**
 * Ottiene le anomalie dai metadati
 */
function getAnomalies() {
    // Se non siamo in modalità dati elaborati o non ci sono metadati, restituisci null
    if (config.dataSource !== 'processed' || !cache.metadata || !cache.metadata.statistics) {
        return null;
    }

    // Ottieni le anomalie dai metadati
    const statistics = cache.metadata.statistics;

    // Verifica se ci sono anomalie
    if (!statistics.anomalies) {
        return null;
    }

    // Crea un oggetto con le anomalie indicizzate per riga e colonna
    const anomalies = {};

    // Elabora le anomalie di valori mancanti
    if (statistics.anomalies.missing_values) {
        statistics.anomalies.missing_values.forEach(anomaly => {
            const rowIndex = anomaly.row_index;
            const column = anomaly.column;

            if (!anomalies[rowIndex]) {
                anomalies[rowIndex] = {};
            }

            anomalies[rowIndex][column] = {
                type: 'missing',
                description: 'Valore mancante'
            };
        });
    }

    // Elabora le anomalie di formato
    if (statistics.anomalies.format_issues) {
        statistics.anomalies.format_issues.forEach(anomaly => {
            const rowIndex = anomaly.row_index;
            const column = anomaly.column;
            const expected = anomaly.expected_format;
            const actual = anomaly.actual_value;

            if (!anomalies[rowIndex]) {
                anomalies[rowIndex] = {};
            }

            anomalies[rowIndex][column] = {
                type: 'format',
                description: `Formato non valido. Atteso: ${expected}, Trovato: ${actual}`
            };
        });
    }

    // Elabora gli outlier
    if (statistics.anomalies.outliers) {
        statistics.anomalies.outliers.forEach(anomaly => {
            const rowIndex = anomaly.row_index;
            const column = anomaly.column;
            const value = anomaly.value;
            const threshold = anomaly.threshold;

            if (!anomalies[rowIndex]) {
                anomalies[rowIndex] = {};
            }

            anomalies[rowIndex][column] = {
                type: 'outlier',
                description: `Valore anomalo: ${value} (soglia: ${threshold})`
            };
        });
    }

    return anomalies;
}

/**
 * Renderizza la paginazione
 */
function renderPagination() {
    if (!pagination) return;

    pagination.innerHTML = '';

    // Se ci sono meno di 2 pagine, non mostrare la paginazione
    if (config.totalPages < 2) return;

    // Pulsante "Precedente"
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${config.currentPage === 1 ? 'disabled' : ''}`;
    const prevLink = document.createElement('a');
    prevLink.className = 'page-link';
    prevLink.href = '#';
    prevLink.innerHTML = '&laquo;';
    prevLink.setAttribute('aria-label', 'Precedente');
    prevLink.addEventListener('click', (e) => {
        e.preventDefault();
        if (config.currentPage > 1) {
            config.currentPage--;
            renderData();
        }
    });
    prevLi.appendChild(prevLink);
    pagination.appendChild(prevLi);

    // Pagine
    const maxVisiblePages = 5;
    let startPage = Math.max(1, config.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(config.totalPages, startPage + maxVisiblePages - 1);

    // Aggiusta startPage se necessario
    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // Prima pagina (se non è già inclusa)
    if (startPage > 1) {
        const firstLi = document.createElement('li');
        firstLi.className = 'page-item';
        const firstLink = document.createElement('a');
        firstLink.className = 'page-link';
        firstLink.href = '#';
        firstLink.textContent = '1';
        firstLink.addEventListener('click', (e) => {
            e.preventDefault();
            config.currentPage = 1;
            renderData();
        });
        firstLi.appendChild(firstLink);
        pagination.appendChild(firstLi);

        // Ellipsis se necessario
        if (startPage > 2) {
            const ellipsisLi = document.createElement('li');
            ellipsisLi.className = 'page-item disabled';
            const ellipsisSpan = document.createElement('span');
            ellipsisSpan.className = 'page-link';
            ellipsisSpan.innerHTML = '&hellip;';
            ellipsisLi.appendChild(ellipsisSpan);
            pagination.appendChild(ellipsisLi);
        }
    }

    // Pagine visibili
    for (let i = startPage; i <= endPage; i++) {
        const pageLi = document.createElement('li');
        pageLi.className = `page-item ${i === config.currentPage ? 'active' : ''}`;
        const pageLink = document.createElement('a');
        pageLink.className = 'page-link';
        pageLink.href = '#';
        pageLink.textContent = i;
        pageLink.addEventListener('click', (e) => {
            e.preventDefault();
            config.currentPage = i;
            renderData();
        });
        pageLi.appendChild(pageLink);
        pagination.appendChild(pageLi);
    }

    // Ultima pagina (se non è già inclusa)
    if (endPage < config.totalPages) {
        // Ellipsis se necessario
        if (endPage < config.totalPages - 1) {
            const ellipsisLi = document.createElement('li');
            ellipsisLi.className = 'page-item disabled';
            const ellipsisSpan = document.createElement('span');
            ellipsisSpan.className = 'page-link';
            ellipsisSpan.innerHTML = '&hellip;';
            ellipsisLi.appendChild(ellipsisSpan);
            pagination.appendChild(ellipsisLi);
        }

        const lastLi = document.createElement('li');
        lastLi.className = 'page-item';
        const lastLink = document.createElement('a');
        lastLink.className = 'page-link';
        lastLink.href = '#';
        lastLink.textContent = config.totalPages;
        lastLink.addEventListener('click', (e) => {
            e.preventDefault();
            config.currentPage = config.totalPages;
            renderData();
        });
        lastLi.appendChild(lastLink);
        pagination.appendChild(lastLi);
    }

    // Pulsante "Successivo"
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${config.currentPage === config.totalPages ? 'disabled' : ''}`;
    const nextLink = document.createElement('a');
    nextLink.className = 'page-link';
    nextLink.href = '#';
    nextLink.innerHTML = '&raquo;';
    nextLink.setAttribute('aria-label', 'Successivo');
    nextLink.addEventListener('click', (e) => {
        e.preventDefault();
        if (config.currentPage < config.totalPages) {
            config.currentPage++;
            renderData();
        }
    });
    nextLi.appendChild(nextLink);
    pagination.appendChild(nextLi);
}

/**
 * Gestisce il cambio di dimensione della pagina
 */
function handlePageSizeChange() {
    config.pageSize = parseInt(pageSize.value, 10);
    config.totalPages = Math.ceil(cache.filteredData.length / config.pageSize);
    config.currentPage = 1;
    renderData();
}

/**
 * Gestisce l'ordinamento delle colonne
 */
function handleSort(column) {
    // Se la colonna è già ordinata, inverti la direzione
    if (config.sortColumn === column) {
        config.sortDirection = config.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        config.sortColumn = column;
        config.sortDirection = 'asc';
    }

    // Aggiorna le icone di ordinamento
    updateSortIcons(column);

    // Ordina i dati
    sortData();

    // Renderizza i dati ordinati
    renderData();
}

/**
 * Aggiorna le icone di ordinamento
 */
function updateSortIcons(column) {
    const headers = document.querySelectorAll('.sortable');

    headers.forEach(header => {
        const headerColumn = header.getAttribute('data-column');
        header.classList.remove('sort-asc', 'sort-desc');

        if (headerColumn === column) {
            header.classList.add(config.sortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
        }
    });
}

/**
 * Ordina i dati in base alla colonna e direzione corrente
 */
function sortData() {
    if (!config.sortColumn) return;

    cache.filteredData.sort((a, b) => {
        const valueA = a[config.sortColumn];
        const valueB = b[config.sortColumn];

        // Gestisci valori null o undefined
        if (valueA === null || valueA === undefined) return config.sortDirection === 'asc' ? -1 : 1;
        if (valueB === null || valueB === undefined) return config.sortDirection === 'asc' ? 1 : -1;

        // Confronta numeri
        if (!isNaN(valueA) && !isNaN(valueB)) {
            return config.sortDirection === 'asc' ? valueA - valueB : valueB - valueA;
        }

        // Confronta date (se sono date valide)
        const dateA = new Date(valueA);
        const dateB = new Date(valueB);
        if (!isNaN(dateA) && !isNaN(dateB)) {
            return config.sortDirection === 'asc' ? dateA - dateB : dateB - dateA;
        }

        // Confronta stringhe
        const strA = String(valueA).toLowerCase();
        const strB = String(valueB).toLowerCase();

        if (config.sortDirection === 'asc') {
            return strA.localeCompare(strB);
        } else {
            return strB.localeCompare(strA);
        }
    });
}

/**
 * Applica i filtri ai dati
 */
function applyFilters() {
    const column = columnFilter.value;
    const operator = operatorFilter.value;
    const value = valueFilter.value;

    if (!column || !operator || !value) {
        alert('Seleziona una colonna, un operatore e inserisci un valore per filtrare i dati.');
        return;
    }

    // Resetta i dati filtrati
    cache.filteredData = [...cache.rawData];

    // Applica il filtro
    cache.filteredData = cache.filteredData.filter(item => {
        const itemValue = item[column];

        // Gestisci valori null o undefined
        if (itemValue === null || itemValue === undefined) {
            return false;
        }

        const itemStr = String(itemValue).toLowerCase();
        const filterStr = value.toLowerCase();

        switch (operator) {
            case 'contains':
                return itemStr.includes(filterStr);
            case 'equals':
                return itemStr === filterStr;
            case 'starts':
                return itemStr.startsWith(filterStr);
            case 'ends':
                return itemStr.endsWith(filterStr);
            case 'greater':
                // Se è un numero, confronta come numero
                if (!isNaN(itemValue) && !isNaN(value)) {
                    return parseFloat(itemValue) > parseFloat(value);
                }
                // Se è una data, confronta come data
                const itemDate = new Date(itemValue);
                const filterDate = new Date(value);
                if (!isNaN(itemDate) && !isNaN(filterDate)) {
                    return itemDate > filterDate;
                }
                // Altrimenti confronta come stringa
                return itemStr > filterStr;
            case 'less':
                // Se è un numero, confronta come numero
                if (!isNaN(itemValue) && !isNaN(value)) {
                    return parseFloat(itemValue) < parseFloat(value);
                }
                // Se è una data, confronta come data
                const itemDateLess = new Date(itemValue);
                const filterDateLess = new Date(value);
                if (!isNaN(itemDateLess) && !isNaN(filterDateLess)) {
                    return itemDateLess < filterDateLess;
                }
                // Altrimenti confronta come stringa
                return itemStr < filterStr;
            default:
                return true;
        }
    });

    // Aggiorna il numero totale di pagine
    config.totalPages = Math.ceil(cache.filteredData.length / config.pageSize);
    config.currentPage = 1;

    // Renderizza i dati filtrati
    renderData();
}

/**
 * Resetta i filtri
 */
function resetFilters() {
    // Resetta i campi dei filtri
    if (columnFilter) columnFilter.value = '';
    if (operatorFilter) operatorFilter.value = 'contains';
    if (valueFilter) valueFilter.value = '';

    // Resetta i dati filtrati
    cache.filteredData = [...cache.rawData];

    // Aggiorna il numero totale di pagine
    config.totalPages = Math.ceil(cache.filteredData.length / config.pageSize);
    config.currentPage = 1;

    // Renderizza i dati
    renderData();
}

/**
 * Esporta i dati nel formato specificato
 */
function exportData(format) {
    if (!cache.filteredData || cache.filteredData.length === 0) {
        alert('Nessun dato da esportare.');
        return;
    }

    // Preparazione esportazione

    // Crea un form per inviare la richiesta di esportazione
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/export/${format}`;
    form.style.display = 'none';

    // Aggiungi i dati come campo nascosto
    const dataInput = document.createElement('input');
    dataInput.type = 'hidden';
    dataInput.name = 'data';
    dataInput.value = JSON.stringify(cache.filteredData);
    form.appendChild(dataInput);

    // Aggiungi il token CSRF se necessario
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (csrfToken) {
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = csrfToken.getAttribute('content');
        form.appendChild(csrfInput);
    }

    // Aggiungi il form al documento e invialo
    document.body.appendChild(form);

    // Mostra un toast di notifica
    showToast(`Preparazione esportazione in ${format.toUpperCase()}...`, 'info', true);

    // Invia il form dopo un breve ritardo per permettere al toast di essere visualizzato
    setTimeout(() => {
        form.submit();

        // Rimuovi il form dopo l'invio
        setTimeout(() => {
            document.body.removeChild(form);
            showToast(`Esportazione in ${format.toUpperCase()} completata!`, 'success', true);
        }, 500);
    }, 300);
}

/**
 * Mostra un toast di notifica
 * @param {string} message - Il messaggio da mostrare
 * @param {string} type - Il tipo di toast (success, info, warning, error)
 * @param {boolean} autoHide - Se il toast deve scomparire automaticamente
 * @returns {Object} - L'oggetto toast
 */
function showToast(message, type = 'info', autoHide = true) {
    // Crea il container dei toast se non esiste
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // Crea un ID univoco per il toast
    const toastId = 'toast-' + Date.now();

    // Crea il toast
    const toastEl = document.createElement('div');
    toastEl.className = `toast align-items-center text-white bg-${type} border-0`;
    toastEl.id = toastId;
    toastEl.setAttribute('role', 'alert');
    toastEl.setAttribute('aria-live', 'assertive');
    toastEl.setAttribute('aria-atomic', 'true');

    if (autoHide) {
        toastEl.setAttribute('data-bs-delay', '3000');
        toastEl.setAttribute('data-bs-autohide', 'true');
    } else {
        toastEl.setAttribute('data-bs-autohide', 'false');
    }

    // Crea il contenuto del toast
    toastEl.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Chiudi"></button>
        </div>
    `;

    // Aggiungi il toast al container
    toastContainer.appendChild(toastEl);

    // Inizializza il toast con Bootstrap
    const toast = new bootstrap.Toast(toastEl);
    toast.show();

    // Rimuovi il toast dal DOM quando viene nascosto
    toastEl.addEventListener('hidden.bs.toast', () => {
        if (toastContainer.contains(toastEl)) {
            toastContainer.removeChild(toastEl);
        }
        if (toastContainer.children.length === 0 && document.body.contains(toastContainer)) {
            document.body.removeChild(toastContainer);
        }
    });

    // Restituisci l'oggetto toast per poterlo controllare
    return toast;
}

/**
 * Mostra un messaggio di errore
 */
function showError(message) {
    const tbody = dataTable.querySelector('tbody');
    tbody.innerHTML = '';

    const row = document.createElement('tr');
    const cell = document.createElement('td');
    cell.colSpan = dataTable.querySelectorAll('th').length;
    cell.className = 'text-center text-danger';
    cell.innerHTML = `<i class="fas fa-exclamation-circle me-2"></i>${message}`;
    row.appendChild(cell);
    tbody.appendChild(row);
}

// Inizializza la pagina quando il documento è pronto
document.addEventListener('DOMContentLoaded', initRawDataPage);
