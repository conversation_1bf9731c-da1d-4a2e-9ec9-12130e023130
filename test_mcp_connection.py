#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test della connessione al server MCP.
Verifica che il server MCP sia raggiungibile e funzionante.
"""

import requests
import json
import time
from datetime import datetime

def test_mcp_server():
    """Testa la connessione al server MCP."""
    print("🔌 TEST CONNESSIONE SERVER MCP")
    print("=" * 40)

    mcp_url = "http://127.0.0.1:8000"

    # Test 1: Health Check
    print("\n1. 🏥 Test Health Check...")
    try:
        response = requests.get(f"{mcp_url}/", timeout=10)
        print(f"   ✅ Server MCP raggiungibile: {response.status_code}")
        print(f"   📄 Response: {response.text[:100]}...")
    except Exception as e:
        print(f"   ❌ Errore connessione: {str(e)}")
        return False

    # Test 2: Endpoint Info
    print("\n2. ℹ️ Test Endpoint Info...")
    try:
        response = requests.get(f"{mcp_url}/info", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Info endpoint: {response.status_code}")
            print(f"   📊 Server: {data.get('server', 'Unknown')}")
            print(f"   🔢 Version: {data.get('version', 'Unknown')}")
        else:
            print(f"   ⚠️ Info endpoint: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Errore info: {str(e)}")

    # Test 3: Endpoint Docs
    print("\n3. 📚 Test Endpoint Docs...")
    try:
        response = requests.get(f"{mcp_url}/docs", timeout=10)
        print(f"   ✅ Docs endpoint: {response.status_code}")
        if response.status_code == 200:
            print(f"   📄 Docs disponibili ({len(response.content)} bytes)")
    except Exception as e:
        print(f"   ❌ Errore docs: {str(e)}")

    # Test 4: Test dall'app principale
    print("\n4. 🔗 Test Connessione dall'App Principale...")
    try:
        # Importa il client MCP dall'app
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))

        from mcp_client import MCPClient

        # Crea client MCP
        mcp_client = MCPClient(base_url=mcp_url, max_retries=2, timeout=10)

        # Test connessione
        is_connected = mcp_client.test_connection()
        print(f"   {'✅' if is_connected else '❌'} Connessione MCP Client: {is_connected}")

        if is_connected:
            print("   🎉 Server MCP completamente funzionante!")
            return True
        else:
            print("   ⚠️ Problemi di connessione dal client MCP")
            return False

    except Exception as e:
        print(f"   ❌ Errore test client: {str(e)}")
        return False

def test_mcp_endpoints():
    """Testa gli endpoint specifici del server MCP."""
    print("\n" + "=" * 40)
    print("🔧 TEST ENDPOINT SPECIFICI MCP")
    print("=" * 40)

    mcp_url = "http://127.0.0.1:8001"

    endpoints = [
        "/",
        "/info",
        "/docs",
        "/openapi.json",
        "/health"
    ]

    results = {}

    for endpoint in endpoints:
        print(f"\n📍 Test {endpoint}...")
        try:
            response = requests.get(f"{mcp_url}{endpoint}", timeout=10)
            results[endpoint] = {
                'status_code': response.status_code,
                'success': response.status_code == 200,
                'content_length': len(response.content),
                'content_type': response.headers.get('content-type', 'unknown')
            }

            status = "✅" if response.status_code == 200 else "⚠️"
            print(f"   {status} Status: {response.status_code}")
            print(f"   📄 Content: {len(response.content)} bytes")
            print(f"   🏷️ Type: {response.headers.get('content-type', 'unknown')}")

        except Exception as e:
            results[endpoint] = {
                'success': False,
                'error': str(e)
            }
            print(f"   ❌ Errore: {str(e)}")

    # Riepilogo
    print(f"\n📊 RIEPILOGO ENDPOINT:")
    success_count = sum(1 for r in results.values() if r.get('success', False))
    total_count = len(results)

    for endpoint, result in results.items():
        status = "✅" if result.get('success', False) else "❌"
        print(f"   {status} {endpoint}")

    print(f"\n🎯 Risultato: {success_count}/{total_count} endpoint funzionanti")

    return success_count, total_count

def main():
    """Esegue tutti i test MCP."""
    print("🧪 AVVIO TEST COMPLETO SERVER MCP")
    print("=" * 50)

    # Test connessione base
    connection_ok = test_mcp_server()

    # Test endpoint specifici
    success_count, total_count = test_mcp_endpoints()

    # Risultato finale
    print("\n" + "=" * 50)
    print("📋 RISULTATO FINALE")
    print("=" * 50)

    if connection_ok and success_count >= total_count * 0.8:
        print("🎉 SERVER MCP COMPLETAMENTE FUNZIONANTE!")
        print("✅ Connessione OK")
        print(f"✅ Endpoint: {success_count}/{total_count} funzionanti")
        print("\n🔧 Il problema di timeout MCP dovrebbe essere risolto.")
        print("💡 Ora l'app può comunicare correttamente con il server MCP.")
        return True
    elif connection_ok:
        print("⚠️ SERVER MCP PARZIALMENTE FUNZIONANTE")
        print("✅ Connessione OK")
        print(f"⚠️ Endpoint: {success_count}/{total_count} funzionanti")
        print("\n🔧 Alcuni endpoint potrebbero non funzionare correttamente.")
        return False
    else:
        print("❌ SERVER MCP NON FUNZIONANTE")
        print("❌ Connessione FAIL")
        print("\n🔧 Verificare che il server MCP sia avviato sulla porta 8001.")
        print("💡 Comando: cd mcp_server && python run_server.py")
        return False

if __name__ == "__main__":
    success = main()

    # Salva risultati
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f'test_mcp_results_{timestamp}.txt'

    with open(results_file, 'w', encoding='utf-8') as f:
        f.write(f"Test MCP Server - {timestamp}\n")
        f.write(f"Risultato: {'SUCCESS' if success else 'FAIL'}\n")
        f.write(f"Server URL: http://127.0.0.1:8001\n")

    print(f"\n📄 Risultati salvati in: {results_file}")

    exit(0 if success else 1)
