{% extends "base.html" %}

{% block title %}Home - <PERSON><PERSON><PERSON>{% endblock %}
{% block description %}Carica e analizza i tuoi dati aziendali con l'intelligenza artificiale integrata{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow-medium card-interactive">
            <div class="card-header bg-gradient-primary text-white">
                <h1 class="h4 mb-0"><i class="fas fa-file-upload me-2" aria-hidden="true"></i>Importazione Dati</h1>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <h5>Carica i tuoi file per iniziare l'analisi</h5>
                    <p class="text-muted">Formati supportati: CSV (separatore ;) ed Excel (.xlsx, .xls)</p>
                </div>

                <form action="{{ url_for('upload_file', t=timestamp, r=range(1, 1000)|random, nocache=timestamp|string + (range(1, 10000)|random|string)) }}" method="post" enctype="multipart/form-data" id="upload-form" name="upload-form" aria-label="Form di upload file">
                    <div class="mb-4">
                        <div class="upload-area" id="drop-area"
                             aria-label="Area di caricamento file"
                             data-tooltip="Formati supportati: CSV, Excel (.xlsx, .xls). Dimensione massima: 50MB">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt fa-3x text-primary" aria-hidden="true"></i>
                            </div>
                            <p class="upload-text">Trascina qui i tuoi file o <span class="text-primary">sfoglia</span></p>
                            <p class="upload-subtext">Dimensione massima: 50MB</p>
                            <input type="file" name="file" id="file-input" class="d-none"
                                   accept=".csv,.xlsx,.xls"
                                   aria-label="Seleziona file per l'analisi"
                                   aria-describedby="file-help">
                            <div id="file-help" class="visually-hidden">
                                Seleziona un file CSV o Excel per iniziare l'analisi dei dati
                            </div>
                        </div>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-4" id="upload-btn" disabled>
                            <i class="fas fa-upload me-2"></i>Carica File
                        </button>
                        <div class="mt-3">
                            <a href="{{ url_for('clear_session_route') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-broom me-1"></i>Pulisci sessione
                            </a>
                            <small class="d-block text-muted mt-1">Usa questo pulsante se riscontri problemi con i file caricati</small>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        {% if recent_files and recent_files|length > 0 %}
        <div class="card shadow mt-4">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0"><i class="fas fa-history me-2"></i>File Recenti</h4>
            </div>
            <div class="card-body">
                <p class="text-muted mb-3">Seleziona un file recente per continuare l'analisi:</p>
                <div class="list-group mb-3">
                    {% for file_info in recent_files %}
                    <div class="list-group-item d-flex align-items-center">
                        {% if file_info.file_path.endswith('.csv') %}
                        <i class="fas fa-file-alt text-success me-2"></i>
                        {% elif file_info.file_path.endswith(('.xlsx', '.xls')) %}
                        <i class="fas fa-file-alt text-primary me-2"></i>
                        {% else %}
                        <i class="fas fa-file text-secondary me-2"></i>
                        {% endif %}
                        <div class="flex-grow-1">
                            <strong>{{ file_info.filename }}</strong>
                            <small class="d-block text-muted">
                                Tipo: {{ file_info.file_type }} |
                                Caricato: {{ file_info.timestamp|int|datetime('%d/%m/%Y %H:%M') }}
                            </small>
                        </div>
                        <a href="{{ url_for('load_recent_file', index=loop.index0) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-folder-open me-1"></i>Apri
                        </a>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Sezione Configurazioni -->
        <div class="card shadow mt-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-cogs me-2"></i>Configurazioni Sistema</h4>
            </div>
            <div class="card-body">
                <!-- Tab Navigation -->
                <ul class="nav nav-tabs" id="configTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="employees-tab" data-bs-toggle="tab" data-bs-target="#employees" type="button" role="tab">
                            <i class="fas fa-users me-1"></i>Dipendenti
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="vehicles-tab" data-bs-toggle="tab" data-bs-target="#vehicles" type="button" role="tab">
                            <i class="fas fa-car me-1"></i>Veicoli
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="tax-tab" data-bs-toggle="tab" data-bs-target="#tax" type="button" role="tab">
                            <i class="fas fa-percentage me-1"></i>IVA
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content mt-3" id="configTabContent">
                    <!-- Configurazione Dipendenti -->
                    <div class="tab-pane fade show active" id="employees" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-user-plus me-1"></i>Aggiungi/Modifica Dipendente</h6>
                                <form id="employeeForm">
                                    <div class="mb-2">
                                        <input type="text" class="form-control form-control-sm" id="employeeName" placeholder="Nome dipendente" required>
                                    </div>
                                    <div class="mb-2">
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" id="hourlyRate" placeholder="Tariffa oraria" step="0.01" min="0" required>
                                            <span class="input-group-text">€/h</span>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="vatIncluded" id="vatIncludedYes" value="true" checked>
                                            <label class="form-check-label" for="vatIncludedYes">IVA inclusa</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="vatIncluded" id="vatIncludedNo" value="false">
                                            <label class="form-check-label" for="vatIncludedNo">IVA esclusa</label>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <input type="text" class="form-control form-control-sm" id="employeeNotes" placeholder="Note (opzionale)">
                                    </div>
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fas fa-save me-1"></i>Salva
                                    </button>
                                </form>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-list me-1"></i>Dipendenti Configurati</h6>
                                <div id="employeesList" class="list-group config-list">
                                    <!-- Lista dipendenti caricata dinamicamente -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Configurazione Veicoli -->
                    <div class="tab-pane fade" id="vehicles" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-car me-1"></i>Aggiungi/Modifica Veicolo</h6>
                                <form id="vehicleForm">
                                    <div class="mb-2">
                                        <input type="text" class="form-control form-control-sm" id="vehicleName" placeholder="Nome veicolo" required>
                                    </div>
                                    <div class="mb-2">
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" id="fuelConsumption" placeholder="Consumo" step="0.1" min="0" required>
                                            <span class="input-group-text">L/100km</span>
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" id="dailyCost" placeholder="Costo giornaliero" step="0.01" min="0" required>
                                            <span class="input-group-text">€/giorno</span>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fas fa-save me-1"></i>Salva
                                    </button>
                                </form>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-list me-1"></i>Veicoli Configurati</h6>
                                <div id="vehiclesList" class="list-group config-list">
                                    <!-- Lista veicoli caricata dinamicamente -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Configurazione IVA -->
                    <div class="tab-pane fade" id="tax" role="tabpanel">
                        <div class="row">
                            <div class="col-md-8">
                                <h6><i class="fas fa-percentage me-1"></i>Impostazioni IVA</h6>
                                <form id="taxForm">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-2">
                                                <label for="vatRate" class="form-label">Aliquota IVA (%)</label>
                                                <input type="number" class="form-control form-control-sm" id="vatRate" step="0.1" min="0" max="100" value="22">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-2">
                                                <label for="currency" class="form-label">Valuta</label>
                                                <select class="form-select form-select-sm" id="currency">
                                                    <option value="EUR">EUR (€)</option>
                                                    <option value="USD">USD ($)</option>
                                                    <option value="GBP">GBP (£)</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-2">
                                                <label class="form-label">IVA di default</label>
                                                <div>
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="radio" name="defaultVatIncluded" id="defaultVatYes" value="true" checked>
                                                        <label class="form-check-label" for="defaultVatYes">Inclusa</label>
                                                    </div>
                                                    <div class="form-check form-check-inline">
                                                        <input class="form-check-input" type="radio" name="defaultVatIncluded" id="defaultVatNo" value="false">
                                                        <label class="form-check-label" for="defaultVatNo">Esclusa</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fas fa-save me-1"></i>Salva Impostazioni
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .upload-area {
        border: 2px dashed #ccc;
        border-radius: 8px;
        padding: 40px 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s;
    }

    .upload-area:hover, .upload-area.dragover {
        border-color: #0d6efd;
        background-color: rgba(13, 110, 253, 0.05);
    }

    .upload-icon {
        margin-bottom: 15px;
    }

    .upload-text {
        font-size: 1.1rem;
        color: #6c757d;
    }

    .file-info {
        display: none;
        margin-top: 20px;
    }

    /* Stili per le configurazioni */
    .config-list {
        max-height: 200px;
        overflow-y: auto;
    }

    .config-tabs .nav-link {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }

    .config-form .form-control-sm,
    .config-form .form-select-sm {
        font-size: 0.875rem;
    }

    .config-item {
        border-left: 3px solid #007bff;
        margin-bottom: 0.5rem;
    }

    .config-item:hover {
        background-color: #f8f9fa;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Parametro anti-cache: {{ timestamp }}
    document.addEventListener('DOMContentLoaded', function() {
        const uploadForm = document.getElementById('upload-form');
        const dropArea = document.getElementById('drop-area');
        const fileInput = document.getElementById('file-input');
        const uploadBtn = document.getElementById('upload-btn');

        // Apri il selettore di file quando si clicca sull'area di upload
        dropArea.addEventListener('click', function() {
            fileInput.click();
        });

        // Gestisci il drag and drop
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropArea.classList.add('dragover');
        }

        function unhighlight() {
            dropArea.classList.remove('dragover');
        }

        // Gestisci il drop del file
        dropArea.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length > 0) {
                fileInput.files = files;
                updateFileInfo();
            }
        }

        // Gestisci la selezione del file tramite input
        fileInput.addEventListener('change', updateFileInfo);

        function updateFileInfo() {
            if (fileInput.files.length > 0) {
                const fileName = fileInput.files[0].name;
                dropArea.innerHTML = `
                    <div class="upload-icon">
                        <i class="fas fa-file-alt fa-3x text-success"></i>
                    </div>
                    <p class="upload-text">${fileName}</p>
                    <small class="text-muted">Clicca per cambiare file</small>
                    <input type="file" name="file" id="file-input" class="d-none" accept=".csv,.xlsx,.xls" aria-label="Seleziona file">
                `;

                // Riassegna il file all'input dopo aver aggiornato l'HTML
                document.getElementById('file-input').files = fileInput.files;

                uploadBtn.disabled = false;
            } else {
                resetDropArea();
                uploadBtn.disabled = true;
            }
        }

        function resetDropArea() {
            dropArea.innerHTML = `
                <div class="upload-icon">
                    <i class="fas fa-cloud-upload-alt fa-3x text-primary"></i>
                </div>
                <p class="upload-text">Trascina qui i tuoi file o <span class="text-primary">sfoglia</span></p>
                <input type="file" name="file" id="file-input" class="d-none" accept=".csv,.xlsx,.xls" aria-label="Seleziona file">
            `;
        }

        // Verifica prima dell'invio che ci sia un file selezionato
        uploadForm.addEventListener('submit', function(e) {
            const fileInput = document.getElementById('file-input');
            if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
                e.preventDefault();
                alert('Seleziona un file prima di procedere.');
                return false;
            }
            return true;
        });

        // === GESTIONE CONFIGURAZIONI ===

        // Carica configurazioni all'avvio
        loadEmployeesConfig();
        loadVehiclesConfig();
        loadTaxConfig();

        // Form dipendenti
        document.getElementById('employeeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveEmployeeConfig();
        });

        // Form veicoli
        document.getElementById('vehicleForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveVehicleConfig();
        });

        // Form IVA
        document.getElementById('taxForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveTaxConfig();
        });

        function loadEmployeesConfig() {
            fetch('/api/config/employees')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateEmployeesList(data.employees);
                        if (data.tax_settings) {
                            updateTaxForm(data.tax_settings);
                        }
                    }
                })
                .catch(error => console.error('Errore caricamento dipendenti:', error));
        }

        function saveEmployeeConfig() {
            const employeeName = document.getElementById('employeeName').value;
            const hourlyRate = parseFloat(document.getElementById('hourlyRate').value);
            const vatIncluded = document.querySelector('input[name="vatIncluded"]:checked').value === 'true';
            const notes = document.getElementById('employeeNotes').value;

            fetch('/api/config/employees', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    employee_name: employeeName,
                    hourly_rate: hourlyRate,
                    vat_included: vatIncluded,
                    notes: notes
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Configurazione salvata con successo!');
                    document.getElementById('employeeForm').reset();
                    loadEmployeesConfig();
                } else {
                    alert('Errore: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Errore:', error);
                alert('Errore nel salvataggio');
            });
        }

        function updateEmployeesList(employees) {
            const list = document.getElementById('employeesList');
            list.innerHTML = '';

            if (Object.keys(employees).length === 0) {
                list.innerHTML = '<div class="text-muted text-center p-2">Nessun dipendente configurato</div>';
                return;
            }

            for (const [name, config] of Object.entries(employees)) {
                const item = document.createElement('div');
                item.className = 'list-group-item d-flex justify-content-between align-items-center';
                item.innerHTML = `
                    <div>
                        <strong>${name}</strong><br>
                        <small class="text-muted">
                            €${config.hourly_rate}/h - IVA ${config.vat_included ? 'inclusa' : 'esclusa'}
                            ${config.notes ? '<br>' + config.notes : ''}
                        </small>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteEmployee('${name}')">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                list.appendChild(item);
            }
        }

        function deleteEmployee(name) {
            if (confirm(`Rimuovere la configurazione per ${name}?`)) {
                fetch(`/api/config/employees/${encodeURIComponent(name)}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        loadEmployeesConfig();
                    } else {
                        alert('Errore: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Errore:', error);
                    alert('Errore nella rimozione');
                });
            }
        }

        function loadVehiclesConfig() {
            fetch('/api/config/vehicles')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateVehiclesList(data.vehicles.vehicles || {});
                    }
                })
                .catch(error => console.error('Errore caricamento veicoli:', error));
        }

        function saveVehicleConfig() {
            const vehicleName = document.getElementById('vehicleName').value;
            const fuelConsumption = parseFloat(document.getElementById('fuelConsumption').value);
            const dailyCost = parseFloat(document.getElementById('dailyCost').value);

            fetch('/api/config/vehicles', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    vehicle_name: vehicleName,
                    fuel_consumption: fuelConsumption,
                    daily_cost: dailyCost
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Configurazione veicolo salvata!');
                    document.getElementById('vehicleForm').reset();
                    loadVehiclesConfig();
                } else {
                    alert('Errore: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Errore:', error);
                alert('Errore nel salvataggio');
            });
        }

        function updateVehiclesList(vehicles) {
            const list = document.getElementById('vehiclesList');
            list.innerHTML = '';

            if (Object.keys(vehicles).length === 0) {
                list.innerHTML = '<div class="text-muted text-center p-2">Nessun veicolo configurato</div>';
                return;
            }

            for (const [name, config] of Object.entries(vehicles)) {
                const item = document.createElement('div');
                item.className = 'list-group-item d-flex justify-content-between align-items-center';
                item.innerHTML = `
                    <div>
                        <strong>${name}</strong><br>
                        <small class="text-muted">
                            ${config.fuel_consumption}L/100km - €${config.daily_cost}/giorno
                        </small>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteVehicle('${name}')">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                list.appendChild(item);
            }
        }

        function deleteVehicle(name) {
            if (confirm(`Rimuovere la configurazione per ${name}?`)) {
                // Implementa API DELETE per veicoli se necessario
                alert('Funzionalità in sviluppo');
            }
        }

        function loadTaxConfig() {
            // Già caricato con loadEmployeesConfig
        }

        function saveTaxConfig() {
            const vatRate = parseFloat(document.getElementById('vatRate').value);
            const currency = document.getElementById('currency').value;
            const defaultVatIncluded = document.querySelector('input[name="defaultVatIncluded"]:checked').value === 'true';

            fetch('/api/config/tax', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    vat_rate: vatRate,
                    currency: currency,
                    default_vat_included: defaultVatIncluded
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Configurazioni IVA salvate!');
                } else {
                    alert('Errore: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Errore:', error);
                alert('Errore nel salvataggio');
            });
        }

        function updateTaxForm(taxSettings) {
            if (taxSettings.vat_rate) {
                document.getElementById('vatRate').value = taxSettings.vat_rate;
            }
            if (taxSettings.currency) {
                document.getElementById('currency').value = taxSettings.currency;
            }
            if (taxSettings.default_vat_included !== undefined) {
                const radio = taxSettings.default_vat_included ? 'defaultVatYes' : 'defaultVatNo';
                document.getElementById(radio).checked = true;
            }
        }

        // Funzione globale per eliminare dipendenti (chiamata da onclick)
        window.deleteEmployee = deleteEmployee;
        window.deleteVehicle = deleteVehicle;
    });
</script>
{% endblock %}
