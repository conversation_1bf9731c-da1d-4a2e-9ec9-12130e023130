{% extends "base.html" %}

{% block title %}Dashboard Avanzata - Ana<PERSON>i <PERSON>{% endblock %}

{% block content %}
<main class="row mb-4">
    <section class="col-12">
        <article class="card shadow">
            <header class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>Dashboard Avanzata</h4>
            </header>
            <div class="card-body">
                {% if not data %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Questa è una versione avanzata della dashboard. Carica un file per visualizzare i dati.
                </div>
                {% else %}
                <div class="alert alert-info d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-info-circle me-2"></i>
                        Visualizzazione dati dal file: <strong>{{ filename }}</strong>
                        <span class="badge {% if file_type == 'unknown' or file_type == 'generico' %}bg-warning{% else %}bg-success{% endif %} text-white ms-2">
                            <i class="fas {% if file_type == 'attivita' %}fa-tasks{% elif 'teamviewer' in file_type %}fa-desktop{% elif file_type == 'calendario' %}fa-calendar{% elif file_type == 'timbrature' %}fa-clock{% elif file_type == 'permessi' %}fa-calendar-check{% else %}fa-file{% endif %} me-1"></i>
                            Tipo: {{ file_type }}
                        </span>
                        {% if session.get('mcp_file_id') %}
                        <span class="badge bg-info text-white ms-2" title="Elaborato con MCP">
                            <i class="fas fa-server me-1"></i>MCP
                        </span>
                        {% endif %}
                    </div>
                    <div>
                        <a href="{{ url_for('dashboard') }}" class="btn btn-outline-primary btn-sm me-2">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard Standard
                        </a>
                        <!-- Dati Grezzi rimossi - sezione legacy -->
                        <a href="{{ url_for('interactive_charts') }}" class="btn btn-outline-primary btn-sm me-2">
                            <i class="fas fa-chart-bar me-1"></i>Grafici Interattivi
                        </a>
                        <a href="/agents/dashboard" class="btn btn-outline-primary btn-sm me-2">
                            <i class="fas fa-robot me-1"></i>Agenti AI
                        </a>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success btn-sm dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-file-export me-1"></i>Esporta
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="{{ url_for('export_excel') }}"><i class="fas fa-file-excel me-2 text-success"></i>Excel</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('export_csv') }}"><i class="fas fa-file-csv me-2 text-primary"></i>CSV</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('export_json') }}"><i class="fas fa-file-code me-2 text-warning"></i>JSON</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('export_pdf') }}"><i class="fas fa-file-pdf me-2 text-danger"></i>PDF</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Filtri -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <button class="btn btn-link text-decoration-none p-0" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
                            <i class="fas fa-filter me-1"></i>Filtri
                        </button>
                    </div>
                    <div class="collapse" id="filterCollapse">
                        <div class="card-body">
                            <form id="dashboard-filters">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label for="date-range" class="form-label">Intervallo Date</label>
                                        <input type="text" class="form-control" id="date-range" placeholder="Seleziona intervallo date">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="technician-filter" class="form-label">Tecnico</label>
                                        <select class="form-select" id="technician-filter">
                                            <option value="">Tutti i tecnici</option>
                                            {% for technician in technicians %}
                                            <option value="{{ technician }}">{{ technician }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label for="client-filter" class="form-label">Cliente</label>
                                        <select class="form-select" id="client-filter">
                                            <option value="">Tutti i clienti</option>
                                            {% for client in clients %}
                                            <option value="{{ client }}">{{ client }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-end">
                                    <button type="button" class="btn btn-outline-secondary me-2" id="reset-filters">
                                        <i class="fas fa-redo me-1"></i>Reset
                                    </button>
                                    <button type="button" class="btn btn-primary" id="apply-filters">
                                        <i class="fas fa-search me-1"></i>Applica Filtri
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- KPI Widgets -->
                <h5 class="mb-3">KPI - Indicatori Chiave di Performance</h5>
                <div class="widget-grid mb-4">
                    <!-- KPI 1: Ore Totali -->
                    <div id="kpi-total-hours" class="widget-sm"></div>

                    <!-- KPI 2: Durata Media -->
                    <div id="kpi-avg-duration" class="widget-sm"></div>

                    <!-- KPI 3: Tecnici Attivi -->
                    <div id="kpi-technicians" class="widget-sm"></div>

                    <!-- KPI 4: Clienti Serviti -->
                    <div id="kpi-clients" class="widget-sm"></div>

                    <!-- KPI 5: Efficienza -->
                    <div id="kpi-efficiency" class="widget-sm"></div>

                    <!-- KPI 6: Carico di Lavoro -->
                    <div id="kpi-workload" class="widget-sm"></div>
                </div>

                <!-- Chart Widgets -->
                <h5 class="mb-3">Grafici</h5>
                <div class="widget-grid mb-4">
                    <!-- Chart 1: Attività nel Tempo -->
                    <div id="chart-time" class="widget-lg"></div>

                    <!-- Chart 2: Distribuzione Tecnici -->
                    <div id="chart-technicians" class="widget-md"></div>

                    <!-- Chart 3: Distribuzione Clienti -->
                    <div id="chart-clients" class="widget-md"></div>

                    <!-- Chart 4: Durata Sessioni -->
                    <div id="chart-duration" class="widget-md"></div>

                    <!-- Chart 5: Heatmap Attività -->
                    <div id="chart-heatmap" class="widget-lg"></div>
                </div>

                <!-- Table Widget -->
                <h5 class="mb-3">Dati Recenti</h5>
                <div class="widget-grid mb-4">
                    <div id="table-recent" class="widget-xl"></div>
                </div>
                {% endif %}
            </div>
        </article>
    </section>
</main>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/widgets.css') }}">
{% endblock %}

{% block extra_js %}
<!-- Carica Plotly (versione completa per maggiore compatibilità) -->
<script src="https://cdn.plot.ly/plotly-2.24.1.min.js"></script>

<!-- Carica moment.js prima di daterangepicker (necessario per il funzionamento) -->
<script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>

<!-- Carica daterangepicker dopo moment.js -->
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

<!-- Carica lo script per i widget -->
<script src="{{ url_for('static', filename='js/widgets.js') }}?v=1.0.0"></script>

<!-- Carica lo script della dashboard avanzata -->
<script src="{{ url_for('static', filename='js/advanced_dashboard.js') }}?v=1.0.0"></script>
{% endblock %}
