#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Client per l'API di OpenRouter.
Gestisce le chiamate all'API di OpenRouter per l'interazione con i modelli LLM.
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional
import requests
from urllib.parse import urljoin

# Configurazione del logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OpenRouterClient:
    """
    Client per l'API di OpenRouter.
    Gestisce le chiamate all'API di OpenRouter per l'interazione con i modelli LLM.
    """

    def __init__(self, api_key: Optional[str] = None, base_url: str = "https://openrouter.ai/api/v1",
                 timeout: int = 60, max_retries: int = 2):
        """
        Inizializza il client OpenRouter.

        Args:
            api_key: Chiave API per OpenRouter (opzionale, può essere impostata tramite variabile d'ambiente)
            base_url: URL base per l'API di OpenRouter
            timeout: Timeout in secondi per le richieste HTTP
            max_retries: Numero massimo di tentativi per le richieste HTTP
        """
        self.api_key = api_key or os.environ.get("OPENROUTER_API_KEY")
        if not self.api_key:
            logger.warning("Nessuna chiave API OpenRouter fornita. Alcune funzionalità potrebbero non essere disponibili.")

        self.base_url = base_url
        self.timeout = timeout
        self.max_retries = max_retries

        # Inizializza la sessione HTTP
        self.session = requests.Session()

        # Configura gli headers di base
        self.session.headers.update({
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}" if self.api_key else "",
            "HTTP-Referer": os.environ.get("APP_URL", "http://localhost:5000"),  # Richiesto da OpenRouter
            "X-Title": "App Roberto - Analisi Dati Aziendali"  # Nome dell'applicazione
        })

        # Verifica la connessione all'API
        self.is_available = self._check_connection()

    def _check_connection(self) -> bool:
        """
        Verifica la connessione all'API di OpenRouter.

        Returns:
            True se la connessione è disponibile, False altrimenti
        """
        if not self.api_key:
            logger.warning("Impossibile verificare la connessione a OpenRouter: chiave API mancante")
            return False

        try:
            # Effettua una richiesta di test all'API
            response = self.session.get(
                f"{self.base_url}/models",  # Concatenazione diretta
                timeout=self.timeout
            )

            if response.status_code == 200:
                logger.info("Connessione a OpenRouter stabilita")
                return True
            else:
                logger.warning(f"OpenRouter non risponde correttamente: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"Impossibile connettersi a OpenRouter: {str(e)}")
            return False

    def get_models(self, include_free: bool = True, include_paid: bool = True) -> List[Dict[str, Any]]:
        """
        Ottiene la lista dei modelli disponibili su OpenRouter.

        Args:
            include_free: Se True, include i modelli con quote gratuite
            include_paid: Se True, include i modelli a pagamento

        Returns:
            Lista dei modelli disponibili
        """
        if not self.is_available:
            logger.warning("OpenRouter non disponibile, impossibile ottenere i modelli")
            return []

        try:
            # Verifica prima se la chiave API è valida
            if not self.api_key or not self.api_key.startswith('sk-or-v1-'):
                logger.warning("Chiave API OpenRouter non valida o mancante")
                return self._get_default_models()

            # Aggiungi un header User-Agent per evitare problemi con l'API
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": os.environ.get("APP_URL", "http://localhost:5000"),
                "X-Title": "App Roberto - Analisi Dati Aziendali",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }

            response = self.session.get(
                "https://openrouter.ai/api/v1/models",
                timeout=self.timeout,
                headers=headers
            )

            # Debug della risposta
            logger.info(f"OpenRouter response status: {response.status_code}")
            logger.info(f"OpenRouter response headers: {dict(response.headers)}")

            # Verifica se la risposta è valida
            if response.status_code == 200:
                # Verifica se il contenuto è JSON valido
                content_type = response.headers.get('Content-Type', '')
                if 'application/json' not in content_type:
                    logger.warning(f"Risposta non JSON da OpenRouter: {content_type}")
                    logger.warning(f"Contenuto risposta: {response.text[:200]}...")
                    # Fallback a modelli predefiniti
                    return self._get_default_models()

                # Prova a decodificare il JSON
                try:
                    data = response.json()
                except Exception as json_error:
                    logger.error(f"Errore nella decodifica JSON: {str(json_error)} - Contenuto: {response.text[:100]}...")
                    # Fallback a modelli predefiniti
                    return self._get_default_models()
            elif response.status_code == 401:
                logger.error("Chiave API OpenRouter non autorizzata")
                return self._get_default_models()
            elif response.status_code == 403:
                logger.error("Accesso negato a OpenRouter")
                return self._get_default_models()
            else:
                logger.error(f"Errore nella richiesta a OpenRouter: {response.status_code} - {response.text}")
                return self._get_default_models()

            models = data.get("data", [])

            # Se non ci sono modelli, usa i modelli predefiniti
            if not models:
                logger.warning("Nessun modello restituito da OpenRouter, utilizzo modelli predefiniti")
                return self._get_default_models()

            # Aggiungi informazioni sui modelli gratuiti
            for model in models:
                # Verifica se il modello ha un prezzo zero (gratuito)
                try:
                    prompt_price = float(model.get("pricing", {}).get("prompt", "0"))
                    completion_price = float(model.get("pricing", {}).get("completion", "0"))
                except (ValueError, TypeError):
                    prompt_price = 0
                    completion_price = 0
                    logger.warning(f"Errore nella conversione del prezzo per il modello {model.get('id', 'unknown')}")

                # Aggiungi un flag per indicare se il modello è gratuito
                model["is_free"] = prompt_price == 0 and completion_price == 0

                # Aggiungi un flag per indicare se il modello ha quote gratuite
                # Questo è un'approssimazione, poiché OpenRouter non fornisce questa informazione direttamente
                # Consideriamo modelli con quote gratuite quelli di Claude, GPT e Gemini
                model_id = model.get("id", "").lower()
                has_free_quota = any(provider in model_id for provider in ["anthropic", "openai", "google"])
                model["has_free_quota"] = has_free_quota

            # Filtra i modelli in base ai parametri
            filtered_models = []
            for model in models:
                if (include_free and (model.get("is_free", False) or model.get("has_free_quota", False))) or \
                   (include_paid and not model.get("is_free", False)):
                    filtered_models.append(model)

            return filtered_models

        except Exception as e:
            logger.error(f"Errore nel recupero dei modelli: {str(e)}")
            # Fallback a modelli predefiniti
            return self._get_default_models()

    def _get_default_models(self) -> List[Dict[str, Any]]:
        """
        Restituisce una lista di modelli predefiniti quando non è possibile ottenere i modelli da OpenRouter.

        Returns:
            Lista di modelli predefiniti
        """
        logger.info("Utilizzo modelli predefiniti")
        default_models = [
            {
                "id": "anthropic/claude-3-haiku",
                "name": "Claude 3 Haiku",
                "description": "Modello veloce ed economico di Anthropic",
                "context_length": 200000,
                "is_free": False,
                "has_free_quota": True,
                "pricing": {
                    "prompt": "0.000025",
                    "completion": "0.000125"
                }
            },
            {
                "id": "anthropic/claude-3-sonnet",
                "name": "Claude 3 Sonnet",
                "description": "Modello bilanciato di Anthropic",
                "context_length": 200000,
                "is_free": False,
                "has_free_quota": True,
                "pricing": {
                    "prompt": "0.000075",
                    "completion": "0.000225"
                }
            },
            {
                "id": "anthropic/claude-3-opus",
                "name": "Claude 3 Opus",
                "description": "Modello più potente di Anthropic",
                "context_length": 200000,
                "is_free": False,
                "has_free_quota": True,
                "pricing": {
                    "prompt": "0.000150",
                    "completion": "0.000450"
                }
            },
            {
                "id": "openai/gpt-4o",
                "name": "GPT-4o",
                "description": "Modello più recente di OpenAI",
                "context_length": 128000,
                "is_free": False,
                "has_free_quota": True,
                "pricing": {
                    "prompt": "0.000050",
                    "completion": "0.000150"
                }
            },
            {
                "id": "google/gemini-1.5-pro",
                "name": "Gemini 1.5 Pro",
                "description": "Modello avanzato di Google",
                "context_length": 1000000,
                "is_free": False,
                "has_free_quota": True,
                "pricing": {
                    "prompt": "0.000035",
                    "completion": "0.000105"
                }
            },
            {
                "id": "meta-llama/llama-3-70b-instruct",
                "name": "Llama 3 70B Instruct",
                "description": "Modello open source di Meta",
                "context_length": 8192,
                "is_free": False,
                "has_free_quota": False,
                "pricing": {
                    "prompt": "0.000020",
                    "completion": "0.000060"
                }
            }
        ]
        return default_models

    def chat_completion(self,
                        messages: List[Dict[str, str]],
                        model: str = "anthropic/claude-3-haiku",
                        temperature: float = 0.7,
                        max_tokens: int = 1000,
                        stream: bool = False) -> Dict[str, Any]:
        """
        Effettua una richiesta di completamento chat a OpenRouter.

        Args:
            messages: Lista di messaggi per la conversazione
            model: ID del modello da utilizzare
            temperature: Temperatura per la generazione (0.0-1.0)
            max_tokens: Numero massimo di token da generare
            stream: Se True, la risposta viene restituita in streaming

        Returns:
            Risposta dell'API di OpenRouter
        """
        if not self.is_available:
            logger.warning("OpenRouter non disponibile, impossibile effettuare la richiesta")
            return {"error": "OpenRouter non disponibile"}

        try:
            # Prepara i dati per la richiesta
            data = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": stream
            }

            # Prepara gli headers per questa richiesta
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}",
                "HTTP-Referer": os.environ.get("APP_URL", "http://localhost:5000"),
                "X-Title": "App Roberto - Analisi Dati Aziendali"
            }

            # Effettua la richiesta
            response = self.session.post(
                f"{self.base_url}/chat/completions",  # Concatenazione diretta
                json=data,
                headers=headers,  # Aggiunti headers espliciti
                timeout=self.timeout
            )

            # Verifica la risposta
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Errore nella richiesta a OpenRouter: {response.status_code} - {response.text}")
                return {"error": f"Errore nella richiesta a OpenRouter: {response.status_code}"}

        except json.JSONDecodeError as e:
            logger.error(f"Errore JSON decode nella richiesta di completamento chat: {str(e)}")
            logger.error(f"Tentativo di fallback con richiesta diretta...")
            return self._fallback_chat_completion(messages, model, temperature, max_tokens)
        except Exception as e:
            logger.error(f"Errore generico nella richiesta di completamento chat: {str(e)}")
            logger.error(f"Tentativo di fallback con richiesta diretta...")
            return self._fallback_chat_completion(messages, model, temperature, max_tokens)

    def _fallback_chat_completion(self, messages, model, temperature, max_tokens):
        """
        Metodo di fallback che usa una richiesta diretta quando il client principale fallisce.
        """
        try:
            logger.info("Usando fallback con richiesta diretta a OpenRouter")

            # Headers semplificati che sappiamo funzionare
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json',
                'HTTP-Referer': 'http://localhost:5000',
                'X-Title': 'App Roberto - Fallback'
            }

            # Dati della richiesta
            data = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "stream": False
            }

            # Richiesta diretta
            response = requests.post(
                'https://openrouter.ai/api/v1/chat/completions',
                headers=headers,
                json=data,
                timeout=30
            )

            logger.info(f"Fallback - Status code: {response.status_code}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    logger.info("Fallback riuscito!")
                    return result
                except json.JSONDecodeError as e:
                    logger.error(f"Fallback - Errore JSON: {e}")
                    return {"error": f"Fallback JSON error: {str(e)}"}
            else:
                logger.error(f"Fallback - Errore HTTP: {response.status_code} - {response.text}")
                return {"error": f"Fallback HTTP error: {response.status_code}"}

        except Exception as e:
            logger.error(f"Fallback fallito: {e}")
            return {"error": f"Fallback failed: {str(e)}"}

    def serialize_data_for_llm(self, data: List[Dict[str, Any]],
                              max_rows: int = 5,
                              include_stats: bool = True) -> str:
        """
        Serializza i dati per l'LLM in un formato conciso e comprensibile.

        Args:
            data: Dati da serializzare
            max_rows: Numero massimo di righe da includere
            include_stats: Se True, include statistiche sui dati

        Returns:
            Dati serializzati in formato stringa
        """
        if not data:
            return "Nessun dato disponibile."

        # Limita il numero di righe
        sample_data = data[:max_rows]

        # Crea una rappresentazione testuale dei dati
        result = f"Campione di dati ({min(max_rows, len(data))} righe su {len(data)} totali):\n"

        # Aggiungi le righe di esempio
        for i, row in enumerate(sample_data):
            result += f"Riga {i+1}: {json.dumps(row, ensure_ascii=False)}\n"

        # Aggiungi statistiche se richiesto
        if include_stats and len(data) > 0:
            result += "\nStatistiche:\n"

            # Calcola le colonne disponibili
            columns = list(data[0].keys())
            result += f"- Colonne: {', '.join(columns)}\n"

            # Calcola il numero di valori unici per ogni colonna
            for col in columns:
                unique_values = set(row.get(col) for row in data if row.get(col) is not None)
                result += f"- Valori unici in '{col}': {len(unique_values)}\n"

        return result
