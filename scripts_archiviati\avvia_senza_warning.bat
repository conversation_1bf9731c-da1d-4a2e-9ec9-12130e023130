@echo off 
echo Avvio applicazione senza warning Excel... 
call clean_env\Scripts\activate 
 
REM Imposta le variabili d'ambiente 
set OPENROUTER_API_KEY=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 
set APP_URL=http://localhost:5000 
set MCP_URL=http://localhost:8000 
 
REM Filtra i warning di Excel 
set PYTHONWARNINGS=ignore::UserWarning:pandas.io.excel 
 
echo Avvio del server MCP... 
start "MCP Server" cmd /k "call clean_env\Scripts\activate ^&^& cd mcp_server ^&^& python -W ignore run_server.py" 
 
echo Attesa avvio server (5 secondi)... 
ping 127.0.0.1 -n 6 > nul 
 
echo Avvio applicazione principale... 
start "App Roberto" cmd /k "call clean_env\Scripts\activate ^&^& python -W ignore app.py" 
 
echo ===================================== 
echo Applicazione avviata senza warning Excel! 
echo - Flask: http://localhost:5000 
echo - MCP: http://localhost:8000 
echo ===================================== 
 
pause 
