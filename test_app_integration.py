#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FASE 6: Test Integrazione App Roberto
Suite di test completa per l'applicazione Flask.
Versione: 1.0.0 - FASE 6 Testing e Stabilizzazione
"""

import pytest
import asyncio
import json
import os
import sys
import tempfile
import time
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock
import requests
from io import BytesIO

# Aggiungi il percorso per gli import
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import dell'applicazione
try:
    from app import app, supabase_client
    from health_monitor import health_monitor
    APP_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ App non disponibile per testing: {e}")
    APP_AVAILABLE = False

class TestAppIntegration:
    """Test di integrazione per App Roberto."""

    @pytest.fixture
    def client(self):
        """Client di test Flask."""
        if not APP_AVAILABLE:
            pytest.skip("App non disponibile")

        app.config['TESTING'] = True
        app.config['WTF_CSRF_ENABLED'] = False

        with app.test_client() as client:
            with app.app_context():
                yield client

    @pytest.fixture
    def sample_csv_file(self):
        """File CSV di esempio per test."""
        csv_content = """Nome,Età,Città
Mario,30,Roma
Luigi,25,Milano
Anna,35,Napoli"""

        file_obj = BytesIO(csv_content.encode('utf-8'))
        file_obj.name = 'test_data.csv'
        return file_obj

    def test_home_page(self, client):
        """Test pagina home."""
        response = client.get('/')
        assert response.status_code == 200
        assert b'Importazione Dati' in response.data
        assert b'upload-area' in response.data

    def test_dashboard_page(self, client):
        """Test pagina dashboard."""
        response = client.get('/dashboard')
        assert response.status_code == 200
        assert b'Dashboard' in response.data

    def test_configuration_page(self, client):
        """Test pagina configurazione."""
        response = client.get('/configuration')
        assert response.status_code == 200
        assert b'Configurazione Sistema' in response.data
        assert b'system-tab' in response.data

    def test_chat_page(self, client):
        """Test pagina chat AI."""
        response = client.get('/chat')
        assert response.status_code == 200
        assert b'Chat AI' in response.data

    def test_api_health_endpoint(self, client):
        """Test endpoint health API."""
        response = client.get('/api/health')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        assert 'status' in data['data']
        assert 'version' in data['data']
        assert 'services' in data['data']

    def test_api_endpoints_mapping(self, client):
        """Test endpoint mappatura API."""
        response = client.get('/api/endpoints')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert 'data' in data
        assert 'endpoints' in data['data']
        assert 'total_endpoints' in data['data']

    @pytest.mark.asyncio
    async def test_file_upload_simulation(self, client, sample_csv_file):
        """Test simulazione upload file."""
        # Mock del sistema di upload
        with patch('app.process_uploaded_file') as mock_process:
            mock_process.return_value = {
                'success': True,
                'message': 'File processato con successo',
                'data': {'rows': 3, 'columns': 3}
            }

            response = client.post('/upload', data={
                'file': (sample_csv_file, 'test_data.csv')
            }, content_type='multipart/form-data')

            # Verifica redirect o risposta
            assert response.status_code in [200, 302]

    def test_static_files_accessibility(self, client):
        """Test accessibilità file statici."""
        static_files = [
            '/static/css/style.css',
            '/static/css/dark-theme.css',
            '/static/js/main.js',
            '/static/js/theme-manager.js',
            '/static/js/ui-enhancements.js'
        ]

        for file_path in static_files:
            response = client.get(file_path)
            assert response.status_code == 200, f"File {file_path} non accessibile"

    def test_error_handling_404(self, client):
        """Test gestione errore 404."""
        response = client.get('/pagina-inesistente')
        assert response.status_code == 404

    def test_api_error_handling(self, client):
        """Test gestione errori API."""
        # Test endpoint inesistente
        response = client.get('/api/endpoint-inesistente')
        assert response.status_code == 404

        # Test metodo non supportato
        response = client.delete('/api/health')
        assert response.status_code == 405

    @pytest.mark.slow
    def test_performance_basic(self, client):
        """Test performance di base."""
        start_time = time.time()

        # Test multiple richieste
        for _ in range(10):
            response = client.get('/')
            assert response.status_code == 200

        end_time = time.time()
        avg_time = (end_time - start_time) / 10

        # Verifica che il tempo medio sia ragionevole
        assert avg_time < 1.0, f"Tempo medio troppo alto: {avg_time:.3f}s"

    def test_memory_usage(self, client):
        """Test utilizzo memoria."""
        import psutil

        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # Esegui operazioni che potrebbero causare memory leak
        for _ in range(50):
            client.get('/')
            client.get('/dashboard')
            client.get('/api/health')

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        # Verifica che l'aumento di memoria sia ragionevole
        assert memory_increase < 50, f"Aumento memoria eccessivo: {memory_increase:.1f} MB"

class TestHealthMonitorIntegration:
    """Test integrazione Health Monitor."""

    def test_health_monitor_initialization(self):
        """Test inizializzazione health monitor."""
        assert health_monitor is not None
        assert hasattr(health_monitor, 'check_system_health')
        assert hasattr(health_monitor, 'start_monitoring')

    def test_system_health_check(self):
        """Test controllo salute sistema."""
        health = health_monitor.check_system_health()

        assert health.overall_status in ['healthy', 'warning', 'critical']
        assert isinstance(health.metrics, list)
        assert len(health.metrics) > 0
        assert health.uptime_seconds >= 0

    def test_health_report_generation(self):
        """Test generazione report salute."""
        report = health_monitor.get_health_report()

        required_keys = [
            'current_status', 'uptime_hours', 'current_metrics',
            'historical_averages', 'issues', 'monitoring_active'
        ]

        for key in required_keys:
            assert key in report, f"Chiave mancante nel report: {key}"

    @pytest.mark.slow
    def test_monitoring_lifecycle(self):
        """Test ciclo di vita monitoraggio."""
        # Verifica stato iniziale
        assert not health_monitor.is_monitoring

        # Avvia monitoraggio
        health_monitor.start_monitoring()
        time.sleep(2)  # Attendi avvio
        assert health_monitor.is_monitoring

        # Ferma monitoraggio
        health_monitor.stop_monitoring()
        assert not health_monitor.is_monitoring

class TestSecurityBasics:
    """Test di sicurezza di base."""

    def test_csrf_protection_disabled_in_testing(self, client):
        """Test che CSRF sia disabilitato nei test."""
        # Questo test verifica che i test possano funzionare
        # In produzione CSRF dovrebbe essere abilitato
        response = client.post('/upload', data={})
        # Non dovrebbe fallire per CSRF nei test
        assert response.status_code != 400

    def test_file_upload_security(self, client):
        """Test sicurezza upload file."""
        # Test file con estensione non permessa
        malicious_file = BytesIO(b"malicious content")
        malicious_file.name = 'malicious.exe'

        response = client.post('/upload', data={
            'file': (malicious_file, 'malicious.exe')
        }, content_type='multipart/form-data')

        # Dovrebbe rifiutare file non permessi
        assert response.status_code in [400, 302]  # Error o redirect

    def test_sql_injection_basic(self, client):
        """Test protezione SQL injection di base."""
        # Test parametri con caratteri SQL
        malicious_params = ["'; DROP TABLE users; --", "1' OR '1'='1"]

        for param in malicious_params:
            response = client.get(f'/dashboard?param={param}')
            # Non dovrebbe causare errori server
            assert response.status_code != 500

# Configurazione pytest
def pytest_configure(config):
    """Configurazione pytest."""
    config.addinivalue_line("markers", "slow: marks tests as slow")
    config.addinivalue_line("markers", "integration: marks tests as integration tests")

class TestErrorHandlingAndRecovery:
    """Test gestione errori e recovery."""

    def test_database_connection_failure(self, client):
        """Test gestione fallimento connessione database."""
        with patch('app.supabase_client') as mock_supabase:
            # Simula errore connessione
            mock_supabase.table.side_effect = Exception("Connection failed")

            response = client.get('/dashboard')
            # L'app dovrebbe gestire gracefully l'errore
            assert response.status_code in [200, 500]

    def test_file_processing_error(self, client):
        """Test gestione errore processamento file."""
        with patch('app.process_uploaded_file') as mock_process:
            # Simula errore processamento
            mock_process.side_effect = Exception("Processing failed")

            file_obj = BytesIO(b"test,data\n1,2")
            file_obj.name = 'test.csv'

            response = client.post('/upload', data={
                'file': (file_obj, 'test.csv')
            }, content_type='multipart/form-data')

            # Dovrebbe gestire l'errore senza crash
            assert response.status_code in [200, 302, 400, 500]

    def test_api_error_responses(self, client):
        """Test risposte errore API standardizzate."""
        with patch('app.supabase_client') as mock_supabase:
            mock_supabase.table.side_effect = Exception("Database error")

            response = client.get('/api/health')

            if response.status_code == 200:
                data = json.loads(response.data)
                # Verifica formato errore standardizzato
                if not data.get('success', True):
                    assert 'error' in data
                    assert 'timestamp' in data

if __name__ == "__main__":
    # Esegui test
    pytest.main([__file__, "-v", "--tb=short"])
