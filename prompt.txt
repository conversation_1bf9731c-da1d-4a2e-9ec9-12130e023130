# Richiesta di miglioramento del sistema di riconoscimento dati nei file

Vorrei migliorare il sistema attuale per renderlo più intelligente nel riconoscimento dei dati all'interno dei file, indipendentemente dai nomi dei file o delle colonne. Ecco i requisiti specifici:

## Obiettivo principale
Sviluppare un sistema che riconosca automaticamente il tipo di file (attività, appuntamenti, sessioni remote, timbrature, registro auto, permessi) basandosi sul contenuto e non sul nome del file, e che estragga i dati rilevanti per inserirli nelle tabelle appropriate del database.

## Contesto attuale
- Attualmente lavoro con file di export che ho già parzialmente formattato (eliminando o modificando colonne)
- Questi dati vengono utilizzati per compilare quotidianamente un file Excel dove:
  - Sovrascrivo i fogli con gli export aggiornati
  - Creo tabelle pivot per riassumere i dati dei tecnici
  - Eseguo controlli incrociati (es. se un tecnico è segnato per 4 ore in un'azienda nel calendario, mi aspetto di trovare il riferimento nel foglio attività)

## Problemi da risolvere
1. Il sistema deve riconoscere i file in base al contenuto, non al nome (es. riconoscere un file attività anche se si chiama "gino" invece di "attività")
2. Il sistema deve identificare le colonne rilevanti anche se hanno nomi diversi (es. "Tecnico", "Risorsa", "Attendee" sono tutte colonne che identificano il dipendente)
3. Il sistema deve essere in grado di estrarre dati anche se non sono nella prima riga (es. copiare dalla cella 2 anziché dalla 1)

## Funzionalità richieste
1. Estrazione intelligente dei dati dai file caricati
2. Inserimento dei dati in tabelle prestrutturate, aggiornandole con i nuovi file
3. Dashboard per visualizzare la situazione aggiornata
4. Sezione di impostazione per:
   - Configurare i costi per ciascun dipendente (con possibilità di modifica)
   - Aggiornare dinamicamente l'elenco dei dipendenti in base ai dati dei file
   - Configurare i dati del registro auto (manualmente o tramite chat LLM)

## Approccio richiesto
1. Strutturare un piano d'azione suddiviso in task specifici
2. Salvare il piano in un file markdown
3. Per ogni fase:
   - Controllare con Context 7 le librerie e funzioni aggiornate
   - Considerare l'integrazione con Supabase e Augment Code
   - Consultare e aggiornare la memoria del sistema
   - Creare test esaustivi
   - Eseguire commit e push al completamento di ogni fase
   - Procedere alla fase successiva

## File di riferimento
I file nella cartella @test_file_grezzi/ devono essere utilizzati come riferimento per lo sviluppo e i test del sistema.