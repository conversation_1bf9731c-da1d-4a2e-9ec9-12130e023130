#!/usr/bin/env python3
"""
Test per le route di debug
"""

import requests
import json

def test_debug_routes():
    """Test route di debug e problematiche"""
    
    print("🔍 TEST ROUTE DEBUG")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test 1: Verifica connettività
    print("1️⃣ Test connettività...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   Homepage: {response.status_code} ({'✅' if response.status_code == 200 else '❌'})")
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
        return
    
    # Test 2: Test route di debug
    print("\n2️⃣ Test route di debug...")
    
    debug_tests = [
        ("GET", "/api/test-debug"),
        ("POST", "/api/test-debug"),
    ]
    
    for method, path in debug_tests:
        try:
            print(f"   Test {method} {path}...")
            
            if method == "GET":
                response = requests.get(f"{base_url}{path}", timeout=5)
            else:
                response = requests.post(
                    f"{base_url}{path}",
                    json={"test": True},
                    headers={"Content-Type": "application/json"},
                    timeout=5
                )
            
            print(f"      Status: {response.status_code}")
            
            if response.status_code == 200:
                print("      ✅ Route di debug funzionante!")
                try:
                    data = response.json()
                    print(f"      📄 Risposta: {data.get('message', 'N/A')}")
                except:
                    print("      📄 Risposta non JSON")
            elif response.status_code == 404:
                print("      ❌ Route di debug NON registrata!")
            else:
                print(f"      ⚠️ Status inaspettato: {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ Errore: {str(e)}")
    
    # Test 3: Test route problematica
    print("\n3️⃣ Test route problematica...")
    
    try:
        print("   Test POST /api/get-processed-data...")
        
        response = requests.post(
            f"{base_url}/api/get-processed-data",
            json={"filename": "test.csv"},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        
        print(f"      Status: {response.status_code}")
        
        if response.status_code == 200:
            print("      ✅ Route problematica ora funziona!")
            try:
                data = response.json()
                print(f"      📄 Risposta: {data.get('success', 'N/A')}")
            except:
                print("      📄 Risposta non JSON")
        elif response.status_code == 404:
            print("      ❌ Route problematica ancora 404!")
        elif response.status_code == 400:
            print("      ⚠️ Route registrata ma errore 400 (normale per dati test)")
        else:
            print(f"      ⚠️ Status inaspettato: {response.status_code}")
            
    except Exception as e:
        print(f"      ❌ Errore: {str(e)}")
    
    # Test 4: Confronto con route funzionanti
    print("\n4️⃣ Test route funzionanti...")
    
    working_routes = [
        ("GET", "/api/health"),
        ("GET", "/api/processed_data"),
        ("POST", "/api/wizard/complete"),
    ]
    
    for method, path in working_routes:
        try:
            print(f"   Test {method} {path}...")
            
            if method == "GET":
                response = requests.get(f"{base_url}{path}", timeout=5)
            else:
                response = requests.post(
                    f"{base_url}{path}",
                    json={"test": True},
                    headers={"Content-Type": "application/json"},
                    timeout=5
                )
            
            status = "✅" if response.status_code in [200, 400, 500] else "❌"
            print(f"      Status: {response.status_code} {status}")
            
        except Exception as e:
            print(f"      ❌ Errore: {str(e)}")
    
    print("\n" + "=" * 50)
    print("📊 RISULTATI")
    print("=" * 50)
    print("Se la route di debug funziona ma /api/get-processed-data no:")
    print("- Il problema è specifico di quella route")
    print("- Potrebbe essere un errore di sintassi nella definizione")
    print("- Potrebbe essere un conflitto di nomi di funzione")
    
    return True

if __name__ == "__main__":
    test_debug_routes()
