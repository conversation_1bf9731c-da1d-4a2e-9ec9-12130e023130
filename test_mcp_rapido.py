#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test rapido per verificare che il server MCP sia operativo.
"""

import requests
import time
from datetime import datetime

def test_mcp_rapido():
    """Test rapido del server MCP."""
    print("⚡ TEST RAPIDO SERVER MCP")
    print("=" * 30)
    
    mcp_url = "http://127.0.0.1:8001"
    
    try:
        print("🔍 Test connessione...")
        start_time = time.time()
        response = requests.get(f"{mcp_url}/health", timeout=5)
        response_time = (time.time() - start_time) * 1000
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Server MCP operativo!")
            print(f"📊 Status: {data.get('status', 'unknown')}")
            print(f"⏱️ Tempo risposta: {response_time:.1f}ms")
            print(f"🕒 Timestamp: {data.get('timestamp', 'unknown')}")
            print()
            print("🎉 PROBLEMA TIMEOUT RISOLTO!")
            print("✅ Il server MCP risponde correttamente")
            print("✅ Nessun errore di connessione")
            return True
        else:
            print(f"⚠️ Server risponde ma con status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Errore connessione: Server MCP non raggiungibile")
        print(f"💡 Assicurati che il server MCP sia avviato: cd mcp_server && python run_server.py")
        return False
    except requests.exceptions.Timeout as e:
        print(f"❌ Timeout: Server MCP non risponde entro 5 secondi")
        return False
    except Exception as e:
        print(f"❌ Errore generico: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_mcp_rapido()
    
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"\n📅 Test eseguito: {timestamp}")
    
    if success:
        print("🚀 Server MCP completamente operativo!")
    else:
        print("🔧 Server MCP necessita di essere riavviato")
    
    exit(0 if success else 1)
