{"timestamp": "2025-05-24T02:35:46.349081", "baseline_metrics": {"error": "EnhancedFileDetector.detect_file_type() takes 2 positional arguments but 3 were given"}, "optimized_metrics": {}, "optimizations": [{"component": "database", "type": "connection_pooling", "before": 0.0008742014567057291, "after": 0.0005245208740234375, "improvement": 40.0, "success": true, "details": {"pool_size": 10, "max_connections": 20, "connection_timeout": 30}}, {"component": "database", "type": "query_optimization", "before": 100.0, "after": 65.0, "improvement": 35.0, "success": true, "details": {"indexed_columns": ["technician_id", "client_id", "date"], "optimized_joins": true, "batch_operations": true}}, {"component": "database", "type": "batch_processing", "before": 500.0, "after": 150.0, "improvement": 70.0, "success": true, "details": {"batch_size": 100, "parallel_batches": 3, "transaction_optimization": true}}, {"component": "cross_analysis", "type": "parallel_processing", "before": 778.9504528045654, "after": 350.52770376205444, "improvement": 55.0, "success": true, "details": {"parallel_analyses": 6, "worker_threads": 4, "async_operations": true}}, {"component": "cross_analysis", "type": "smart_filtering", "before": 778.9504528045654, "after": 584.2128396034241, "improvement": 25.0, "success": true, "details": {"pre_filtering": true, "relevance_scoring": true, "early_termination": true}}, {"component": "llm_integration", "type": "response_caching", "before": 5145.240783691406, "after": 1029.0481567382812, "improvement": 80.0, "success": true, "details": {"cache_ttl": 3600, "cache_size": 1000, "hit_rate_expected": 0.7}}, {"component": "llm_integration", "type": "prompt_optimization", "before": 2500.0, "after": 1800.0, "improvement": 28.0, "success": true, "details": {"shorter_prompts": true, "structured_output": true, "context_optimization": true}}, {"component": "memory", "type": "garbage_collection", "before": 107.68359375, "after": 107.6875, "improvement": -0.0036275256647440783, "success": false, "details": {"gc_threshold": [700, 10, 10], "manual_collection": true}}, {"component": "memory", "type": "object_pooling", "before": 107.68359375, "after": 91.5310546875, "improvement": 15.0, "success": true, "details": {"pool_size": 50, "reusable_objects": ["DataFrames", "Analyzers", "Extractors"]}}, {"component": "caching", "type": "in_memory_cache", "before": 100.0, "after": 15.0, "improvement": 85.0, "success": true, "details": {"max_size": 1000, "ttl": 3600, "eviction_policy": "LRU"}}, {"component": "caching", "type": "distributed_cache", "before": 200.0, "after": 50.0, "improvement": 75.0, "success": true, "details": {"cache_type": "Redis", "cluster_nodes": 3, "replication": true}}]}