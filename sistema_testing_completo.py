#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
🧪 SISTEMA TESTING COMPLETO E AUTOMATIZZATO - APP ROBERTO
Sistema di testing esaustivo per identificare e risolvere tutti i problemi.

OBIETTIVO: Completare definitivamente la fase di debugging per passare al lavoro produttivo.
"""

import asyncio
import aiohttp
import json
import time
import os
import sys
from datetime import datetime
from typing import Dict, List, Tuple, Any
import subprocess
import threading
import queue
import requests
from pathlib import Path

class AppRobertoTestingSuite:
    """Suite di testing completa per App-Roberto."""

    def __init__(self, base_url: str = "http://127.0.0.1:5001"):
        self.base_url = base_url
        self.test_results = {}
        self.errors_found = []
        self.warnings_found = []
        self.start_time = None
        self.app_process = None
        self.terminal_output = queue.Queue()

        # LISTA COMPLETA DI TUTTI I COMPONENTI DA TESTARE
        self.all_endpoints = [
            # PAGINE PRINCIPALI
            ("GET", "/", "Homepage"),
            ("GET", "/dashboard", "Dashboard Standard"),
            ("GET", "/advanced-dashboard", "Dashboard Avanzata"),
            ("GET", "/intelligent-dashboard", "Dashboard Intelligente"),
            ("GET", "/interactive-charts", "Grafici Interattivi"),
            ("GET", "/setup-wizard", "Setup Wizard"),
            ("GET", "/chat", "Chat AI"),
            ("GET", "/agents-dashboard", "Dashboard Agenti"),
            ("GET", "/configuration", "Pagina Configurazione"),

            # API CORE
            ("GET", "/api/health", "Health Check"),
            ("GET", "/api/endpoints", "Lista Endpoints"),
            ("GET", "/api/debug/flask-routes", "Debug Route Flask"),

            # API DATI
            ("GET", "/api/data", "API Dati Grezzi"),
            ("GET", "/api/processed_data", "API Dati Elaborati"),
            ("GET", "/api/dashboard_data", "API Dashboard Data"),
            ("GET", "/api/chart_data", "API Chart Data"),

            # API CONFIGURAZIONE
            ("GET", "/api/config/employees", "API Config Dipendenti"),
            ("GET", "/api/config/vehicles", "API Config Veicoli"),
            ("POST", "/api/calculate-employee-cost", "API Calcolo Costo Dipendente"),

            # API CHAT E AI
            ("GET", "/api/models", "API Modelli Chat"),
            ("POST", "/api/chat/send", "API Invio Chat"),

            # API AGENTI AI
            ("GET", "/api/agents/status", "API Status Agenti"),
            ("POST", "/api/agents/execute", "API Esecuzione Agenti"),
            ("GET", "/agents/list", "API Lista Agenti"),
            ("POST", "/agents/execute", "API Esecuzione Agenti Blueprint"),

            # API WIZARD
            ("GET", "/api/wizard/status", "API Status Wizard"),
            ("POST", "/api/wizard/complete", "API Completamento Wizard"),
            ("POST", "/api/wizard/reset", "API Reset Wizard"),
            ("GET", "/api/wizard/test", "API Test Wizard"),

            # API AUTOMAZIONE
            ("GET", "/api/automation/rules", "API Regole Automazione"),
            ("POST", "/api/automation/trigger", "API Trigger Automazione"),

            # API SISTEMA INTELLIGENTE
            ("POST", "/api/intelligent-system/analyze", "API Analisi Sistema Intelligente"),

            # API ONBOARDING
            ("POST", "/api/onboarding/analyze", "API Analisi Onboarding"),
            ("POST", "/api/onboarding/progress", "API Progresso Onboarding"),
            ("GET", "/api/onboarding/templates", "API Template Onboarding"),

            # API PERSISTENZA
            ("GET", "/api/persistence/status", "API Status Persistenza"),
            ("POST", "/api/persistence/cleanup", "API Cleanup Persistenza"),

            # API DATABASE
            ("GET", "/api/database/status", "API Status Database"),

            # API FILE
            ("POST", "/upload", "API Upload File"),
            ("POST", "/api/file/analyze_enhanced", "API Analisi File Avanzata"),

            # API MONITORING
            ("GET", "/monitoring/health", "API Health Monitoring"),
            ("GET", "/monitoring/metrics", "API Metriche Sistema"),
            ("GET", "/monitoring/status", "API Status Generale"),
        ]

        # FUNZIONALITÀ CORE DA TESTARE
        self.core_functionalities = [
            "upload_file_workflow",
            "data_processing_workflow",
            "chart_generation_workflow",
            "ai_chat_workflow",
            "setup_wizard_workflow",
            "agent_execution_workflow",
            "database_operations_workflow",
            "supabase_integration_workflow"
        ]

        # INTERFACCE UTENTE DA TESTARE
        self.ui_components = [
            "setup_wizard_ui",
            "dashboard_standard_ui",
            "dashboard_advanced_ui",
            "dashboard_intelligent_ui",
            "interactive_charts_ui",
            "chat_ai_ui",
            "agents_dashboard_ui",
            "configuration_ui"
        ]

    def log_test(self, test_name: str, status: str, details: str = "", error: str = ""):
        """Registra il risultato di un test."""
        timestamp = datetime.now().isoformat()

        self.test_results[test_name] = {
            'status': status,
            'timestamp': timestamp,
            'details': details,
            'error': error
        }

        # Stampa risultato
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {status}")
        if details:
            print(f"   📝 {details}")
        if error:
            print(f"   🚨 {error}")
            if status == "FAIL":
                self.errors_found.append(f"{test_name}: {error}")
            else:
                self.warnings_found.append(f"{test_name}: {error}")

    def start_app_monitoring(self):
        """Avvia l'app e monitora l'output del terminale."""
        def monitor_terminal():
            try:
                # Avvia l'app
                self.app_process = subprocess.Popen(
                    [sys.executable, "app.py"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    bufsize=1,
                    universal_newlines=True
                )

                # Monitora output
                for line in iter(self.app_process.stdout.readline, ''):
                    self.terminal_output.put(line.strip())

            except Exception as e:
                self.terminal_output.put(f"ERROR: {str(e)}")

        # Avvia monitoring in thread separato
        monitor_thread = threading.Thread(target=monitor_terminal)
        monitor_thread.daemon = True
        monitor_thread.start()

        # Aspetta che l'app si avvii con retry intelligente
        print("🚀 Avviando App-Roberto...")
        print("⏱️ Attendendo avvio (fino a 60 secondi)...")

        # Retry con timeout progressivo
        max_attempts = 12
        for attempt in range(max_attempts):
            try:
                print(f"   🔄 Tentativo {attempt + 1}/{max_attempts}...")
                response = requests.get(f"{self.base_url}/api/health", timeout=10)
                if response.status_code == 200:
                    self.log_test("app_startup", "PASS", f"App avviata correttamente (tentativo {attempt + 1})")
                    return True
                else:
                    print(f"   ⚠️ Status code: {response.status_code}")
            except requests.exceptions.ConnectionError:
                print(f"   ⏳ App non ancora pronta...")
            except requests.exceptions.Timeout:
                print(f"   ⏳ Timeout - app ancora in avvio...")
            except Exception as e:
                print(f"   ❌ Errore: {str(e)}")

            # Aspetta prima del prossimo tentativo
            time.sleep(5)

        # Se arriviamo qui, l'app non si è avviata
        self.log_test("app_startup", "FAIL", f"App non si è avviata dopo {max_attempts * 5} secondi")
        return False

    def stop_app(self):
        """Ferma l'app."""
        if self.app_process:
            self.app_process.terminate()
            self.app_process.wait()

    async def test_endpoint(self, method: str, endpoint: str, description: str) -> Dict[str, Any]:
        """Testa un singolo endpoint."""
        test_name = f"endpoint_{method}_{endpoint.replace('/', '_').replace('<', '').replace('>', '')}"

        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}{endpoint}"

                if method == "GET":
                    async with session.get(url, timeout=10) as response:
                        status_code = response.status
                        content = await response.text()

                elif method == "POST":
                    # Dati di test per POST
                    test_data = self.get_test_data_for_endpoint(endpoint)
                    async with session.post(url, json=test_data, timeout=10) as response:
                        status_code = response.status
                        content = await response.text()

                # Valuta risultato
                if status_code in [200, 201]:
                    self.log_test(test_name, "PASS", f"{description} - Status: {status_code}")
                elif status_code in [400, 404, 422]:
                    # Errori accettabili per alcuni endpoint
                    if self.is_acceptable_error(endpoint, status_code, content):
                        self.log_test(test_name, "WARN", f"{description} - Status: {status_code} (accettabile)")
                    else:
                        self.log_test(test_name, "FAIL", f"{description} - Status: {status_code}", content[:200])
                else:
                    self.log_test(test_name, "FAIL", f"{description} - Status: {status_code}", content[:200])

                return {
                    'endpoint': endpoint,
                    'method': method,
                    'status_code': status_code,
                    'content_length': len(content),
                    'description': description
                }

        except Exception as e:
            self.log_test(test_name, "FAIL", f"{description} - Errore connessione", str(e))
            return {
                'endpoint': endpoint,
                'method': method,
                'status_code': 0,
                'error': str(e),
                'description': description
            }

    def get_test_data_for_endpoint(self, endpoint: str) -> Dict[str, Any]:
        """Restituisce dati di test appropriati per l'endpoint."""
        test_data_map = {
            "/api/chat/send": {
                "message": "Test message",
                "model": "gpt-3.5-turbo"
            },
            "/api/wizard/complete": {
                "files": [],
                "employees": [],
                "vehicles": [],
                "configuration": {}
            },
            "/api/calculate-employee-cost": {
                "employee_name": "Test Employee",
                "hours": 8,
                "rate": 25.0
            },
            "/api/agents/execute": {
                "agent_name": "data_cleaning",
                "parameters": {"test": True}
            },
            "/api/intelligent-system/analyze": {
                "data": {"test": "data"}
            },
            "/upload": {
                "file": "test_file.csv"
            }
        }

        return test_data_map.get(endpoint, {"test": True})

    def is_acceptable_error(self, endpoint: str, status_code: int, content: str) -> bool:
        """Determina se un errore è accettabile per un endpoint."""
        acceptable_errors = {
            "/api/data": [404],  # Normale se non ci sono dati
            "/api/processed_data": [404],  # Normale se non ci sono dati elaborati
            "/api/chart_data": [400],  # Normale se mancano parametri
            "/upload": [400, 422],  # Normale se manca file
            "/api/chat/send": [400],  # Normale se mancano parametri
        }

        return status_code in acceptable_errors.get(endpoint, [])

    async def test_all_endpoints(self):
        """Testa tutti gli endpoint dell'app."""
        print("\n🔍 TESTING TUTTI GLI ENDPOINT API")
        print("=" * 50)

        tasks = []
        for method, endpoint, description in self.all_endpoints:
            task = self.test_endpoint(method, endpoint, description)
            tasks.append(task)

        # Esegui tutti i test in parallelo
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Analizza risultati
        passed = sum(1 for r in results if isinstance(r, dict) and r.get('status_code') in [200, 201])
        total = len(results)

        print(f"\n📊 RISULTATI ENDPOINT: {passed}/{total} PASSED")
        return results

    async def test_core_functionalities(self):
        """Testa le funzionalità core dell'app."""
        print("\n🎯 TESTING FUNZIONALITÀ CORE")
        print("=" * 50)

        for functionality in self.core_functionalities:
            await self.test_functionality(functionality)

    async def test_functionality(self, functionality_name: str):
        """Testa una specifica funzionalità."""
        try:
            if functionality_name == "upload_file_workflow":
                await self.test_upload_workflow()
            elif functionality_name == "data_processing_workflow":
                await self.test_data_processing_workflow()
            elif functionality_name == "chart_generation_workflow":
                await self.test_chart_generation_workflow()
            elif functionality_name == "ai_chat_workflow":
                await self.test_ai_chat_workflow()
            elif functionality_name == "setup_wizard_workflow":
                await self.test_setup_wizard_workflow()
            elif functionality_name == "agent_execution_workflow":
                await self.test_agent_execution_workflow()
            elif functionality_name == "database_operations_workflow":
                await self.test_database_operations_workflow()
            elif functionality_name == "supabase_integration_workflow":
                await self.test_supabase_integration_workflow()
            else:
                self.log_test(functionality_name, "SKIP", "Test non implementato")

        except Exception as e:
            self.log_test(functionality_name, "FAIL", "Errore durante test funzionalità", str(e))

    async def test_upload_workflow(self):
        """Testa il workflow di upload file."""
        test_name = "upload_file_workflow"

        try:
            # Simula upload file
            async with aiohttp.ClientSession() as session:
                # Test senza file (dovrebbe dare errore controllato)
                async with session.post(f"{self.base_url}/upload") as response:
                    if response.status in [400, 422]:
                        self.log_test(test_name, "PASS", "Upload senza file gestito correttamente")
                    else:
                        self.log_test(test_name, "FAIL", f"Upload senza file: status {response.status}")

        except Exception as e:
            self.log_test(test_name, "FAIL", "Errore test upload", str(e))

    async def test_data_processing_workflow(self):
        """Testa il workflow di elaborazione dati."""
        test_name = "data_processing_workflow"

        try:
            # Verifica API dati
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/data") as response:
                    if response.status in [200, 404]:  # 404 è normale se non ci sono dati
                        self.log_test(test_name, "PASS", "API dati risponde correttamente")
                    else:
                        self.log_test(test_name, "FAIL", f"API dati: status {response.status}")

        except Exception as e:
            self.log_test(test_name, "FAIL", "Errore test elaborazione dati", str(e))

    async def test_chart_generation_workflow(self):
        """Testa il workflow di generazione grafici."""
        test_name = "chart_generation_workflow"

        try:
            # Test API chart_data con parametri
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/api/chart_data?x_column=data&type=bar"
                async with session.get(url) as response:
                    content = await response.text()
                    if response.status in [200, 400]:  # 400 è normale se non ci sono dati
                        self.log_test(test_name, "PASS", "API chart_data risponde correttamente")
                    else:
                        self.log_test(test_name, "FAIL", f"API chart_data: status {response.status}")

        except Exception as e:
            self.log_test(test_name, "FAIL", "Errore test generazione grafici", str(e))

    async def test_ai_chat_workflow(self):
        """Testa il workflow di chat AI."""
        test_name = "ai_chat_workflow"

        try:
            # Test modelli disponibili
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/models") as response:
                    if response.status == 200:
                        self.log_test(test_name, "PASS", "API modelli chat funzionante")
                    else:
                        self.log_test(test_name, "FAIL", f"API modelli: status {response.status}")

        except Exception as e:
            self.log_test(test_name, "FAIL", "Errore test chat AI", str(e))

    async def test_setup_wizard_workflow(self):
        """Testa il workflow del setup wizard."""
        test_name = "setup_wizard_workflow"

        try:
            # Test status wizard
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/wizard/status") as response:
                    if response.status == 200:
                        self.log_test(test_name, "PASS", "Setup wizard status funzionante")
                    else:
                        self.log_test(test_name, "FAIL", f"Wizard status: status {response.status}")

        except Exception as e:
            self.log_test(test_name, "FAIL", "Errore test setup wizard", str(e))

    async def test_agent_execution_workflow(self):
        """Testa il workflow di esecuzione agenti."""
        test_name = "agent_execution_workflow"

        try:
            # Test status agenti
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/agents/status") as response:
                    if response.status == 200:
                        self.log_test(test_name, "PASS", "Status agenti funzionante")
                    else:
                        self.log_test(test_name, "FAIL", f"Status agenti: status {response.status}")

        except Exception as e:
            self.log_test(test_name, "FAIL", "Errore test agenti", str(e))

    async def test_database_operations_workflow(self):
        """Testa il workflow delle operazioni database."""
        test_name = "database_operations_workflow"

        try:
            # Test status database
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/database/status") as response:
                    if response.status == 200:
                        self.log_test(test_name, "PASS", "Status database funzionante")
                    else:
                        self.log_test(test_name, "FAIL", f"Status database: status {response.status}")

        except Exception as e:
            self.log_test(test_name, "FAIL", "Errore test database", str(e))

    async def test_supabase_integration_workflow(self):
        """Testa il workflow di integrazione Supabase."""
        test_name = "supabase_integration_workflow"

        try:
            # Test config employees (usa Supabase)
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/config/employees") as response:
                    if response.status == 200:
                        content = await response.json()
                        if content.get('success'):
                            self.log_test(test_name, "PASS", "Integrazione Supabase funzionante")
                        else:
                            self.log_test(test_name, "WARN", "Supabase risponde ma con errori")
                    else:
                        self.log_test(test_name, "FAIL", f"Supabase integration: status {response.status}")

        except Exception as e:
            self.log_test(test_name, "FAIL", "Errore test Supabase", str(e))

    def analyze_minimal_mode_impact(self):
        """Analizza l'impatto della modalità minimal sui test."""
        print("\n🔍 ANALISI IMPATTO MODALITÀ MINIMAL")
        print("=" * 50)

        # Controlla output terminale per messaggi modalità minimal
        minimal_indicators = []

        try:
            while not self.terminal_output.empty():
                line = self.terminal_output.get_nowait()
                if any(keyword in line.lower() for keyword in ['minimal', 'disabilitato', 'disabled', 'skip']):
                    minimal_indicators.append(line)
        except:
            pass

        if minimal_indicators:
            print("⚠️ MODALITÀ MINIMAL ATTIVA - Sistemi disabilitati:")
            for indicator in minimal_indicators[:10]:  # Mostra solo i primi 10
                print(f"   📝 {indicator}")

            self.log_test("minimal_mode_analysis", "WARN",
                         f"Modalità minimal attiva - {len(minimal_indicators)} sistemi disabilitati")
        else:
            self.log_test("minimal_mode_analysis", "PASS", "Modalità minimal non rilevata o gestita correttamente")

    def generate_final_report(self):
        """Genera il report finale completo."""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()

        # Calcola statistiche
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results.values() if r['status'] == 'PASS')
        failed_tests = sum(1 for r in self.test_results.values() if r['status'] == 'FAIL')
        warning_tests = sum(1 for r in self.test_results.values() if r['status'] == 'WARN')

        # Genera report
        report = f"""
# 🧪 REPORT TESTING COMPLETO APP-ROBERTO

## 📊 STATISTICHE FINALI
- **Durata test**: {duration:.2f} secondi
- **Test totali**: {total_tests}
- **✅ PASSED**: {passed_tests} ({passed_tests/total_tests*100:.1f}%)
- **❌ FAILED**: {failed_tests} ({failed_tests/total_tests*100:.1f}%)
- **⚠️ WARNING**: {warning_tests} ({warning_tests/total_tests*100:.1f}%)

## 🎯 STATO SISTEMA
- **Operatività**: {passed_tests/total_tests*100:.1f}%
- **Stabilità**: {'ALTA' if failed_tests < 5 else 'MEDIA' if failed_tests < 10 else 'BASSA'}
- **Pronto per produzione**: {'✅ SÌ' if failed_tests < 3 else '❌ NO'}

## 🚨 ERRORI CRITICI TROVATI
"""

        if self.errors_found:
            for error in self.errors_found:
                report += f"- ❌ {error}\n"
        else:
            report += "- ✅ Nessun errore critico trovato\n"

        report += f"""
## ⚠️ WARNING TROVATI
"""

        if self.warnings_found:
            for warning in self.warnings_found:
                report += f"- ⚠️ {warning}\n"
        else:
            report += "- ✅ Nessun warning trovato\n"

        report += f"""
## 📋 DETTAGLI TEST
"""

        for test_name, result in self.test_results.items():
            status_icon = "✅" if result['status'] == "PASS" else "❌" if result['status'] == "FAIL" else "⚠️"
            report += f"- {status_icon} **{test_name}**: {result['status']}\n"
            if result['details']:
                report += f"  - 📝 {result['details']}\n"
            if result['error']:
                report += f"  - 🚨 {result['error']}\n"

        report += f"""
## 🎯 RACCOMANDAZIONI

### ✅ SISTEMA OPERATIVO
{'Il sistema è operativo e pronto per il lavoro produttivo.' if failed_tests < 3 else 'Il sistema richiede correzioni prima del lavoro produttivo.'}

### 🔧 AZIONI RICHIESTE
"""

        if failed_tests == 0:
            report += "- ✅ Nessuna azione richiesta - sistema completamente operativo\n"
        elif failed_tests < 3:
            report += "- 🔧 Correggere errori minori identificati\n"
        else:
            report += "- 🚨 Correggere errori critici prima di procedere\n"

        report += f"""
### 🚀 PROSSIMI PASSI
1. **Database quotidiano**: Implementare aggiornamenti automatici
2. **Analisi continue**: Configurare elaborazione dati automatica
3. **Monitoring**: Implementare monitoraggio produzione
4. **Backup**: Configurare backup automatici

---
**Report generato**: {end_time.isoformat()}
**Durata testing**: {duration:.2f} secondi
"""

        # Salva report
        with open("TESTING_REPORT_FINALE.md", "w", encoding="utf-8") as f:
            f.write(report)

        print("\n" + "="*60)
        print("🎉 TESTING COMPLETO TERMINATO")
        print("="*60)
        print(f"📊 Risultati: {passed_tests}/{total_tests} PASSED")
        print(f"🚨 Errori: {failed_tests}")
        print(f"⚠️ Warning: {warning_tests}")
        print(f"📄 Report salvato: TESTING_REPORT_FINALE.md")
        print("="*60)

    async def run_complete_testing_suite(self):
        """Esegue la suite completa di testing."""
        self.start_time = datetime.now()

        print("🧪 SISTEMA TESTING COMPLETO APP-ROBERTO")
        print("🎯 OBIETTIVO: Completare debugging per lavoro produttivo")
        print("="*60)

        # 1. Avvia app e monitora
        if not self.start_app_monitoring():
            print("❌ ERRORE CRITICO: Impossibile avviare l'app")
            return False

        # 2. Testa tutti gli endpoint
        await self.test_all_endpoints()

        # 3. Testa funzionalità core
        await self.test_core_functionalities()

        # 4. Analizza impatto modalità minimal
        self.analyze_minimal_mode_impact()

        # 5. Genera report finale
        self.generate_final_report()

        # 6. Ferma app
        self.stop_app()

        return True


async def main():
    """Funzione principale."""
    suite = AppRobertoTestingSuite()
    success = await suite.run_complete_testing_suite()

    if success:
        print("\n🎉 TESTING SUITE COMPLETATA CON SUCCESSO!")
        print("📋 Controlla TESTING_REPORT_FINALE.md per i dettagli completi")
    else:
        print("\n❌ TESTING SUITE FALLITA")
        print("🔧 Controlla i log per identificare i problemi")

    return success


if __name__ == "__main__":
    asyncio.run(main())
