
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ report.title }}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; line-height: 1.6; }
        .header { border-bottom: 3px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }
        .title { color: #007bff; font-size: 2.5em; margin: 0; }
        .subtitle { color: #6c757d; font-size: 1.2em; margin: 10px 0; }
        .section { margin: 30px 0; }
        .section h2 { color: #495057; border-left: 4px solid #007bff; padding-left: 15px; }
        .metrics { display: flex; justify-content: space-around; margin: 20px 0; }
        .metric { text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; }
        .metric-value { font-size: 2em; font-weight: bold; color: #007bff; }
        .metric-label { color: #6c757d; margin-top: 5px; }
        .recommendations { background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }
        .footer { margin-top: 50px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #6c757d; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="title">{{ report.title }}</h1>
        <p class="subtitle">Periodo: {{ report.period }}</p>
        <p class="subtitle">Generato il: {{ generated_at.strftime('%d/%m/%Y alle %H:%M') }}</p>
    </div>

    <div class="section">
        <h2>Riepilogo Esecutivo</h2>
        {{ report.metadata.narrative_content | markdown | safe }}
    </div>

    {% if report.analysis_results.global_summary %}
    <div class="section">
        <h2>Metriche Principali</h2>
        <div class="metrics">
            <div class="metric">
                <div class="metric-value">{{ report.analysis_results.global_summary.total_records_analyzed or 0 }}</div>
                <div class="metric-label">Record Analizzati</div>
            </div>
            <div class="metric">
                <div class="metric-value">{{ report.analysis_results.global_summary.discrepancies_found | length }}</div>
                <div class="metric-label">Discrepanze Trovate</div>
            </div>
            <div class="metric">
                <div class="metric-value">{{ report.analysis_results.global_summary.processing_time_ms }}ms</div>
                <div class="metric-label">Tempo Elaborazione</div>
            </div>
        </div>
    </div>
    {% endif %}

    {% if recommendations %}
    <div class="section">
        <h2>Raccomandazioni Principali</h2>
        <div class="recommendations">
            <ul>
            {% for rec in recommendations[:5] %}
                <li>{{ rec }}</li>
            {% endfor %}
            </ul>
        </div>
    </div>
    {% endif %}

    <div class="footer">
        <p>Report generato automaticamente dal Sistema di Riconoscimento Intelligente</p>
        <p>Generato da: {{ report.metadata.generated_by }}</p>
    </div>
</body>
</html>
        