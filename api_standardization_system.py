"""
Sistema di Standardizzazione API per App Roberto
Implementa best practices per allineamento frontend-backend
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from functools import wraps
from flask import jsonify, request

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class APIStandardizationSystem:
    """
    Sistema per standardizzare le risposte API e mappare gli endpoints
    secondo le best practices RESTful.
    """
    
    def __init__(self):
        """Inizializza il sistema di standardizzazione API."""
        self.endpoints_registry = {}
        self.response_schemas = {}
        self.error_codes = self._load_error_codes()
        
        logger.info("APIStandardizationSystem inizializzato")
    
    def _load_error_codes(self) -> Dict[str, Dict[str, Any]]:
        """Carica i codici di errore standardizzati."""
        return {
            # Client Errors (4xx)
            'BAD_REQUEST': {
                'code': 400,
                'type': 'client_error',
                'message': 'Richiesta non valida',
                'description': 'La richiesta contiene parametri non validi o mancanti'
            },
            'UNAUTHORIZED': {
                'code': 401,
                'type': 'client_error',
                'message': 'Non autorizzato',
                'description': 'Autenticazione richiesta o non valida'
            },
            'FORBIDDEN': {
                'code': 403,
                'type': 'client_error',
                'message': 'Accesso negato',
                'description': 'Non hai i permessi per accedere a questa risorsa'
            },
            'NOT_FOUND': {
                'code': 404,
                'type': 'client_error',
                'message': 'Risorsa non trovata',
                'description': 'La risorsa richiesta non esiste'
            },
            'METHOD_NOT_ALLOWED': {
                'code': 405,
                'type': 'client_error',
                'message': 'Metodo non consentito',
                'description': 'Il metodo HTTP non è supportato per questa risorsa'
            },
            'CONFLICT': {
                'code': 409,
                'type': 'client_error',
                'message': 'Conflitto',
                'description': 'La richiesta è in conflitto con lo stato attuale della risorsa'
            },
            'VALIDATION_ERROR': {
                'code': 422,
                'type': 'client_error',
                'message': 'Errore di validazione',
                'description': 'I dati forniti non superano la validazione'
            },
            
            # Server Errors (5xx)
            'INTERNAL_ERROR': {
                'code': 500,
                'type': 'server_error',
                'message': 'Errore interno del server',
                'description': 'Si è verificato un errore interno'
            },
            'SERVICE_UNAVAILABLE': {
                'code': 503,
                'type': 'server_error',
                'message': 'Servizio non disponibile',
                'description': 'Il servizio è temporaneamente non disponibile'
            },
            'GATEWAY_TIMEOUT': {
                'code': 504,
                'type': 'server_error',
                'message': 'Timeout del gateway',
                'description': 'Timeout nella comunicazione con servizi esterni'
            }
        }
    
    def create_standard_response(self, 
                                success: bool = True,
                                data: Any = None,
                                error: Optional[str] = None,
                                error_code: Optional[str] = None,
                                message: Optional[str] = None,
                                meta: Optional[Dict[str, Any]] = None,
                                status_code: int = 200) -> Dict[str, Any]:
        """
        Crea una risposta API standardizzata secondo le best practices.
        
        Args:
            success: Indica se l'operazione è riuscita
            data: Dati da restituire
            error: Messaggio di errore
            error_code: Codice di errore standardizzato
            message: Messaggio informativo
            meta: Metadati aggiuntivi (paginazione, etc.)
            status_code: Codice di stato HTTP
            
        Returns:
            Dict con la risposta standardizzata
        """
        response = {
            'success': success,
            'timestamp': datetime.now().isoformat(),
            'api_version': '1.0.0'
        }
        
        # Aggiungi dati se presenti
        if data is not None:
            response['data'] = data
        
        # Gestione errori standardizzata
        if not success or error:
            response['success'] = False
            
            if error_code and error_code in self.error_codes:
                error_info = self.error_codes[error_code]
                response['error'] = {
                    'code': error_code,
                    'type': error_info['type'],
                    'message': error or error_info['message'],
                    'description': error_info['description'],
                    'status_code': status_code or error_info['code']
                }
            else:
                response['error'] = {
                    'code': 'GENERIC_ERROR',
                    'type': 'unknown',
                    'message': error or 'Si è verificato un errore',
                    'status_code': status_code
                }
        
        # Aggiungi messaggio se presente
        if message:
            response['message'] = message
        
        # Aggiungi metadati se presenti
        if meta:
            response['meta'] = meta
        
        return response
    
    def create_paginated_response(self,
                                 items: List[Any],
                                 page: int = 1,
                                 page_size: int = 10,
                                 total_items: int = 0,
                                 total_pages: int = 0,
                                 has_next: bool = False,
                                 has_prev: bool = False,
                                 message: Optional[str] = None) -> Dict[str, Any]:
        """
        Crea una risposta paginata standardizzata.
        
        Args:
            items: Lista degli elementi della pagina corrente
            page: Numero della pagina corrente
            page_size: Dimensione della pagina
            total_items: Numero totale di elementi
            total_pages: Numero totale di pagine
            has_next: Se esiste una pagina successiva
            has_prev: Se esiste una pagina precedente
            message: Messaggio opzionale
            
        Returns:
            Dict con la risposta paginata standardizzata
        """
        pagination_meta = {
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_items': total_items,
                'total_pages': total_pages,
                'has_next': has_next,
                'has_prev': has_prev,
                'first_page': 1,
                'last_page': total_pages,
                'previous_page': page - 1 if has_prev else None,
                'next_page': page + 1 if has_next else None
            }
        }
        
        return self.create_standard_response(
            success=True,
            data=items,
            message=message,
            meta=pagination_meta
        )
    
    def register_endpoint(self, 
                         path: str,
                         method: str,
                         description: str,
                         request_schema: Optional[Dict[str, Any]] = None,
                         response_schema: Optional[Dict[str, Any]] = None,
                         tags: Optional[List[str]] = None) -> None:
        """
        Registra un endpoint nell'API registry.
        
        Args:
            path: Percorso dell'endpoint
            method: Metodo HTTP
            description: Descrizione dell'endpoint
            request_schema: Schema della richiesta
            response_schema: Schema della risposta
            tags: Tag per categorizzare l'endpoint
        """
        endpoint_key = f"{method.upper()}:{path}"
        
        self.endpoints_registry[endpoint_key] = {
            'path': path,
            'method': method.upper(),
            'description': description,
            'request_schema': request_schema,
            'response_schema': response_schema,
            'tags': tags or [],
            'registered_at': datetime.now().isoformat()
        }
        
        logger.info(f"Endpoint registrato: {endpoint_key}")
    
    def get_endpoints_map(self) -> Dict[str, Any]:
        """
        Restituisce la mappa completa degli endpoints registrati.
        
        Returns:
            Dict con tutti gli endpoints registrati
        """
        return {
            'total_endpoints': len(self.endpoints_registry),
            'endpoints': self.endpoints_registry,
            'generated_at': datetime.now().isoformat()
        }
    
    def validate_request_data(self, data: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        Valida i dati della richiesta contro uno schema.
        
        Args:
            data: Dati da validare
            schema: Schema di validazione
            
        Returns:
            Dict con il risultato della validazione
        """
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        required_fields = schema.get('required', [])
        properties = schema.get('properties', {})
        
        # Verifica campi obbligatori
        for field in required_fields:
            if field not in data:
                validation_result['valid'] = False
                validation_result['errors'].append(f"Campo obbligatorio mancante: {field}")
        
        # Verifica tipi di dati
        for field, value in data.items():
            if field in properties:
                expected_type = properties[field].get('type')
                if expected_type and not self._validate_type(value, expected_type):
                    validation_result['valid'] = False
                    validation_result['errors'].append(f"Tipo non valido per {field}: atteso {expected_type}")
        
        return validation_result
    
    def _validate_type(self, value: Any, expected_type: str) -> bool:
        """Valida il tipo di un valore."""
        type_mapping = {
            'string': str,
            'integer': int,
            'number': (int, float),
            'boolean': bool,
            'array': list,
            'object': dict
        }
        
        expected_python_type = type_mapping.get(expected_type)
        if expected_python_type:
            return isinstance(value, expected_python_type)
        
        return True
    
    def create_error_response(self, error_code: str, custom_message: Optional[str] = None) -> Dict[str, Any]:
        """
        Crea una risposta di errore standardizzata.
        
        Args:
            error_code: Codice di errore standardizzato
            custom_message: Messaggio personalizzato
            
        Returns:
            Dict con la risposta di errore
        """
        if error_code not in self.error_codes:
            error_code = 'INTERNAL_ERROR'
        
        error_info = self.error_codes[error_code]
        
        return self.create_standard_response(
            success=False,
            error=custom_message or error_info['message'],
            error_code=error_code,
            status_code=error_info['code']
        )

# Decoratore per standardizzare le risposte API
def standardize_api_response(api_system: APIStandardizationSystem):
    """
    Decoratore per standardizzare automaticamente le risposte API.
    
    Args:
        api_system: Istanza del sistema di standardizzazione
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # Esegui la funzione originale
                result = func(*args, **kwargs)
                
                # Se il risultato è già una Response Flask, restituiscilo così com'è
                if hasattr(result, 'status_code'):
                    return result
                
                # Se il risultato è un tuple (data, status_code)
                if isinstance(result, tuple) and len(result) == 2:
                    data, status_code = result
                    if isinstance(data, dict) and 'success' in data:
                        # Già standardizzato
                        return jsonify(data), status_code
                    else:
                        # Standardizza
                        response = api_system.create_standard_response(
                            success=True,
                            data=data,
                            status_code=status_code
                        )
                        return jsonify(response), status_code
                
                # Se il risultato è un dict
                if isinstance(result, dict):
                    if 'success' in result:
                        # Già standardizzato
                        return jsonify(result)
                    else:
                        # Standardizza
                        response = api_system.create_standard_response(
                            success=True,
                            data=result
                        )
                        return jsonify(response)
                
                # Per altri tipi di risultato
                response = api_system.create_standard_response(
                    success=True,
                    data=result
                )
                return jsonify(response)
                
            except Exception as e:
                logger.error(f"Errore in {func.__name__}: {str(e)}")
                error_response = api_system.create_error_response(
                    'INTERNAL_ERROR',
                    f"Errore interno: {str(e)}"
                )
                return jsonify(error_response), 500
        
        return wrapper
    return decorator

# Istanza globale del sistema di standardizzazione
api_standardization = APIStandardizationSystem()

# Funzioni helper per compatibilità con il codice esistente
def create_api_response(success=True, data=None, error=None, message=None, status_code=200, error_code=None, meta=None):
    """
    Funzione helper per mantenere compatibilità con il codice esistente.
    """
    return api_standardization.create_standard_response(
        success=success,
        data=data,
        error=error,
        error_code=error_code,
        message=message,
        meta=meta,
        status_code=status_code
    ), status_code

def create_paginated_response(items, page=1, page_size=10, total_items=0, total_pages=0, has_next=False, has_prev=False, message=None):
    """
    Funzione helper per creare risposte paginate.
    """
    return api_standardization.create_paginated_response(
        items=items,
        page=page,
        page_size=page_size,
        total_items=total_items,
        total_pages=total_pages,
        has_next=has_next,
        has_prev=has_prev,
        message=message
    ), 200
