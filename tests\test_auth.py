#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test per il modulo auth.py.
"""

import os
import pytest
import jwt
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock
from flask import Flask, jsonify, g

from auth import User, get_user_by_username, generate_token, verify_token, login_required, admin_required

class TestUser:
    """Test per la classe User."""
    
    def test_init(self):
        """Test per il costruttore."""
        user = User(1, "test", "hash", "user")
        assert user.id == 1
        assert user.username == "test"
        assert user.password_hash == "hash"
        assert user.role == "user"
    
    def test_check_password(self):
        """Test per il metodo check_password."""
        # Crea un utente con una password hashata
        password = "password"
        password_hash = User.hash_password(password)
        user = User(1, "test", password_hash, "user")
        
        # Verifica che la password corretta sia accettata
        assert user.check_password(password) == True
        
        # Verifica che una password errata sia rifiutata
        assert user.check_password("wrong_password") == False
    
    def test_hash_password(self):
        """Test per il metodo hash_password."""
        password = "password"
        password_hash = User.hash_password(password)
        
        # Verifica che l'hash non sia uguale alla password originale
        assert password_hash != password
        
        # Verifica che l'hash sia una stringa
        assert isinstance(password_hash, str)
        
        # Verifica che l'hash sia diverso per password diverse
        password2 = "password2"
        password_hash2 = User.hash_password(password2)
        assert password_hash != password_hash2

class TestAuth:
    """Test per le funzioni di autenticazione."""
    
    def test_get_user_by_username(self):
        """Test per la funzione get_user_by_username."""
        # Verifica che un utente esistente sia trovato
        user = get_user_by_username("admin")
        assert user is not None
        assert user.username == "admin"
        assert user.role == "admin"
        
        # Verifica che un utente non esistente non sia trovato
        user = get_user_by_username("non_esistente")
        assert user is None
    
    def test_generate_token(self):
        """Test per la funzione generate_token."""
        # Genera un token
        token = generate_token(1, "test", "user", 3600)
        
        # Verifica che il token sia una stringa
        assert isinstance(token, str)
        
        # Decodifica il token e verifica il payload
        secret_key = os.environ.get("JWT_SECRET_KEY", "default_secret_key")
        payload = jwt.decode(token, secret_key, algorithms=["HS256"])
        
        assert payload["user_id"] == 1
        assert payload["username"] == "test"
        assert payload["role"] == "user"
        
        # Verifica che il token scada dopo il tempo specificato
        exp_time = datetime.utcfromtimestamp(payload["exp"])
        now = datetime.utcnow()
        assert exp_time > now
        assert exp_time < now + timedelta(seconds=3601)
    
    def test_verify_token_valid(self):
        """Test per la funzione verify_token con un token valido."""
        # Genera un token
        token = generate_token(1, "test", "user", 3600)
        
        # Verifica il token
        payload = verify_token(token)
        
        # Verifica che il payload sia corretto
        assert payload is not None
        assert payload["user_id"] == 1
        assert payload["username"] == "test"
        assert payload["role"] == "user"
    
    def test_verify_token_expired(self):
        """Test per la funzione verify_token con un token scaduto."""
        # Genera un token scaduto
        payload = {
            "user_id": 1,
            "username": "test",
            "role": "user",
            "exp": datetime.utcnow() - timedelta(seconds=1)
        }
        
        secret_key = os.environ.get("JWT_SECRET_KEY", "default_secret_key")
        token = jwt.encode(payload, secret_key, algorithm="HS256")
        
        # Verifica il token
        result = verify_token(token)
        
        # Verifica che il risultato sia None
        assert result is None
    
    def test_verify_token_invalid(self):
        """Test per la funzione verify_token con un token non valido."""
        # Verifica un token non valido
        result = verify_token("token_non_valido")
        
        # Verifica che il risultato sia None
        assert result is None

class TestDecorators:
    """Test per i decoratori di autenticazione."""
    
    def setup_method(self):
        """Setup per i test."""
        # Crea un'app Flask di test
        self.app = Flask(__name__)
        
        # Definisci una route protetta con login_required
        @self.app.route("/protected")
        @login_required
        def protected():
            return jsonify({"message": "Accesso consentito", "user": g.username})
        
        # Definisci una route protetta con admin_required
        @self.app.route("/admin")
        @admin_required
        def admin():
            return jsonify({"message": "Accesso consentito", "user": g.username})
        
        # Crea un client di test
        self.client = self.app.test_client()
    
    def test_login_required_no_token(self):
        """Test per il decoratore login_required senza token."""
        # Effettua una richiesta senza token
        response = self.client.get("/protected")
        
        # Verifica che la risposta sia un errore 401
        assert response.status_code == 401
        assert response.json["error"] == "Token mancante"
    
    def test_login_required_invalid_token(self):
        """Test per il decoratore login_required con token non valido."""
        # Effettua una richiesta con un token non valido
        response = self.client.get("/protected", headers={"Authorization": "Bearer token_non_valido"})
        
        # Verifica che la risposta sia un errore 401
        assert response.status_code == 401
        assert response.json["error"] == "Token non valido o scaduto"
    
    def test_login_required_valid_token(self):
        """Test per il decoratore login_required con token valido."""
        # Genera un token valido
        token = generate_token(1, "test", "user", 3600)
        
        # Effettua una richiesta con il token valido
        response = self.client.get("/protected", headers={"Authorization": f"Bearer {token}"})
        
        # Verifica che la risposta sia un successo
        assert response.status_code == 200
        assert response.json["message"] == "Accesso consentito"
        assert response.json["user"] == "test"
    
    def test_admin_required_user_token(self):
        """Test per il decoratore admin_required con token di un utente normale."""
        # Genera un token per un utente normale
        token = generate_token(2, "user", "user", 3600)
        
        # Effettua una richiesta con il token
        response = self.client.get("/admin", headers={"Authorization": f"Bearer {token}"})
        
        # Verifica che la risposta sia un errore 403
        assert response.status_code == 403
        assert response.json["error"] == "Accesso negato"
    
    def test_admin_required_admin_token(self):
        """Test per il decoratore admin_required con token di un amministratore."""
        # Genera un token per un amministratore
        token = generate_token(1, "admin", "admin", 3600)
        
        # Effettua una richiesta con il token
        response = self.client.get("/admin", headers={"Authorization": f"Bearer {token}"})
        
        # Verifica che la risposta sia un successo
        assert response.status_code == 200
        assert response.json["message"] == "Accesso consentito"
        assert response.json["user"] == "admin"
