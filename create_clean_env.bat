@echo off
setlocal enabledelayedexpansion
echo ===================================
echo Creazione di un ambiente virtuale pulito
echo ===================================
echo.

REM Definisci il nome del nuovo ambiente
set NEW_ENV=clean_env

echo Questo script creerà un nuovo ambiente virtuale chiamato "%NEW_ENV%"
echo senza toccare l'ambiente "venv" esistente.
echo.
set /p CONFIRM=Vuoi procedere? (S/N):
if /i not "%CONFIRM%"=="S" (
    echo Operazione annullata.
    goto :EOF
)

REM Crea il nuovo ambiente virtuale
echo.
echo Creazione del nuovo ambiente virtuale "%NEW_ENV%"...
python -m venv %NEW_ENV%
if %errorlevel% neq 0 (
    echo ERRORE: Impossibile creare il nuovo ambiente virtuale.
    goto :EOF
)
echo Nuovo ambiente virtuale creato con successo.
echo.

REM Attiva il nuovo ambiente
echo Attivazione del nuovo ambiente...
call %NEW_ENV%\Scripts\activate
if %errorlevel% neq 0 (
    echo ERRORE: Impossibile attivare il nuovo ambiente.
    goto :EOF
)
echo Ambiente attivato.
echo.

REM Aggiorna pip all'ultima versione
echo Aggiornamento di pip...
python -m pip install --upgrade pip
echo.

REM Installa le dipendenze di base direttamente (senza usare requirements.txt)
echo Installazione delle dipendenze di base...
pip install python-dotenv fastapi flask pandas cachelib
echo.

REM Installa plotly con un approccio diverso
echo Installazione di plotly con approccio alternativo...
pip install --no-deps plotly==5.16.1
pip install tenacity packaging
echo.

REM Verifica l'installazione di plotly
echo Verifica dell'installazione di plotly...
python -c "import plotly; print(f'Plotly {plotly.__version__} installato correttamente')"
if %errorlevel% neq 0 (
    echo ERRORE: Verifica di plotly fallita.
) else (
    echo Plotly verificato con successo.
)
echo.

REM Installa le altre dipendenze dal requirements.txt, escludendo plotly
echo Installazione delle altre dipendenze...
pip install -r requirements.txt --no-deps
echo.

echo ===================================
echo Ambiente "%NEW_ENV%" creato con successo!
echo.
echo Per utilizzare questo nuovo ambiente:
echo   call %NEW_ENV%\Scripts\activate
echo.
echo Per aggiornare gli script di avvio esistenti:
echo   1. Modifica start_app.bat, start_app_with_env.bat, ecc.
echo   2. Sostituisci "venv" con "%NEW_ENV%" in tutti i percorsi
echo ===================================

pause
