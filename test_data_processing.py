#!/usr/bin/env python3
"""
Test completo per identificare errori nell'elaborazione dati
"""

import pandas as pd
import sys
import os
import traceback
from datetime import datetime

# Aggiungi il percorso corrente per importare i moduli
sys.path.append('.')

from enhanced_file_detector import EnhancedFileDetector
from teamviewer_processor import TeamViewerProcessor
from calendar_processor import CalendarProcessor
from attendance_processor import AttendanceProcessor
from vehicle_registry_processor import VehicleRegistryProcessor

def test_data_processing():
    """Testa l'elaborazione completa dei dati per ogni tipo di file"""
    
    print("🔧 Test completo elaborazione dati...")
    
    # Inizializza i componenti
    enhanced_detector = EnhancedFileDetector()
    teamviewer_processor = TeamViewerProcessor()
    calendar_processor = CalendarProcessor()
    attendance_processor = AttendanceProcessor()
    vehicle_processor = VehicleRegistryProcessor()
    
    # Test 1: TeamViewer con colonne inglesi
    print("\n🖥️ Test 1: TeamViewer (colonne inglesi)")
    try:
        df_teamviewer = pd.read_csv('test_raw_file.csv')
        print(f"✅ File caricato: {df_teamviewer.shape[0]} righe, {df_teamviewer.shape[1]} colonne")
        print(f"Colonne: {df_teamviewer.columns.tolist()}")
        
        # Rilevamento tipo
        detected_type, confidence, scores = enhanced_detector.detect_file_type(df_teamviewer)
        print(f"✅ Tipo rilevato: {detected_type} (confidenza: {confidence:.3f})")
        
        # Test elaborazione
        print("🔄 Elaborazione con TeamViewerProcessor...")
        processed_df = teamviewer_processor.process_teamviewer_file('test_raw_file.csv')
        print(f"✅ Elaborazione completata: {processed_df.shape[0]} righe elaborate")
        print(f"Colonne elaborate: {processed_df.columns.tolist()}")
        
        # Test statistiche
        print("📊 Generazione statistiche...")
        stats = teamviewer_processor.generate_summary_stats(processed_df)
        print(f"✅ Statistiche generate: {len(stats)} metriche")
        for key, value in stats.items():
            print(f"  - {key}: {value}")
        
    except Exception as e:
        print(f"❌ ERRORE TeamViewer: {e}")
        print(f"Traceback: {traceback.format_exc()}")
    
    # Test 2: Registro Auto
    print("\n🚗 Test 2: Registro Auto")
    try:
        df_auto = pd.read_csv('test_registro_auto.csv')
        print(f"✅ File caricato: {df_auto.shape[0]} righe, {df_auto.shape[1]} colonne")
        print(f"Colonne: {df_auto.columns.tolist()}")
        
        # Rilevamento tipo
        detected_type, confidence, scores = enhanced_detector.detect_file_type(df_auto)
        print(f"✅ Tipo rilevato: {detected_type} (confidenza: {confidence:.3f})")
        
        # Test elaborazione
        print("🔄 Elaborazione con VehicleRegistryProcessor...")
        processed_df = vehicle_processor.process_vehicle_registry_file('test_registro_auto.csv')
        print(f"✅ Elaborazione completata: {processed_df.shape[0]} righe elaborate")
        print(f"Colonne elaborate: {processed_df.columns.tolist()}")
        
        # Test statistiche
        print("📊 Generazione statistiche...")
        stats = vehicle_processor.generate_summary_stats(processed_df)
        print(f"✅ Statistiche generate: {len(stats)} metriche")
        for key, value in stats.items():
            print(f"  - {key}: {value}")
        
    except Exception as e:
        print(f"❌ ERRORE Registro Auto: {e}")
        print(f"Traceback: {traceback.format_exc()}")
    
    # Test 3: Calendario simulato
    print("\n📅 Test 3: Calendario")
    try:
        calendar_data = {
            'Data': ['2025-01-15', '2025-01-16', '2025-01-17'],
            'Ora inizio': ['09:00', '14:00', '10:30'],
            'Ora fine': ['10:00', '15:30', '12:00'],
            'Titolo': ['Meeting 1', 'Conference Call', 'Training'],
            'Luogo': ['Room A', 'Online', 'Room B']
        }
        
        df_calendar = pd.DataFrame(calendar_data)
        print(f"✅ File creato: {df_calendar.shape[0]} righe, {df_calendar.shape[1]} colonne")
        print(f"Colonne: {df_calendar.columns.tolist()}")
        
        # Rilevamento tipo
        detected_type, confidence, scores = enhanced_detector.detect_file_type(df_calendar)
        print(f"✅ Tipo rilevato: {detected_type} (confidenza: {confidence:.3f})")
        
        # Salva temporaneamente per test
        df_calendar.to_csv('temp_calendar.csv', index=False)
        
        # Test elaborazione
        print("🔄 Elaborazione con CalendarProcessor...")
        processed_df = calendar_processor.process_calendar_file('temp_calendar.csv')
        print(f"✅ Elaborazione completata: {processed_df.shape[0]} righe elaborate")
        print(f"Colonne elaborate: {processed_df.columns.tolist()}")
        
        # Test statistiche
        print("📊 Generazione statistiche...")
        stats = calendar_processor.generate_summary_stats(processed_df)
        print(f"✅ Statistiche generate: {len(stats)} metriche")
        for key, value in stats.items():
            print(f"  - {key}: {value}")
        
        # Pulisci file temporaneo
        os.remove('temp_calendar.csv')
        
    except Exception as e:
        print(f"❌ ERRORE Calendario: {e}")
        print(f"Traceback: {traceback.format_exc()}")
    
    # Test 4: Timbrature simulate
    print("\n⏰ Test 4: Timbrature")
    try:
        attendance_data = {
            'Data': ['2025-01-15', '2025-01-16', '2025-01-17'],
            'Dipendente': ['John Doe', 'Jane Smith', 'Mike Wilson'],
            'Entrata': ['08:00', '08:30', '09:00'],
            'Uscita': ['17:00', '17:30', '18:00'],
            'Ore lavorate': ['8.0', '8.5', '8.0']
        }
        
        df_attendance = pd.DataFrame(attendance_data)
        print(f"✅ File creato: {df_attendance.shape[0]} righe, {df_attendance.shape[1]} colonne")
        print(f"Colonne: {df_attendance.columns.tolist()}")
        
        # Rilevamento tipo
        detected_type, confidence, scores = enhanced_detector.detect_file_type(df_attendance)
        print(f"✅ Tipo rilevato: {detected_type} (confidenza: {confidence:.3f})")
        
        # Salva temporaneamente per test
        df_attendance.to_csv('temp_attendance.csv', index=False)
        
        # Test elaborazione
        print("🔄 Elaborazione con AttendanceProcessor...")
        processed_df = attendance_processor.process_attendance_file('temp_attendance.csv')
        print(f"✅ Elaborazione completata: {processed_df.shape[0]} righe elaborate")
        print(f"Colonne elaborate: {processed_df.columns.tolist()}")
        
        # Test statistiche
        print("📊 Generazione statistiche...")
        stats = attendance_processor.generate_summary_stats(processed_df)
        print(f"✅ Statistiche generate: {len(stats)} metriche")
        for key, value in stats.items():
            print(f"  - {key}: {value}")
        
        # Pulisci file temporaneo
        os.remove('temp_attendance.csv')
        
    except Exception as e:
        print(f"❌ ERRORE Timbrature: {e}")
        print(f"Traceback: {traceback.format_exc()}")
    
    print("\n✅ Test elaborazione dati completati!")

if __name__ == "__main__":
    test_data_processing()
