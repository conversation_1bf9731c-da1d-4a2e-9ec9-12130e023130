#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per creare la tabella onboarding_progress in Supabase
Esegue automaticamente lo script SQL tramite l'API REST di Supabase
"""

import os
import sys
import logging
import requests
from dotenv import load_dotenv

# Carica variabili d'ambiente
load_dotenv()

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_onboarding_table():
    """Crea la tabella onboarding_progress in Supabase."""
    
    # Ottieni credenziali Supabase
    supabase_url = os.environ.get("SUPABASE_URL")
    supabase_service_key = os.environ.get("SUPABASE_SERVICE_KEY")
    
    if not supabase_url or not supabase_service_key:
        logger.error("❌ Variabili d'ambiente SUPABASE_URL e SUPABASE_SERVICE_KEY richieste")
        return False
    
    # SQL per creare la tabella
    sql_script = """
    -- Crea la tabella onboarding_progress se non esiste
    CREATE TABLE IF NOT EXISTS onboarding_progress (
        id SERIAL PRIMARY KEY,
        uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
        user_id VARCHAR(100) NOT NULL,
        progress_data JSONB NOT NULL DEFAULT '{}',
        current_step INTEGER DEFAULT 1,
        completed_steps INTEGER[] DEFAULT '{}',
        business_type VARCHAR(50) DEFAULT 'generic',
        confidence_score DECIMAL(5,2) DEFAULT 0.0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
    );

    -- Crea indici per migliorare le performance
    CREATE INDEX IF NOT EXISTS idx_onboarding_progress_user_id ON onboarding_progress(user_id);
    CREATE INDEX IF NOT EXISTS idx_onboarding_progress_business_type ON onboarding_progress(business_type);
    CREATE INDEX IF NOT EXISTS idx_onboarding_progress_created_at ON onboarding_progress(created_at);

    -- Crea trigger per aggiornare automaticamente updated_at
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
    END;
    $$ language 'plpgsql';

    -- Applica il trigger alla tabella
    DROP TRIGGER IF EXISTS update_onboarding_progress_updated_at ON onboarding_progress;
    CREATE TRIGGER update_onboarding_progress_updated_at
        BEFORE UPDATE ON onboarding_progress
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();

    -- Aggiungi commenti per documentazione
    COMMENT ON TABLE onboarding_progress IS 'Tabella per salvare il progresso del wizard di onboarding intelligente';
    COMMENT ON COLUMN onboarding_progress.user_id IS 'ID utente o session ID';
    COMMENT ON COLUMN onboarding_progress.progress_data IS 'Dati completi del progresso in formato JSON';
    COMMENT ON COLUMN onboarding_progress.current_step IS 'Step corrente del wizard (1-5)';
    COMMENT ON COLUMN onboarding_progress.completed_steps IS 'Array degli step completati';
    COMMENT ON COLUMN onboarding_progress.business_type IS 'Tipo di business rilevato (generic, it_services, consulting, etc.)';
    COMMENT ON COLUMN onboarding_progress.confidence_score IS 'Punteggio di confidenza del rilevamento (0.0-1.0)';
    """
    
    try:
        # Usa l'API REST di Supabase per eseguire SQL
        url = f"{supabase_url}/rest/v1/rpc/exec_sql"
        headers = {
            "apikey": supabase_service_key,
            "Authorization": f"Bearer {supabase_service_key}",
            "Content-Type": "application/json"
        }
        
        # Prova prima con l'endpoint RPC
        logger.info("🔄 Tentativo creazione tabella tramite API REST...")
        
        # Metodo alternativo: usa il client Python Supabase
        try:
            from supabase import create_client
            
            client = create_client(supabase_url, supabase_service_key)
            
            # Esegui ogni statement SQL separatamente
            sql_statements = [
                """CREATE TABLE IF NOT EXISTS onboarding_progress (
                    id SERIAL PRIMARY KEY,
                    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
                    user_id VARCHAR(100) NOT NULL,
                    progress_data JSONB NOT NULL DEFAULT '{}',
                    current_step INTEGER DEFAULT 1,
                    completed_steps INTEGER[] DEFAULT '{}',
                    business_type VARCHAR(50) DEFAULT 'generic',
                    confidence_score DECIMAL(5,2) DEFAULT 0.0,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                )""",
                
                "CREATE INDEX IF NOT EXISTS idx_onboarding_progress_user_id ON onboarding_progress(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_onboarding_progress_business_type ON onboarding_progress(business_type)",
                "CREATE INDEX IF NOT EXISTS idx_onboarding_progress_created_at ON onboarding_progress(created_at)",
                
                """CREATE OR REPLACE FUNCTION update_updated_at_column()
                RETURNS TRIGGER AS $$
                BEGIN
                    NEW.updated_at = CURRENT_TIMESTAMP;
                    RETURN NEW;
                END;
                $$ language 'plpgsql'""",
                
                """DROP TRIGGER IF EXISTS update_onboarding_progress_updated_at ON onboarding_progress;
                CREATE TRIGGER update_onboarding_progress_updated_at
                    BEFORE UPDATE ON onboarding_progress
                    FOR EACH ROW
                    EXECUTE FUNCTION update_updated_at_column()"""
            ]
            
            logger.info("📋 Esecuzione script SQL tramite client Python...")
            
            # Nota: Il client Python Supabase non supporta direttamente l'esecuzione di SQL DDL
            # Dobbiamo usare un approccio diverso
            
            # Verifica se la tabella esiste già
            try:
                result = client.table("onboarding_progress").select("id").limit(1).execute()
                logger.info("✅ Tabella onboarding_progress già esistente")
                return True
            except Exception:
                logger.info("📝 Tabella onboarding_progress non esiste, deve essere creata manualmente")
                
                # Mostra istruzioni per la creazione manuale
                print("\n" + "="*80)
                print("🔧 ISTRUZIONI PER CREARE LA TABELLA MANUALMENTE:")
                print("="*80)
                print("1. Vai su: https://app.supabase.com/project/zqjllwxqjxjhdkbcawfr/sql")
                print("2. Copia e incolla il seguente script SQL:")
                print("="*80)
                print(sql_script)
                print("="*80)
                print("3. Clicca 'Run' per eseguire lo script")
                print("4. Riavvia questo script per verificare")
                print("="*80)
                
                return False
                
        except ImportError:
            logger.error("❌ Supabase client non disponibile")
            return False
            
    except Exception as e:
        logger.error(f"❌ Errore durante la creazione della tabella: {str(e)}")
        return False

def verify_table_creation():
    """Verifica che la tabella sia stata creata correttamente."""
    try:
        from supabase import create_client
        
        supabase_url = os.environ.get("SUPABASE_URL")
        supabase_service_key = os.environ.get("SUPABASE_SERVICE_KEY")
        
        if not supabase_url or not supabase_service_key:
            return False
            
        client = create_client(supabase_url, supabase_service_key)
        
        # Prova a fare una query sulla tabella
        result = client.table("onboarding_progress").select("id").limit(1).execute()
        
        logger.info("✅ Tabella onboarding_progress verificata con successo")
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore verifica tabella: {str(e)}")
        return False

def main():
    """Funzione principale."""
    print("🚀 Creazione tabella onboarding_progress in Supabase")
    print("="*60)
    
    # Verifica se la tabella esiste già
    if verify_table_creation():
        print("✅ Tabella onboarding_progress già esistente e funzionante")
        return True
    
    # Tenta di creare la tabella
    success = create_onboarding_table()
    
    if success:
        print("✅ Tabella creata con successo")
    else:
        print("❌ Creazione tabella fallita - seguire le istruzioni manuali")
    
    return success

if __name__ == "__main__":
    main()
