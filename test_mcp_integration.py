#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test integrazione MCP con l'app principale.
Verifica che il problema di timeout sia risolto.
"""

import sys
import os
import requests
import time
from datetime import datetime

# Aggiungi il percorso del progetto
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mcp_integration():
    """Testa l'integrazione MCP con l'app principale."""
    print("🔗 TEST INTEGRAZIONE MCP CON APP PRINCIPALE")
    print("=" * 50)

    # Test 1: Verifica che entrambi i server siano attivi
    print("\n1. 🌐 Verifica Server Attivi...")

    # Test App principale
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        app_status = response.status_code == 200
        print(f"   {'✅' if app_status else '❌'} App Principale: {response.status_code}")
    except Exception as e:
        app_status = False
        print(f"   ❌ App Principale: {str(e)}")

    # Test Server MCP
    try:
        response = requests.get('http://127.0.0.1:8001', timeout=5)
        mcp_status = response.status_code == 200
        print(f"   {'✅' if mcp_status else '❌'} Server MCP: {response.status_code}")
    except Exception as e:
        mcp_status = False
        print(f"   ❌ Server MCP: {str(e)}")

    if not (app_status and mcp_status):
        print("\n❌ Uno o entrambi i server non sono attivi!")
        return False

    # Test 2: Test Client MCP dall'app
    print("\n2. 🔌 Test Client MCP dall'App...")
    try:
        from mcp_client import MCPClient

        # Crea client con nuova configurazione
        mcp_client = MCPClient(base_url='http://127.0.0.1:8001', max_retries=2, timeout=10)

        print(f"   ✅ Client MCP creato: {mcp_client.base_url}")
        print(f"   ⏱️ Timeout: {mcp_client.timeout}s")
        print(f"   🔄 Max Retries: {mcp_client.max_retries}")

    except Exception as e:
        print(f"   ❌ Errore creazione client: {str(e)}")
        return False

    # Test 3: Test connessione diretta
    print("\n3. 🎯 Test Connessione Diretta...")
    try:
        # Test endpoint health del server MCP
        response = requests.get('http://127.0.0.1:8001/health', timeout=10)

        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Health Check MCP: {response.status_code}")
            print(f"   📊 Status: {data.get('status', 'unknown')}")
            print(f"   ⏱️ Timestamp: {data.get('timestamp', 'unknown')}")
        else:
            print(f"   ⚠️ Health Check MCP: {response.status_code}")

    except Exception as e:
        print(f"   ❌ Errore health check: {str(e)}")
        return False

    # Test 4: Simula chiamata MCP dall'app
    print("\n4. 🚀 Test Simulazione Chiamata MCP...")
    try:
        # Simula una chiamata come farebbe l'app
        test_data = {
            "test": True,
            "timestamp": datetime.now().isoformat(),
            "message": "Test integrazione MCP"
        }

        # Test POST al server MCP (se supportato)
        try:
            response = requests.post(
                'http://127.0.0.1:8001/health',
                json=test_data,
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )
            print(f"   ✅ Test POST MCP: {response.status_code}")
        except:
            # Se POST non supportato, prova GET
            response = requests.get('http://127.0.0.1:8001/health', timeout=10)
            print(f"   ✅ Test GET MCP: {response.status_code}")

    except Exception as e:
        print(f"   ❌ Errore test chiamata: {str(e)}")
        return False

    # Test 5: Verifica configurazione app
    print("\n5. ⚙️ Verifica Configurazione App...")
    try:
        # Verifica che l'app usi la porta corretta
        import app

        # Controlla la configurazione MCP nell'app
        mcp_url_configured = getattr(app, 'mcp_url', 'Non configurato')
        print(f"   📍 MCP URL configurato: {mcp_url_configured}")

        if '8000' in str(mcp_url_configured):
            print("   ✅ Porta 8000 configurata correttamente")
        else:
            print("   ⚠️ Porta potrebbe non essere aggiornata")

    except Exception as e:
        print(f"   ❌ Errore verifica config: {str(e)}")

    return True

def test_timeout_resolution():
    """Testa che il problema di timeout sia risolto."""
    print("\n" + "=" * 50)
    print("⏱️ TEST RISOLUZIONE TIMEOUT MCP")
    print("=" * 50)

    print("\n🔍 Verifica risoluzione problema timeout...")

    # Test connessioni multiple per verificare stabilità
    success_count = 0
    total_tests = 5

    for i in range(total_tests):
        print(f"\n   Test {i+1}/{total_tests}...")
        try:
            start_time = time.time()
            response = requests.get('http://127.0.0.1:8001/health', timeout=5)
            end_time = time.time()

            response_time = (end_time - start_time) * 1000  # in ms

            if response.status_code == 200:
                success_count += 1
                print(f"   ✅ Successo in {response_time:.1f}ms")
            else:
                print(f"   ⚠️ Status {response.status_code} in {response_time:.1f}ms")

        except Exception as e:
            print(f"   ❌ Timeout/Errore: {str(e)}")

    success_rate = (success_count / total_tests) * 100
    print(f"\n📊 Risultato: {success_count}/{total_tests} test riusciti ({success_rate:.1f}%)")

    if success_rate >= 80:
        print("🎉 PROBLEMA TIMEOUT RISOLTO!")
        print("✅ Server MCP stabile e raggiungibile")
        print("✅ Connessioni multiple funzionanti")
        return True
    else:
        print("⚠️ Problema timeout potrebbe persistere")
        return False

def main():
    """Esegue tutti i test di integrazione MCP."""
    print("🧪 AVVIO TEST COMPLETO INTEGRAZIONE MCP")
    print("=" * 60)

    # Test integrazione
    integration_ok = test_mcp_integration()

    # Test risoluzione timeout
    timeout_resolved = test_timeout_resolution()

    # Risultato finale
    print("\n" + "=" * 60)
    print("📋 RISULTATO FINALE INTEGRAZIONE MCP")
    print("=" * 60)

    if integration_ok and timeout_resolved:
        print("🎉 INTEGRAZIONE MCP COMPLETAMENTE FUNZIONANTE!")
        print("✅ Server MCP attivo sulla porta 8001")
        print("✅ App principale configurata correttamente")
        print("✅ Problema timeout risolto")
        print("✅ Connessioni stabili e veloci")
        print("\n💡 L'errore 'Timeout nella connessione al server MCP' non dovrebbe più apparire.")
        print("🚀 Sistema pronto per utilizzo completo con MCP!")
        return True
    elif integration_ok:
        print("⚠️ INTEGRAZIONE MCP PARZIALMENTE FUNZIONANTE")
        print("✅ Server MCP attivo")
        print("✅ App principale configurata")
        print("⚠️ Possibili problemi di stabilità")
        return False
    else:
        print("❌ PROBLEMI INTEGRAZIONE MCP")
        print("❌ Verificare configurazione server")
        print("💡 Riavviare entrambi i server se necessario")
        return False

if __name__ == "__main__":
    success = main()

    # Salva risultati
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f'test_mcp_integration_results_{timestamp}.txt'

    with open(results_file, 'w', encoding='utf-8') as f:
        f.write(f"Test Integrazione MCP - {timestamp}\n")
        f.write(f"Risultato: {'SUCCESS' if success else 'FAIL'}\n")
        f.write(f"App URL: http://localhost:5000\n")
        f.write(f"MCP URL: http://127.0.0.1:8001\n")
        f.write(f"Problema timeout: {'RISOLTO' if success else 'PERSISTE'}\n")

    print(f"\n📄 Risultati salvati in: {results_file}")

    exit(0 if success else 1)
