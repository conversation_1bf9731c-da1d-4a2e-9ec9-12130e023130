#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Warning Suppressor - Elimina tutti i warning dell'applicazione
"""

import warnings
import logging
import os
import sys

def suppress_all_warnings():
    """Sopprime tutti i warning dell'applicazione."""

    # Sopprime tutti i warning Python
    warnings.filterwarnings("ignore")

    # Sopprime warning specifici
    warnings.filterwarnings("ignore", category=DeprecationWarning)
    warnings.filterwarnings("ignore", category=PendingDeprecationWarning)
    warnings.filterwarnings("ignore", category=FutureWarning)
    warnings.filterwarnings("ignore", category=UserWarning)
    warnings.filterwarnings("ignore", category=RuntimeWarning)

    # Sopprime warning di librerie specifiche
    warnings.filterwarnings("ignore", module="plotly.*")
    warnings.filterwarnings("ignore", module="sklearn.*")
    warnings.filterwarnings("ignore", module="pandas.*")
    warnings.filterwarnings("ignore", module="numpy.*")
    warnings.filterwarnings("ignore", module="matplotlib.*")
    warnings.filterwarnings("ignore", module="seaborn.*")
    warnings.filterwarnings("ignore", module="scipy.*")
    warnings.filterwarnings("ignore", module="langchain.*")
    warnings.filterwarnings("ignore", module="openai.*")
    warnings.filterwarnings("ignore", module="redis.*")
    warnings.filterwarnings("ignore", module="psutil.*")

    # Sopprime completamente WeasyPrint warnings e messaggi
    warnings.filterwarnings("ignore", module="weasyprint.*")
    warnings.filterwarnings("ignore", category=UserWarning, module="weasyprint")
    warnings.filterwarnings("ignore", category=RuntimeWarning, module="weasyprint")
    warnings.filterwarnings("ignore", message=".*WeasyPrint.*")
    warnings.filterwarnings("ignore", message=".*libgobject.*")
    warnings.filterwarnings("ignore", message=".*GTK.*")

    # Configura logging per ridurre verbosità
    logging.getLogger("urllib3").setLevel(logging.ERROR)
    logging.getLogger("requests").setLevel(logging.ERROR)
    logging.getLogger("httpx").setLevel(logging.ERROR)
    logging.getLogger("openai").setLevel(logging.ERROR)
    logging.getLogger("langchain").setLevel(logging.ERROR)
    logging.getLogger("redis").setLevel(logging.ERROR)
    logging.getLogger("psutil").setLevel(logging.ERROR)

    # Sopprime completamente i logger di WeasyPrint
    logging.getLogger("weasyprint").setLevel(logging.CRITICAL)
    logging.getLogger("weasyprint.progress").setLevel(logging.CRITICAL)
    logging.getLogger("weasyprint.html").setLevel(logging.CRITICAL)
    logging.getLogger("weasyprint.css").setLevel(logging.CRITICAL)
    logging.getLogger("weasyprint.pdf").setLevel(logging.CRITICAL)

    # Variabili d'ambiente per sopprimere warning
    os.environ["PYTHONWARNINGS"] = "ignore"
    os.environ["TF_CPP_MIN_LOG_LEVEL"] = "3"  # TensorFlow
    os.environ["TOKENIZERS_PARALLELISM"] = "false"  # Hugging Face

    print("✅ Warning suppressor attivato - tutti i warning sono stati soppressi")

def configure_clean_logging():
    """Configura logging pulito senza warning."""

    # Configura il logger root
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # Rimuovi tutti gli handler esistenti
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Crea un formatter pulito
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # Crea handler per console
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # Aggiungi handler al logger root
    root_logger.addHandler(console_handler)

    # Configura logger specifici per essere meno verbosi
    loggers_to_quiet = [
        'urllib3.connectionpool',
        'requests.packages.urllib3.connectionpool',
        'httpx',
        'openai',
        'langchain',
        'redis',
        'psutil',
        'matplotlib',
        'plotly',
        'werkzeug',
        'weasyprint',
        'weasyprint.progress',
        'weasyprint.html',
        'weasyprint.css',
        'weasyprint.pdf'
    ]

    for logger_name in loggers_to_quiet:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.ERROR)
        logger.propagate = False

def check_optional_dependencies():
    """Verifica dipendenze opzionali e mostra solo messaggi informativi."""

    dependencies = {
        'redis': 'Cache avanzata',
        'psutil': 'Monitoring sistema',
        'langchain': 'Agenti AI avanzati',
        'plotly': 'Grafici interattivi',
        'sklearn': 'Machine Learning',
        'tensorflow': 'Deep Learning',
        'torch': 'PyTorch'
    }

    available = []
    missing = []

    for dep, description in dependencies.items():
        try:
            __import__(dep)
            available.append(f"✅ {dep} - {description}")
        except ImportError:
            missing.append(f"⚪ {dep} - {description} (opzionale)")

    if available:
        print("\n📦 DIPENDENZE DISPONIBILI:")
        for dep in available:
            print(f"   {dep}")

    if missing:
        print("\n📦 DIPENDENZE OPZIONALI NON INSTALLATE:")
        for dep in missing:
            print(f"   {dep}")

    print()

if __name__ == "__main__":
    suppress_all_warnings()
    configure_clean_logging()
    check_optional_dependencies()
