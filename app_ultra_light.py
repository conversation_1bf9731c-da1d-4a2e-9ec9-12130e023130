#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
App Roberto - Versione Ultra-Leggera per Test Route
Carica SOLO le route essenziali per testare il problema 404
"""

import os
import sys
from datetime import datetime

# Carica le variabili d'ambiente dal file .env se presente
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("Variabili d'ambiente caricate dal file .env")
except ImportError:
    print("python-dotenv non installato. Le variabili d'ambiente devono essere impostate manualmente.")
except Exception as e:
    print(f"Errore nel caricamento delle variabili d'ambiente: {str(e)}")

# Importazioni Flask essenziali
from flask import Flask, jsonify

# Crea app Flask ultra-leggera
app = Flask(__name__)
app.secret_key = 'bait_service_app_secret_key'

print("🔧 ULTRA-LIGHT: App Flask ultra-leggera inizializzata")

# Simulazione AdvancedDatabaseManager ultra-leggera
class UltraLightDatabaseManager:
    def __init__(self):
        self.is_connected = True
        print("✅ UltraLightDatabaseManager inizializzato")
    
    def get_master_technicians(self):
        return [
            {
                'name': 'Mario Rossi',
                'hourly_rate': 25.0,
                'vat_included': True,
                'notes': 'Tecnico senior - Ultra Light',
                'source': 'ultra_light'
            },
            {
                'name': 'Luigi Verdi', 
                'hourly_rate': 20.0,
                'vat_included': True,
                'notes': 'Tecnico junior - Ultra Light',
                'source': 'ultra_light'
            }
        ]

# Inizializza database manager ultra-leggero
app.db_manager = UltraLightDatabaseManager()

# ROUTE DI DEBUG
print("🔧 ULTRA-LIGHT: Registrando route di debug...")

@app.route('/api/debug/flask-routes', methods=['GET'])
def debug_flask_routes():
    """Debug: Lista tutte le route Flask registrate."""
    routes = []
    for rule in app.url_map.iter_rules():
        routes.append({
            'endpoint': rule.endpoint,
            'methods': list(rule.methods),
            'rule': str(rule)
        })
    return jsonify({
        'success': True,
        'total_routes': len(routes),
        'routes': routes,
        'app_name': 'ultra_light'
    })

@app.route('/api/test-ultra-light', methods=['GET'])
def test_ultra_light():
    return jsonify({
        "message": "Route ultra-light funziona!", 
        "status": "success",
        "app": "ultra_light"
    })

# API per configurazione dipendenti - VERSIONE ULTRA-LEGGERA
print("🔧 ULTRA-LIGHT: Registrando route /api/config/employees...")

@app.route('/api/config/employees', methods=['GET'])
def get_config_employees():
    """
    API per ottenere la lista dei dipendenti configurati.
    Versione ultra-leggera per test.
    """
    print("=== API GET_CONFIG_EMPLOYEES ULTRA-LIGHT ===")
    
    try:
        employees = []
        data_source_used = 'unknown'

        # Usa database manager ultra-leggero
        if hasattr(app, 'db_manager') and app.db_manager:
            try:
                print("🚀 ULTRA-LIGHT: Caricamento dipendenti...")

                # Ottieni dipendenti da database ultra-leggero
                ultra_light_employees = app.db_manager.get_master_technicians()

                if ultra_light_employees and len(ultra_light_employees) > 0:
                    employees = ultra_light_employees
                    data_source_used = 'ultra_light'

                    print(f"✅ ULTRA-LIGHT: {len(employees)} dipendenti caricati con successo")
                else:
                    print("ℹ️ ULTRA-LIGHT: Nessun dipendente trovato")

            except Exception as e:
                print(f"❌ ULTRA-LIGHT: Errore caricamento dipendenti: {str(e)}")

        # FALLBACK: Dati demo se nessun dipendente disponibile
        if not employees:
            print("🎭 ULTRA-LIGHT: Generazione dipendenti demo...")

            data_source_used = 'demo'
            employees = [
                {
                    'name': 'Mario Rossi',
                    'hourly_rate': 25.0,
                    'vat_included': True,
                    'notes': 'Tecnico senior - Demo Ultra Light',
                    'source': 'demo_ultra_light'
                },
                {
                    'name': 'Luigi Verdi',
                    'hourly_rate': 20.0,
                    'vat_included': True,
                    'notes': 'Tecnico junior - Demo Ultra Light',
                    'source': 'demo_ultra_light'
                }
            ]

        # Log finale
        print(f"👥 Config employees - Fonte: {data_source_used}, Dipendenti: {len(employees)}")

        return jsonify({
            'success': True,
            'employees': employees,
            'data_source': data_source_used,
            'count': len(employees),
            'timestamp': datetime.now().isoformat(),
            'app': 'ultra_light'
        })

    except Exception as e:
        import traceback
        print(f"❌ Errore API config/employees: {str(e)}")
        print(traceback.format_exc())

        return jsonify({
            'success': False,
            'error': str(e),
            'employees': [],
            'data_source': 'error',
            'app': 'ultra_light'
        }), 500

# Route di health check
@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({
        'status': 'healthy',
        'message': 'Server Flask ultra-leggero funzionante',
        'timestamp': datetime.now().isoformat(),
        'db_connected': hasattr(app, 'db_manager') and app.db_manager.is_connected,
        'app': 'ultra_light'
    })

if __name__ == '__main__':
    print("🚀 Avvio server Flask ultra-leggero...")
    print("📋 Route registrate:")
    print("   - /api/debug/flask-routes")
    print("   - /api/test-ultra-light")
    print("   - /api/config/employees")
    print("   - /api/health")
    
    # Lista tutte le route registrate per debug
    print("\n🔍 DEBUG: Route Flask registrate:")
    for rule in app.url_map.iter_rules():
        print(f"   - {rule.rule} [{', '.join(rule.methods)}] -> {rule.endpoint}")
    
    app.run(host='127.0.0.1', port=5003, debug=False)
