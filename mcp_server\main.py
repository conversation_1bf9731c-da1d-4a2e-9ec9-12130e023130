#!/usr/bin/env python
# -*- coding: utf-8 -*-

# SOPPRESSIONE WARNING - DEVE ESSERE PRIMA DI TUTTO
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from warning_suppressor import suppress_all_warnings, configure_clean_logging
suppress_all_warnings()
configure_clean_logging()

import json
import logging
import math
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta

# Carica le variabili d'ambiente dal file .env se presente
try:
    from dotenv import load_dotenv
    # Carica le variabili d'ambiente dal file .env
    load_dotenv()
    print("Variabili d'ambiente caricate dal file .env")
except ImportError:
    print("python-dotenv non installato. Le variabili d'ambiente devono essere impostate manualmente.")
except Exception as e:
    print(f"Errore nel caricamento delle variabili d'ambiente: {str(e)}")

from fastapi import FastAPI, HTTPException, UploadFile, File, Form, Query, Body, Depends
from fastapi.responses import JSONResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field
import shutil
from uuid import uuid4

# Importazioni relative quando il modulo è importato come pacchetto
try:
    from .column_mapper import ColumnMapper
    from .duration_parser import DurationParser
    from .activity_processor import ActivityProcessor
    from .vehicle_registry_processor import VehicleRegistryProcessor
    from .openrouter_client import OpenRouterClient
    from .data_cleaning_endpoints import router as data_cleaning_router
# Importazioni assolute quando il modulo è eseguito direttamente
except ImportError:
    from column_mapper import ColumnMapper
    from duration_parser import DurationParser
    from activity_processor import ActivityProcessor
    from vehicle_registry_processor import VehicleRegistryProcessor
    from openrouter_client import OpenRouterClient
    from data_cleaning_endpoints import router as data_cleaning_router

# Configurazione del logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clean_data_for_json(data):
    """
    Pulisce i dati per renderli compatibili con la serializzazione JSON.
    Converte NaN, infinito e altri valori non JSON-compatibili.

    Args:
        data: Dati da pulire (lista di dizionari o singolo dizionario)

    Returns:
        Dati puliti e JSON-compatibili
    """
    if isinstance(data, list):
        return [clean_data_for_json(item) for item in data]
    elif isinstance(data, dict):
        cleaned = {}
        for key, value in data.items():
            cleaned[key] = clean_data_for_json(value)
        return cleaned
    elif isinstance(data, (pd.Timestamp, datetime)):
        # Converti Timestamp e datetime in formato ISO
        return data.isoformat()
    elif isinstance(data, (pd.Timedelta, timedelta)):
        # Converti Timedelta in secondi totali
        return data.total_seconds()
    elif isinstance(data, np.timedelta64):
        # Converti np.timedelta64 in secondi totali
        return float(data / np.timedelta64(1, 's'))
    elif isinstance(data, float):
        if math.isnan(data):
            return None
        elif math.isinf(data):
            return "Infinity" if data > 0 else "-Infinity"
        else:
            return data
    elif isinstance(data, np.floating):
        if np.isnan(data):
            return None
        elif np.isinf(data):
            return "Infinity" if data > 0 else "-Infinity"
        else:
            return float(data)
    elif isinstance(data, np.integer):
        return int(data)
    elif pd.isna(data):
        return None
    else:
        return data

# Inizializzazione dell'app FastAPI
app = FastAPI(
    title="MCP Server - Model Context Protocol",
    description="Server per l'analisi e l'elaborazione dei dati di attività tramite Model Context Protocol",
    version="1.0.0",
    # Aggiungi gestori di eventi Lifespan per evitare avvisi
    on_startup=[lambda: logger.info("MCP Server avviato")],
    on_shutdown=[lambda: logger.info("MCP Server arrestato")]
)

# Configurazione CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In produzione, specificare i domini consentiti
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configurazione delle cartelle
UPLOAD_FOLDER = "uploads"
TEMP_FOLDER = os.path.join(UPLOAD_FOLDER, "temp")
PROCESSED_FOLDER = os.path.join(UPLOAD_FOLDER, "processed")

# Assicurarsi che le cartelle esistano
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(TEMP_FOLDER, exist_ok=True)
os.makedirs(PROCESSED_FOLDER, exist_ok=True)

# Inizializzazione delle classi di elaborazione
column_mapper = ColumnMapper()
duration_parser = DurationParser()
activity_processor = ActivityProcessor()
vehicle_processor = VehicleRegistryProcessor()

# Inizializzazione del client OpenRouter
openrouter_api_key = os.environ.get('OPENROUTER_API_KEY')
openrouter_client = OpenRouterClient(api_key=openrouter_api_key, timeout=30)

# Registra il router degli endpoint di pulizia dei dati
app.include_router(data_cleaning_router)

# Dizionario per tenere traccia dei file elaborati
# Chiave: file_id, Valore: {path, original_filename, file_type, processed_data, etc.}
processed_files = {}

# Modelli Pydantic per la validazione e la documentazione
class HealthResponse(BaseModel):
    status: str
    version: str
    timestamp: str

class ProcessFileRequest(BaseModel):
    file_id: str
    file_type: str
    options: Optional[Dict[str, Any]] = None

class ProcessFileResponse(BaseModel):
    file_id: str
    original_filename: str
    file_type: str
    processing_status: str
    statistics: Dict[str, Any]
    message: str

class ActivitySummary(BaseModel):
    total_activities: int
    total_hours: float
    average_duration: float
    technicians_count: int
    clients_count: int

class ColumnMappingRequest(BaseModel):
    file_id: str
    column_mapping: Dict[str, str]

class ColumnMappingResponse(BaseModel):
    file_id: str
    success: bool
    message: str
    applied_mapping: Dict[str, str]

class LLMQueryRequest(BaseModel):
    file_id: str
    query: str
    model_id: str = "anthropic/claude-3-haiku"

class LLMQueryResponse(BaseModel):
    response: str
    source: str = "mcp"
    model_id: str
    processing_time: float

class Issue(BaseModel):
    type: str
    row_index: int
    column: Optional[str] = None
    value: Any = None
    description: str

class IssueIdentificationRequest(BaseModel):
    file_id: str
    issue_types: Optional[List[str]] = None

class IssueIdentificationResponse(BaseModel):
    file_id: str
    issues_found: List[Issue]
    total_issues: int
    issue_types: Dict[str, int]
    processing_time: float

class Correction(BaseModel):
    issue_id: int
    type: str
    row_index: int
    column: Optional[str] = None
    old_value: Any = None
    new_value: Any = None
    description: str
    confidence: float = 0.0

class CorrectionSuggestionRequest(BaseModel):
    file_id: str
    issues: List[Issue]

class CorrectionSuggestionResponse(BaseModel):
    file_id: str
    corrections: List[Correction]
    total_corrections: int
    processing_time: float

class CorrectionApplicationRequest(BaseModel):
    file_id: str
    corrections: List[Correction]

class CorrectionApplicationResponse(BaseModel):
    file_id: str
    corrections_applied: List[Correction]
    total_applied: int
    success: bool
    message: str
    processing_time: float

class FormatStandardizationRequest(BaseModel):
    file_id: str
    format_specs: Dict[str, str]

class FormatStandardizationResponse(BaseModel):
    file_id: str
    standardized_columns: List[str]
    total_standardized: int
    success: bool
    message: str
    processing_time: float

# Endpoint API
@app.get("/")
async def root():
    """Endpoint di base per verificare che l'API sia attiva."""
    return {"message": "MCP Server - Model Context Protocol"}

@app.get("/export/{file_id}/{format}")
async def export_data(file_id: str, format: str):
    """
    Esporta i dati elaborati in vari formati.

    Args:
        file_id: ID del file da esportare
        format: Formato di esportazione (csv, excel, json)

    Returns:
        File da scaricare
    """
    try:
        # Verifica che il formato sia supportato
        if format not in ["csv", "excel", "json"]:
            raise HTTPException(status_code=400, detail=f"Formato non supportato: {format}")

        # Cerca il file nella cartella processed
        processed_path = os.path.join(PROCESSED_FOLDER, f"{file_id}.csv")
        if not os.path.exists(processed_path):
            # Cerca nella cartella uploads
            processed_path = os.path.join(UPLOAD_FOLDER, f"{file_id}.csv")
            if not os.path.exists(processed_path):
                raise HTTPException(status_code=404, detail=f"File non trovato: {file_id}")

        # Leggi il file
        df = pd.read_csv(processed_path)

        # Crea il percorso per il file esportato
        export_filename = f"{file_id}_export.{format}"
        export_path = os.path.join(PROCESSED_FOLDER, export_filename)

        # Esporta nel formato richiesto
        if format == "csv":
            df.to_csv(export_path, index=False)
            media_type = "text/csv"
        elif format == "excel":
            df.to_excel(export_path, index=False)
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        elif format == "json":
            df.to_json(export_path, orient="records", date_format="iso")
            media_type = "application/json"

        # Restituisci il file
        return FileResponse(
            path=export_path,
            filename=export_filename,
            media_type=media_type
        )

    except Exception as e:
        logger.exception(f"Errore nell'esportazione dei dati: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore nell'esportazione dei dati: {str(e)}")

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Endpoint per verificare lo stato del server."""
    return HealthResponse(
        status="ok",
        version="1.0.0",
        timestamp=datetime.now().isoformat()
    )

@app.post("/upload-file/")
async def upload_file(file: UploadFile = File(...), file_id: str = Form(...), file_type: str = Form(...)):
    """
    Carica un file sul server MCP.

    Args:
        file: File da caricare
        file_id: ID univoco del file
        file_type: Tipo di file (es. "attivita", "teamviewer", ecc.)

    Returns:
        Informazioni sul file caricato
    """
    try:
        # Crea un percorso univoco per il file
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = f"{file_id}{file_extension}"
        file_path = os.path.join(UPLOAD_FOLDER, unique_filename)

        # Salva il file
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # Carica il file in base all'estensione
        if file_path.endswith('.csv'):
            try:
                df = pd.read_csv(file_path, sep=';', encoding='utf-8-sig')
            except:
                df = pd.read_csv(file_path, sep=',', encoding='utf-8-sig')
        elif file_path.endswith(('.xlsx', '.xls')):
            df = pd.read_excel(file_path)
        else:
            raise HTTPException(status_code=400, detail=f"Formato file non supportato: {file.filename}")

        # Salva le informazioni sul file
        processed_files[file_id] = {
            "path": file_path,
            "original_filename": file.filename,
            "file_type": file_type,
            "dataframe": df,
            "upload_time": datetime.now().isoformat()
        }

        return {
            "file_id": file_id,
            "original_filename": file.filename,
            "file_type": file_type,
            "message": "File caricato con successo"
        }

    except Exception as e:
        logger.error(f"Errore nel caricamento del file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore nel caricamento del file: {str(e)}")

@app.post("/process-file/", response_model=ProcessFileResponse)
async def process_file(request: ProcessFileRequest):
    """
    Elabora un file precedentemente caricato.

    Args:
        request: Richiesta contenente l'ID del file, il tipo e le opzioni di elaborazione

    Returns:
        Informazioni sull'elaborazione del file
    """
    file_id = request.file_id
    file_type = request.file_type
    options = request.options or {}

    if file_id not in processed_files:
        raise HTTPException(status_code=404, detail=f"File con ID {file_id} non trovato")

    file_info = processed_files[file_id]

    try:
        # Carica il DataFrame
        df = file_info.get("dataframe")

        if df is None:
            # Se il DataFrame non è in memoria, caricalo dal file
            file_path = file_info.get("path")
            if not file_path or not os.path.exists(file_path):
                raise HTTPException(status_code=404, detail=f"File con ID {file_id} non trovato su disco")

            # Carica il file in base all'estensione
            if file_path.endswith('.csv'):
                try:
                    df = pd.read_csv(file_path, sep=';', encoding='utf-8-sig')
                except:
                    df = pd.read_csv(file_path, sep=',', encoding='utf-8-sig')
            elif file_path.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(file_path)
            else:
                raise HTTPException(status_code=400, detail=f"Formato file non supportato: {os.path.basename(file_path)}")

        # Elabora i dati in base al tipo di file
        if file_type == "registro_auto":
            # Usa il processore specifico per i veicoli
            try:
                df_processed = vehicle_processor.process_vehicle_registry_file(file_info.get("path"))
                # Crea un report di elaborazione compatibile
                processing_report = {
                    "statistics": {
                        "total_rows": len(df_processed),
                        "columns": df_processed.columns.tolist()
                    },
                    "discrepancies": [],
                    "missing_values": {},
                    "anomalies": {}
                }
            except Exception as e:
                logger.error(f"Errore nell'elaborazione del registro auto: {str(e)}")
                # Fallback al processore generico
                df_processed, processing_report = activity_processor.process_activity_data(df, file_type)
        else:
            # Usa il processore generico per altri tipi di file
            df_processed, processing_report = activity_processor.process_activity_data(df, file_type)

        # Salva i risultati
        processed_files[file_id].update({
            "processed_dataframe": df_processed,
            "processing_report": processing_report,
            "processing_status": "completed",
            "processing_time": datetime.now().isoformat()
        })

        # Estrai statistiche di base
        statistics = processing_report.get("statistics", {})

        return ProcessFileResponse(
            file_id=file_id,
            original_filename=file_info.get("original_filename", ""),
            file_type=file_type,
            processing_status="completed",
            statistics=statistics,
            message="File elaborato con successo"
        )

    except Exception as e:
        logger.error(f"Errore nell'elaborazione del file {file_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore nell'elaborazione del file: {str(e)}")

@app.get("/file-summary/{file_id}", response_model=ActivitySummary)
async def get_file_summary(file_id: str):
    """
    Ottiene un riepilogo delle attività per un file elaborato.

    Args:
        file_id: ID del file

    Returns:
        Riepilogo delle attività
    """
    if file_id not in processed_files:
        raise HTTPException(status_code=404, detail=f"File con ID {file_id} non trovato")

    file_info = processed_files[file_id]

    if "processing_report" not in file_info:
        raise HTTPException(status_code=400, detail=f"File con ID {file_id} non è stato elaborato")

    report = file_info["processing_report"]
    statistics = report.get("statistics", {})

    # Estrai le statistiche rilevanti
    total_activities = statistics.get("total_rows", 0)

    duration_stats = statistics.get("duration", {})
    total_hours = duration_stats.get("total_hours", 0.0)
    average_duration = duration_stats.get("average_hours", 0.0)

    technicians_stats = statistics.get("technicians", {})
    technicians_count = technicians_stats.get("count", 0)

    clients_stats = statistics.get("clients", {})
    clients_count = clients_stats.get("count", 0)

    return ActivitySummary(
        total_activities=total_activities,
        total_hours=total_hours,
        average_duration=average_duration,
        technicians_count=technicians_count,
        clients_count=clients_count
    )

@app.post("/update-column-mapping/", response_model=ColumnMappingResponse)
async def update_column_mapping(request: ColumnMappingRequest):
    """
    Aggiorna la mappatura delle colonne per un file elaborato.

    Args:
        request: Richiesta contenente l'ID del file e la nuova mappatura delle colonne

    Returns:
        Risultato dell'aggiornamento della mappatura
    """
    file_id = request.file_id
    column_mapping = request.column_mapping

    if file_id not in processed_files:
        raise HTTPException(status_code=404, detail=f"File con ID {file_id} non trovato")

    file_info = processed_files[file_id]

    try:
        # Ottieni il DataFrame originale
        df_raw = file_info.get("dataframe")

        if df_raw is None:
            raise HTTPException(status_code=400, detail=f"DataFrame originale non disponibile per il file {file_id}")

        # Applica la nuova mappatura
        df_mapped = df_raw.rename(columns=column_mapping)

        # Aggiorna il file_info
        file_info["custom_column_mapping"] = column_mapping

        # Rielabora i dati con la nuova mappatura
        file_type = file_info.get("file_type", "generico")
        df_processed, processing_report = activity_processor.process_activity_data(df_mapped, file_type)

        # Aggiorna i risultati
        file_info.update({
            "processed_dataframe": df_processed,
            "processing_report": processing_report,
            "processing_status": "completed",
            "processing_time": datetime.now().isoformat()
        })

        return ColumnMappingResponse(
            file_id=file_id,
            success=True,
            message="Mappatura delle colonne aggiornata con successo",
            applied_mapping=column_mapping
        )

    except Exception as e:
        logger.error(f"Errore nell'aggiornamento della mappatura delle colonne per il file {file_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore nell'aggiornamento della mappatura: {str(e)}")

@app.get("/standard-columns/{file_type}")
async def get_standard_columns(file_type: str):
    """
    Ottiene le colonne standard per un tipo di file.

    Args:
        file_type: Tipo di file

    Returns:
        Lista delle colonne standard
    """
    standard_columns = column_mapper.get_standard_columns(file_type)

    if not standard_columns:
        raise HTTPException(status_code=400, detail=f"Tipo di file non supportato: {file_type}")

    return {"file_type": file_type, "standard_columns": standard_columns}

@app.get("/duration-formats")
async def get_duration_formats():
    """
    Ottiene i formati di durata supportati.

    Returns:
        Informazioni sui formati di durata supportati
    """
    return {
        "supported_formats": [
            {"format": "HH:MM", "example": "1:30", "description": "Ore e minuti separati da due punti"},
            {"format": "H.MM", "example": "1.5", "description": "Ore decimali con punto"},
            {"format": "H,MM", "example": "1,5", "description": "Ore decimali con virgola"},
            {"format": "H", "example": "1", "description": "Solo ore (intero)"}
        ]
    }

@app.get("/processed-data/{file_id}")
async def get_processed_data(file_id: str, max_rows: int = Query(1000, ge=1, le=10000)):
    """
    Ottiene i dati elaborati per un file.

    Args:
        file_id: ID del file
        max_rows: Numero massimo di righe da restituire

    Returns:
        Dati elaborati
    """
    if file_id not in processed_files:
        raise HTTPException(status_code=404, detail=f"File con ID {file_id} non trovato")

    file_info = processed_files[file_id]

    if "processed_dataframe" not in file_info:
        raise HTTPException(status_code=400, detail=f"File con ID {file_id} non è stato elaborato")

    try:
        # Ottieni il DataFrame elaborato
        df_processed = file_info["processed_dataframe"]

        # Limita il numero di righe
        if len(df_processed) > max_rows:
            df_limited = df_processed.head(max_rows)
            is_limited = True
        else:
            df_limited = df_processed
            is_limited = False

        # Converti in formato JSON e pulisci i dati
        data = df_limited.to_dict(orient='records')

        # Pulisci i dati per renderli JSON-compatibili
        clean_data = clean_data_for_json(data)

        # Ottieni i metadati
        metadata = {
            "total_rows": len(df_processed),
            "returned_rows": len(df_limited),
            "is_limited": is_limited,
            "file_type": file_info.get("file_type", "generico"),
            "processing_time": file_info.get("processing_time", ""),
            "columns": df_processed.columns.tolist()
        }

        # Aggiungi le statistiche se disponibili
        if "processing_report" in file_info:
            metadata["statistics"] = file_info["processing_report"].get("statistics", {})

        # Pulisci anche i metadati
        clean_metadata = clean_data_for_json(metadata)

        return {
            "data": clean_data,
            "metadata": clean_metadata
        }

    except Exception as e:
        logger.error(f"Errore nel recupero dei dati elaborati per il file {file_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore nel recupero dei dati elaborati: {str(e)}")

@app.post("/llm-query/", response_model=LLMQueryResponse)
async def process_llm_query(request: LLMQueryRequest):
    """
    Elabora una query LLM con il contesto dei dati.

    Args:
        request: Richiesta contenente l'ID del file, la query e il modello da utilizzare

    Returns:
        Risposta dell'LLM
    """
    import time

    file_id = request.file_id
    query = request.query
    model_id = request.model_id

    # Verifica se il file esiste
    if file_id not in processed_files:
        raise HTTPException(status_code=404, detail=f"File con ID {file_id} non trovato")

    # Verifica se OpenRouter è disponibile
    if not openrouter_client.is_available:
        raise HTTPException(status_code=503, detail="OpenRouter non disponibile")

    try:
        # Ottieni i dati elaborati
        file_info = processed_files[file_id]

        if "processed_dataframe" not in file_info:
            raise HTTPException(status_code=400, detail=f"File con ID {file_id} non è stato elaborato")

        # Ottieni il DataFrame elaborato
        df_processed = file_info["processed_dataframe"]

        # Ottieni il report di elaborazione
        processing_report = file_info.get("processing_report", {})

        # Converti il DataFrame in una lista di dizionari
        data = df_processed.head(10).to_dict(orient='records')

        # Pulisci i dati per renderli JSON-compatibili
        clean_data = clean_data_for_json(data)

        # Serializza i dati per l'LLM
        serialized_data = openrouter_client.serialize_data_for_llm(
            data=clean_data,
            max_rows=10,
            include_stats=True
        )

        # Aggiungi le statistiche dal report di elaborazione
        statistics = processing_report.get("statistics", {})

        # Crea una rappresentazione testuale delle statistiche
        stats_text = "Statistiche dettagliate:\n"

        # Aggiungi le statistiche di base
        stats_text += f"- Totale righe: {statistics.get('total_rows', 0)}\n"

        # Aggiungi le statistiche sulla durata
        duration_stats = statistics.get("duration", {})
        stats_text += f"- Ore totali: {duration_stats.get('total_hours', 0.0)}\n"
        stats_text += f"- Durata media: {duration_stats.get('average_hours', 0.0)} ore\n"

        # Aggiungi le statistiche sui tecnici
        technicians_stats = statistics.get("technicians", {})
        stats_text += f"- Numero di tecnici: {technicians_stats.get('count', 0)}\n"

        # Aggiungi le statistiche sui clienti
        clients_stats = statistics.get("clients", {})
        stats_text += f"- Numero di clienti: {clients_stats.get('count', 0)}\n"

        # Crea il prompt per l'LLM
        system_prompt = """Sei un assistente AI esperto in analisi dati aziendali.
Aiuta l'utente ad analizzare e comprendere i dati forniti.
Rispondi in modo conciso, professionale e in italiano.
Basa le tue risposte solo sui dati forniti e non inventare informazioni.
Se non puoi rispondere a una domanda in base ai dati disponibili, dillo chiaramente.
"""

        # Crea il messaggio utente con il contesto dei dati
        user_prompt = f"""Ecco i dati da analizzare:

{serialized_data}

{stats_text}

La mia domanda è: {query}
"""

        # Crea i messaggi per OpenRouter
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        # Misura il tempo di elaborazione
        start_time = time.time()

        # Invia la richiesta a OpenRouter
        llm_response = openrouter_client.chat_completion(
            messages=messages,
            model=model_id,
            temperature=0.7,
            max_tokens=1000
        )

        # Calcola il tempo di elaborazione
        processing_time = time.time() - start_time

        # Verifica se ci sono errori
        if 'error' in llm_response:
            raise HTTPException(status_code=500, detail=f"Errore nella richiesta a OpenRouter: {llm_response['error']}")

        # Estrai la risposta
        assistant_message = llm_response.get('choices', [{}])[0].get('message', {}).get('content', '')

        return LLMQueryResponse(
            response=assistant_message,
            source="mcp",
            model_id=model_id,
            processing_time=processing_time
        )

    except HTTPException:
        # Rilancia le eccezioni HTTP
        raise
    except Exception as e:
        logger.error(f"Errore nell'elaborazione della query LLM: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore nell'elaborazione della query LLM: {str(e)}")

@app.post("/identify-issues/{file_id}", response_model=IssueIdentificationResponse)
async def identify_issues(file_id: str, request: IssueIdentificationRequest = Body(None)):
    """
    Identifica problemi nei dati come valori mancanti, anomalie e discrepanze.

    Args:
        file_id: ID del file
        request: Richiesta contenente i tipi di problemi da cercare

    Returns:
        Lista dei problemi trovati con dettagli
    """
    import time

    # Verifica se il file esiste
    if file_id not in processed_files:
        raise HTTPException(status_code=404, detail=f"File con ID {file_id} non trovato")

    # Ottieni i tipi di problemi da cercare (o usa tutti quelli disponibili)
    issue_types = request.issue_types if request and request.issue_types else [
        "missing_values", "negative_duration", "excessive_duration", "future_date"
    ]

    try:
        # Misura il tempo di elaborazione
        start_time = time.time()

        # Ottieni i dati elaborati
        file_info = processed_files[file_id]

        if "processed_dataframe" not in file_info:
            raise HTTPException(status_code=400, detail=f"File con ID {file_id} non è stato elaborato")

        # Ottieni il DataFrame elaborato
        df_processed = file_info["processed_dataframe"]

        # Ottieni il report di elaborazione
        processing_report = file_info.get("processing_report", {})

        # Lista per memorizzare i problemi trovati
        issues_found = []
        issue_id = 0

        # Verifica valori mancanti
        if "missing_values" in issue_types:
            missing_values = processing_report.get("missing_values", {})
            for column, count in missing_values.items():
                # Trova le righe con valori mancanti in questa colonna
                missing_rows = df_processed[df_processed[column].isna()].index.tolist()
                for row_index in missing_rows:
                    issue_id += 1
                    issues_found.append(Issue(
                        type="missing_value",
                        row_index=int(row_index),
                        column=column,
                        description=f"Valore mancante nella colonna '{column}'"
                    ))

        # Verifica anomalie
        anomalies = processing_report.get("anomalies", [])
        for anomaly in anomalies:
            if anomaly["type"] == "negative_duration" and "negative_duration" in issue_types:
                issue_id += 1
                issues_found.append(Issue(
                    type="negative_duration",
                    row_index=int(anomaly["row_index"]),
                    column="Durata_Attivita_Ore_Decimali",
                    value=anomaly["value"],
                    description=f"Durata negativa: {anomaly['value']} ore"
                ))
            elif anomaly["type"] == "excessive_duration" and "excessive_duration" in issue_types:
                issue_id += 1
                issues_found.append(Issue(
                    type="excessive_duration",
                    row_index=int(anomaly["row_index"]),
                    column="Durata_Attivita_Ore_Decimali",
                    value=anomaly["value"],
                    description=f"Durata eccessiva: {anomaly['value']} ore"
                ))
            elif anomaly["type"] == "future_date" and "future_date" in issue_types:
                issue_id += 1
                issues_found.append(Issue(
                    type="future_date",
                    row_index=int(anomaly["row_index"]),
                    column=anomaly["column"],
                    value=anomaly["value"],
                    description=f"Data futura: {anomaly['value']}"
                ))

        # Calcola il tempo di elaborazione
        processing_time = time.time() - start_time

        # Conta i tipi di problemi
        issue_type_counts = {}
        for issue in issues_found:
            if issue.type not in issue_type_counts:
                issue_type_counts[issue.type] = 0
            issue_type_counts[issue.type] += 1

        return IssueIdentificationResponse(
            file_id=file_id,
            issues_found=issues_found,
            total_issues=len(issues_found),
            issue_types=issue_type_counts,
            processing_time=processing_time
        )

    except HTTPException:
        # Rilancia le eccezioni HTTP
        raise
    except Exception as e:
        logger.error(f"Errore nell'identificazione dei problemi: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore nell'identificazione dei problemi: {str(e)}")

@app.post("/suggest-corrections/{file_id}", response_model=CorrectionSuggestionResponse)
async def suggest_corrections(file_id: str, request: CorrectionSuggestionRequest):
    """
    Suggerisce correzioni per i problemi identificati nei dati.

    Args:
        file_id: ID del file
        request: Richiesta contenente i problemi da correggere

    Returns:
        Lista delle correzioni suggerite
    """
    import time

    # Verifica se il file esiste
    if file_id not in processed_files:
        raise HTTPException(status_code=404, detail=f"File con ID {file_id} non trovato")

    try:
        # Misura il tempo di elaborazione
        start_time = time.time()

        # Ottieni i dati elaborati
        file_info = processed_files[file_id]

        if "processed_dataframe" not in file_info:
            raise HTTPException(status_code=400, detail=f"File con ID {file_id} non è stato elaborato")

        # Ottieni il DataFrame elaborato
        df_processed = file_info["processed_dataframe"]

        # Lista per memorizzare le correzioni suggerite
        corrections = []

        # Suggerisci correzioni per ogni problema
        for i, issue in enumerate(request.issues):
            if issue.type == "missing_value":
                # Suggerisci un valore di default in base al tipo di colonna
                if issue.column in df_processed.columns:
                    column_type = str(df_processed[issue.column].dtype)

                    if "float" in column_type or "int" in column_type:
                        # Per colonne numeriche, suggerisci la media o zero
                        mean_value = df_processed[issue.column].mean()
                        if pd.notna(mean_value):
                            new_value = mean_value
                            confidence = 0.7
                            description = f"Sostituisci con la media della colonna: {mean_value:.2f}"
                        else:
                            new_value = 0
                            confidence = 0.5
                            description = "Sostituisci con zero"

                    elif "datetime" in column_type:
                        # Per date, suggerisci la data mediana
                        median_date = df_processed[issue.column].median()
                        if pd.notna(median_date):
                            new_value = median_date.isoformat()
                            confidence = 0.6
                            description = f"Sostituisci con la data mediana: {median_date.strftime('%d/%m/%Y')}"
                        else:
                            new_value = pd.Timestamp.now().isoformat()
                            confidence = 0.4
                            description = "Sostituisci con la data corrente"

                    else:
                        # Per stringhe, suggerisci il valore più comune o "N/A"
                        most_common = df_processed[issue.column].mode()
                        if len(most_common) > 0 and pd.notna(most_common[0]):
                            new_value = most_common[0]
                            confidence = 0.6
                            description = f"Sostituisci con il valore più comune: '{new_value}'"
                        else:
                            new_value = "N/A"
                            confidence = 0.5
                            description = "Sostituisci con 'N/A'"

                    corrections.append(Correction(
                        issue_id=i,
                        type="missing_value_correction",
                        row_index=issue.row_index,
                        column=issue.column,
                        old_value=None,
                        new_value=new_value,
                        description=description,
                        confidence=confidence
                    ))

            elif issue.type == "negative_duration":
                # Suggerisci di convertire la durata negativa in positiva
                abs_value = abs(float(issue.value))
                corrections.append(Correction(
                    issue_id=i,
                    type="negative_duration_correction",
                    row_index=issue.row_index,
                    column=issue.column,
                    old_value=issue.value,
                    new_value=abs_value,
                    description=f"Converti la durata negativa in positiva: {abs_value} ore",
                    confidence=0.9
                ))

            elif issue.type == "excessive_duration":
                # Suggerisci di limitare la durata a un valore massimo ragionevole (8 ore)
                max_reasonable = 8.0
                corrections.append(Correction(
                    issue_id=i,
                    type="excessive_duration_correction",
                    row_index=issue.row_index,
                    column=issue.column,
                    old_value=issue.value,
                    new_value=max_reasonable,
                    description=f"Limita la durata eccessiva a {max_reasonable} ore",
                    confidence=0.7
                ))

            elif issue.type == "future_date":
                # Suggerisci di impostare la data al giorno corrente
                current_date = pd.Timestamp.now().isoformat()
                corrections.append(Correction(
                    issue_id=i,
                    type="future_date_correction",
                    row_index=issue.row_index,
                    column=issue.column,
                    old_value=issue.value,
                    new_value=current_date,
                    description="Imposta la data al giorno corrente",
                    confidence=0.8
                ))

        # Calcola il tempo di elaborazione
        processing_time = time.time() - start_time

        return CorrectionSuggestionResponse(
            file_id=file_id,
            corrections=corrections,
            total_corrections=len(corrections),
            processing_time=processing_time
        )

    except HTTPException:
        # Rilancia le eccezioni HTTP
        raise
    except Exception as e:
        logger.error(f"Errore nel suggerimento delle correzioni: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore nel suggerimento delle correzioni: {str(e)}")

@app.post("/apply-corrections/{file_id}", response_model=CorrectionApplicationResponse)
async def apply_corrections(file_id: str, request: CorrectionApplicationRequest):
    """
    Applica le correzioni suggerite ai dati.

    Args:
        file_id: ID del file
        request: Richiesta contenente le correzioni da applicare

    Returns:
        Conferma delle correzioni applicate
    """
    import time

    # Verifica se il file esiste
    if file_id not in processed_files:
        raise HTTPException(status_code=404, detail=f"File con ID {file_id} non trovato")

    try:
        # Misura il tempo di elaborazione
        start_time = time.time()

        # Ottieni i dati elaborati
        file_info = processed_files[file_id]

        if "processed_dataframe" not in file_info:
            raise HTTPException(status_code=400, detail=f"File con ID {file_id} non è stato elaborato")

        # Ottieni il DataFrame elaborato (crea una copia per non modificare l'originale)
        df_processed = file_info["processed_dataframe"].copy()

        # Lista per memorizzare le correzioni applicate
        corrections_applied = []

        # Applica le correzioni
        for correction in request.corrections:
            try:
                # Verifica che la riga e la colonna esistano
                if correction.row_index >= len(df_processed):
                    continue

                if correction.column and correction.column not in df_processed.columns:
                    continue

                # Applica la correzione in base al tipo
                if correction.type == "missing_value_correction" and correction.column:
                    # Converti il valore al tipo corretto
                    column_type = str(df_processed[correction.column].dtype)
                    new_value = correction.new_value

                    if "float" in column_type:
                        new_value = float(new_value)
                    elif "int" in column_type:
                        new_value = int(float(new_value))

                    # Applica la correzione
                    df_processed.at[correction.row_index, correction.column] = new_value
                    corrections_applied.append(correction)

                elif correction.type == "negative_duration_correction" and correction.column:
                    # Applica la correzione
                    df_processed.at[correction.row_index, correction.column] = float(correction.new_value)
                    corrections_applied.append(correction)

                elif correction.type == "excessive_duration_correction" and correction.column:
                    # Applica la correzione
                    df_processed.at[correction.row_index, correction.column] = float(correction.new_value)
                    corrections_applied.append(correction)

                elif correction.type == "future_date_correction" and correction.column:
                    # Converti la stringa ISO in timestamp
                    new_date = pd.Timestamp(correction.new_value)
                    # Applica la correzione
                    df_processed.at[correction.row_index, correction.column] = new_date
                    corrections_applied.append(correction)

            except Exception as e:
                logger.warning(f"Errore nell'applicazione della correzione {correction.type} alla riga {correction.row_index}: {str(e)}")
                continue

        # Aggiorna il DataFrame elaborato
        file_info["processed_dataframe"] = df_processed

        # Aggiorna il report di elaborazione
        if "processing_report" in file_info:
            # Ricalcola le statistiche
            statistics = activity_processor._calculate_statistics(df_processed)
            file_info["processing_report"]["statistics"] = statistics

            # Ricalcola i problemi
            missing_values, anomalies = activity_processor._identify_issues(df_processed)
            file_info["processing_report"]["missing_values"] = missing_values
            file_info["processing_report"]["anomalies"] = anomalies

        # Calcola il tempo di elaborazione
        processing_time = time.time() - start_time

        return CorrectionApplicationResponse(
            file_id=file_id,
            corrections_applied=corrections_applied,
            total_applied=len(corrections_applied),
            success=True,
            message=f"Applicate {len(corrections_applied)} correzioni su {len(request.corrections)} richieste",
            processing_time=processing_time
        )

    except HTTPException:
        # Rilancia le eccezioni HTTP
        raise
    except Exception as e:
        logger.error(f"Errore nell'applicazione delle correzioni: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore nell'applicazione delle correzioni: {str(e)}")

@app.post("/standardize-formats/{file_id}", response_model=FormatStandardizationResponse)
async def standardize_formats(file_id: str, request: FormatStandardizationRequest):
    """
    Standardizza i formati di date, durate e altri campi.

    Args:
        file_id: ID del file
        request: Richiesta contenente le specifiche dei formati da applicare

    Returns:
        Dettagli sulle standardizzazioni applicate
    """
    import time

    # Verifica se il file esiste
    if file_id not in processed_files:
        raise HTTPException(status_code=404, detail=f"File con ID {file_id} non trovato")

    try:
        # Misura il tempo di elaborazione
        start_time = time.time()

        # Ottieni i dati elaborati
        file_info = processed_files[file_id]

        if "processed_dataframe" not in file_info:
            raise HTTPException(status_code=400, detail=f"File con ID {file_id} non è stato elaborato")

        # Ottieni il DataFrame elaborato (crea una copia per non modificare l'originale)
        df_processed = file_info["processed_dataframe"].copy()

        # Lista per memorizzare le colonne standardizzate
        standardized_columns = []

        # Applica le standardizzazioni
        for column, format_spec in request.format_specs.items():
            if column not in df_processed.columns:
                continue

            # Standardizza in base al tipo di formato
            if format_spec == "date":
                # Converti in formato data standard
                try:
                    df_processed[column] = pd.to_datetime(df_processed[column]).dt.date
                    standardized_columns.append(column)
                except:
                    logger.warning(f"Impossibile standardizzare la colonna {column} come data")

            elif format_spec == "datetime":
                # Converti in formato datetime standard
                try:
                    df_processed[column] = pd.to_datetime(df_processed[column])
                    standardized_columns.append(column)
                except:
                    logger.warning(f"Impossibile standardizzare la colonna {column} come datetime")

            elif format_spec == "duration_decimal":
                # Converti in formato durata decimale
                try:
                    # Usa il parser di durate per convertire
                    df_processed[column] = df_processed[column].apply(
                        lambda x: duration_parser.parse_duration(str(x)) if pd.notna(x) else x
                    )
                    standardized_columns.append(column)
                except:
                    logger.warning(f"Impossibile standardizzare la colonna {column} come durata decimale")

            elif format_spec == "duration_hhmm":
                # Converti in formato durata HH:MM
                try:
                    # Converti ore decimali in formato HH:MM
                    df_processed[column] = df_processed[column].apply(
                        lambda x: duration_parser.decimal_to_hhmm(float(x)) if pd.notna(x) else x
                    )
                    standardized_columns.append(column)
                except:
                    logger.warning(f"Impossibile standardizzare la colonna {column} come durata HH:MM")

            elif format_spec == "text_uppercase":
                # Converti in maiuscolo
                try:
                    df_processed[column] = df_processed[column].str.upper()
                    standardized_columns.append(column)
                except:
                    logger.warning(f"Impossibile standardizzare la colonna {column} in maiuscolo")

            elif format_spec == "text_lowercase":
                # Converti in minuscolo
                try:
                    df_processed[column] = df_processed[column].str.lower()
                    standardized_columns.append(column)
                except:
                    logger.warning(f"Impossibile standardizzare la colonna {column} in minuscolo")

        # Aggiorna il DataFrame elaborato
        file_info["processed_dataframe"] = df_processed

        # Calcola il tempo di elaborazione
        processing_time = time.time() - start_time

        return FormatStandardizationResponse(
            file_id=file_id,
            standardized_columns=standardized_columns,
            total_standardized=len(standardized_columns),
            success=True,
            message=f"Standardizzate {len(standardized_columns)} colonne su {len(request.format_specs)} richieste",
            processing_time=processing_time
        )

    except HTTPException:
        # Rilancia le eccezioni HTTP
        raise
    except Exception as e:
        logger.error(f"Errore nella standardizzazione dei formati: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore nella standardizzazione dei formati: {str(e)}")

# Avvio dell'applicazione
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
