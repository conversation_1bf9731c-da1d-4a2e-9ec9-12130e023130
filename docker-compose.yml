version: "3.8"

services:
  # Servizio per l'applicazione principale Flask
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: app-roberto-flask
    ports:
      - "5001:5001"
    volumes:
      - .:/app
      - ./uploads:/app/uploads
    environment:
      - FLASK_DEBUG=1
      - FLASK_APP=app.py
      - MCP_URL=http://mcp_server:8000
      - DOCKER_CONTAINER=true
    depends_on:
      - mcp_server
    networks:
      - app-network
    command: python app.py

  # Servizio per il server MCP (MODEL CONTEXT PROTOCOL)
  mcp_server:
    build:
      context: ./mcp_server
      dockerfile: Dockerfile
    container_name: app-roberto-mcp
    ports:
      - "8000:8000"
    volumes:
      - ./mcp_server:/app
      - ./uploads:/app/uploads
    environment:
      - PYTHONPATH=/app
    networks:
      - app-network
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

networks:
  app-network:
    driver: bridge
