#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Fixture comuni per i test.
"""

import os
import sys
import pytest
import pandas as pd
from io import StringIO
from flask import Flask
from unittest.mock import MagicMock

# Aggiungi la directory principale al path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Fixture per creare un'app Flask di test
@pytest.fixture
def app():
    """Crea un'app Flask di test."""
    app = Flask(__name__)
    app.config['TESTING'] = True
    app.config['UPLOAD_FOLDER'] = 'tests/uploads'
    app.config['ALLOWED_EXTENSIONS'] = {'csv', 'xlsx', 'xls'}
    app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max
    app.secret_key = 'test_secret_key'
    
    # Crea la cartella uploads se non esiste
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    
    return app

# Fixture per creare un client Flask di test
@pytest.fixture
def client(app):
    """Crea un client Flask di test."""
    return app.test_client()

# Fixture per creare un DataFrame di test con dati di attività
@pytest.fixture
def activity_df():
    """Crea un DataFrame di test con dati di attività."""
    data = """
    Data,Ora Inizio,Ora Fine,Durata,Attività,Progetto,Note
    01/01/2023,09:00,10:30,1:30,Sviluppo,Progetto A,Implementazione feature X
    01/01/2023,11:00,12:30,1:30,Testing,Progetto A,Test feature X
    02/01/2023,09:00,11:00,2:00,Riunione,Progetto B,Kickoff meeting
    02/01/2023,14:00,17:00,3:00,Sviluppo,Progetto B,Implementazione feature Y
    """
    return pd.read_csv(StringIO(data.strip()), sep=',')

# Fixture per creare un DataFrame di test con dati di TeamViewer
@pytest.fixture
def teamviewer_df():
    """Crea un DataFrame di test con dati di TeamViewer."""
    data = """
    ID Sessione,Utente,Computer,Gruppo,Data,Ora Inizio,Ora Fine,Durata
    12345,User1,PC-001,Gruppo A,01/01/2023,09:00,10:30,01:30:00
    12346,User1,PC-002,Gruppo A,01/01/2023,11:00,12:30,01:30:00
    12347,User2,PC-003,Gruppo B,02/01/2023,09:00,11:00,02:00:00
    12348,User2,PC-004,Gruppo B,02/01/2023,14:00,17:00,03:00:00
    """
    return pd.read_csv(StringIO(data.strip()), sep=',')

# Fixture per creare un client MCP di test
@pytest.fixture
def mcp_client():
    """Crea un client MCP di test."""
    mock_client = MagicMock()
    mock_client.upload_file.return_value = {"file_id": "test_file_id"}
    mock_client.analyze_file.return_value = {"analysis": "test_analysis"}
    mock_client.clean_file.return_value = {"cleaned_file_id": "test_cleaned_file_id"}
    return mock_client

# Fixture per creare un client OpenRouter di test
@pytest.fixture
def openrouter_client():
    """Crea un client OpenRouter di test."""
    mock_client = MagicMock()
    mock_client.chat_completion.return_value = {
        "choices": [
            {
                "message": {
                    "content": "Test response"
                }
            }
        ]
    }
    return mock_client
