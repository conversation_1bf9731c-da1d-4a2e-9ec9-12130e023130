#!/usr/bin/env python3
"""
Script per analizzare completamente il sistema attuale e identificare
tutte le dipendenze dal nome del file e le mappature delle colonne.
"""

import os
import re
import ast
import json
from pathlib import Path
from typing import Dict, List, Set, Any

class SystemAnalyzer:
    """Analizza il sistema attuale per identificare dipendenze e mappature"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.analysis_results = {
            "filename_dependencies": [],
            "column_mappings": {},
            "hardcoded_patterns": [],
            "processor_dependencies": {},
            "dashboard_dependencies": [],
            "file_extensions": set(),
            "column_names_found": set()
        }
    
    def analyze_complete_system(self) -> Dict[str, Any]:
        """Esegue l'analisi completa del sistema"""
        print("🔍 Avvio analisi completa del sistema...")
        
        # 1. Ana<PERSON><PERSON> dipendenze dal nome file
        self._analyze_filename_dependencies()
        
        # 2. Analizza mappature colonne
        self._analyze_column_mappings()
        
        # 3. <PERSON><PERSON><PERSON> pattern hardcoded
        self._analyze_hardcoded_patterns()
        
        # 4. Analizza processori
        self._analyze_processors()
        
        # 5. Analizza dashboard e template
        self._analyze_dashboard_dependencies()
        
        # 6. Genera report
        self._generate_analysis_report()
        
        return self.analysis_results
    
    def _analyze_filename_dependencies(self):
        """Trova tutte le dipendenze dal nome del file"""
        print("📁 Analizzando dipendenze dal nome file...")
        
        python_files = list(self.project_root.glob("**/*.py"))
        
        filename_patterns = [
            r"filename.*\.lower\(\)",
            r"'teamviewer'.*in.*filename",
            r"'calendario'.*in.*filename", 
            r"'attivita'.*in.*filename",
            r"'timbrature'.*in.*filename",
            r"'permessi'.*in.*filename",
            r"'controlli'.*in.*filename",
            r"filename.*endswith",
            r"file_extension.*==",
            r"\.csv.*in.*filename",
            r"\.xlsx.*in.*filename"
        ]
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    
                    for i, line in enumerate(lines, 1):
                        for pattern in filename_patterns:
                            if re.search(pattern, line, re.IGNORECASE):
                                self.analysis_results["filename_dependencies"].append({
                                    "file": str(py_file),
                                    "line": i,
                                    "code": line.strip(),
                                    "pattern": pattern
                                })
            except Exception as e:
                print(f"⚠️ Errore leggendo {py_file}: {e}")
    
    def _analyze_column_mappings(self):
        """Analizza tutte le mappature delle colonne"""
        print("🗂️ Analizzando mappature colonne...")
        
        # File con mappature note
        mapping_files = [
            "data_processor.py",
            "column_mapper.py", 
            "teamviewer_processor.py",
            "calendar_processor.py",
            "attendance_processor.py",
            "file_detector.py"
        ]
        
        for mapping_file in mapping_files:
            file_path = self.project_root / mapping_file
            if not file_path.exists():
                file_path = self.project_root / "mcp_server" / mapping_file
            
            if file_path.exists():
                self._extract_mappings_from_file(file_path, mapping_file)
    
    def _extract_mappings_from_file(self, file_path: Path, file_name: str):
        """Estrae mappature da un file specifico"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Cerca dizionari di mappatura
            mapping_patterns = [
                r"(\w+_fields)\s*=\s*{([^}]+)}",
                r"(\w+_columns)\s*=\s*{([^}]+)}",
                r"(COLUMN_MAPPINGS)\s*=\s*{",
                r"(FILE_SIGNATURES)\s*=\s*{",
                r"(standard_columns)\s*=\s*{"
            ]
            
            mappings_found = []
            
            for pattern in mapping_patterns:
                matches = re.finditer(pattern, content, re.MULTILINE | re.DOTALL)
                for match in matches:
                    mapping_name = match.group(1)
                    
                    # Estrai il contenuto del dizionario
                    start_pos = match.start()
                    brace_count = 0
                    end_pos = start_pos
                    
                    for i, char in enumerate(content[start_pos:], start_pos):
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                end_pos = i + 1
                                break
                    
                    mapping_text = content[start_pos:end_pos]
                    mappings_found.append({
                        "name": mapping_name,
                        "content": mapping_text[:500] + "..." if len(mapping_text) > 500 else mapping_text
                    })
            
            if mappings_found:
                self.analysis_results["column_mappings"][file_name] = mappings_found
                
        except Exception as e:
            print(f"⚠️ Errore analizzando {file_path}: {e}")
    
    def _analyze_hardcoded_patterns(self):
        """Trova pattern hardcoded nel codice"""
        print("🔧 Analizzando pattern hardcoded...")
        
        python_files = list(self.project_root.glob("**/*.py"))
        
        hardcoded_patterns = [
            r"'teamviewer_bait'",
            r"'teamviewer_gruppo'", 
            r"'calendario'",
            r"'attivita'",
            r"'timbrature'",
            r"'permessi'",
            r"'controlli'",
            r"'Utente'.*'Computer'",
            r"'Data'.*'Ora inizio'",
            r"'Dipendente'.*'Entrata'"
        ]
        
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    
                    for i, line in enumerate(lines, 1):
                        for pattern in hardcoded_patterns:
                            if re.search(pattern, line):
                                self.analysis_results["hardcoded_patterns"].append({
                                    "file": str(py_file),
                                    "line": i,
                                    "code": line.strip(),
                                    "pattern": pattern
                                })
            except Exception as e:
                print(f"⚠️ Errore leggendo {py_file}: {e}")
    
    def _analyze_processors(self):
        """Analizza i processori di dati"""
        print("⚙️ Analizzando processori...")
        
        processor_files = [
            "teamviewer_processor.py",
            "calendar_processor.py", 
            "attendance_processor.py",
            "data_processor.py"
        ]
        
        for processor_file in processor_files:
            file_path = self.project_root / processor_file
            if not file_path.exists():
                file_path = self.project_root / "mcp_server" / processor_file
            
            if file_path.exists():
                self._analyze_processor_file(file_path, processor_file)
    
    def _analyze_processor_file(self, file_path: Path, file_name: str):
        """Analizza un file processore specifico"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Cerca metodi di elaborazione
            methods = re.findall(r"def\s+(\w+)\s*\([^)]*\):", content)
            
            # Cerca colonne hardcoded
            column_refs = re.findall(r"'([A-Za-z_][A-Za-z0-9_\s]*)'", content)
            
            self.analysis_results["processor_dependencies"][file_name] = {
                "methods": methods,
                "column_references": list(set(column_refs)),
                "file_path": str(file_path)
            }
            
            # Aggiungi nomi colonne al set globale
            self.analysis_results["column_names_found"].update(column_refs)
            
        except Exception as e:
            print(f"⚠️ Errore analizzando {file_path}: {e}")
    
    def _analyze_dashboard_dependencies(self):
        """Analizza dipendenze nei template e dashboard"""
        print("📊 Analizzando dipendenze dashboard...")
        
        template_files = list(self.project_root.glob("templates/**/*.html"))
        js_files = list(self.project_root.glob("static/**/*.js"))
        
        all_files = template_files + js_files
        
        dashboard_patterns = [
            r"file_type\s*==",
            r"teamviewer.*in.*file_type",
            r"calendario.*==.*file_type",
            r"attivita.*==.*file_type",
            r"\.csv.*endswith",
            r"\.xlsx.*endswith"
        ]
        
        for template_file in all_files:
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.split('\n')
                    
                    for i, line in enumerate(lines, 1):
                        for pattern in dashboard_patterns:
                            if re.search(pattern, line, re.IGNORECASE):
                                self.analysis_results["dashboard_dependencies"].append({
                                    "file": str(template_file),
                                    "line": i,
                                    "code": line.strip(),
                                    "pattern": pattern
                                })
            except Exception as e:
                print(f"⚠️ Errore leggendo {template_file}: {e}")
    
    def _generate_analysis_report(self):
        """Genera un report dettagliato dell'analisi"""
        print("📋 Generando report analisi...")
        
        # Converti set in list per JSON serialization
        self.analysis_results["file_extensions"] = list(self.analysis_results["file_extensions"])
        self.analysis_results["column_names_found"] = list(self.analysis_results["column_names_found"])
        
        # Salva risultati in JSON
        with open("system_analysis_report.json", "w", encoding="utf-8") as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)
        
        # Genera report testuale
        self._generate_text_report()
    
    def _generate_text_report(self):
        """Genera report in formato testo leggibile"""
        report_lines = [
            "# 🔍 ANALISI SISTEMA ATTUALE - REPORT DETTAGLIATO",
            "=" * 60,
            "",
            f"## 📁 DIPENDENZE DAL NOME FILE ({len(self.analysis_results['filename_dependencies'])} trovate)",
            ""
        ]
        
        for dep in self.analysis_results['filename_dependencies'][:10]:  # Prime 10
            report_lines.append(f"- **{dep['file']}:{dep['line']}**")
            report_lines.append(f"  ```{dep['code']}```")
            report_lines.append("")
        
        report_lines.extend([
            f"## 🗂️ MAPPATURE COLONNE ({len(self.analysis_results['column_mappings'])} file)",
            ""
        ])
        
        for file_name, mappings in self.analysis_results['column_mappings'].items():
            report_lines.append(f"### {file_name}")
            for mapping in mappings:
                report_lines.append(f"- **{mapping['name']}**")
                report_lines.append(f"  ```{mapping['content'][:200]}...```")
            report_lines.append("")
        
        report_lines.extend([
            f"## 🔧 PATTERN HARDCODED ({len(self.analysis_results['hardcoded_patterns'])} trovati)",
            ""
        ])
        
        for pattern in self.analysis_results['hardcoded_patterns'][:15]:  # Prime 15
            report_lines.append(f"- **{pattern['file']}:{pattern['line']}**")
            report_lines.append(f"  ```{pattern['code']}```")
            report_lines.append("")
        
        report_lines.extend([
            f"## ⚙️ PROCESSORI ANALIZZATI ({len(self.analysis_results['processor_dependencies'])} file)",
            ""
        ])
        
        for proc_name, proc_info in self.analysis_results['processor_dependencies'].items():
            report_lines.append(f"### {proc_name}")
            report_lines.append(f"- **Metodi**: {', '.join(proc_info['methods'][:5])}")
            report_lines.append(f"- **Colonne**: {', '.join(proc_info['column_references'][:10])}")
            report_lines.append("")
        
        report_lines.extend([
            f"## 📊 DIPENDENZE DASHBOARD ({len(self.analysis_results['dashboard_dependencies'])} trovate)",
            ""
        ])
        
        for dep in self.analysis_results['dashboard_dependencies'][:10]:  # Prime 10
            report_lines.append(f"- **{dep['file']}:{dep['line']}**")
            report_lines.append(f"  ```{dep['code']}```")
            report_lines.append("")
        
        report_lines.extend([
            "## 📈 STATISTICHE GENERALI",
            "",
            f"- **Dipendenze filename**: {len(self.analysis_results['filename_dependencies'])}",
            f"- **File con mappature**: {len(self.analysis_results['column_mappings'])}",
            f"- **Pattern hardcoded**: {len(self.analysis_results['hardcoded_patterns'])}",
            f"- **Processori**: {len(self.analysis_results['processor_dependencies'])}",
            f"- **Dipendenze dashboard**: {len(self.analysis_results['dashboard_dependencies'])}",
            f"- **Nomi colonne unici**: {len(self.analysis_results['column_names_found'])}",
            "",
            "## 🎯 PRIORITÀ REFACTORING",
            "",
            "1. **CRITICO**: Rimuovere controlli forzati filename in `app.py`",
            "2. **ALTO**: Unificare sistemi mappatura colonne",
            "3. **MEDIO**: Rendere processori flessibili",
            "4. **BASSO**: Adattare dashboard per strutture variabili"
        ])
        
        # Salva report
        with open("system_analysis_report.md", "w", encoding="utf-8") as f:
            f.write("\n".join(report_lines))

def main():
    """Funzione principale"""
    analyzer = SystemAnalyzer()
    results = analyzer.analyze_complete_system()
    
    print("\n" + "="*60)
    print("✅ ANALISI COMPLETATA!")
    print("="*60)
    print(f"📁 Dipendenze filename: {len(results['filename_dependencies'])}")
    print(f"🗂️ File con mappature: {len(results['column_mappings'])}")
    print(f"🔧 Pattern hardcoded: {len(results['hardcoded_patterns'])}")
    print(f"⚙️ Processori: {len(results['processor_dependencies'])}")
    print(f"📊 Dipendenze dashboard: {len(results['dashboard_dependencies'])}")
    print(f"📝 Nomi colonne unici: {len(results['column_names_found'])}")
    print("\n📋 Report salvati:")
    print("- system_analysis_report.json")
    print("- system_analysis_report.md")

if __name__ == "__main__":
    main()
