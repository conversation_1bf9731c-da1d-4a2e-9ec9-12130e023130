#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Performance Profiler - Sistema di profiling e analisi performance per app-roberto.
Monitora e analizza le performance dell'applicazione in tempo reale identificando bottleneck.
"""

import time
import threading
import logging
import functools
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
from collections import defaultdict, deque

# Import librerie di sistema
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    # psutil non disponibile - profiling limitato alla memoria base

# Import dei moduli esistenti
try:
    from intelligent_cache_system import intelligent_cache
    from query_optimizer import query_optimizer
except ImportError as e:
    logging.warning(f"Alcuni moduli non disponibili: {e}")

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BottleneckType(Enum):
    """Tipi di bottleneck."""
    CPU_INTENSIVE = "cpu_intensive"
    MEMORY_LEAK = "memory_leak"
    IO_BOUND = "io_bound"
    DATABASE_SLOW = "database_slow"
    CACHE_MISS = "cache_miss"
    NETWORK_LATENCY = "network_latency"
    LOCK_CONTENTION = "lock_contention"

class ProfilerLevel(Enum):
    """Livelli di profiling."""
    BASIC = "basic"
    DETAILED = "detailed"
    COMPREHENSIVE = "comprehensive"

@dataclass
class PerformanceSnapshot:
    """Snapshot delle performance di sistema."""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float
    active_threads: int
    open_files: int

@dataclass
class FunctionProfile:
    """Profilo di performance di una funzione."""
    function_name: str
    module_name: str
    call_count: int
    total_time: float
    avg_time: float
    min_time: float
    max_time: float
    memory_usage_mb: float
    last_called: datetime
    bottleneck_score: float = 0.0

@dataclass
class BottleneckAlert:
    """Allerta per bottleneck rilevato."""
    bottleneck_type: BottleneckType
    severity: str  # low, medium, high, critical
    description: str
    affected_function: Optional[str]
    metric_value: float
    threshold: float
    timestamp: datetime
    suggestions: List[str]

class PerformanceProfiler:
    """
    Sistema di profiling performance che:
    - Monitora performance di sistema in tempo reale
    - Profila funzioni e metodi automaticamente
    - Rileva bottleneck e genera allerte
    - Fornisce suggerimenti di ottimizzazione
    - Integra con cache e query optimizer
    """

    def __init__(self):
        # Configurazione profiler
        self.PROFILING_LEVEL = ProfilerLevel.DETAILED
        self.SNAPSHOT_INTERVAL = 15  # secondi - Aumentato da 5s a 15s per ridurre overhead
        self.MAX_SNAPSHOTS = 1000
        self.BOTTLENECK_THRESHOLDS = {
            "cpu_percent": 95.0,  # Aumentato da 80% a 95% per ridurre falsi allarmi
            "memory_percent": 90.0,  # Aumentato da 85% a 90%
            "function_time": 2.0,  # Aumentato da 1.0s a 2.0s
            "cache_miss_rate": 0.7  # Aumentato da 50% a 70%
        }

        # Storage dati profiling
        self.performance_snapshots = deque(maxlen=self.MAX_SNAPSHOTS)
        self.function_profiles = {}
        self.bottleneck_alerts = deque(maxlen=100)
        self.active_profiles = {}  # Profili attivi per funzioni in esecuzione

        # Metriche sistema
        self.system_metrics = {
            "baseline_cpu": 0.0,
            "baseline_memory": 0.0,
            "peak_cpu": 0.0,
            "peak_memory": 0.0,
            "total_function_calls": 0,
            "slow_function_calls": 0
        }

        # Threading
        self.monitoring_active = False
        self.monitoring_thread = None
        self.lock = threading.RLock()

        # Inizializza baseline
        if PSUTIL_AVAILABLE:
            self._establish_baseline()

        # Avvia monitoraggio solo se non disabilitato
        import os
        if not os.environ.get('DISABLE_PERFORMANCE_MONITORING'):
            self.start_monitoring()
            logger.info("PerformanceProfiler inizializzato con monitoraggio attivo")
        else:
            logger.info("PerformanceProfiler inizializzato ma monitoraggio disabilitato (modalità minimal)")

    def profile_function(self, func: Callable = None, *,
                        name: str = None,
                        track_memory: bool = True,
                        alert_threshold: float = None):
        """
        Decoratore per profilare funzioni.

        Args:
            func: Funzione da profilare
            name: Nome personalizzato per il profilo
            track_memory: Se tracciare l'uso memoria
            alert_threshold: Soglia per allerte (secondi)
        """
        def decorator(f):
            profile_name = name or f"{f.__module__}.{f.__name__}"
            threshold = alert_threshold or self.BOTTLENECK_THRESHOLDS["function_time"]

            @functools.wraps(f)
            def wrapper(*args, **kwargs):
                return self._execute_with_profiling(
                    f, args, kwargs, profile_name, track_memory, threshold
                )
            return wrapper

        if func is None:
            return decorator
        else:
            return decorator(func)

    def _execute_with_profiling(self, func: Callable, args: tuple, kwargs: dict,
                               profile_name: str, track_memory: bool, threshold: float):
        """Esegue funzione con profiling completo."""
        start_time = time.time()
        start_memory = self._get_memory_usage() if track_memory and PSUTIL_AVAILABLE else 0.0

        # Registra inizio esecuzione
        execution_id = id(threading.current_thread()) + int(start_time * 1000000)
        self.active_profiles[execution_id] = {
            "function": profile_name,
            "start_time": start_time,
            "start_memory": start_memory
        }

        try:
            # Esegui funzione
            result = func(*args, **kwargs)

            # Calcola metriche
            end_time = time.time()
            execution_time = end_time - start_time
            end_memory = self._get_memory_usage() if track_memory and PSUTIL_AVAILABLE else 0.0
            memory_delta = max(0, end_memory - start_memory)

            # Aggiorna profilo funzione
            self._update_function_profile(
                profile_name, execution_time, memory_delta
            )

            # Verifica bottleneck
            if execution_time > threshold:
                self._detect_function_bottleneck(
                    profile_name, execution_time, threshold
                )

            return result

        except Exception as e:
            # Registra errore nel profilo
            execution_time = time.time() - start_time
            self._update_function_profile(
                profile_name, execution_time, 0.0
            )
            raise

        finally:
            # Pulisci profilo attivo
            if execution_id in self.active_profiles:
                del self.active_profiles[execution_id]

    def start_monitoring(self):
        """Avvia monitoraggio continuo delle performance."""
        if self.monitoring_active or not PSUTIL_AVAILABLE:
            return

        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitoring_thread.start()
        logger.info("🔍 Monitoraggio performance avviato")

    def stop_monitoring(self):
        """Ferma monitoraggio delle performance."""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        logger.info("⏹️ Monitoraggio performance fermato")

    def get_performance_report(self) -> Dict[str, Any]:
        """Genera report completo delle performance."""
        with self.lock:
            # Calcola statistiche sistema
            recent_snapshots = list(self.performance_snapshots)[-20:]  # Ultimi 20 snapshot

            if not recent_snapshots:
                return {"error": "Nessun dato di performance disponibile"}

            avg_cpu = sum(s.cpu_percent for s in recent_snapshots) / len(recent_snapshots)
            avg_memory = sum(s.memory_percent for s in recent_snapshots) / len(recent_snapshots)

            # Top funzioni lente
            slow_functions = sorted(
                self.function_profiles.values(),
                key=lambda x: x.avg_time,
                reverse=True
            )[:10]

            # Bottleneck attivi
            recent_bottlenecks = [
                alert for alert in self.bottleneck_alerts
                if (datetime.now() - alert.timestamp).total_seconds() < 300  # Ultimi 5 minuti
            ]

            return {
                "timestamp": datetime.now().isoformat(),
                "system_performance": {
                    "avg_cpu_percent": avg_cpu,
                    "avg_memory_percent": avg_memory,
                    "peak_cpu": self.system_metrics["peak_cpu"],
                    "peak_memory": self.system_metrics["peak_memory"],
                    "baseline_cpu": self.system_metrics["baseline_cpu"],
                    "baseline_memory": self.system_metrics["baseline_memory"]
                },
                "function_performance": {
                    "total_functions_profiled": len(self.function_profiles),
                    "total_calls": self.system_metrics["total_function_calls"],
                    "slow_calls": self.system_metrics["slow_function_calls"],
                    "slow_call_percentage": (
                        self.system_metrics["slow_function_calls"] /
                        max(1, self.system_metrics["total_function_calls"]) * 100
                    )
                },
                "bottlenecks": {
                    "active_bottlenecks": len(recent_bottlenecks),
                    "total_alerts": len(self.bottleneck_alerts),
                    "by_type": self._group_bottlenecks_by_type(recent_bottlenecks)
                },
                "top_slow_functions": [
                    {
                        "name": f.function_name,
                        "avg_time_ms": f.avg_time * 1000,
                        "call_count": f.call_count,
                        "total_time_s": f.total_time,
                        "bottleneck_score": f.bottleneck_score
                    }
                    for f in slow_functions
                ],
                "recommendations": self._generate_performance_recommendations()
            }

    def _establish_baseline(self):
        """Stabilisce baseline delle performance di sistema."""
        if not PSUTIL_AVAILABLE:
            return

        # Prendi 5 misurazioni in 10 secondi per baseline
        cpu_readings = []
        memory_readings = []

        for _ in range(5):
            cpu_readings.append(psutil.cpu_percent(interval=1))
            memory_readings.append(psutil.virtual_memory().percent)
            time.sleep(1)

        self.system_metrics["baseline_cpu"] = sum(cpu_readings) / len(cpu_readings)
        self.system_metrics["baseline_memory"] = sum(memory_readings) / len(memory_readings)

        logger.info(f"Baseline stabilita - CPU: {self.system_metrics['baseline_cpu']:.1f}%, "
                   f"Memory: {self.system_metrics['baseline_memory']:.1f}%")

    def _monitoring_loop(self):
        """Loop principale di monitoraggio performance."""
        logger.info("🔄 Loop monitoraggio performance avviato")

        while self.monitoring_active:
            try:
                # Cattura snapshot performance
                snapshot = self._capture_performance_snapshot()
                if snapshot:
                    with self.lock:
                        self.performance_snapshots.append(snapshot)

                        # Aggiorna metriche peak
                        self.system_metrics["peak_cpu"] = max(
                            self.system_metrics["peak_cpu"],
                            snapshot.cpu_percent
                        )
                        self.system_metrics["peak_memory"] = max(
                            self.system_metrics["peak_memory"],
                            snapshot.memory_percent
                        )

                        # Rileva bottleneck di sistema
                        self._detect_system_bottlenecks(snapshot)

                time.sleep(self.SNAPSHOT_INTERVAL)

            except Exception as e:
                logger.error(f"Errore nel monitoraggio performance: {e}")
                time.sleep(self.SNAPSHOT_INTERVAL)

        logger.info("🔄 Loop monitoraggio performance terminato")

    def _capture_performance_snapshot(self) -> Optional[PerformanceSnapshot]:
        """Cattura snapshot corrente delle performance."""
        if not PSUTIL_AVAILABLE:
            return None

        try:
            # Metriche CPU e memoria
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()

            # Metriche I/O disco
            disk_io = psutil.disk_io_counters()
            disk_read_mb = disk_io.read_bytes / (1024 * 1024) if disk_io else 0.0
            disk_write_mb = disk_io.write_bytes / (1024 * 1024) if disk_io else 0.0

            # Metriche rete
            net_io = psutil.net_io_counters()
            net_sent_mb = net_io.bytes_sent / (1024 * 1024) if net_io else 0.0
            net_recv_mb = net_io.bytes_recv / (1024 * 1024) if net_io else 0.0

            # Metriche processo corrente
            current_process = psutil.Process()
            active_threads = current_process.num_threads()

            try:
                open_files = len(current_process.open_files())
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                open_files = 0

            return PerformanceSnapshot(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / (1024 * 1024),
                disk_io_read_mb=disk_read_mb,
                disk_io_write_mb=disk_write_mb,
                network_sent_mb=net_sent_mb,
                network_recv_mb=net_recv_mb,
                active_threads=active_threads,
                open_files=open_files
            )

        except Exception as e:
            logger.warning(f"Errore cattura snapshot: {e}")
            return None

    def _get_memory_usage(self) -> float:
        """Ottiene uso memoria corrente in MB."""
        if not PSUTIL_AVAILABLE:
            return 0.0

        try:
            process = psutil.Process()
            return process.memory_info().rss / (1024 * 1024)
        except:
            return 0.0

    def _update_function_profile(self, function_name: str, execution_time: float,
                               memory_delta: float):
        """Aggiorna profilo di una funzione."""
        with self.lock:
            if function_name not in self.function_profiles:
                # Estrai modulo e nome funzione
                parts = function_name.split('.')
                module_name = '.'.join(parts[:-1]) if len(parts) > 1 else 'unknown'
                func_name = parts[-1] if parts else function_name

                self.function_profiles[function_name] = FunctionProfile(
                    function_name=func_name,
                    module_name=module_name,
                    call_count=0,
                    total_time=0.0,
                    avg_time=0.0,
                    min_time=float('inf'),
                    max_time=0.0,
                    memory_usage_mb=0.0,
                    last_called=datetime.now()
                )

            profile = self.function_profiles[function_name]

            # Aggiorna statistiche
            profile.call_count += 1
            profile.total_time += execution_time
            profile.avg_time = profile.total_time / profile.call_count
            profile.min_time = min(profile.min_time, execution_time)
            profile.max_time = max(profile.max_time, execution_time)
            profile.memory_usage_mb += memory_delta
            profile.last_called = datetime.now()

            # Calcola bottleneck score
            profile.bottleneck_score = self._calculate_bottleneck_score(profile)

            # Aggiorna contatori globali
            self.system_metrics["total_function_calls"] += 1
            if execution_time > self.BOTTLENECK_THRESHOLDS["function_time"]:
                self.system_metrics["slow_function_calls"] += 1

    def _calculate_bottleneck_score(self, profile: FunctionProfile) -> float:
        """Calcola score di bottleneck per una funzione."""
        # Fattori per il calcolo
        time_factor = min(10.0, profile.avg_time / self.BOTTLENECK_THRESHOLDS["function_time"])
        frequency_factor = min(5.0, profile.call_count / 100.0)
        memory_factor = min(3.0, profile.memory_usage_mb / 100.0)

        # Score combinato (0-10)
        score = (time_factor * 0.5 + frequency_factor * 0.3 + memory_factor * 0.2)
        return min(10.0, score)

    def _detect_system_bottlenecks(self, snapshot: PerformanceSnapshot):
        """Rileva bottleneck di sistema."""
        alerts = []

        # CPU bottleneck
        if snapshot.cpu_percent > self.BOTTLENECK_THRESHOLDS["cpu_percent"]:
            alerts.append(BottleneckAlert(
                bottleneck_type=BottleneckType.CPU_INTENSIVE,
                severity="high" if snapshot.cpu_percent > 90 else "medium",
                description=f"Alto utilizzo CPU: {snapshot.cpu_percent:.1f}%",
                affected_function=None,
                metric_value=snapshot.cpu_percent,
                threshold=self.BOTTLENECK_THRESHOLDS["cpu_percent"],
                timestamp=snapshot.timestamp,
                suggestions=[
                    "Verifica processi CPU-intensivi",
                    "Considera ottimizzazione algoritmi",
                    "Valuta parallelizzazione"
                ]
            ))

        # Memory bottleneck
        if snapshot.memory_percent > self.BOTTLENECK_THRESHOLDS["memory_percent"]:
            alerts.append(BottleneckAlert(
                bottleneck_type=BottleneckType.MEMORY_LEAK,
                severity="critical" if snapshot.memory_percent > 95 else "high",
                description=f"Alto utilizzo memoria: {snapshot.memory_percent:.1f}%",
                affected_function=None,
                metric_value=snapshot.memory_percent,
                threshold=self.BOTTLENECK_THRESHOLDS["memory_percent"],
                timestamp=snapshot.timestamp,
                suggestions=[
                    "Verifica memory leak",
                    "Ottimizza cache e buffer",
                    "Considera garbage collection"
                ]
            ))

        # Aggiungi allerte
        for alert in alerts:
            self.bottleneck_alerts.append(alert)
            logger.warning(f"🚨 Bottleneck rilevato: {alert.description}")

    def _detect_function_bottleneck(self, function_name: str, execution_time: float, threshold: float):
        """Rileva bottleneck di funzione."""
        profile = self.function_profiles.get(function_name)
        if not profile:
            return

        severity = "critical" if execution_time > threshold * 3 else "high"

        alert = BottleneckAlert(
            bottleneck_type=BottleneckType.CPU_INTENSIVE,
            severity=severity,
            description=f"Funzione lenta: {function_name} ({execution_time:.3f}s)",
            affected_function=function_name,
            metric_value=execution_time,
            threshold=threshold,
            timestamp=datetime.now(),
            suggestions=[
                "Profila funzione in dettaglio",
                "Verifica algoritmi utilizzati",
                "Considera caching risultati",
                "Ottimizza query database se presenti"
            ]
        )

        self.bottleneck_alerts.append(alert)
        logger.warning(f"🐌 Funzione lenta rilevata: {function_name} ({execution_time:.3f}s)")

    def _group_bottlenecks_by_type(self, bottlenecks: List[BottleneckAlert]) -> Dict[str, int]:
        """Raggruppa bottleneck per tipo."""
        groups = defaultdict(int)
        for bottleneck in bottlenecks:
            groups[bottleneck.bottleneck_type.value] += 1
        return dict(groups)

    def _generate_performance_recommendations(self) -> List[str]:
        """Genera raccomandazioni per migliorare le performance."""
        recommendations = []

        # Analizza funzioni lente
        slow_functions = [
            p for p in self.function_profiles.values()
            if p.avg_time > self.BOTTLENECK_THRESHOLDS["function_time"]
        ]

        if slow_functions:
            recommendations.append(
                f"🐌 {len(slow_functions)} funzioni lente rilevate. "
                "Considera ottimizzazione o caching."
            )

        # Analizza utilizzo memoria
        if self.system_metrics["peak_memory"] > 80:
            recommendations.append(
                "💾 Alto utilizzo memoria rilevato. "
                "Verifica memory leak e ottimizza cache."
            )

        # Analizza cache hit rate
        try:
            cache_stats = intelligent_cache.get_cache_statistics()
            hit_rate = cache_stats["performance"]["hit_rate_percentage"]
            if hit_rate < 70:
                recommendations.append(
                    f"📊 Basso hit rate cache ({hit_rate:.1f}%). "
                    "Rivedi strategia di caching."
                )
        except:
            pass

        # Analizza query performance
        try:
            query_report = query_optimizer.get_performance_report()
            slow_queries = query_report["summary"]["slow_queries_percentage"]
            if slow_queries > 20:
                recommendations.append(
                    f"🗃️ {slow_queries:.1f}% query lente. "
                    "Ottimizza query database e indici."
                )
        except:
            pass

        if not recommendations:
            recommendations.append("✅ Performance ottimali - nessuna raccomandazione.")

        return recommendations

    def get_bottleneck_alerts(self, severity: str = None,
                             since_minutes: int = 60) -> List[Dict[str, Any]]:
        """
        Ottiene allerte di bottleneck.

        Args:
            severity: Filtra per severità (low, medium, high, critical)
            since_minutes: Allerte degli ultimi N minuti

        Returns:
            Lista di allerte
        """
        cutoff_time = datetime.now() - timedelta(minutes=since_minutes)

        filtered_alerts = [
            alert for alert in self.bottleneck_alerts
            if alert.timestamp >= cutoff_time
        ]

        if severity:
            filtered_alerts = [
                alert for alert in filtered_alerts
                if alert.severity == severity
            ]

        return [
            {
                "type": alert.bottleneck_type.value,
                "severity": alert.severity,
                "description": alert.description,
                "function": alert.affected_function,
                "value": alert.metric_value,
                "threshold": alert.threshold,
                "timestamp": alert.timestamp.isoformat(),
                "suggestions": alert.suggestions
            }
            for alert in filtered_alerts
        ]

    def get_function_profiles(self, top_n: int = 10,
                            sort_by: str = "avg_time") -> List[Dict[str, Any]]:
        """
        Ottiene profili delle funzioni.

        Args:
            top_n: Numero di funzioni da restituire
            sort_by: Campo per ordinamento (avg_time, call_count, total_time, bottleneck_score)

        Returns:
            Lista di profili funzioni
        """
        profiles = list(self.function_profiles.values())

        # Ordina per campo specificato
        if sort_by == "avg_time":
            profiles.sort(key=lambda x: x.avg_time, reverse=True)
        elif sort_by == "call_count":
            profiles.sort(key=lambda x: x.call_count, reverse=True)
        elif sort_by == "total_time":
            profiles.sort(key=lambda x: x.total_time, reverse=True)
        elif sort_by == "bottleneck_score":
            profiles.sort(key=lambda x: x.bottleneck_score, reverse=True)

        return [
            {
                "function_name": p.function_name,
                "module_name": p.module_name,
                "call_count": p.call_count,
                "total_time_s": p.total_time,
                "avg_time_ms": p.avg_time * 1000,
                "min_time_ms": p.min_time * 1000 if p.min_time != float('inf') else 0,
                "max_time_ms": p.max_time * 1000,
                "memory_usage_mb": p.memory_usage_mb,
                "bottleneck_score": p.bottleneck_score,
                "last_called": p.last_called.isoformat()
            }
            for p in profiles[:top_n]
        ]

    def get_system_metrics(self) -> Dict[str, Any]:
        """Ottiene metriche di sistema correnti."""
        if not self.performance_snapshots:
            return {"error": "Nessun dato disponibile"}

        latest_snapshot = self.performance_snapshots[-1]

        return {
            "current": {
                "cpu_percent": latest_snapshot.cpu_percent,
                "memory_percent": latest_snapshot.memory_percent,
                "memory_used_mb": latest_snapshot.memory_used_mb,
                "active_threads": latest_snapshot.active_threads,
                "open_files": latest_snapshot.open_files
            },
            "baseline": {
                "cpu_percent": self.system_metrics["baseline_cpu"],
                "memory_percent": self.system_metrics["baseline_memory"]
            },
            "peaks": {
                "cpu_percent": self.system_metrics["peak_cpu"],
                "memory_percent": self.system_metrics["peak_memory"]
            },
            "function_stats": {
                "total_calls": self.system_metrics["total_function_calls"],
                "slow_calls": self.system_metrics["slow_function_calls"],
                "functions_profiled": len(self.function_profiles)
            }
        }

    def reset_statistics(self):
        """Resetta tutte le statistiche di profiling."""
        with self.lock:
            self.function_profiles.clear()
            self.bottleneck_alerts.clear()
            self.performance_snapshots.clear()

            self.system_metrics.update({
                "peak_cpu": 0.0,
                "peak_memory": 0.0,
                "total_function_calls": 0,
                "slow_function_calls": 0
            })

            logger.info("📊 Statistiche profiler resettate")

# Istanza globale
performance_profiler = PerformanceProfiler()

# Decoratore di convenienza
def profile(name: str = None, track_memory: bool = True, alert_threshold: float = None):
    """
    Decoratore di convenienza per profilare funzioni.

    Args:
        name: Nome personalizzato per il profilo
        track_memory: Se tracciare l'uso memoria
        alert_threshold: Soglia per allerte (secondi)

    Example:
        @profile(name="my_function", alert_threshold=0.5)
        def my_slow_function():
            time.sleep(1)
            return "result"
    """
    return performance_profiler.profile_function(
        name=name,
        track_memory=track_memory,
        alert_threshold=alert_threshold
    )
