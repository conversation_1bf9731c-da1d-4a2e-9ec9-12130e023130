#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FASE 7 - TASK 7.3: Security Testing
Test di sicurezza per il sistema app-roberto.
"""

import pytest
import sys
import os
import tempfile
import json
import hashlib
import secrets
import time
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, List, Any
import re

# Aggiungi il percorso corrente per gli import
sys.path.append('.')

# Import moduli del sistema
try:
    from intelligent_cache_system import intelligent_cache
    from agent_system import agent_orchestrator, AgentType, AgentTask
    from data_cleaning_agent import data_cleaning_agent
    from business_analysis_agent import business_analysis_agent
    from workflow_automation_agent import workflow_automation_agent
    from recommendation_agent import recommendation_agent

    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Alcuni moduli non disponibili: {e}")
    MODULES_AVAILABLE = False

class SecurityTestUtils:
    """Utilità per test di sicurezza."""

    @staticmethod
    def generate_malicious_payloads() -> List[str]:
        """Genera payload malevoli per test."""
        return [
            # SQL Injection
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "1' UNION SELECT * FROM users--",

            # XSS
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",

            # Command Injection
            "; rm -rf /",
            "| cat /etc/passwd",
            "&& whoami",

            # Path Traversal
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",

            # NoSQL Injection
            "{'$ne': null}",
            "{'$gt': ''}",

            # LDAP Injection
            "*)(uid=*",
            "admin)(&(password=*))",

            # XML Injection
            "<?xml version='1.0'?><!DOCTYPE root [<!ENTITY test SYSTEM 'file:///etc/passwd'>]><root>&test;</root>",

            # Buffer Overflow
            "A" * 10000,

            # Format String
            "%s%s%s%s%s%s%s%s%s%s",
            "%x%x%x%x%x%x%x%x%x%x"
        ]

    @staticmethod
    def is_safe_string(value: str) -> bool:
        """Verifica se una stringa è sicura."""
        dangerous_patterns = [
            r'<script.*?>.*?</script>',
            r'javascript:',
            r'on\w+\s*=',
            r'(union|select|insert|update|delete|drop|create|alter)\s+',
            r'(\.\./|\.\.\\)',
            r'[;&|`$]',
            r'%[0-9a-fA-F]{2}',
            r'\\x[0-9a-fA-F]{2}'
        ]

        for pattern in dangerous_patterns:
            if re.search(pattern, value, re.IGNORECASE):
                return False

        return True

    @staticmethod
    def generate_secure_token() -> str:
        """Genera token sicuro."""
        return secrets.token_urlsafe(32)

class TestInputValidation:
    """Test di validazione input."""

    @pytest.fixture(autouse=True)
    def setup_input_validation(self):
        """Setup per test validazione input."""
        if not MODULES_AVAILABLE:
            pytest.skip("Moduli non disponibili")

        intelligent_cache.clear_all()
        yield
        intelligent_cache.clear_all()

    def test_cache_key_validation(self):
        """Test validazione chiavi cache."""
        malicious_payloads = SecurityTestUtils.generate_malicious_payloads()

        for payload in malicious_payloads:
            # Test che la cache gestisca input malevoli
            try:
                intelligent_cache.set(payload, "test_value", ttl=60)
                retrieved_value = intelligent_cache.get(payload)

                # Se il valore è stato salvato, verifica che sia sicuro
                if retrieved_value is not None:
                    assert retrieved_value == "test_value"

            except (ValueError, TypeError, KeyError) as e:
                # È accettabile che la cache rifiuti input non validi
                print(f"✅ Cache correttamente rifiutata chiave malevola: {payload[:50]}...")
                continue

    def test_cache_value_validation(self):
        """Test validazione valori cache."""
        malicious_payloads = SecurityTestUtils.generate_malicious_payloads()

        for i, payload in enumerate(malicious_payloads):
            key = f"test_key_{i}"

            try:
                intelligent_cache.set(key, payload, ttl=60)
                retrieved_value = intelligent_cache.get(key)

                # Verifica che il valore non sia stato alterato
                if retrieved_value is not None:
                    assert retrieved_value == payload

            except (ValueError, TypeError) as e:
                # È accettabile che la cache rifiuti valori non validi
                print(f"✅ Cache correttamente rifiutato valore malevolo")
                continue

    def test_agent_input_sanitization(self):
        """Test sanitizzazione input agenti."""
        if not MODULES_AVAILABLE:
            pytest.skip("Moduli agenti non disponibili")

        malicious_payloads = SecurityTestUtils.generate_malicious_payloads()

        for i, payload in enumerate(malicious_payloads[:5]):  # Test solo primi 5 per velocità
            # Test con data cleaning agent
            try:
                task = AgentTask(
                    task_id=f"security_test_{i}",
                    agent_type=AgentType.DATA_CLEANING,
                    input_data={
                        "file_id": payload,
                        "cleaning_level": "medium"
                    },
                    priority=5,
                    timeout_seconds=10
                )

                # Verifica che il task sia creato senza errori critici
                assert task.task_id == f"security_test_{i}"
                assert task.input_data["file_id"] == payload

            except (ValueError, TypeError) as e:
                # È accettabile che l'agente rifiuti input non validi
                print(f"✅ Agente correttamente rifiutato input malevolo")
                continue

class TestDataProtection:
    """Test di protezione dati."""

    def test_sensitive_data_handling(self):
        """Test gestione dati sensibili."""
        if not MODULES_AVAILABLE:
            pytest.skip("Moduli non disponibili")

        # Simula dati sensibili
        sensitive_data = {
            "password": "super_secret_password",
            "api_key": "sk-1234567890abcdef",
            "credit_card": "4111-1111-1111-1111",
            "ssn": "***********",
            "email": "<EMAIL>"
        }

        for key, value in sensitive_data.items():
            # Salva in cache
            cache_key = f"sensitive_{key}"
            intelligent_cache.set(cache_key, value, ttl=60)

            # Verifica che il dato sia recuperabile
            retrieved = intelligent_cache.get(cache_key)
            assert retrieved == value

            # Verifica che non ci siano leak nei log (simulato)
            # In un sistema reale, verificheresti i log effettivi
            assert len(value) > 0  # Placeholder per test log

    def test_data_encryption_simulation(self):
        """Test simulazione crittografia dati."""
        # Simula crittografia semplice (in produzione usare librerie sicure)
        def simple_encrypt(data: str, key: str) -> str:
            """Crittografia semplice per test."""
            return hashlib.sha256((data + key).encode()).hexdigest()

        def simple_decrypt(encrypted: str, key: str, original: str) -> bool:
            """Verifica decrittografia semplice."""
            return encrypted == hashlib.sha256((original + key).encode()).hexdigest()

        # Test crittografia
        secret_key = SecurityTestUtils.generate_secure_token()
        sensitive_data = "confidential_information"

        encrypted = simple_encrypt(sensitive_data, secret_key)
        assert encrypted != sensitive_data
        assert len(encrypted) == 64  # SHA256 hex length

        # Test decrittografia
        is_valid = simple_decrypt(encrypted, secret_key, sensitive_data)
        assert is_valid

        # Test con chiave sbagliata
        wrong_key = SecurityTestUtils.generate_secure_token()
        is_invalid = simple_decrypt(encrypted, wrong_key, sensitive_data)
        assert not is_invalid

class TestAccessControl:
    """Test di controllo accessi."""

    def test_agent_access_control(self):
        """Test controllo accessi agenti."""
        if not MODULES_AVAILABLE:
            pytest.skip("Moduli agenti non disponibili")

        # Simula controllo accessi
        def check_agent_permissions(agent_type: AgentType, operation: str) -> bool:
            """Simula controllo permessi agente."""
            permissions = {
                AgentType.DATA_CLEANING: ["read_data", "clean_data", "validate_data"],
                AgentType.BUSINESS_ANALYSIS: ["read_data", "analyze_data", "generate_reports"],
                AgentType.WORKFLOW_AUTOMATION: ["read_workflows", "execute_workflows", "schedule_tasks"],
                AgentType.RECOMMENDATION: ["read_preferences", "generate_recommendations", "update_profiles"]
            }

            return operation in permissions.get(agent_type, [])

        # Test permessi validi
        assert check_agent_permissions(AgentType.DATA_CLEANING, "clean_data")
        assert check_agent_permissions(AgentType.BUSINESS_ANALYSIS, "analyze_data")
        assert check_agent_permissions(AgentType.WORKFLOW_AUTOMATION, "execute_workflows")
        assert check_agent_permissions(AgentType.RECOMMENDATION, "generate_recommendations")

        # Test permessi non validi
        assert not check_agent_permissions(AgentType.DATA_CLEANING, "delete_database")
        assert not check_agent_permissions(AgentType.BUSINESS_ANALYSIS, "execute_workflows")
        assert not check_agent_permissions(AgentType.WORKFLOW_AUTOMATION, "clean_data")
        assert not check_agent_permissions(AgentType.RECOMMENDATION, "analyze_data")

    def test_cache_access_control(self):
        """Test controllo accessi cache."""
        if not MODULES_AVAILABLE:
            pytest.skip("Moduli non disponibili")

        # Simula namespace di sicurezza
        def get_secure_cache_key(user_id: str, key: str) -> str:
            """Genera chiave cache sicura con namespace utente."""
            return f"user:{user_id}:{key}"

        def validate_cache_access(user_id: str, cache_key: str) -> bool:
            """Valida accesso cache per utente."""
            expected_prefix = f"user:{user_id}:"
            return cache_key.startswith(expected_prefix)

        # Test accesso valido
        user_id = "user123"
        secure_key = get_secure_cache_key(user_id, "my_data")
        assert validate_cache_access(user_id, secure_key)

        # Test accesso non valido
        other_user_key = get_secure_cache_key("other_user", "their_data")
        assert not validate_cache_access(user_id, other_user_key)

        # Test con cache reale
        intelligent_cache.set(secure_key, "user_specific_data", ttl=60)
        retrieved = intelligent_cache.get(secure_key)
        assert retrieved == "user_specific_data"

class TestSecurityHeaders:
    """Test di header di sicurezza."""

    def test_security_headers_simulation(self):
        """Test simulazione header di sicurezza."""
        # Simula header di sicurezza per API
        def get_security_headers() -> Dict[str, str]:
            """Ottieni header di sicurezza raccomandati."""
            return {
                "X-Content-Type-Options": "nosniff",
                "X-Frame-Options": "DENY",
                "X-XSS-Protection": "1; mode=block",
                "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
                "Content-Security-Policy": "default-src 'self'",
                "Referrer-Policy": "strict-origin-when-cross-origin",
                "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
            }

        headers = get_security_headers()

        # Verifica presenza header critici
        assert "X-Content-Type-Options" in headers
        assert "X-Frame-Options" in headers
        assert "X-XSS-Protection" in headers
        assert "Strict-Transport-Security" in headers
        assert "Content-Security-Policy" in headers

        # Verifica valori corretti
        assert headers["X-Content-Type-Options"] == "nosniff"
        assert headers["X-Frame-Options"] == "DENY"
        assert "max-age" in headers["Strict-Transport-Security"]

class TestRateLimiting:
    """Test di rate limiting."""

    def test_cache_rate_limiting(self):
        """Test rate limiting cache."""
        if not MODULES_AVAILABLE:
            pytest.skip("Moduli non disponibili")

        # Simula rate limiting
        class RateLimiter:
            def __init__(self, max_requests: int, time_window: int):
                self.max_requests = max_requests
                self.time_window = time_window
                self.requests = {}

            def is_allowed(self, client_id: str) -> bool:
                now = time.time()

                if client_id not in self.requests:
                    self.requests[client_id] = []

                # Rimuovi richieste vecchie
                self.requests[client_id] = [
                    req_time for req_time in self.requests[client_id]
                    if now - req_time < self.time_window
                ]

                # Verifica limite
                if len(self.requests[client_id]) >= self.max_requests:
                    return False

                # Aggiungi richiesta corrente
                self.requests[client_id].append(now)
                return True

        # Test rate limiter
        limiter = RateLimiter(max_requests=5, time_window=1)  # 5 richieste per secondo

        client_id = "test_client"

        # Prime 5 richieste dovrebbero passare
        for i in range(5):
            assert limiter.is_allowed(client_id), f"Richiesta {i+1} dovrebbe essere permessa"

        # Sesta richiesta dovrebbe essere bloccata
        assert not limiter.is_allowed(client_id), "Sesta richiesta dovrebbe essere bloccata"

        # Dopo 1 secondo, dovrebbe essere permessa di nuovo
        time.sleep(1.1)
        assert limiter.is_allowed(client_id), "Richiesta dopo timeout dovrebbe essere permessa"

class TestSecurityLogging:
    """Test di logging di sicurezza."""

    def test_security_event_logging(self):
        """Test logging eventi di sicurezza."""
        # Simula sistema di logging sicurezza
        class SecurityLogger:
            def __init__(self):
                self.events = []

            def log_security_event(self, event_type: str, details: Dict[str, Any]):
                event = {
                    "timestamp": datetime.now().isoformat(),
                    "event_type": event_type,
                    "details": details,
                    "severity": self._get_severity(event_type)
                }
                self.events.append(event)

            def _get_severity(self, event_type: str) -> str:
                high_severity = ["injection_attempt", "unauthorized_access", "data_breach"]
                medium_severity = ["rate_limit_exceeded", "invalid_input", "authentication_failure"]

                if event_type in high_severity:
                    return "HIGH"
                elif event_type in medium_severity:
                    return "MEDIUM"
                else:
                    return "LOW"

            def get_events(self) -> List[Dict[str, Any]]:
                return self.events

        # Test logger
        logger = SecurityLogger()

        # Log eventi di test
        logger.log_security_event("injection_attempt", {
            "payload": "'; DROP TABLE users; --",
            "source_ip": "*************",
            "user_agent": "test_agent"
        })

        logger.log_security_event("rate_limit_exceeded", {
            "client_id": "test_client",
            "requests_count": 100,
            "time_window": 60
        })

        # Verifica eventi
        events = logger.get_events()
        assert len(events) == 2

        # Verifica primo evento
        injection_event = events[0]
        assert injection_event["event_type"] == "injection_attempt"
        assert injection_event["severity"] == "HIGH"
        assert "payload" in injection_event["details"]

        # Verifica secondo evento
        rate_limit_event = events[1]
        assert rate_limit_event["event_type"] == "rate_limit_exceeded"
        assert rate_limit_event["severity"] == "MEDIUM"
        assert "client_id" in rate_limit_event["details"]

# Configurazione pytest per security testing
def pytest_configure(config):
    """Configurazione pytest per security testing."""
    config.addinivalue_line(
        "markers", "security: marks tests as security tests"
    )
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )

if __name__ == "__main__":
    # Esegui test di sicurezza
    pytest.main([__file__, "-v", "--tb=short", "-m", "security"])
