#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FASE 7 - TASK 7.2: Performance Testing
Test di performance avanzati per il sistema app-roberto.
"""

import pytest
import asyncio
import time
import sys
import threading
import concurrent.futures
from datetime import datetime, timedelta
import statistics
import psutil
import gc
from typing import List, Dict, Any
import json

# Aggiungi il percorso corrente per gli import
sys.path.append('.')

# Import moduli del sistema
try:
    from performance_profiler import performance_profiler, profile
    from intelligent_cache_system import intelligent_cache
    from auto_tuner import auto_tuner
    from agent_system import agent_orchestrator, AgentType, AgentTask
    from data_cleaning_agent import data_cleaning_agent
    from business_analysis_agent import business_analysis_agent
    from workflow_automation_agent import workflow_automation_agent
    from recommendation_agent import recommendation_agent
    
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Alcuni moduli non disponibili: {e}")
    MODULES_AVAILABLE = False

class PerformanceBenchmark:
    """Classe per benchmark di performance."""
    
    def __init__(self):
        self.results = {}
        self.start_time = None
        self.end_time = None
    
    def start_benchmark(self, name: str):
        """Inizia un benchmark."""
        self.start_time = time.perf_counter()
        self.results[name] = {"start_time": self.start_time}
    
    def end_benchmark(self, name: str):
        """Termina un benchmark."""
        self.end_time = time.perf_counter()
        if name in self.results:
            self.results[name]["end_time"] = self.end_time
            self.results[name]["duration"] = self.end_time - self.results[name]["start_time"]
    
    def get_results(self) -> Dict[str, Any]:
        """Ottieni risultati benchmark."""
        return self.results

class TestCachePerformance:
    """Test di performance per il sistema di cache."""
    
    @pytest.fixture(autouse=True)
    def setup_cache_performance(self):
        """Setup per test performance cache."""
        if not MODULES_AVAILABLE:
            pytest.skip("Moduli non disponibili")
        
        intelligent_cache.clear_all()
        yield
        intelligent_cache.clear_all()
    
    def test_cache_write_performance(self):
        """Test performance scrittura cache."""
        benchmark = PerformanceBenchmark()
        
        # Test scrittura sequenziale
        benchmark.start_benchmark("sequential_write")
        for i in range(1000):
            intelligent_cache.set(f"perf_key_{i}", f"value_{i}", ttl=300)
        benchmark.end_benchmark("sequential_write")
        
        # Test scrittura parallela
        def write_batch(start_idx: int, count: int):
            for i in range(start_idx, start_idx + count):
                intelligent_cache.set(f"parallel_key_{i}", f"value_{i}", ttl=300)
        
        benchmark.start_benchmark("parallel_write")
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            futures = []
            for i in range(0, 1000, 250):
                future = executor.submit(write_batch, i, 250)
                futures.append(future)
            
            concurrent.futures.wait(futures)
        benchmark.end_benchmark("parallel_write")
        
        # Verifica risultati
        results = benchmark.get_results()
        sequential_time = results["sequential_write"]["duration"]
        parallel_time = results["parallel_write"]["duration"]
        
        print(f"📊 Cache Write Performance:")
        print(f"   Sequential: {sequential_time:.3f}s ({1000/sequential_time:.1f} ops/s)")
        print(f"   Parallel: {parallel_time:.3f}s ({1000/parallel_time:.1f} ops/s)")
        
        # Verifica che il parallel sia più veloce (o almeno non molto più lento)
        assert parallel_time <= sequential_time * 1.5, "Parallel write dovrebbe essere competitivo"
    
    def test_cache_read_performance(self):
        """Test performance lettura cache."""
        # Prepara dati
        for i in range(1000):
            intelligent_cache.set(f"read_key_{i}", f"value_{i}", ttl=300)
        
        benchmark = PerformanceBenchmark()
        
        # Test lettura sequenziale
        benchmark.start_benchmark("sequential_read")
        for i in range(1000):
            value = intelligent_cache.get(f"read_key_{i}")
            assert value == f"value_{i}"
        benchmark.end_benchmark("sequential_read")
        
        # Test lettura parallela
        def read_batch(start_idx: int, count: int):
            for i in range(start_idx, start_idx + count):
                value = intelligent_cache.get(f"read_key_{i}")
                assert value == f"value_{i}"
        
        benchmark.start_benchmark("parallel_read")
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            futures = []
            for i in range(0, 1000, 250):
                future = executor.submit(read_batch, i, 250)
                futures.append(future)
            
            concurrent.futures.wait(futures)
        benchmark.end_benchmark("parallel_read")
        
        # Verifica risultati
        results = benchmark.get_results()
        sequential_time = results["sequential_read"]["duration"]
        parallel_time = results["parallel_read"]["duration"]
        
        print(f"📊 Cache Read Performance:")
        print(f"   Sequential: {sequential_time:.3f}s ({1000/sequential_time:.1f} ops/s)")
        print(f"   Parallel: {parallel_time:.3f}s ({1000/parallel_time:.1f} ops/s)")
        
        # Lettura dovrebbe essere veloce
        assert sequential_time < 1.0, "Lettura sequenziale dovrebbe essere < 1s"
        assert parallel_time < 1.0, "Lettura parallela dovrebbe essere < 1s"
    
    def test_cache_memory_usage(self):
        """Test utilizzo memoria cache."""
        # Misura memoria iniziale
        gc.collect()
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # Aggiungi molti dati alla cache
        large_data = "x" * 1024  # 1KB per item
        for i in range(10000):
            intelligent_cache.set(f"memory_key_{i}", large_data, ttl=300)
        
        # Misura memoria dopo
        gc.collect()
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"📊 Cache Memory Usage:")
        print(f"   Initial: {initial_memory:.1f} MB")
        print(f"   Final: {final_memory:.1f} MB")
        print(f"   Increase: {memory_increase:.1f} MB")
        print(f"   Per item: {memory_increase * 1024 / 10000:.2f} KB")
        
        # Verifica che l'aumento di memoria sia ragionevole
        # 10000 items * 1KB = ~10MB + overhead
        assert memory_increase < 50, f"Aumento memoria troppo alto: {memory_increase:.1f} MB"

class TestAgentPerformance:
    """Test di performance per il sistema di agenti."""
    
    @pytest.fixture(autouse=True)
    def setup_agent_performance(self):
        """Setup per test performance agenti."""
        if not MODULES_AVAILABLE:
            pytest.skip("Moduli agenti non disponibili")
        
        agent_orchestrator.clear_all_tasks()
        
        # Registra agenti
        agent_orchestrator.register_agent(data_cleaning_agent)
        agent_orchestrator.register_agent(business_analysis_agent)
        agent_orchestrator.register_agent(workflow_automation_agent)
        agent_orchestrator.register_agent(recommendation_agent)
        
        yield
        agent_orchestrator.clear_all_tasks()
    
    @pytest.mark.asyncio
    async def test_agent_task_throughput(self):
        """Test throughput task agenti."""
        benchmark = PerformanceBenchmark()
        
        # Crea molti task
        tasks = []
        for i in range(50):
            task = AgentTask(
                task_id=f"throughput_task_{i}",
                agent_type=AgentType.DATA_CLEANING,
                input_data={"file_id": f"test_file_{i}", "cleaning_level": "light"},
                priority=5,
                timeout_seconds=30
            )
            tasks.append(task)
        
        # Sottometti tutti i task
        benchmark.start_benchmark("task_submission")
        submitted_tasks = []
        for task in tasks:
            task_id = agent_orchestrator.submit_task(task)
            submitted_tasks.append(task_id)
        benchmark.end_benchmark("task_submission")
        
        # Attendi completamento
        benchmark.start_benchmark("task_completion")
        completed_count = 0
        max_wait = 60  # 60 secondi timeout
        start_wait = time.time()
        
        while completed_count < len(submitted_tasks) and (time.time() - start_wait) < max_wait:
            await asyncio.sleep(0.1)
            
            for task_id in submitted_tasks:
                task_status = agent_orchestrator.get_task_status(task_id)
                if task_status and task_status["status"] in ["completed", "error"]:
                    completed_count += 1
        
        benchmark.end_benchmark("task_completion")
        
        # Verifica risultati
        results = benchmark.get_results()
        submission_time = results["task_submission"]["duration"]
        completion_time = results["task_completion"]["duration"]
        
        print(f"📊 Agent Task Performance:")
        print(f"   Submission: {submission_time:.3f}s ({len(tasks)/submission_time:.1f} tasks/s)")
        print(f"   Completion: {completion_time:.3f}s")
        print(f"   Completed: {completed_count}/{len(tasks)} tasks")
        print(f"   Success rate: {completed_count/len(tasks)*100:.1f}%")
        
        # Verifica performance
        assert submission_time < 5.0, "Submission dovrebbe essere < 5s"
        assert completed_count >= len(tasks) * 0.8, "Almeno 80% dei task dovrebbero completare"
    
    @pytest.mark.asyncio
    async def test_agent_concurrent_execution(self):
        """Test esecuzione concorrente agenti."""
        benchmark = PerformanceBenchmark()
        
        # Crea task per diversi tipi di agenti
        task_types = [
            (AgentType.DATA_CLEANING, {"file_id": "test_file", "cleaning_level": "medium"}),
            (AgentType.BUSINESS_ANALYSIS, {"file_id": "business_data", "analysis_type": "kpi"}),
            (AgentType.WORKFLOW_AUTOMATION, {"operation_type": "analyze_patterns", "data_source": "logs"}),
            (AgentType.RECOMMENDATION, {"operation_type": "generate_recommendations", "user_id": "user_001"})
        ]
        
        # Sottometti task concorrenti
        benchmark.start_benchmark("concurrent_execution")
        submitted_tasks = []
        
        for i in range(20):  # 5 task per tipo
            agent_type, input_data = task_types[i % len(task_types)]
            task = AgentTask(
                task_id=f"concurrent_task_{i}",
                agent_type=agent_type,
                input_data=input_data,
                priority=5,
                timeout_seconds=30
            )
            task_id = agent_orchestrator.submit_task(task)
            submitted_tasks.append(task_id)
        
        # Attendi completamento
        completed_count = 0
        max_wait = 45
        start_wait = time.time()
        
        while completed_count < len(submitted_tasks) and (time.time() - start_wait) < max_wait:
            await asyncio.sleep(0.2)
            
            new_completed = 0
            for task_id in submitted_tasks:
                task_status = agent_orchestrator.get_task_status(task_id)
                if task_status and task_status["status"] in ["completed", "error"]:
                    new_completed += 1
            
            completed_count = new_completed
        
        benchmark.end_benchmark("concurrent_execution")
        
        # Verifica risultati
        results = benchmark.get_results()
        execution_time = results["concurrent_execution"]["duration"]
        
        print(f"📊 Concurrent Agent Execution:")
        print(f"   Total time: {execution_time:.3f}s")
        print(f"   Completed: {completed_count}/{len(submitted_tasks)} tasks")
        print(f"   Throughput: {completed_count/execution_time:.1f} tasks/s")
        
        # Verifica performance concorrente
        assert completed_count >= len(submitted_tasks) * 0.7, "Almeno 70% dei task dovrebbero completare"
        assert execution_time < 40, "Esecuzione concorrente dovrebbe essere < 40s"

class TestSystemLoadTesting:
    """Test di carico per l'intero sistema."""
    
    @pytest.fixture(autouse=True)
    def setup_load_testing(self):
        """Setup per test di carico."""
        if not MODULES_AVAILABLE:
            pytest.skip("Moduli non disponibili")
        
        # Reset tutti i sistemi
        intelligent_cache.clear_all()
        performance_profiler.reset_metrics()
        agent_orchestrator.clear_all_tasks()
        
        yield
        
        # Cleanup
        intelligent_cache.clear_all()
        performance_profiler.reset_metrics()
        agent_orchestrator.clear_all_tasks()
    
    def test_mixed_workload_performance(self):
        """Test performance con carico misto."""
        benchmark = PerformanceBenchmark()
        
        def cache_workload():
            """Carico di lavoro cache."""
            for i in range(500):
                intelligent_cache.set(f"load_key_{i}", f"value_{i}", ttl=300)
                if i % 2 == 0:
                    intelligent_cache.get(f"load_key_{i//2}")
        
        def profiler_workload():
            """Carico di lavoro profiler."""
            @profile(name="load_test_function")
            def test_function():
                time.sleep(0.001)  # 1ms
                return sum(range(100))
            
            for _ in range(100):
                test_function()
        
        # Esegui carichi di lavoro in parallelo
        benchmark.start_benchmark("mixed_workload")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            futures = [
                executor.submit(cache_workload),
                executor.submit(cache_workload),
                executor.submit(profiler_workload),
                executor.submit(profiler_workload)
            ]
            
            concurrent.futures.wait(futures)
        
        benchmark.end_benchmark("mixed_workload")
        
        # Verifica risultati
        results = benchmark.get_results()
        total_time = results["mixed_workload"]["duration"]
        
        # Verifica statistiche cache
        cache_stats = intelligent_cache.get_cache_statistics()
        
        # Verifica statistiche profiler
        profiler_report = performance_profiler.get_performance_report()
        
        print(f"📊 Mixed Workload Performance:")
        print(f"   Total time: {total_time:.3f}s")
        print(f"   Cache requests: {cache_stats['total_requests']}")
        print(f"   Cache hit rate: {cache_stats['hit_rate']:.2%}")
        print(f"   Profiler functions: {len(profiler_report['function_metrics'])}")
        
        # Verifica che il sistema gestisca il carico
        assert total_time < 10.0, "Carico misto dovrebbe completare in < 10s"
        assert cache_stats["total_requests"] > 0, "Cache dovrebbe aver processato richieste"
    
    def test_memory_stability_under_load(self):
        """Test stabilità memoria sotto carico."""
        # Misura memoria iniziale
        gc.collect()
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        # Simula carico prolungato
        for cycle in range(10):
            # Carico cache
            for i in range(100):
                intelligent_cache.set(f"stability_key_{cycle}_{i}", f"value_{cycle}_{i}", ttl=60)
            
            # Carico profiler
            @profile(name=f"stability_function_{cycle}")
            def stability_function():
                return sum(range(1000))
            
            for _ in range(10):
                stability_function()
            
            # Cleanup periodico
            if cycle % 3 == 0:
                gc.collect()
        
        # Misura memoria finale
        gc.collect()
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"📊 Memory Stability Under Load:")
        print(f"   Initial: {initial_memory:.1f} MB")
        print(f"   Final: {final_memory:.1f} MB")
        print(f"   Increase: {memory_increase:.1f} MB")
        
        # Verifica stabilità memoria
        assert memory_increase < 100, f"Aumento memoria eccessivo: {memory_increase:.1f} MB"

# Configurazione pytest per performance testing
def pytest_configure(config):
    """Configurazione pytest per performance testing."""
    config.addinivalue_line(
        "markers", "performance: marks tests as performance tests"
    )
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )

if __name__ == "__main__":
    # Esegui test di performance
    pytest.main([__file__, "-v", "--tb=short", "-m", "performance"])
