/**
 * Theme Manager - A<PERSON> Roberto
 * Gestione dinamica del tema scuro/chiaro
 * Versione: 1.0.0
 */

// Theme Manager v1.0.0

class ThemeManager {
    constructor() {
        this.currentTheme = 'light';
        this.storageKey = 'app-roberto-theme';
        this.transitionDuration = 300; // ms

        this.init();
    }

    /**
     * Inizializza il theme manager
     */
    init() {
        // Carica tema salvato o rileva preferenza sistema
        this.loadSavedTheme();

        // Applica il tema iniziale
        this.applyTheme(this.currentTheme, false);

        // Setup event listeners
        this.setupEventListeners();

        // Crea il toggle button
        this.createThemeToggle();
    }

    /**
     * Carica il tema salvato o rileva preferenza sistema
     */
    loadSavedTheme() {
        // Prova a caricare da localStorage
        const savedTheme = localStorage.getItem(this.storageKey);

        if (savedTheme && ['light', 'dark'].includes(savedTheme)) {
            this.currentTheme = savedTheme;
        } else {
            // Rileva preferenza sistema
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            this.currentTheme = prefersDark ? 'dark' : 'light';
        }
    }

    /**
     * Applica il tema specificato - VERSIONE SICURA
     */
    applyTheme(theme, animate = true) {
        try {
            // Verifica che document.body esista
            if (!document.body) {
                console.warn('⚠️ document.body non disponibile, attendere caricamento DOM');
                // Riprova quando il DOM è pronto
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', () => {
                        this.applyTheme(theme, animate);
                    });
                    return;
                }
            }

            // Aggiungi classe di transizione se richiesta e body disponibile
            if (animate && document.body && document.body.style) {
                try {
                    document.body.style.transition = `background-color ${this.transitionDuration}ms ease, color ${this.transitionDuration}ms ease`;

                    // Rimuovi la transizione dopo l'animazione
                    setTimeout(() => {
                        if (document.body && document.body.style) {
                            document.body.style.transition = '';
                        }
                    }, this.transitionDuration);
                } catch (styleError) {
                    console.warn('⚠️ Errore applicazione transizione:', styleError);
                }
            }

            // Applica il tema in modo sicuro
            try {
                if (theme === 'dark') {
                    // Applica attributo data-theme
                    if (document.documentElement && document.documentElement.setAttribute) {
                        document.documentElement.setAttribute('data-theme', 'dark');
                    }
                    
                    // Applica classe dark-theme al body
                    if (document.body && document.body.classList && typeof document.body.classList.add === 'function') {
                        document.body.classList.add('dark-theme');
                    }
                } else {
                    // Rimuovi attributo data-theme
                    if (document.documentElement && document.documentElement.removeAttribute) {
                        document.documentElement.removeAttribute('data-theme');
                    }
                    
                    // Rimuovi classe dark-theme dal body
                    if (document.body && document.body.classList && typeof document.body.classList.remove === 'function') {
                        document.body.classList.remove('dark-theme');
                    }
                }
            } catch (themeError) {
                console.error('❌ Errore applicazione tema:', themeError);
            }

            // Aggiorna stato corrente
            this.currentTheme = theme;

            // Salva preferenza in modo sicuro
            try {
                if (typeof Storage !== 'undefined' && localStorage) {
                    localStorage.setItem(this.storageKey, theme);
                }
            } catch (storageError) {
                console.warn('⚠️ Errore salvataggio tema in localStorage:', storageError);
            }

            // Aggiorna toggle button
            this.updateToggleButton();

            // Aggiorna grafici se presenti
            this.updateCharts();

            // Aggiorna componenti Material Design 3
            this.updateMaterialComponents();

            // Aggiorna meta theme-color per compatibilità Firefox
            this.updateThemeColorMeta();

            // Trigger evento personalizzato
            this.dispatchThemeChangeEvent(theme);

            console.log('✅ Tema applicato con successo:', theme);
        } catch (error) {
            console.error('❌ Errore critico in applyTheme:', error);
        }
    }

    /**
     * Cambia tema (toggle)
     */
    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme, true);

        // Analytics/tracking (opzionale)
        this.trackThemeChange(newTheme);
    }

    /**
     * Crea il pulsante di toggle del tema
     */
    createThemeToggle() {
        // Trova la navbar
        const navbar = document.querySelector('.navbar-nav');
        if (!navbar) {
            // Navbar non presente in questa pagina (es. setup wizard)
            return;
        }

        // Crea l'elemento del toggle
        const toggleItem = document.createElement('li');
        toggleItem.className = 'nav-item';

        const toggleButton = document.createElement('button');
        toggleButton.className = 'btn btn-link nav-link border-0 p-2';
        toggleButton.id = 'theme-toggle';
        toggleButton.setAttribute('aria-label', 'Cambia tema');
        toggleButton.setAttribute('title', 'Cambia tema');
        toggleButton.style.color = 'inherit';

        // Icona iniziale
        this.updateToggleButtonContent(toggleButton);

        // Event listener
        toggleButton.addEventListener('click', () => {
            this.toggleTheme();
        });

        toggleItem.appendChild(toggleButton);
        navbar.appendChild(toggleItem);

        console.log('Toggle tema aggiunto alla navbar');
    }

    /**
     * Aggiorna il contenuto del toggle button
     */
    updateToggleButtonContent(button) {
        if (!button) {
            button = document.getElementById('theme-toggle');
        }

        if (!button) return;

        const isDark = this.currentTheme === 'dark';
        const icon = isDark ? 'fa-sun' : 'fa-moon';

        button.innerHTML = `<i class="fas ${icon}"></i>`;
        button.setAttribute('title', `Passa al ${isDark ? 'tema chiaro' : 'tema scuro'}`);
    }

    /**
     * Aggiorna il toggle button
     */
    updateToggleButton() {
        this.updateToggleButtonContent();
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Ascolta cambiamenti preferenze sistema
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        mediaQuery.addEventListener('change', (e) => {
            // Solo se non c'è una preferenza salvata
            if (!localStorage.getItem(this.storageKey)) {
                const newTheme = e.matches ? 'dark' : 'light';
                this.applyTheme(newTheme, true);
                console.log(`Tema cambiato automaticamente per preferenza sistema: ${newTheme}`);
            }
        });

        // Ascolta eventi di storage (per sincronizzazione tra tab)
        window.addEventListener('storage', (e) => {
            if (e.key === this.storageKey && e.newValue !== this.currentTheme) {
                this.applyTheme(e.newValue, true);
                console.log(`Tema sincronizzato da altra tab: ${e.newValue}`);
            }
        });
    }

    /**
     * Aggiorna i grafici per il nuovo tema
     */
    updateCharts() {
        // Aggiorna Plotly se presente
        if (typeof Plotly !== 'undefined') {
            this.updatePlotlyCharts();
        }

        // Aggiorna Chart.js se presente
        if (typeof Chart !== 'undefined') {
            this.updateChartJsCharts();
        }
    }

    /**
     * Aggiorna grafici Plotly
     */
    updatePlotlyCharts() {
        const plotlyDivs = document.querySelectorAll('[id*="chart"], .plotly-graph-div');

        plotlyDivs.forEach(div => {
            if (div._fullLayout) {
                const isDark = this.currentTheme === 'dark';

                const updateLayout = {
                    paper_bgcolor: isDark ? '#1e1e1e' : '#ffffff',
                    plot_bgcolor: isDark ? '#1e1e1e' : '#ffffff',
                    font: {
                        color: isDark ? '#b3b3b3' : '#495057'
                    },
                    xaxis: {
                        gridcolor: isDark ? '#404040' : '#e9ecef',
                        zerolinecolor: isDark ? '#404040' : '#e9ecef'
                    },
                    yaxis: {
                        gridcolor: isDark ? '#404040' : '#e9ecef',
                        zerolinecolor: isDark ? '#404040' : '#e9ecef'
                    }
                };

                Plotly.relayout(div, updateLayout);
            }
        });

        console.log('Grafici Plotly aggiornati per tema', this.currentTheme);
    }

    /**
     * Aggiorna grafici Chart.js
     */
    updateChartJsCharts() {
        if (Chart.instances) {
            Chart.instances.forEach(chart => {
                const isDark = this.currentTheme === 'dark';

                // Aggiorna colori del grafico
                if (chart.options.scales) {
                    Object.keys(chart.options.scales).forEach(scaleKey => {
                        const scale = chart.options.scales[scaleKey];
                        if (scale.grid) {
                            scale.grid.color = isDark ? '#404040' : '#e9ecef';
                        }
                        if (scale.ticks) {
                            scale.ticks.color = isDark ? '#b3b3b3' : '#495057';
                        }
                    });
                }

                chart.update();
            });
        }

        console.log('Grafici Chart.js aggiornati per tema', this.currentTheme);
    }

    /**
     * Aggiorna meta theme-color per compatibilità cross-browser
     */
    updateThemeColorMeta() {
        const themeColorMeta = document.getElementById('theme-color-meta');
        if (themeColorMeta) {
            const isDark = this.currentTheme === 'dark';
            const color = isDark ? '#121212' : '#0d6efd';
            themeColorMeta.setAttribute('content', color);

            console.log(`Meta theme-color aggiornato: ${color}`);
        }

        // Aggiorna anche altri meta tag correlati
        this.updateRelatedMetaTags();
    }

    /**
     * Aggiorna meta tag correlati al tema
     */
    updateRelatedMetaTags() {
        const isDark = this.currentTheme === 'dark';

        // Aggiorna msapplication-navbutton-color (Microsoft Edge)
        const navButtonMeta = document.querySelector('meta[name="msapplication-navbutton-color"]');
        if (navButtonMeta) {
            navButtonMeta.setAttribute('content', isDark ? '#121212' : '#0d6efd');
        }

        // Aggiorna apple-mobile-web-app-status-bar-style (iOS Safari)
        // Valori validi: 'default', 'black', 'black-translucent'
        const statusBarMeta = document.querySelector('meta[name="apple-mobile-web-app-status-bar-style"]');
        if (statusBarMeta) {
            statusBarMeta.setAttribute('content', isDark ? 'black-translucent' : 'default');
        }

        // Aggiorna msapplication-TileColor (Windows)
        const tileMeta = document.querySelector('meta[name="msapplication-TileColor"]');
        if (tileMeta) {
            tileMeta.setAttribute('content', isDark ? '#121212' : '#0d6efd');
        }

        // Fallback per Firefox: aggiorna CSS custom property
        document.documentElement.style.setProperty('--browser-theme-color', isDark ? '#121212' : '#0d6efd');
    }

    /**
     * Dispatch evento personalizzato per cambio tema
     */
    dispatchThemeChangeEvent(theme) {
        const event = new CustomEvent('themeChanged', {
            detail: {
                theme: theme,
                isDark: theme === 'dark'
            }
        });

        document.dispatchEvent(event);
    }

    /**
     * Tracking cambio tema (per analytics)
     */
    trackThemeChange(newTheme) {
        // Implementa tracking se necessario
        console.log(`Theme changed to: ${newTheme}`);

        // Esempio Google Analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'theme_change', {
                'theme': newTheme
            });
        }
    }

    /**
     * Ottieni tema corrente
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * Verifica se è attivo il tema scuro
     */
    isDarkTheme() {
        return this.currentTheme === 'dark';
    }

    /**
     * Forza un tema specifico
     */
    setTheme(theme) {
        if (['light', 'dark'].includes(theme)) {
            this.applyTheme(theme, true);
        } else {
            console.warn(`Tema non valido: ${theme}`);
        }
    }

    /**
     * Reset tema alle preferenze sistema
     */
    resetToSystemPreference() {
        localStorage.removeItem(this.storageKey);
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        const systemTheme = prefersDark ? 'dark' : 'light';
        this.applyTheme(systemTheme, true);
        console.log(`Tema resettato alle preferenze sistema: ${systemTheme}`);
    }

    // ===== MATERIAL DESIGN 3 ENHANCEMENTS =====

    /**
     * Aggiorna componenti Material Design 3
     */
    updateMaterialComponents() {
        this.updateSkeletonLoaders();
        this.updateGlassElements();
        this.updateChipComponents();
        this.updateRippleEffects();
    }

    /**
     * Aggiorna skeleton loaders
     */
    updateSkeletonLoaders() {
        const skeletons = document.querySelectorAll('.skeleton');
        const gradient = this.getSkeletonGradient();

        skeletons.forEach(skeleton => {
            skeleton.style.background = gradient;
        });
    }

    /**
     * Ottiene il gradiente per skeleton loader
     */
    getSkeletonGradient() {
        const isDark = this.currentTheme === 'dark';
        if (isDark) {
            return 'linear-gradient(90deg, #1e1e1e 25%, #2a2a2a 50%, #1e1e1e 75%)';
        } else {
            return 'linear-gradient(90deg, #f8f9fa 25%, #e9ecef 50%, #f8f9fa 75%)';
        }
    }

    /**
     * Aggiorna elementi glass morphism
     */
    updateGlassElements() {
        const glassElements = document.querySelectorAll('.glass');

        glassElements.forEach(element => {
            this.updateGlassEffect(element);
        });
    }

    /**
     * Aggiorna effetto glass morphism
     */
    updateGlassEffect(element) {
        const isDark = this.currentTheme === 'dark';
        if (isDark) {
            element.style.background = 'rgba(0, 0, 0, 0.3)';
            element.style.border = '1px solid rgba(255, 255, 255, 0.1)';
        } else {
            element.style.background = 'rgba(255, 255, 255, 0.1)';
            element.style.border = '1px solid rgba(255, 255, 255, 0.2)';
        }
    }

    /**
     * Aggiorna chip components
     */
    updateChipComponents() {
        const chips = document.querySelectorAll('.chip');

        chips.forEach(chip => {
            // I colori vengono gestiti automaticamente dalle CSS custom properties
            // Aggiunge animazioni se necessario
            if (!chip.classList.contains('chip-animated')) {
                chip.classList.add('chip-animated');
            }
        });
    }

    /**
     * Aggiorna effetti ripple
     */
    updateRippleEffects() {
        const rippleElements = document.querySelectorAll('.ripple');

        rippleElements.forEach(element => {
            if (!element.hasAttribute('data-ripple-initialized')) {
                this.initializeRippleEffect(element);
                element.setAttribute('data-ripple-initialized', 'true');
            }
        });
    }

    /**
     * Inizializza effetto ripple su un elemento
     */
    initializeRippleEffect(element) {
        element.addEventListener('click', (e) => {
            const ripple = document.createElement('span');
            const rect = element.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple-effect');

            element.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    }

    /**
     * Crea floating action button dinamico
     */
    createFloatingActionButton(options = {}) {
        const fab = document.createElement('button');
        fab.className = 'fab';
        fab.innerHTML = options.icon || '<i class="fas fa-plus"></i>';
        fab.title = options.title || 'Azione rapida';

        if (options.onClick) {
            fab.addEventListener('click', options.onClick);
        }

        document.body.appendChild(fab);
        return fab;
    }

    /**
     * Crea chip component dinamico
     */
    createChip(text, options = {}) {
        const chip = document.createElement('span');
        chip.className = 'chip';

        if (options.icon) {
            const icon = document.createElement('i');
            icon.className = `chip-icon ${options.icon}`;
            chip.appendChild(icon);
        }

        const textSpan = document.createElement('span');
        textSpan.textContent = text;
        chip.appendChild(textSpan);

        if (options.closable) {
            const closeBtn = document.createElement('button');
            closeBtn.className = 'chip-close';
            closeBtn.innerHTML = '<i class="fas fa-times"></i>';
            closeBtn.addEventListener('click', () => {
                chip.remove();
                if (options.onClose) options.onClose();
            });
            chip.appendChild(closeBtn);
        }

        return chip;
    }

    /**
     * Mostra skeleton loader
     */
    showSkeleton(container, type = 'text') {
        const skeleton = document.createElement('div');
        skeleton.className = `skeleton skeleton-${type}`;

        switch (type) {
            case 'text':
                skeleton.style.height = '1rem';
                skeleton.style.borderRadius = '0.25rem';
                break;
            case 'card':
                skeleton.style.height = '200px';
                skeleton.style.borderRadius = '0.5rem';
                break;
            case 'avatar':
                skeleton.style.width = '40px';
                skeleton.style.height = '40px';
                skeleton.style.borderRadius = '50%';
                break;
        }

        container.appendChild(skeleton);
        return skeleton;
    }

    /**
     * Nasconde skeleton loader
     */
    hideSkeleton(skeleton) {
        if (skeleton && skeleton.parentNode) {
            skeleton.remove();
        }
    }
}

// Inizializza il theme manager quando il DOM è pronto
document.addEventListener('DOMContentLoaded', function() {
    // Crea istanza globale
    window.themeManager = new ThemeManager();

    // Esponi metodi utili globalmente
    window.toggleTheme = () => window.themeManager.toggleTheme();
    window.setTheme = (theme) => window.themeManager.setTheme(theme);
    window.getCurrentTheme = () => window.themeManager.getCurrentTheme();
    window.isDarkTheme = () => window.themeManager.isDarkTheme();

    // Esponi metodi Material Design 3
    window.createFAB = (options) => window.themeManager.createFloatingActionButton(options);
    window.createChip = (text, options) => window.themeManager.createChip(text, options);
    window.showSkeleton = (container, type) => window.themeManager.showSkeleton(container, type);
    window.hideSkeleton = (skeleton) => window.themeManager.hideSkeleton(skeleton);
});

// Applica tema immediatamente per evitare flash
(function() {
    const savedTheme = localStorage.getItem('app-roberto-theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const isDark = savedTheme === 'dark' || (!savedTheme && prefersDark);

    if (isDark) {
        document.documentElement.setAttribute('data-theme', 'dark');
        document.body.classList.add('dark-theme');

        // Aggiorna meta theme-color immediatamente
        const themeColorMeta = document.getElementById('theme-color-meta');
        if (themeColorMeta) {
            themeColorMeta.setAttribute('content', '#121212');
        }
    }
})();
