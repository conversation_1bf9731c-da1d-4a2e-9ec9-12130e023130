[pytest]
# Configurazione pytest per app-roberto - FASE 6 Testing e Stabilizzazione

# Directory di test
testpaths = . tests

# Pattern per file di test
python_files = test_*.py *_test.py health_monitor.py

# Pattern per classi di test
python_classes = Test*

# Pattern per funzioni di test
python_functions = test_*

# Markers personalizzati
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    performance: marks tests as performance tests
    security: marks tests as security tests
    unit: marks tests as unit tests
    smoke: marks tests as smoke tests
    regression: marks tests as regression tests
    stability: marks tests as stability tests
    monitoring: marks tests as monitoring tests

# Opzioni di default
addopts =
    -v
    --strict-markers
    --tb=short
    --maxfail=10
    --durations=10
    --color=yes
    --disable-warnings

# Filtri warning
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:requests.*
    ignore::UserWarning:urllib3.*

# Timeout per test
timeout = 300

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# File di log per test
log_file = tests.log
log_file_level = DEBUG
log_file_format = %(asctime)s [%(levelname)8s] %(filename)s:%(lineno)d: %(message)s
log_file_date_format = %Y-%m-%d %H:%M:%S

# Directory da ignorare
norecursedirs =
    .git
    .tox
    dist
    build
    *.egg
    __pycache__
    .pytest_cache
    node_modules
