# 🔍 PIANO DEBUG H2A (Human 2 AI) - APP-ROBERTO

**Data:** 28/05/2025 01:13  
**Approccio:** Collaborativo, sistematico, non invasivo  
**Principio:** "Fix solo ciò che è rotto, preserva ciò che funziona"

## 🎯 **OBIETTIVO**
Debug sistematico e definitivo di App-<PERSON> con approccio H2A, dove ogni problema viene identificato, discusso, risolto e committato prima di procedere.

## 📋 **METODOLOGIA H2A**

### **🔒 REGOLE FERREE**
1. **MAI toccare codice funzionante** senza esplicito permesso
2. **SEMPRE chiedere conferma** prima di qualsiasi modifica
3. **Identificare → Discutere → Risolvere → Testare → Commit → Proseguire**
4. **Riferimento costante** a Context 7 e best practice
5. **Checkpoint obbligatori** con Git commit/push per ogni fix

### **📊 STATO ATTUALE (da README.md)**
- ✅ **FUNZIONANTE**: MCP timeout risolto, test 100%, riconoscimento intelligente, Chat AI, Dashboard
- 🔄 **IN REVISIONE**: Database Supabase, persistenza file, tema scuro, configurazione guidata

## 🧪 **FASI DI DEBUG SISTEMATICO**

### **FASE 0: PREPARAZIONE AMBIENTE**

#### **0.1 Backup e Versioning** 🔐
```bash
# Backup completo pre-debug
git status
git add .
git commit -m "CHECKPOINT: Stato pre-debug H2A"
git push
```

#### **0.2 Verifica Componenti Base** 🔍
- [ ] **Server MCP**: `http://localhost:8000/health`
- [ ] **App Flask**: `http://127.0.0.1:5001`
- [ ] **Supabase**: Connessione attiva
- [ ] **Context 7**: Accesso funzionante

**→ CHECKPOINT H2A**: Solo se TUTTO è verde, procediamo

---

### **FASE 1: TEST FUNZIONALITÀ CORE**

#### **1.1 Test Homepage e Navigazione** 🏠
**Test da eseguire:**
```
1. Accedere a http://127.0.0.1:5001
2. Verificare caricamento homepage
3. Testare navigazione menu principale
4. Verificare responsività
```

**🤖 AI Action**: Monitoro console browser per errori JS/CSS  
**👨 Human Input**: Confermi che tutto carica correttamente?

#### **1.2 Test Upload File (CRITICO)** 📁
**Test da eseguire:**
```
1. Drag & drop file CSV test
2. Verifica anteprima dati
3. Test diversi formati (CSV, Excel)
4. Test file malformati
```

**🚨 FOCUS**: Questa è l'area identificata come critica nella revisione_finale.md

#### **1.3 Test Dashboard Multiple** 📊
**Test sequenziale:**
- [ ] Dashboard standard
- [ ] Dashboard avanzata  
- [ ] Dashboard intelligente
- [ ] Chat AI
- [ ] Grafici interattivi

**→ CHECKPOINT H2A**: Se problema trovato, STOP e risolvi

---

### **FASE 2: TEST API E BACKEND**

#### **2.1 Audit API (da AUDIT_FRONTEND_BACKEND.md)** 🔌
**Endpoint da testare sistematicamente:**
```
- GET /api/data
- GET /api/processed_data  
- GET /api/config/employees
- GET /api/config/vehicles
- POST /api/chat/send
- GET /api/charts/*
```

**🤖 AI Action**: Verifico formato risposte e standardizzazione  
**👨 Human Validation**: Per ogni discrepanza trovata

#### **2.2 Test Integrazione Supabase** 🗄️
**Test da eseguire:**
```
1. Connessione database
2. Query di lettura
3. Inserimento dati test
4. Controlli di coerenza
```

**→ CHECKPOINT H2A**: Problemi di connessione = priorità assoluta

---

### **FASE 3: TEST SISTEMA INTELLIGENTE**

#### **3.1 Test Server MCP** 🤖
**Validazione Context 7:**
```bash
# Test health MCP
curl http://localhost:8000/health

# Test elaborazione file
curl -X POST http://localhost:8000/upload-file/
```

#### **3.2 Test Chat AI e LLM** 💬
**Test da eseguire:**
```
1. Caricamento modelli OpenRouter
2. Invio query test
3. Verifica risposte
4. Test con diversi modelli
```

---

### **FASE 4: TEST WIZARD INTERATTIVO (TASK 3)**

#### **4.1 Analisi Stato Attuale** 🧙‍♂️
**Da revisione_finale.md**: "la parte più importante per l'ingestione dati"

**Test da implementare:**
- [ ] Validazione step-by-step upload
- [ ] Feedback loop utente  
- [ ] Mappatura entità guidata
- [ ] Conferma pre-inserimento

**🤖 AI Proposal**: Implementazione graduale con approval  
**👨 Human Direction**: Priorità e specifiche

---

### **FASE 5: CONTROLLI DI COERENZA (TASK 6)**

#### **5.1 Test Coerenza Dati** ✅
**Controlli da implementare:**
```
1. Dipendente-Attività-Auto correlation
2. Assenze vs Attività conflicts  
3. Permessi vs Timeline validation
```

**🤖 AI Implementation**: Query di controllo  
**👨 Human Validation**: Logic business rules

---

## 🔄 **PROTOCOLLO H2A PER OGNI PROBLEMA**

### **Quando Troviamo un Errore:**

#### **STEP 1: IDENTIFICAZIONE** 🔍
- **AI**: "Ho trovato il problema X nella funzione Y"
- **Dettagli**: Errore esatto, stack trace, riproduzione
- **Impatto**: Gravità e componenti affetti

#### **STEP 2: DISCUSSIONE** 💭
- **AI**: "Propongo la soluzione Z per il motivo W"
- **Human**: Approva/modifica/respinge
- **Context 7**: Verifica best practice

#### **STEP 3: IMPLEMENTAZIONE** 🔧
- **Modifica minima** e chirurgica
- **Test immediato** della correzione
- **Verifica non-regressione**

#### **STEP 4: CHECKPOINT** ✅
```bash
git add .
git commit -m "FIX: [Descrizione problema risolto]"
git push
```

#### **STEP 5: PROSEGUIMENTO** ➡️
- Continua da dove interrotto
- Aggiorna piano se necessario

## 📊 **TRACKING PROGRESS**

### **Dashboard Debug H2A**
- [ ] **Fase 0**: Preparazione ⏳
- [ ] **Fase 1**: Funzionalità Core ⏳  
- [ ] **Fase 2**: API e Backend ⏳
- [ ] **Fase 3**: Sistema Intelligente ⏳
- [ ] **Fase 4**: Wizard Interattivo ⏳
- [ ] **Fase 5**: Controlli Coerenza ⏳

### **Problemi Trovati e Risolti**
```
| ID | Componente | Problema | Soluzione | Status | Commit |
|----|------------|----------|-----------|--------|--------|
| 001| ...        | ...      | ...       | ...    | ...    |
```

## 🎯 **READY TO START?**

**Prima domanda H2A**: Sei pronto a iniziare dalla Fase 0 con il backup e la verifica componenti base?

**Next Action**: Conferma e procediamo con la preparazione ambiente per il debug sistematico.
