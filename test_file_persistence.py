#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test di persistenza file per verificare che i file caricati non vengano cancellati
"""

import os
import time
import json
from datetime import datetime

def test_file_persistence():
    """Test della persistenza dei file nella cartella uploads"""
    print('🧪 Test Persistenza File')
    print('=' * 50)
    
    # Crea cartella uploads se non esiste
    uploads_dir = "uploads"
    if not os.path.exists(uploads_dir):
        os.makedirs(uploads_dir)
        print(f'✅ Cartella {uploads_dir} creata')
    
    # Crea file di test
    test_files = [
        "test_file_1.txt",
        "test_file_2.csv", 
        "test_file_3.xlsx"
    ]
    
    created_files = []
    
    for filename in test_files:
        filepath = os.path.join(uploads_dir, filename)
        
        # Crea contenuto di test
        if filename.endswith('.txt'):
            content = f"File di test creato il {datetime.now().isoformat()}"
        elif filename.endswith('.csv'):
            content = "nome,cognome,eta\n<PERSON><PERSON>,<PERSON>,30\<PERSON><PERSON><PERSON><PERSON>,<PERSON>,25"
        elif filename.endswith('.xlsx'):
            content = "Questo è un file Excel di test"
        
        # Scrivi file
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        created_files.append(filepath)
        print(f'✅ File creato: {filepath}')
    
    # Verifica che i file esistano
    print('\n🔍 Verifica file creati:')
    all_exist = True
    for filepath in created_files:
        if os.path.exists(filepath):
            size = os.path.getsize(filepath)
            print(f'✅ {filepath} - {size} bytes')
        else:
            print(f'❌ {filepath} - NON TROVATO')
            all_exist = False
    
    if all_exist:
        print('\n✅ Tutti i file di test sono stati creati correttamente')
        
        # Crea file di report
        report = {
            "test_timestamp": datetime.now().isoformat(),
            "files_created": created_files,
            "test_status": "SUCCESS",
            "note": "File di test per verificare persistenza dopo riavvio"
        }
        
        report_path = os.path.join(uploads_dir, "persistence_test_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f'📄 Report salvato: {report_path}')
        
        print('\n🎯 ISTRUZIONI PER IL TEST:')
        print('1. Riavvia l\'applicazione con avvio_completo.bat')
        print('2. Verifica che i file siano ancora presenti')
        print('3. Esegui di nuovo questo script per verificare')
        
        return True
    else:
        print('\n❌ Errore nella creazione dei file di test')
        return False

def verify_file_persistence():
    """Verifica che i file di test siano ancora presenti"""
    print('🔍 Verifica Persistenza File')
    print('=' * 50)
    
    uploads_dir = "uploads"
    report_path = os.path.join(uploads_dir, "persistence_test_report.json")
    
    if not os.path.exists(report_path):
        print('❌ File di report non trovato. Eseguire prima test_file_persistence()')
        return False
    
    # Leggi report precedente
    with open(report_path, 'r', encoding='utf-8') as f:
        report = json.load(f)
    
    print(f'📄 Report precedente: {report["test_timestamp"]}')
    print(f'📁 File da verificare: {len(report["files_created"])}')
    
    # Verifica file
    missing_files = []
    existing_files = []
    
    for filepath in report["files_created"]:
        if os.path.exists(filepath):
            size = os.path.getsize(filepath)
            existing_files.append(filepath)
            print(f'✅ {filepath} - {size} bytes - PRESENTE')
        else:
            missing_files.append(filepath)
            print(f'❌ {filepath} - MANCANTE')
    
    # Risultato
    if not missing_files:
        print(f'\n🎉 SUCCESSO! Tutti i {len(existing_files)} file sono persistenti')
        print('✅ La persistenza dei file funziona correttamente')
        return True
    else:
        print(f'\n⚠️ ATTENZIONE! {len(missing_files)} file mancanti:')
        for filepath in missing_files:
            print(f'   - {filepath}')
        print('❌ Problema di persistenza file identificato')
        return False

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'verify':
        verify_file_persistence()
    else:
        test_file_persistence()
