#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test rapido del Data Standardizer.
"""

import pandas as pd
import sys
import os

# Aggiungi il percorso del progetto
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_standardizer import DataStandardizer

def test_standardizer():
    """
    Test rapido del Data Standardizer.
    """
    print("🧪 TEST RAPIDO DATA STANDARDIZER")
    print("=" * 40)
    
    # Crea dati di test
    test_data = {
        'Tecnico': ['marco birocchi', 'GABRIELE DE PALMA', 'matteo signo'],
        'Azienda': ['bait service s.r.l.', 'ITALMONDO S.P.A.', 'generalfrigo srl'],
        'Data': ['25/05/2025', '2025-05-26', '26-05-2025'],
        'Durata': ['2h 30m', '120', '1,5'],
        'Codice': ['P25123', 'S25-456', 'A25789']
    }
    
    df = pd.DataFrame(test_data)
    print(f"📊 Dati di test creati: {len(df)} righe")
    print(df)
    print()
    
    # Inizializza standardizer
    standardizer = DataStandardizer()
    
    # Mappatura colonne
    entity_mapping = {
        'Tecnico': 'technician',
        'Azienda': 'client',
        'Data': 'date',
        'Durata': 'duration',
        'Codice': 'project'
    }
    
    # Esegui standardizzazione
    print("🔧 Esecuzione standardizzazione...")
    result = standardizer.standardize_data(df, 'test', entity_mapping)
    
    if 'error' in result:
        print(f"❌ Errore: {result['error']}")
        return
    
    print("✅ Standardizzazione completata!")
    print()
    
    # Mostra risultati
    standardized_df = result['standardized_data']
    print("📋 Dati standardizzati:")
    print(standardized_df)
    print()
    
    # Mostra log
    log_entries = result.get('standardization_log', [])
    print(f"📝 Operazioni eseguite: {len(log_entries)}")
    for entry in log_entries[:5]:  # Mostra prime 5
        print(f"   {entry['operation']}: {entry['original']} -> {entry['standardized']}")
    
    # Mostra metriche
    metrics = result.get('quality_metrics', {})
    print(f"\n📊 Metriche qualità:")
    print(f"   Cambiamenti totali: {metrics.get('total_changes', 0)}")
    print(f"   Operazioni: {metrics.get('changes_by_operation', {})}")

if __name__ == "__main__":
    test_standardizer()
