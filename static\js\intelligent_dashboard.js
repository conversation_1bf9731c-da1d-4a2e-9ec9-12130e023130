/**
 * Script per la Dashboard Intelligente
 * Sistema di Analisi Incrociata e Controllo Qualità Dati
 * Versione: 1.0.0
 */

console.log('Intelligent Dashboard v1.0.0 caricato');

// Configurazione globale
const intelligentDashboard = {
    config: {
        dateRange: {
            start: moment().subtract(30, 'days').format('YYYY-MM-DD'),
            end: moment().format('YYYY-MM-DD')
        },
        analysisType: 'comprehensive',
        severityFilter: 'all',
        autoRefresh: false,
        refreshInterval: 300000 // 5 minuti
    },

    data: {
        currentAnalysis: null,
        systemStatus: null,
        cache: {}
    },

    ui: {
        loading: false,
        modal: null
    }
};

/**
 * Inizializza la dashboard intelligente
 */
function initIntelligentDashboard() {
    console.log('Inizializzazione Dashboard Intelligente...');

    // Inizializza componenti UI
    initDateRangePicker();
    initEventListeners();
    initModal();

    // Carica stato sistema
    loadSystemStatus();

    console.log('Dashboard Intelligente inizializzata');
}

/**
 * Inizializza il date range picker
 */
function initDateRangePicker() {
    $('#date-range').daterangepicker({
        startDate: intelligentDashboard.config.dateRange.start,
        endDate: intelligentDashboard.config.dateRange.end,
        locale: {
            format: 'DD/MM/YYYY',
            separator: ' - ',
            applyLabel: 'Applica',
            cancelLabel: 'Annulla',
            fromLabel: 'Da',
            toLabel: 'A',
            customRangeLabel: 'Personalizzato',
            weekLabel: 'S',
            daysOfWeek: ['Dom', 'Lun', 'Mar', 'Mer', 'Gio', 'Ven', 'Sab'],
            monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',
                        'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
            firstDay: 1
        },
        ranges: {
            'Oggi': [moment(), moment()],
            'Ieri': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Ultimi 7 giorni': [moment().subtract(6, 'days'), moment()],
            'Ultimi 30 giorni': [moment().subtract(29, 'days'), moment()],
            'Questo mese': [moment().startOf('month'), moment().endOf('month')],
            'Mese scorso': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        }
    });

    // Event listener per cambio date
    $('#date-range').on('apply.daterangepicker', function(ev, picker) {
        intelligentDashboard.config.dateRange.start = picker.startDate.format('YYYY-MM-DD');
        intelligentDashboard.config.dateRange.end = picker.endDate.format('YYYY-MM-DD');
        console.log('Periodo aggiornato:', intelligentDashboard.config.dateRange);
    });
}

/**
 * Inizializza event listeners
 */
function initEventListeners() {
    // Bottone avvia analisi
    document.getElementById('run-analysis-btn').addEventListener('click', runComprehensiveAnalysis);

    // Bottone applica filtri
    document.getElementById('apply-filters-btn').addEventListener('click', applyFilters);

    // Cambio tipo analisi
    document.getElementById('analysis-type').addEventListener('change', function() {
        intelligentDashboard.config.analysisType = this.value;
    });

    // Cambio filtro severità
    document.getElementById('severity-filter').addEventListener('change', function() {
        intelligentDashboard.config.severityFilter = this.value;
        filterDiscrepancies();
    });
}

/**
 * Inizializza modal
 */
function initModal() {
    intelligentDashboard.ui.modal = new bootstrap.Modal(document.getElementById('discrepancy-modal'));
}

/**
 * Carica stato del sistema
 */
async function loadSystemStatus() {
    try {
        showLoading('Caricamento stato sistema...');

        const response = await fetch('/api/intelligent-system/status');
        const data = await response.json();

        if (data.success) {
            intelligentDashboard.data.systemStatus = data.status;
            updateSystemTab(data.status);
            updateSystemHealthMetric(data.status);
        } else {
            console.error('Errore caricamento stato sistema:', data.error);
            showError('Errore caricamento stato sistema');
        }
    } catch (error) {
        console.error('Errore caricamento stato sistema:', error);
        showError('Errore di connessione');
    } finally {
        hideLoading();
    }
}

/**
 * Esegue analisi completa
 */
async function runComprehensiveAnalysis() {
    try {
        showLoading('Esecuzione analisi completa...');
        updateGlobalMetrics({ processing: true });

        const requestData = {
            date_from: intelligentDashboard.config.dateRange.start,
            date_to: intelligentDashboard.config.dateRange.end,
            analysis_type: intelligentDashboard.config.analysisType
        };

        const response = await fetch('/api/intelligent-system/analyze', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        const data = await response.json();

        if (data.success) {
            intelligentDashboard.data.currentAnalysis = data.results;
            displayAnalysisResults(data.results);
            showSuccess('Analisi completata con successo');
        } else {
            console.error('Errore analisi:', data.error);
            showError('Errore durante l\'analisi: ' + data.error);
        }
    } catch (error) {
        console.error('Errore analisi:', error);
        showError('Errore di connessione durante l\'analisi');
    } finally {
        hideLoading();
    }
}

/**
 * Applica filtri
 */
function applyFilters() {
    if (intelligentDashboard.data.currentAnalysis) {
        displayAnalysisResults(intelligentDashboard.data.currentAnalysis);
    } else {
        runComprehensiveAnalysis();
    }
}

/**
 * Mostra risultati analisi
 */
function displayAnalysisResults(results) {
    console.log('Visualizzazione risultati analisi:', results);

    // Aggiorna metriche globali
    updateGlobalMetrics(results);

    // Aggiorna tabs
    updateSummaryTab(results);
    updateDiscrepanciesTab(results);
    updateRecommendationsTab(results);
    updateAnalyticsTab(results);

    // Mostra risultati
    document.getElementById('analysis-results').classList.remove('wizard-hidden');
}

/**
 * Aggiorna metriche globali
 */
function updateGlobalMetrics(results) {
    if (results.processing) {
        document.getElementById('total-records').textContent = '...';
        document.getElementById('total-discrepancies').textContent = '...';
        document.getElementById('critical-issues').textContent = '...';
        document.getElementById('processing-time').textContent = '...';
        document.getElementById('data-quality-score').textContent = '...';
        return;
    }

    const globalSummary = results.global_summary || {};
    const stats = globalSummary.summary_stats || {};

    document.getElementById('total-records').textContent = stats.total_records_analyzed || 0;
    document.getElementById('total-discrepancies').textContent = stats.total_discrepancies || 0;
    document.getElementById('critical-issues').textContent =
        (stats.severity_breakdown && stats.severity_breakdown.critical) || 0;
    document.getElementById('processing-time').textContent =
        stats.processing_time_ms ? `${stats.processing_time_ms}ms` : '-';

    // Calcola score qualità dati
    const qualityScore = calculateDataQualityScore(results);
    document.getElementById('data-quality-score').textContent = `${qualityScore}%`;
}

/**
 * Aggiorna tab riepilogo
 */
function updateSummaryTab(results) {
    const globalSummary = results.global_summary || {};
    const stats = globalSummary.summary_stats || {};

    let html = '<div class="row">';

    // Statistiche per tipo di analisi
    Object.keys(results).forEach(analysisType => {
        if (analysisType === 'global_summary') return;

        const analysis = results[analysisType];
        if (analysis.discrepancies_found) {
            html += `
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">${formatAnalysisType(analysisType)}</h6>
                            <p class="card-text">
                                <strong>${analysis.discrepancies_found.length}</strong> discrepanze trovate<br>
                                <small class="text-muted">${analysis.total_records_analyzed} record analizzati</small>
                            </p>
                        </div>
                    </div>
                </div>
            `;
        }
    });

    html += '</div>';

    // Raccomandazioni globali
    if (globalSummary.recommendations && globalSummary.recommendations.length > 0) {
        html += '<h6 class="mt-3">Raccomandazioni Principali</h6>';
        html += '<ul class="list-group list-group-flush">';
        globalSummary.recommendations.slice(0, 5).forEach(rec => {
            html += `<li class="list-group-item">${rec}</li>`;
        });
        html += '</ul>';
    }

    document.getElementById('summary-content').innerHTML = html;
}

/**
 * Aggiorna tab discrepanze
 */
function updateDiscrepanciesTab(results) {
    const allDiscrepancies = [];

    // Raccoglie tutte le discrepanze
    Object.keys(results).forEach(analysisType => {
        if (analysisType === 'global_summary') return;

        const analysis = results[analysisType];
        if (analysis.discrepancies_found) {
            analysis.discrepancies_found.forEach(disc => {
                disc.analysis_type = analysisType;
                allDiscrepancies.push(disc);
            });
        }
    });

    // Filtra per severità
    const filteredDiscrepancies = filterDiscrepanciesBySeverity(allDiscrepancies);

    let html = '';

    if (filteredDiscrepancies.length === 0) {
        html = '<p class="text-muted">Nessuna discrepanza trovata con i filtri attuali.</p>';
    } else {
        filteredDiscrepancies.forEach(disc => {
            html += createDiscrepancyCard(disc);
        });
    }

    document.getElementById('discrepancies-content').innerHTML = html;
}

/**
 * Crea card per discrepanza
 */
function createDiscrepancyCard(discrepancy) {
    const severityClass = `discrepancy-${discrepancy.severity}`;
    const severityIcon = getSeverityIcon(discrepancy.severity);

    return `
        <div class="discrepancy-item ${severityClass}" onclick="showDiscrepancyDetails('${discrepancy.id}')">
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge severity-badge bg-${getSeverityColor(discrepancy.severity)} me-2">
                            ${severityIcon} ${discrepancy.severity.toUpperCase()}
                        </span>
                        <small class="text-muted">${formatAnalysisType(discrepancy.analysis_type)}</small>
                    </div>
                    <h6 class="mb-1">${discrepancy.description}</h6>
                    <p class="mb-2 text-muted small">${discrepancy.suggested_action}</p>
                    <div class="mb-0">
                        ${discrepancy.affected_entities.map(entity =>
                            `<span class="entity-badge">${entity}</span>`
                        ).join('')}
                    </div>
                </div>
                <div class="text-end">
                    <small class="text-muted">Confidenza: ${(discrepancy.confidence * 100).toFixed(0)}%</small>
                </div>
            </div>
        </div>
    `;
}

/**
 * Aggiorna tab raccomandazioni
 */
function updateRecommendationsTab(results) {
    const allRecommendations = [];

    // Raccoglie tutte le raccomandazioni
    Object.keys(results).forEach(analysisType => {
        if (analysisType === 'global_summary') return;

        const analysis = results[analysisType];
        if (analysis.recommendations) {
            analysis.recommendations.forEach(rec => {
                allRecommendations.push({
                    text: rec,
                    analysis_type: analysisType
                });
            });
        }
    });

    let html = '';

    if (allRecommendations.length === 0) {
        html = '<p class="text-muted">Nessuna raccomandazione disponibile.</p>';
    } else {
        allRecommendations.forEach(rec => {
            html += `
                <div class="recommendation-item">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <p class="mb-1">${rec.text}</p>
                            <small class="text-muted">${formatAnalysisType(rec.analysis_type)}</small>
                        </div>
                    </div>
                </div>
            `;
        });
    }

    document.getElementById('recommendations-content').innerHTML = html;
}

/**
 * Aggiorna tab analytics
 */
function updateAnalyticsTab(results) {
    // Crea grafici analytics
    createSeverityChart(results);
    createAnalysisTypeChart(results);
    createTimelineChart(results);
    createEntitiesChart(results);
}

/**
 * Aggiorna tab sistema
 */
function updateSystemTab(systemStatus) {
    let html = `
        <div class="row">
            <div class="col-md-6">
                <h6>Stato Componenti</h6>
                <ul class="list-group">
    `;

    const components = systemStatus.components || {};
    Object.keys(components).forEach(comp => {
        const status = components[comp];
        const icon = status ? 'fa-check-circle text-success' : 'fa-times-circle text-danger';
        html += `
            <li class="list-group-item d-flex justify-content-between align-items-center">
                ${comp}
                <i class="fas ${icon}"></i>
            </li>
        `;
    });

    html += `
                </ul>
            </div>
            <div class="col-md-6">
                <h6>Entità Master</h6>
                <ul class="list-group">
    `;

    const masterEntities = systemStatus.master_entities || {};
    Object.keys(masterEntities).forEach(entity => {
        const count = masterEntities[entity];
        html += `
            <li class="list-group-item d-flex justify-content-between align-items-center">
                ${entity}
                <span class="badge bg-primary rounded-pill">${count}</span>
            </li>
        `;
    });

    html += `
                </ul>
            </div>
        </div>
    `;

    document.getElementById('system-content').innerHTML = html;
}

// Funzioni utility
function formatAnalysisType(type) {
    const types = {
        'time_consistency': 'Coerenza Temporale',
        'activity_remote_correlation': 'Correlazione Attività-Remote',
        'duplicates_overlaps': 'Duplicati e Sovrapposizioni',
        'productivity_analysis': 'Analisi Produttività',
        'cost_analysis': 'Analisi Costi',
        'data_quality': 'Qualità Dati'
    };
    return types[type] || type;
}

function getSeverityIcon(severity) {
    const icons = {
        'critical': 'fas fa-exclamation-triangle',
        'high': 'fas fa-exclamation-circle',
        'medium': 'fas fa-exclamation',
        'low': 'fas fa-info-circle'
    };
    return `<i class="${icons[severity] || 'fas fa-info'}"></i>`;
}

function getSeverityColor(severity) {
    const colors = {
        'critical': 'danger',
        'high': 'warning',
        'medium': 'warning',
        'low': 'info'
    };
    return colors[severity] || 'secondary';
}

function filterDiscrepanciesBySeverity(discrepancies) {
    const filter = intelligentDashboard.config.severityFilter;
    if (filter === 'all') return discrepancies;

    const severityOrder = ['low', 'medium', 'high', 'critical'];
    const minIndex = severityOrder.indexOf(filter);

    return discrepancies.filter(disc => {
        const discIndex = severityOrder.indexOf(disc.severity);
        return discIndex >= minIndex;
    });
}

function calculateDataQualityScore(results) {
    // Calcolo semplificato del punteggio qualità
    const dataQuality = results.data_quality;
    if (!dataQuality || !dataQuality.summary_stats) return 85; // Default

    const stats = dataQuality.summary_stats;
    if (stats.error) return 50;

    // Calcola basandosi sul numero di problemi trovati
    const totalDiscrepancies = Object.values(results).reduce((sum, analysis) => {
        return sum + (analysis.discrepancies_found ? analysis.discrepancies_found.length : 0);
    }, 0);

    return Math.max(50, 100 - (totalDiscrepancies * 2));
}

function updateSystemHealthMetric(systemStatus) {
    const components = systemStatus.components || {};
    const activeComponents = Object.values(components).filter(status => status).length;
    const totalComponents = Object.keys(components).length;

    const healthPercentage = totalComponents > 0 ? Math.round((activeComponents / totalComponents) * 100) : 0;
    document.getElementById('system-health').textContent = `${healthPercentage}%`;
}

// Funzioni UI
function showLoading(message = 'Caricamento...') {
    intelligentDashboard.ui.loading = true;
    const spinner = document.getElementById('loading-spinner');
    spinner.classList.remove('wizard-hidden');
    spinner.querySelector('p').textContent = message;
}

function hideLoading() {
    intelligentDashboard.ui.loading = false;
    document.getElementById('loading-spinner').classList.add('wizard-hidden');
}

function showSuccess(message) {
    // Implementa notifica di successo
    console.log('Success:', message);
}

function showError(message) {
    // Implementa notifica di errore
    console.error('Error:', message);
}

/**
 * Crea grafico distribuzione severità
 */
function createSeverityChart(results) {
    const severityCounts = { critical: 0, high: 0, medium: 0, low: 0 };

    Object.keys(results).forEach(analysisType => {
        if (analysisType === 'global_summary') return;

        const analysis = results[analysisType];
        if (analysis.discrepancies_found) {
            analysis.discrepancies_found.forEach(disc => {
                severityCounts[disc.severity] = (severityCounts[disc.severity] || 0) + 1;
            });
        }
    });

    const data = [{
        values: Object.values(severityCounts),
        labels: ['Critiche', 'Alte', 'Medie', 'Basse'],
        type: 'pie',
        marker: {
            colors: ['#dc3545', '#fd7e14', '#ffc107', '#17a2b8']
        }
    }];

    const layout = {
        title: 'Distribuzione Severità Discrepanze',
        height: 280,
        margin: { t: 40, b: 20, l: 20, r: 20 }
    };

    Plotly.newPlot('severity-chart', data, layout, { responsive: true });
}

/**
 * Crea grafico tipi di analisi
 */
function createAnalysisTypeChart(results) {
    const analysisTypes = [];
    const discrepancyCounts = [];

    Object.keys(results).forEach(analysisType => {
        if (analysisType === 'global_summary') return;

        const analysis = results[analysisType];
        if (analysis.discrepancies_found) {
            analysisTypes.push(formatAnalysisType(analysisType));
            discrepancyCounts.push(analysis.discrepancies_found.length);
        }
    });

    const data = [{
        x: analysisTypes,
        y: discrepancyCounts,
        type: 'bar',
        marker: {
            color: '#007bff'
        }
    }];

    const layout = {
        title: 'Discrepanze per Tipo di Analisi',
        height: 280,
        margin: { t: 40, b: 60, l: 40, r: 20 },
        xaxis: { tickangle: -45 }
    };

    Plotly.newPlot('analysis-type-chart', data, layout, { responsive: true });
}

/**
 * Crea grafico timeline
 */
function createTimelineChart(results) {
    const globalSummary = results.global_summary || {};
    const processingTime = globalSummary.summary_stats?.processing_time_ms || 0;

    // Simula timeline di processing
    const steps = ['Lettura Dati', 'Analisi Temporale', 'Correlazioni', 'Duplicati', 'Produttività', 'Costi', 'Qualità'];
    const times = steps.map((_, i) => (processingTime / steps.length) * (i + 1));

    const data = [{
        x: steps,
        y: times,
        type: 'scatter',
        mode: 'lines+markers',
        line: { color: '#28a745' },
        marker: { size: 8 }
    }];

    const layout = {
        title: 'Timeline Elaborazione',
        height: 280,
        margin: { t: 40, b: 60, l: 60, r: 20 },
        xaxis: { tickangle: -45 },
        yaxis: { title: 'Tempo (ms)' }
    };

    Plotly.newPlot('timeline-chart', data, layout, { responsive: true });
}

/**
 * Crea grafico entità coinvolte
 */
function createEntitiesChart(results) {
    const entityCounts = {};

    Object.keys(results).forEach(analysisType => {
        if (analysisType === 'global_summary') return;

        const analysis = results[analysisType];
        if (analysis.discrepancies_found) {
            analysis.discrepancies_found.forEach(disc => {
                disc.affected_entities.forEach(entity => {
                    entityCounts[entity] = (entityCounts[entity] || 0) + 1;
                });
            });
        }
    });

    const entities = Object.keys(entityCounts).slice(0, 10); // Top 10
    const counts = entities.map(entity => entityCounts[entity]);

    const data = [{
        x: counts,
        y: entities,
        type: 'bar',
        orientation: 'h',
        marker: {
            color: '#6f42c1'
        }
    }];

    const layout = {
        title: 'Entità Più Coinvolte',
        height: 280,
        margin: { t: 40, b: 20, l: 100, r: 20 }
    };

    Plotly.newPlot('entities-chart', data, layout, { responsive: true });
}

/**
 * Mostra dettagli discrepanza
 */
function showDiscrepancyDetails(discrepancyId) {
    const allDiscrepancies = [];
    const results = intelligentDashboard.data.currentAnalysis;

    // Trova la discrepanza
    Object.keys(results).forEach(analysisType => {
        if (analysisType === 'global_summary') return;

        const analysis = results[analysisType];
        if (analysis.discrepancies_found) {
            analysis.discrepancies_found.forEach(disc => {
                if (disc.id === discrepancyId) {
                    disc.analysis_type = analysisType;
                    allDiscrepancies.push(disc);
                }
            });
        }
    });

    if (allDiscrepancies.length === 0) return;

    const discrepancy = allDiscrepancies[0];

    const modalBody = document.getElementById('discrepancy-modal-body');
    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>Informazioni Generali</h6>
                <table class="table table-sm">
                    <tr><td><strong>ID:</strong></td><td>${discrepancy.id}</td></tr>
                    <tr><td><strong>Tipo:</strong></td><td>${discrepancy.type}</td></tr>
                    <tr><td><strong>Severità:</strong></td><td>
                        <span class="badge bg-${getSeverityColor(discrepancy.severity)}">${discrepancy.severity}</span>
                    </td></tr>
                    <tr><td><strong>Confidenza:</strong></td><td>${(discrepancy.confidence * 100).toFixed(1)}%</td></tr>
                    <tr><td><strong>Analisi:</strong></td><td>${formatAnalysisType(discrepancy.analysis_type)}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Entità Coinvolte</h6>
                <div class="mb-3">
                    ${discrepancy.affected_entities.map(entity =>
                        `<span class="badge bg-secondary me-1">${entity}</span>`
                    ).join('')}
                </div>
                <h6>Dettagli Tecnici</h6>
                <pre class="bg-light p-2 small">${JSON.stringify(discrepancy.data_details, null, 2)}</pre>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <h6>Descrizione</h6>
                <p>${discrepancy.description}</p>
                <h6>Azione Suggerita</h6>
                <p class="text-primary">${discrepancy.suggested_action}</p>
            </div>
        </div>
    `;

    intelligentDashboard.ui.modal.show();
}

/**
 * Filtra discrepanze
 */
function filterDiscrepancies() {
    if (intelligentDashboard.data.currentAnalysis) {
        updateDiscrepanciesTab(intelligentDashboard.data.currentAnalysis);
    }
}

// Inizializzazione quando il documento è pronto
document.addEventListener('DOMContentLoaded', function() {
    initIntelligentDashboard();
});
