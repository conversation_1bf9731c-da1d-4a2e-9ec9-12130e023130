#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FASE 7 - TASK 7.1: Test Suite Completa
Test suite avanzata per l'intero sistema app-roberto con pytest.
"""

import pytest
import asyncio
import time
import sys
import os
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import json

# Aggiungi il percorso corrente per gli import
sys.path.append('.')

# Import moduli del sistema
try:
    # Fase 5 - Performance & Caching
    from performance_profiler import performance_profiler, profile
    from intelligent_cache_system import intelligent_cache
    from auto_tuner import auto_tuner
    from query_optimizer import query_optimizer

    # Fase 6 - Agenti AI
    from agent_system import agent_orchestrator, AgentType, AgentTask, AgentStatus
    from data_cleaning_agent import data_cleaning_agent
    from business_analysis_agent import business_analysis_agent
    from workflow_automation_agent import workflow_automation_agent
    from recommendation_agent import recommendation_agent

    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Alcuni moduli non disponibili: {e}")
    MODULES_AVAILABLE = False

class TestSystemIntegration:
    """Test di integrazione per l'intero sistema."""

    @pytest.fixture(autouse=True)
    def setup_system(self):
        """Setup del sistema per ogni test."""
        # Setup base sempre disponibile
        yield

        # Cleanup se moduli disponibili
        if MODULES_AVAILABLE:
            try:
                intelligent_cache.clear_all()
                performance_profiler.reset_metrics()
            except:
                pass

    def test_basic_system_availability(self):
        """Test disponibilità base del sistema."""
        # Test che dovrebbe sempre passare
        assert True

        # Test import moduli
        if MODULES_AVAILABLE:
            assert intelligent_cache is not None
            assert performance_profiler is not None
            assert agent_orchestrator is not None
        else:
            # Se moduli non disponibili, test comunque valido
            assert True

    def test_performance_profiler_integration(self):
        """Test integrazione performance profiler."""
        if not MODULES_AVAILABLE:
            pytest.skip("Moduli del sistema non disponibili")

        # Test decoratore profile
        @profile(name="test_function")
        def test_func():
            time.sleep(0.1)
            return "test_result"

        result = test_func()
        assert result == "test_result"

        # Verifica metriche
        metrics = performance_profiler.get_performance_report()
        assert "test_function" in metrics["function_metrics"]
        assert metrics["function_metrics"]["test_function"]["call_count"] >= 1
        assert metrics["function_metrics"]["test_function"]["avg_time"] > 0

    def test_intelligent_cache_integration(self):
        """Test integrazione cache intelligente."""
        # Test cache base
        intelligent_cache.set("test_key", "test_value", ttl=60)
        assert intelligent_cache.get("test_key") == "test_value"

        # Test cache con tags
        intelligent_cache.set("tagged_key", "tagged_value", ttl=60, tags=["test_tag"])
        assert intelligent_cache.get("tagged_key") == "tagged_value"

        # Test invalidazione per tag
        intelligent_cache.invalidate_by_tags(["test_tag"])
        assert intelligent_cache.get("tagged_key") is None

        # Test statistiche
        stats = intelligent_cache.get_cache_statistics()
        assert "performance" in stats
        assert "total_requests" in stats["performance"]
        assert "cache_hits" in stats["performance"]

    def test_auto_tuner_integration(self):
        """Test integrazione auto-tuner."""
        # Test stato tuning
        status = auto_tuner.get_tuning_status()
        assert isinstance(status, dict)
        assert "tuning_active" in status
        assert "strategy" in status
        assert "last_optimization" in status

        # Test report ottimizzazione
        report = auto_tuner.get_optimization_report()
        assert isinstance(report, dict)

    def test_query_optimizer_integration(self):
        """Test integrazione query optimizer."""
        # Test suggerimenti ottimizzazione
        suggestions = query_optimizer.suggest_optimizations()
        assert isinstance(suggestions, list)

        # Test report performance
        report = query_optimizer.get_performance_report()
        assert isinstance(report, dict)
        # Può essere vuoto se non ci sono query registrate
        assert "summary" in report or "message" in report

class TestAgentSystem:
    """Test per il sistema di agenti AI."""

    @pytest.fixture(autouse=True)
    def setup_agents(self):
        """Setup agenti per ogni test."""
        if not MODULES_AVAILABLE:
            pytest.skip("Moduli agenti non disponibili")

        # Reset orchestratore
        agent_orchestrator.clear_all_tasks()

        # Registra agenti
        agent_orchestrator.register_agent(data_cleaning_agent)
        agent_orchestrator.register_agent(business_analysis_agent)
        agent_orchestrator.register_agent(workflow_automation_agent)
        agent_orchestrator.register_agent(recommendation_agent)

        yield

        # Cleanup
        agent_orchestrator.clear_all_tasks()

    def test_agent_orchestrator_basic(self):
        """Test funzionalità base orchestratore."""
        # Test stato sistema
        status = agent_orchestrator.get_system_status()
        assert "orchestrator_active" in status
        assert "agents_registered" in status
        assert status["agents_registered"] >= 4  # 4 agenti registrati

    def test_data_cleaning_agent(self):
        """Test data cleaning agent."""
        # Test stato agente
        agent_status = data_cleaning_agent.get_status()
        assert agent_status["agent_type"] == "data_cleaning"
        assert agent_status["status"] == "idle"

        # Test tools disponibili
        assert len(data_cleaning_agent.tools) > 0

    def test_business_analysis_agent(self):
        """Test business analysis agent."""
        # Test configurazione
        assert len(business_analysis_agent.KPI_CATEGORIES) > 0
        assert "productivity" in business_analysis_agent.KPI_CATEGORIES

        # Test soglie insights
        thresholds = business_analysis_agent.INSIGHT_THRESHOLDS
        assert "trend_significance" in thresholds
        assert thresholds["trend_significance"] > 0

    def test_workflow_automation_agent(self):
        """Test workflow automation agent."""
        # Test scheduler
        assert workflow_automation_agent.scheduler_active

        # Test action types supportati
        actions = workflow_automation_agent.SUPPORTED_ACTIONS
        assert len(actions) >= 6
        assert "email" in actions
        assert "api_call" in actions

    def test_recommendation_agent(self):
        """Test recommendation agent."""
        # Test configurazione algoritmi
        config = recommendation_agent.ALGORITHM_CONFIG
        assert "collaborative_threshold" in config
        assert "max_recommendations" in config

        # Test dati demo
        assert len(recommendation_agent.items) > 0
        assert len(recommendation_agent.user_profiles) > 0

    @pytest.mark.asyncio
    async def test_agent_task_execution(self):
        """Test esecuzione task agenti."""
        # Crea task di test
        task = AgentTask(
            task_id="test_task_001",
            agent_type=AgentType.DATA_CLEANING,
            input_data={
                "file_id": "test_file_123",
                "cleaning_level": "medium"
            },
            priority=5,
            timeout_seconds=30
        )

        # Sottometti task
        task_id = agent_orchestrator.submit_task(task)
        assert task_id == "test_task_001"

        # Attendi completamento (con timeout)
        max_wait = 10
        wait_time = 0
        task_completed = False

        while wait_time < max_wait and not task_completed:
            await asyncio.sleep(0.5)
            wait_time += 0.5

            task_status = agent_orchestrator.get_task_status(task_id)
            if task_status and task_status["status"] in ["completed", "error"]:
                task_completed = True
                break

        # Verifica completamento
        assert task_completed, f"Task non completato in {max_wait}s"

        # Verifica risultato
        task_result = agent_orchestrator.get_task_result(task_id)
        assert task_result is not None
        assert task_result.task_id == task_id

class TestPerformanceMetrics:
    """Test per metriche di performance."""

    @pytest.fixture(autouse=True)
    def setup_performance(self):
        """Setup performance testing."""
        if not MODULES_AVAILABLE:
            pytest.skip("Moduli performance non disponibili")

        performance_profiler.reset_metrics()
        yield
        performance_profiler.reset_metrics()

    def test_function_profiling(self):
        """Test profiling funzioni."""
        @profile(name="test_performance_func")
        def slow_function():
            time.sleep(0.05)  # 50ms
            return "completed"

        # Esegui più volte
        for _ in range(3):
            result = slow_function()
            assert result == "completed"

        # Verifica metriche
        report = performance_profiler.get_performance_report()
        func_metrics = report["function_metrics"]["test_performance_func"]

        assert func_metrics["call_count"] == 3
        assert func_metrics["avg_time"] >= 0.04  # Almeno 40ms
        assert func_metrics["total_time"] >= 0.12  # Almeno 120ms

    def test_memory_profiling(self):
        """Test profiling memoria."""
        @profile(name="memory_test_func", track_memory=True)
        def memory_function():
            # Alloca memoria
            data = [i for i in range(10000)]
            return len(data)

        result = memory_function()
        assert result == 10000

        # Verifica metriche memoria
        report = performance_profiler.get_performance_report()
        func_metrics = report["function_metrics"]["memory_test_func"]

        assert "memory_usage" in func_metrics
        assert func_metrics["memory_usage"] > 0

    def test_cache_performance(self):
        """Test performance cache."""
        # Test hit/miss rates
        for i in range(10):
            intelligent_cache.set(f"perf_key_{i}", f"value_{i}", ttl=60)

        # Test hits
        for i in range(10):
            value = intelligent_cache.get(f"perf_key_{i}")
            assert value == f"value_{i}"

        # Test misses
        for i in range(10, 20):
            value = intelligent_cache.get(f"perf_key_{i}")
            assert value is None

        # Verifica statistiche
        stats = intelligent_cache.get_cache_statistics()
        assert stats["hit_rate"] >= 0.5  # Almeno 50% hit rate
        assert stats["total_requests"] >= 20

class TestErrorHandling:
    """Test per gestione errori."""

    def test_cache_error_handling(self):
        """Test gestione errori cache."""
        if not MODULES_AVAILABLE:
            pytest.skip("Moduli non disponibili")

        # Test con chiave None
        result = intelligent_cache.get(None)
        assert result is None

        # Test con TTL negativo
        intelligent_cache.set("test_key", "test_value", ttl=-1)
        result = intelligent_cache.get("test_key")
        assert result is None  # Non dovrebbe essere salvato

    def test_profiler_error_handling(self):
        """Test gestione errori profiler."""
        if not MODULES_AVAILABLE:
            pytest.skip("Moduli non disponibili")

        @profile(name="error_test_func")
        def error_function():
            raise ValueError("Test error")

        # Verifica che l'errore sia propagato
        with pytest.raises(ValueError, match="Test error"):
            error_function()

        # Verifica che le metriche siano comunque registrate
        report = performance_profiler.get_performance_report()
        assert "error_test_func" in report["function_metrics"]

        func_metrics = report["function_metrics"]["error_test_func"]
        assert func_metrics["call_count"] == 1
        assert func_metrics["error_count"] == 1

    @pytest.mark.asyncio
    async def test_agent_timeout_handling(self):
        """Test gestione timeout agenti."""
        if not MODULES_AVAILABLE:
            pytest.skip("Moduli agenti non disponibili")

        # Crea task con timeout molto breve
        task = AgentTask(
            task_id="timeout_test_001",
            agent_type=AgentType.DATA_CLEANING,
            input_data={"file_id": "test_file"},
            priority=5,
            timeout_seconds=1  # 1 secondo timeout
        )

        # Sottometti task
        task_id = agent_orchestrator.submit_task(task)

        # Attendi più del timeout
        await asyncio.sleep(2)

        # Verifica stato task
        task_status = agent_orchestrator.get_task_status(task_id)
        # Il task potrebbe essere completato o in timeout, entrambi sono validi
        assert task_status is not None

class TestDataIntegrity:
    """Test per integrità dati."""

    def test_cache_data_integrity(self):
        """Test integrità dati cache."""
        if not MODULES_AVAILABLE:
            pytest.skip("Moduli non disponibili")

        # Test con diversi tipi di dati
        test_data = {
            "string": "test_string",
            "integer": 42,
            "float": 3.14,
            "list": [1, 2, 3],
            "dict": {"key": "value"},
            "boolean": True,
            "none": None
        }

        # Salva tutti i tipi
        for key, value in test_data.items():
            intelligent_cache.set(f"integrity_{key}", value, ttl=60)

        # Verifica integrità
        for key, expected_value in test_data.items():
            cached_value = intelligent_cache.get(f"integrity_{key}")
            assert cached_value == expected_value, f"Integrità fallita per {key}: {cached_value} != {expected_value}"

    def test_agent_data_consistency(self):
        """Test consistenza dati agenti."""
        if not MODULES_AVAILABLE:
            pytest.skip("Moduli agenti non disponibili")

        # Test consistenza profili utente recommendation agent
        user_profiles = recommendation_agent.user_profiles
        for user_id, profile in user_profiles.items():
            assert profile.user_id == user_id
            assert isinstance(profile.preferences, dict)
            assert isinstance(profile.skills, list)
            assert profile.last_activity is not None

        # Test consistenza items recommendation
        items = recommendation_agent.items
        for item_id, item in items.items():
            assert item.item_id == item_id
            assert isinstance(item.features, dict)
            assert isinstance(item.tags, list)
            assert item.popularity_score >= 0
            assert item.quality_score >= 0

# Configurazione pytest
def pytest_configure(config):
    """Configurazione pytest."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )

if __name__ == "__main__":
    # Esegui test con pytest
    pytest.main([__file__, "-v", "--tb=short"])
