/**
 * Agents CSS - App Roberto
 * <PERSON>ili per dashboard agenti AI
 * Versione: 1.0.0
 */

/* ===== CARD AGENTI ===== */
.agent-card {
    margin-bottom: 20px;
    transition: all 0.3s ease;
    border-left: 4px solid #007bff;
}

.agent-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

/* ===== PROGRESS BAR ===== */
.progress {
    height: 10px;
    margin-top: 10px;
}

.agent-progress-bar {
    transition: width 0.3s ease;
}

/* ===== AZIONI AGENTI ===== */
.agent-actions {
    margin-top: 15px;
}

/* ===== BADGE STATUS ===== */
.status-badge {
    font-size: 0.8rem;
    padding: 5px 10px;
}

/* ===== RISULTATI AGENTI ===== */
.agent-result {
    max-height: 300px;
    overflow-y: auto;
}

/* ===== MODAL DETTAGLI ===== */
#agent-details-modal .modal-dialog {
    max-width: 800px;
}

/* ===== STATI AGENTI ===== */
.agent-status-idle {
    border-left-color: #6c757d;
}

.agent-status-running {
    border-left-color: #007bff;
}

.agent-status-completed {
    border-left-color: #28a745;
}

.agent-status-error {
    border-left-color: #dc3545;
}

/* ===== METRICHE AGENTI ===== */
.agent-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.metric-item {
    text-align: center;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
}

.metric-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

/* ===== LOGS AGENTI ===== */
.agent-logs {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    max-height: 200px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

.log-entry {
    margin-bottom: 0.25rem;
}

.log-timestamp {
    color: #6c757d;
    font-weight: bold;
}

.log-level-info {
    color: #0dcaf0;
}

.log-level-warning {
    color: #ffc107;
}

.log-level-error {
    color: #dc3545;
}

.log-level-success {
    color: #198754;
}

/* ===== CONTROLLI AGENTI ===== */
.agent-controls {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.agent-controls .btn {
    flex: 1;
    min-width: 100px;
}

/* ===== TEMA SCURO ===== */
[data-theme="dark"] .agent-card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .agent-card:hover {
    box-shadow: 0 10px 20px rgba(0,0,0,0.3);
}

[data-theme="dark"] .metric-item {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

[data-theme="dark"] .metric-value {
    color: var(--accent-primary);
}

[data-theme="dark"] .metric-label {
    color: var(--text-secondary);
}

[data-theme="dark"] .agent-logs {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .log-timestamp {
    color: var(--text-secondary);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .agent-metrics {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .agent-controls {
        flex-direction: column;
    }
    
    .agent-controls .btn {
        min-width: auto;
    }
    
    #agent-details-modal .modal-dialog {
        max-width: 95%;
        margin: 1rem auto;
    }
}

@media (max-width: 576px) {
    .agent-metrics {
        grid-template-columns: 1fr;
    }
}
