"""
Sistema di Onboarding Intelligente per App Roberto
Gest<PERSON>ce l'onboarding guidato con AI e analisi automatica dei dati
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class IntelligentOnboardingSystem:
    """
    Sistema di onboarding intelligente che analizza i dati caricati
    e suggerisce configurazioni ottimali per l'utente.
    """

    def __init__(self, supabase_manager=None, db_manager=None, persistence_manager=None):
        """
        Inizializza il sistema di onboarding intelligente.

        Args:
            supabase_manager: Istanza di SupabaseManager
            db_manager: Istanza di AdvancedDatabaseManager
            persistence_manager: Istanza di AutomaticPersistenceManager
        """
        self.supabase_manager = supabase_manager
        self.db_manager = db_manager
        self.persistence_manager = persistence_manager

        # Configurazioni predefinite per diversi tipi di business
        self.business_templates = self._load_business_templates()

        # Patterns di riconoscimento dati
        self.data_patterns = self._load_data_patterns()

        logger.info("IntelligentOnboardingSystem inizializzato")

    def _load_business_templates(self) -> Dict[str, Any]:
        """Carica template di configurazione per diversi tipi di business."""
        return {
            'it_services': {
                'name': 'Servizi IT',
                'description': 'Configurazione ottimizzata per aziende di servizi IT',
                'suggested_modules': ['teamviewer_analysis', 'ticket_management', 'time_tracking'],
                'kpis': ['response_time', 'resolution_time', 'customer_satisfaction'],
                'automations': ['daily_reports', 'alert_system', 'backup_automation'],
                'dashboards': ['technical_performance', 'client_overview', 'team_productivity']
            },
            'manufacturing': {
                'name': 'Produzione',
                'description': 'Configurazione per aziende manifatturiere',
                'suggested_modules': ['production_tracking', 'quality_control', 'inventory_management'],
                'kpis': ['production_efficiency', 'quality_rate', 'downtime'],
                'automations': ['production_reports', 'quality_alerts', 'maintenance_scheduling'],
                'dashboards': ['production_overview', 'quality_metrics', 'equipment_status']
            },
            'consulting': {
                'name': 'Consulenza',
                'description': 'Configurazione per studi di consulenza',
                'suggested_modules': ['project_management', 'time_billing', 'client_reporting'],
                'kpis': ['billable_hours', 'project_profitability', 'client_retention'],
                'automations': ['timesheet_reminders', 'invoice_generation', 'project_updates'],
                'dashboards': ['project_status', 'financial_overview', 'team_utilization']
            },
            'generic': {
                'name': 'Generico',
                'description': 'Configurazione base adattabile',
                'suggested_modules': ['data_analysis', 'reporting', 'basic_automation'],
                'kpis': ['efficiency', 'productivity', 'data_quality'],
                'automations': ['basic_reports', 'data_backup', 'system_monitoring'],
                'dashboards': ['overview', 'analytics', 'system_status']
            }
        }

    def _load_data_patterns(self) -> Dict[str, Any]:
        """Carica pattern per il riconoscimento automatico dei dati."""
        return {
            'teamviewer_sessions': {
                'required_columns': ['session_id', 'start_time', 'end_time', 'duration'],
                'optional_columns': ['user', 'computer', 'connection_type'],
                'business_type': 'it_services',
                'confidence_threshold': 0.8
            },
            'time_tracking': {
                'required_columns': ['employee', 'date', 'hours', 'project'],
                'optional_columns': ['task', 'description', 'billable'],
                'business_type': 'consulting',
                'confidence_threshold': 0.7
            },
            'production_data': {
                'required_columns': ['product', 'quantity', 'date', 'line'],
                'optional_columns': ['quality', 'defects', 'operator'],
                'business_type': 'manufacturing',
                'confidence_threshold': 0.75
            },
            'employee_data': {
                'required_columns': ['name', 'department'],
                'optional_columns': ['role', 'email', 'phone', 'hire_date'],
                'business_type': 'generic',
                'confidence_threshold': 0.6
            }
        }

    def analyze_uploaded_files(self, files_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analizza i file caricati e suggerisce configurazioni.

        Args:
            files_data: Lista di dati dei file caricati

        Returns:
            Dict con analisi e suggerimenti
        """
        try:
            import sys

            sys.stdout.write("=== ANALISI INTELLIGENTE ONBOARDING ===\n")
            sys.stdout.flush()

            analysis_results = {
                'detected_business_type': 'generic',
                'confidence_score': 0.0,
                'suggested_configuration': {},
                'detected_entities': {
                    'employees': [],
                    'departments': [],
                    'projects': [],
                    'clients': []
                },
                'recommended_modules': [],
                'suggested_automations': [],
                'data_quality_score': 0.0,
                'onboarding_steps': []
            }

            if not files_data:
                sys.stdout.write("⚠️ Nessun file da analizzare\n")
                sys.stdout.flush()
                return analysis_results

            # Analizza ogni file
            total_confidence = 0.0
            business_type_votes = {}
            all_entities = {
                'employees': set(),
                'departments': set(),
                'projects': set(),
                'clients': set()
            }
            all_file_analyses = []

            for file_data in files_data:
                file_analysis = self._analyze_single_file(file_data)

                # Accumula voti per tipo business
                business_type = file_analysis.get('business_type', 'generic')
                confidence = file_analysis.get('confidence', 0.0)

                if business_type not in business_type_votes:
                    business_type_votes[business_type] = 0.0
                business_type_votes[business_type] += confidence

                total_confidence += confidence

                # Accumula entità rilevate
                for entity_type, entities in file_analysis.get('entities', {}).items():
                    if entity_type in all_entities:
                        all_entities[entity_type].update(entities)

                # Accumula analisi specifiche dei file
                if 'file_analysis' in file_analysis:
                    all_file_analyses.extend(file_analysis['file_analysis'])

            # Determina tipo business più probabile
            if business_type_votes:
                detected_business_type = max(business_type_votes.items(), key=lambda x: x[1])[0]
                analysis_results['detected_business_type'] = detected_business_type
                analysis_results['confidence_score'] = business_type_votes[detected_business_type] / len(files_data)

            # Converti set in liste
            for entity_type, entities in all_entities.items():
                analysis_results['detected_entities'][entity_type] = list(entities)[:20]  # Limita a 20

            # Genera configurazione suggerita
            analysis_results['suggested_configuration'] = self._generate_suggested_configuration(
                detected_business_type, analysis_results['detected_entities']
            )

            # Genera passi di onboarding personalizzati
            analysis_results['onboarding_steps'] = self._generate_onboarding_steps(
                detected_business_type, analysis_results['detected_entities']
            )

            # Calcola punteggio qualità dati
            analysis_results['data_quality_score'] = min(95.0, total_confidence * 20)  # Scala a 0-100

            # Aggiungi analisi specifiche dei file
            analysis_results['file_analyses'] = all_file_analyses

            sys.stdout.write(f"✅ Analisi completata - Tipo: {detected_business_type}, Confidenza: {analysis_results['confidence_score']:.2f}\n")
            sys.stdout.write(f"📊 Entità rilevate: {sum(len(entities) for entities in analysis_results['detected_entities'].values())}\n")
            sys.stdout.write(f"🔍 Analisi specifiche generate: {len(all_file_analyses)}\n")
            sys.stdout.flush()

            return analysis_results

        except Exception as e:
            import traceback
            logger.error(f"Errore analisi onboarding: {str(e)}")
            logger.error(traceback.format_exc())

            return {
                'detected_business_type': 'generic',
                'confidence_score': 0.0,
                'suggested_configuration': self.business_templates['generic'],
                'detected_entities': {'employees': [], 'departments': [], 'projects': [], 'clients': []},
                'recommended_modules': ['data_analysis'],
                'suggested_automations': ['basic_reports'],
                'data_quality_score': 50.0,
                'onboarding_steps': self._generate_basic_onboarding_steps(),
                'error': str(e)
            }

    def _analyze_single_file(self, file_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analizza un singolo file per rilevare pattern e entità."""
        try:
            file_name = file_data.get('name', '')
            preview_data = file_data.get('preview', [])
            file_type = file_data.get('type', 'unknown')
            columns = file_data.get('columns', [])

            if not preview_data and not columns:
                return {'business_type': 'generic', 'confidence': 0.0, 'entities': {}}

            # Ottieni colonne dal primo record
            columns = list(preview_data[0].keys()) if preview_data else []

            # Cerca pattern corrispondenti
            best_match = None
            best_confidence = 0.0

            for pattern_name, pattern in self.data_patterns.items():
                confidence = self._calculate_pattern_confidence(columns, pattern)
                if confidence > best_confidence and confidence >= pattern['confidence_threshold']:
                    best_match = pattern
                    best_confidence = confidence

            # Estrai entità dai dati
            entities = self._extract_entities_from_data(preview_data, columns)

            # Determina il tipo di business
            business_type = best_match['business_type'] if best_match else 'generic'

            # Genera analisi specifiche per il tipo di file
            file_analysis = self._generate_file_specific_analysis(business_type, entities, columns, preview_data, file_name)

            result = {
                'business_type': business_type,
                'confidence': best_confidence,
                'entities': entities,
                'pattern_matched': best_match is not None,
                'file_analysis': file_analysis
            }

            return result

        except Exception as e:
            logger.error(f"Errore analisi file singolo: {str(e)}")
            return {'business_type': 'generic', 'confidence': 0.0, 'entities': {}}

    def _calculate_pattern_confidence(self, columns: List[str], pattern: Dict[str, Any]) -> float:
        """Calcola la confidenza di corrispondenza con un pattern."""
        required_columns = pattern.get('required_columns', [])
        optional_columns = pattern.get('optional_columns', [])

        # Normalizza nomi colonne per confronto case-insensitive
        normalized_columns = [col.lower().strip() for col in columns]

        # Conta colonne richieste presenti
        required_matches = 0
        for req_col in required_columns:
            if any(req_col.lower() in norm_col for norm_col in normalized_columns):
                required_matches += 1

        # Conta colonne opzionali presenti
        optional_matches = 0
        for opt_col in optional_columns:
            if any(opt_col.lower() in norm_col for norm_col in normalized_columns):
                optional_matches += 1

        # Calcola confidenza
        if not required_columns:
            return 0.0

        required_score = required_matches / len(required_columns)
        optional_score = optional_matches / len(optional_columns) if optional_columns else 0.0

        # Peso: 80% colonne richieste, 20% colonne opzionali
        confidence = (required_score * 0.8) + (optional_score * 0.2)

        return confidence

    def _extract_entities_from_data(self, data: List[Dict[str, Any]], columns: List[str]) -> Dict[str, List[str]]:
        """Estrae entità (dipendenti, dipartimenti, etc.) dai dati."""
        entities = {
            'employees': set(),
            'departments': set(),
            'projects': set(),
            'clients': set()
        }

        # Pattern per riconoscere colonne
        employee_patterns = ['dipendente', 'employee', 'user', 'tecnico', 'nome', 'name']
        department_patterns = ['dipartimento', 'department', 'reparto', 'settore']
        project_patterns = ['progetto', 'project', 'contratto', 'commessa']
        client_patterns = ['cliente', 'client', 'azienda', 'company', 'customer']

        # Mappa pattern a entità
        pattern_map = {
            'employees': employee_patterns,
            'departments': department_patterns,
            'projects': project_patterns,
            'clients': client_patterns
        }

        # Trova colonne corrispondenti
        column_mapping = {}
        for entity_type, patterns in pattern_map.items():
            for col in columns:
                col_lower = col.lower()
                if any(pattern in col_lower for pattern in patterns):
                    if entity_type not in column_mapping:
                        column_mapping[entity_type] = []
                    column_mapping[entity_type].append(col)

        # Estrai valori unici
        for entity_type, cols in column_mapping.items():
            for col in cols:
                for row in data[:50]:  # Limita a prime 50 righe
                    value = row.get(col, '')
                    if value and isinstance(value, str) and len(value.strip()) > 0:
                        clean_value = value.strip()
                        if len(clean_value) > 1 and len(clean_value) < 100:  # Filtri di base
                            entities[entity_type].add(clean_value)

        # Converti set in liste limitate
        return {k: list(v)[:10] for k, v in entities.items()}  # Max 10 per tipo

    def _generate_file_specific_analysis(self, business_type: str, entities: Dict[str, List[str]],
                                       columns: List[str], preview_data: List[Dict[str, Any]],
                                       file_name: str) -> List[Dict[str, Any]]:
        """Genera analisi specifiche basate sul tipo di file e contenuto."""
        analyses = []

        # Analisi per file di permessi
        if 'permessi' in file_name.lower() or 'richieste' in file_name.lower():
            analyses.extend([
                {
                    'title': 'Analisi Dipendenti',
                    'description': f'Rilevati {len(entities.get("employees", []))} dipendenti unici nel file permessi',
                    'type': 'employees',
                    'priority': 'high',
                    'action': 'Verifica e sincronizza con database dipendenti'
                },
                {
                    'title': 'Gestione Permessi',
                    'description': f'Analisi di {len(preview_data)} richieste di permesso',
                    'type': 'permissions',
                    'priority': 'high',
                    'action': 'Configura workflow approvazione automatica'
                },
                {
                    'title': 'Controllo Coerenza',
                    'description': 'Verifica date e sovrapposizioni nei permessi',
                    'type': 'validation',
                    'priority': 'medium',
                    'action': 'Attiva controlli automatici per conflitti'
                }
            ])

        # Analisi per file timbrature
        elif any(keyword in file_name.lower() for keyword in ['timbrature', 'presenze', 'orari']):
            analyses.extend([
                {
                    'title': 'Analisi Presenze',
                    'description': f'Rilevati {len(entities.get("employees", []))} dipendenti con timbrature',
                    'type': 'attendance',
                    'priority': 'high',
                    'action': 'Configura dashboard presenze in tempo reale'
                },
                {
                    'title': 'Controllo Orari',
                    'description': 'Verifica conformità orari di lavoro',
                    'type': 'compliance',
                    'priority': 'medium',
                    'action': 'Imposta alert per anomalie orarie'
                }
            ])

        # Analisi per file TeamViewer
        elif 'teamviewer' in file_name.lower():
            analyses.extend([
                {
                    'title': 'Sessioni Remote',
                    'description': f'Analisi di {len(preview_data)} sessioni TeamViewer',
                    'type': 'remote_sessions',
                    'priority': 'high',
                    'action': 'Configura monitoraggio sessioni automatico'
                },
                {
                    'title': 'Performance Tecnici',
                    'description': f'Rilevati {len(entities.get("employees", []))} tecnici attivi',
                    'type': 'performance',
                    'priority': 'medium',
                    'action': 'Attiva dashboard performance tecnici'
                }
            ])

        # Analisi generiche se non riconosciuto
        else:
            analyses.extend([
                {
                    'title': 'Analisi Dati',
                    'description': f'File con {len(columns)} colonne e {len(preview_data)} righe',
                    'type': 'data_overview',
                    'priority': 'medium',
                    'action': 'Configura dashboard personalizzata'
                },
                {
                    'title': 'Qualità Dati',
                    'description': 'Verifica completezza e coerenza dati',
                    'type': 'data_quality',
                    'priority': 'medium',
                    'action': 'Attiva controlli qualità automatici'
                }
            ])

        # Aggiungi sempre analisi entità se presenti
        if entities.get('employees'):
            analyses.append({
                'title': 'Database Dipendenti',
                'description': f'Sincronizzazione di {len(entities["employees"])} dipendenti con Supabase',
                'type': 'sync',
                'priority': 'high',
                'action': 'Aggiorna tabella dipendenti e permessi'
            })

        return analyses

    def _generate_suggested_configuration(self, business_type: str, entities: Dict[str, List[str]]) -> Dict[str, Any]:
        """Genera configurazione suggerita basata sul tipo business e entità rilevate."""
        base_config = self.business_templates.get(business_type, self.business_templates['generic']).copy()

        # Personalizza configurazione basata su entità rilevate
        if entities['employees']:
            base_config['team_size'] = len(entities['employees'])
            base_config['suggested_modules'].append('team_management')

        if entities['projects']:
            base_config['project_count'] = len(entities['projects'])
            base_config['suggested_modules'].append('project_tracking')

        if entities['clients']:
            base_config['client_count'] = len(entities['clients'])
            base_config['suggested_modules'].append('client_management')

        return base_config

    def _generate_onboarding_steps(self, business_type: str, entities: Dict[str, List[str]]) -> List[Dict[str, Any]]:
        """Genera passi di onboarding personalizzati."""
        steps = [
            {
                'step': 1,
                'title': 'Configurazione Base',
                'description': 'Configura le impostazioni fondamentali del sistema',
                'tasks': ['Verifica connessione database', 'Imposta preferenze utente'],
                'estimated_time': '5 minuti'
            }
        ]

        if entities['employees']:
            steps.append({
                'step': 2,
                'title': 'Gestione Team',
                'description': f'Configura il team di {len(entities["employees"])} dipendenti rilevati',
                'tasks': ['Verifica dati dipendenti', 'Assegna ruoli e permessi'],
                'estimated_time': '10 minuti'
            })

        if entities['projects']:
            steps.append({
                'step': 3,
                'title': 'Progetti e Attività',
                'description': f'Configura {len(entities["projects"])} progetti rilevati',
                'tasks': ['Verifica progetti attivi', 'Configura tracking attività'],
                'estimated_time': '15 minuti'
            })

        steps.append({
            'step': len(steps) + 1,
            'title': 'Automazioni e Report',
            'description': 'Configura automazioni e report personalizzati',
            'tasks': ['Seleziona automazioni', 'Configura dashboard'],
            'estimated_time': '10 minuti'
        })

        return steps

    def _generate_basic_onboarding_steps(self) -> List[Dict[str, Any]]:
        """Genera passi di onboarding base per fallback."""
        return [
            {
                'step': 1,
                'title': 'Configurazione Iniziale',
                'description': 'Imposta le configurazioni base del sistema',
                'tasks': ['Connessione database', 'Preferenze utente'],
                'estimated_time': '5 minuti'
            },
            {
                'step': 2,
                'title': 'Caricamento Dati',
                'description': 'Carica e configura i tuoi dati',
                'tasks': ['Upload file', 'Verifica dati'],
                'estimated_time': '10 minuti'
            },
            {
                'step': 3,
                'title': 'Finalizzazione',
                'description': 'Completa la configurazione',
                'tasks': ['Test sistema', 'Attivazione'],
                'estimated_time': '5 minuti'
            }
        ]

    def save_onboarding_progress(self, user_id: str, progress_data: Dict[str, Any]) -> bool:
        """Salva il progresso dell'onboarding in Supabase."""
        try:
            if not self.supabase_manager or not self.supabase_manager.is_connected:
                logger.warning("Supabase non disponibile per salvare progresso onboarding")
                return False

            # Sanitizza i dati per la serializzazione JSON
            sanitized_progress_data = self._sanitize_for_json(progress_data)

            # Prepara dati per Supabase
            onboarding_record = {
                'user_id': user_id,
                'progress_data': sanitized_progress_data,
                'current_step': int(progress_data.get('current_step', 1)),
                'completed_steps': list(progress_data.get('completed_steps', [])),
                'business_type': str(progress_data.get('detected_business_type', 'generic')),
                'confidence_score': float(progress_data.get('confidence_score', 0.0)),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }

            # Salva in Supabase (tabella onboarding_progress)
            try:
                result = self.supabase_manager.client.table("onboarding_progress").upsert(onboarding_record).execute()

                if result.data:
                    logger.info(f"Progresso onboarding salvato per utente {user_id}")
                    return True
                else:
                    logger.error("Errore nel salvare progresso onboarding")
                    # Fallback se il salvataggio principale fallisce
                    return self._save_progress_fallback(user_id, progress_data)

            except Exception as upsert_error:
                logger.error(f"Errore upsert onboarding_progress: {str(upsert_error)}")
                # Se la tabella non esiste o c'è un errore, usa il fallback
                return self._save_progress_fallback(user_id, progress_data)

        except Exception as e:
            logger.error(f"Errore salvataggio progresso onboarding: {str(e)}")
            # Fallback: salva in user_sessions
            return self._save_progress_fallback(user_id, progress_data)

    def _ensure_onboarding_table_exists(self):
        """Assicura che la tabella onboarding_progress esista."""
        # Nota: Supabase Python client non supporta DDL diretto
        # Questo metodo è un placeholder per future implementazioni
        # La tabella deve essere creata manualmente nel dashboard Supabase
        pass

    def _save_progress_fallback(self, user_id: str, progress_data: Dict[str, Any]) -> bool:
        """Salva il progresso in user_sessions come fallback."""
        try:
            # Sanitizza i dati per la serializzazione JSON
            sanitized_progress_data = self._sanitize_for_json(progress_data)

            # Usa la tabella user_sessions esistente come fallback
            session_id = f"onboarding_{user_id}"
            session_record = {
                'session_id': session_id,
                'user_data': {
                    'type': 'onboarding_progress',
                    'progress_data': sanitized_progress_data,
                    'timestamp': datetime.now().isoformat()
                },
                'last_activity': datetime.now().isoformat()
            }

            # Prima prova a fare UPDATE se esiste già
            try:
                # Controlla se il record esiste già
                existing = self.supabase_manager.client.table("user_sessions").select("session_id").eq("session_id", session_id).execute()

                if existing.data:
                    # Record esiste, fai UPDATE
                    result = self.supabase_manager.client.table("user_sessions").update({
                        'user_data': session_record['user_data'],
                        'last_activity': session_record['last_activity']
                    }).eq("session_id", session_id).execute()
                else:
                    # Record non esiste, fai INSERT
                    result = self.supabase_manager.client.table("user_sessions").insert(session_record).execute()

            except Exception as upsert_error:
                # Se l'upsert fallisce, prova con un timestamp unico
                unique_session_id = f"onboarding_{user_id}_{int(datetime.now().timestamp())}"
                session_record['session_id'] = unique_session_id
                result = self.supabase_manager.client.table("user_sessions").insert(session_record).execute()

            if result.data:
                logger.info(f"Progresso onboarding salvato in user_sessions per utente {user_id}")
                return True
            else:
                logger.warning("Fallback salvataggio onboarding fallito")
                return False

        except Exception as e:
            logger.error(f"Errore fallback salvataggio onboarding: {str(e)}")
            return False

    def _sanitize_for_json(self, data: Any) -> Any:
        """Sanitizza i dati per la serializzazione JSON rimuovendo oggetti non serializzabili."""
        import json

        if data is None:
            return None
        elif isinstance(data, (str, int, float, bool)):
            return data
        elif isinstance(data, dict):
            sanitized = {}
            for key, value in data.items():
                try:
                    # Prova a serializzare il valore
                    json.dumps(value)
                    sanitized[str(key)] = self._sanitize_for_json(value)
                except (TypeError, ValueError):
                    # Se non serializzabile, converti in stringa o salta
                    if hasattr(value, '__dict__'):
                        sanitized[str(key)] = str(value)
                    elif callable(value):
                        # Salta le funzioni
                        continue
                    else:
                        sanitized[str(key)] = str(value)
            return sanitized
        elif isinstance(data, (list, tuple)):
            sanitized = []
            for item in data:
                try:
                    json.dumps(item)
                    sanitized.append(self._sanitize_for_json(item))
                except (TypeError, ValueError):
                    if hasattr(item, '__dict__'):
                        sanitized.append(str(item))
                    elif not callable(item):
                        sanitized.append(str(item))
            return sanitized
        else:
            # Per altri tipi, prova a convertire in stringa
            try:
                json.dumps(data)
                return data
            except (TypeError, ValueError):
                return str(data)

    def get_onboarding_recommendations(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Genera raccomandazioni personalizzate basate sull'analisi."""
        business_type = analysis_results.get('detected_business_type', 'generic')
        entities = analysis_results.get('detected_entities', {})

        recommendations = {
            'priority_actions': [],
            'suggested_integrations': [],
            'optimization_tips': [],
            'next_steps': []
        }

        # Azioni prioritarie basate su tipo business
        if business_type == 'it_services':
            recommendations['priority_actions'] = [
                'Configura monitoraggio sessioni TeamViewer',
                'Imposta alert per tempi di risposta',
                'Attiva backup automatico dati clienti'
            ]
        elif business_type == 'consulting':
            recommendations['priority_actions'] = [
                'Configura tracking ore billabili',
                'Imposta reminder timesheet',
                'Attiva generazione fatture automatica'
            ]
        else:
            recommendations['priority_actions'] = [
                'Configura dashboard principale',
                'Imposta backup dati automatico',
                'Attiva report settimanali'
            ]

        # Suggerimenti di ottimizzazione
        if len(entities.get('employees', [])) > 10:
            recommendations['optimization_tips'].append(
                'Team numeroso rilevato: considera l\'attivazione di dashboard per manager'
            )

        if len(entities.get('projects', [])) > 5:
            recommendations['optimization_tips'].append(
                'Molti progetti rilevati: attiva tracking automatico milestone'
            )

        return recommendations
