# Utilizzo di Docker Compose per app-roberto

Questo documento spiega come utilizzare Docker Compose per avviare l'applicazione app-roberto con tutti i suoi componenti.

## Prerequisiti

- Docker installato sul sistema
- Docker Compose installato sul sistema

## Componenti

L'applicazione è composta da due servizi principali:

1. **app** - L'applicazione principale Flask che gestisce l'interfaccia utente e l'elaborazione di base
2. **mcp_server** - Il server MCP (MODEL CONTEXT PROTOCOL) che fornisce funzionalità avanzate di analisi e elaborazione dati

## Avvio dell'applicazione

Per avviare l'applicazione completa, eseguire il seguente comando dalla directory principale del progetto:

```bash
docker-compose up
```

Per eseguire i container in background (modalità detached):

```bash
docker-compose up -d
```

## Accesso all'applicazione

Una volta avviati i container, puoi accedere all'applicazione ai seguenti indirizzi:

- **Applicazione principale**: <http://localhost:5001> (porta aggiornata per evitare conflitti)
- **Server MCP API**: <http://localhost:8000>

**Nota**: L'applicazione principale ora utilizza la porta **5001** come configurazione ottimizzata per Windows.

## Arresto dell'applicazione

Per arrestare l'applicazione:

```bash
docker-compose down
```

## Ricostruzione dei container

Se hai apportato modifiche al codice e vuoi ricostruire i container:

```bash
docker-compose build
```

Oppure per ricostruire e avviare in un unico comando:

```bash
docker-compose up --build
```

## Volumi condivisi

I seguenti volumi sono condivisi tra i container e il sistema host:

- La directory principale del progetto è montata in `/app` nei container
- La directory `uploads` è condivisa tra i container per lo scambio di file

## Risoluzione dei problemi

Se riscontri problemi con i container:

1. Verifica i log dei container:

   ```bash
   docker-compose logs
   ```

2. Verifica lo stato dei container:

   ```bash
   docker-compose ps
   ```

3. Riavvia i container:

   ```bash
   docker-compose restart
   ```
