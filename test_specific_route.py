#!/usr/bin/env python3
"""
Test specifico per la route /api/get-processed-data
"""

import requests
import json

def test_specific_route():
    """Test specifico per /api/get-processed-data"""
    
    print("🔍 TEST SPECIFICO: /api/get-processed-data")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test 1: Verifica connettività
    print("1️⃣ Test connettività...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   Homepage: {response.status_code} ({'✅' if response.status_code == 200 else '❌'})")
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
        return
    
    # Test 2: Test metodi HTTP per /api/get-processed-data
    print("\n2️⃣ Test metodi HTTP per /api/get-processed-data...")
    
    methods_to_test = [
        ("GET", {}),
        ("POST", {"filename": "test.csv"}),
        ("PUT", {"filename": "test.csv"}),
        ("DELETE", {}),
    ]
    
    for method, data in methods_to_test:
        try:
            print(f"   Test {method}...")
            
            if method == "GET":
                response = requests.get(f"{base_url}/api/get-processed-data", timeout=5)
            elif method == "POST":
                response = requests.post(
                    f"{base_url}/api/get-processed-data",
                    json=data,
                    headers={"Content-Type": "application/json"},
                    timeout=5
                )
            elif method == "PUT":
                response = requests.put(
                    f"{base_url}/api/get-processed-data",
                    json=data,
                    headers={"Content-Type": "application/json"},
                    timeout=5
                )
            elif method == "DELETE":
                response = requests.delete(f"{base_url}/api/get-processed-data", timeout=5)
            
            print(f"      Status: {response.status_code}")
            
            if response.status_code == 404:
                print("      ❌ Route non trovata!")
            elif response.status_code == 405:
                print("      ⚠️ Metodo non consentito (normale)")
            elif response.status_code in [200, 400, 500]:
                print("      ✅ Route registrata")
                try:
                    if response.headers.get('content-type', '').startswith('application/json'):
                        data = response.json()
                        print(f"      📄 Risposta: {data.get('success', 'N/A')}")
                except:
                    print("      📄 Risposta non JSON")
            else:
                print(f"      ⚠️ Status inaspettato: {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ Errore: {str(e)}")
    
    # Test 3: Test route simili
    print("\n3️⃣ Test route simili...")
    
    similar_routes = [
        "/api/processed_data",
        "/api/data",
        "/api/get-data",
        "/api/process-data",
    ]
    
    for route in similar_routes:
        try:
            response = requests.get(f"{base_url}{route}", timeout=5)
            status = "✅" if response.status_code == 200 else "❌" if response.status_code == 404 else "⚠️"
            print(f"   GET {route}: {response.status_code} {status}")
        except Exception as e:
            print(f"   GET {route}: ERROR - {str(e)}")
    
    # Test 4: Lista tutte le route API
    print("\n4️⃣ Test lista route API...")
    try:
        response = requests.get(f"{base_url}/api/endpoints", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and 'data' in data:
                endpoints = data['data']
                print(f"   ✅ Trovati {len(endpoints)} endpoint registrati")
                
                # Cerca endpoint che contengono "processed"
                processed_endpoints = [ep for ep in endpoints if 'processed' in ep.get('path', '').lower()]
                if processed_endpoints:
                    print("   📋 Endpoint con 'processed':")
                    for ep in processed_endpoints:
                        print(f"      - {ep.get('method', 'N/A')} {ep.get('path', 'N/A')}")
                else:
                    print("   ❌ Nessun endpoint con 'processed' trovato!")
            else:
                print(f"   ⚠️ Risposta non valida: {data}")
        else:
            print(f"   ❌ Errore: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    print("\n" + "=" * 60)
    print("📊 RISULTATI")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    test_specific_route()
