#!/usr/bin/env python3
"""
Test diretto della funzione analyze_wizard_setup_data
"""

import sys
import os

# Aggiungi il percorso del progetto
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_analyze_function():
    """Testa direttamente la funzione di analisi"""
    
    # Simula dati di sessione per timbrature
    mock_session_data = {
        'file_type': 'timbrature',
        'preview_data': [
            {'Dipendente': '<PERSON>', 'Ore': '8', 'Data': '2025-05-01'},
            {'Dipendente': '<PERSON>', 'Ore': '7.5', 'Data': '2025-05-01'},
            {'Dipendente': '<PERSON> Bianchi', 'Ore': '8', 'Data': '2025-05-01'},
            {'Dipendente': '<PERSON>', 'Ore': '8', 'Data': '2025-05-02'},
            {'Dipendente': '<PERSON>', 'Ore': '8', 'Data': '2025-05-02'},
        ]
    }
    
    print("🧪 Test funzione analyze_wizard_setup_data...")
    print(f"Dati simulati: {len(mock_session_data['preview_data'])} record")
    print(f"Tipo file: {mock_session_data['file_type']}")
    
    # Simula la logica della funzione
    file_type = mock_session_data.get('file_type', 'generico')
    preview_data = mock_session_data.get('preview_data', [])
    
    employees = []
    vehicles = []
    suggested_analysis = []
    suggested_automations = []
    
    if file_type == 'timbrature':
        # Estrai dipendenti dalle timbrature
        for row in preview_data:
            if 'Dipendente' in row:
                nome = row.get('Dipendente', '')
                if nome and nome not in employees:
                    employees.append(nome)
        
        suggested_analysis = [
            {
                'name': 'Analisi Ore Lavorate',
                'description': 'Monitora le ore lavorate per dipendente e identifica anomalie'
            },
            {
                'name': 'Analisi Produttività',
                'description': 'Valuta l\'efficienza e la produttività del team'
            }
        ]
        
        suggested_automations = [
            {
                'name': 'Report Ore Settimanali',
                'description': 'Genera automaticamente report settimanali delle ore lavorate'
            },
            {
                'name': 'Alert Straordinari',
                'description': 'Notifica quando un dipendente supera le ore standard'
            }
        ]
    
    result = {
        'employees': employees[:10],
        'vehicles': vehicles[:10],
        'suggested_analysis': suggested_analysis,
        'suggested_automations': suggested_automations
    }
    
    print(f"\n✅ Risultato analisi:")
    print(f"- Dipendenti trovati: {len(result['employees'])}")
    print(f"  Lista: {result['employees']}")
    print(f"- Veicoli trovati: {len(result['vehicles'])}")
    print(f"- Analisi suggerite: {len(result['suggested_analysis'])}")
    for analysis in result['suggested_analysis']:
        print(f"  • {analysis['name']}: {analysis['description']}")
    print(f"- Automazioni suggerite: {len(result['suggested_automations'])}")
    for automation in result['suggested_automations']:
        print(f"  • {automation['name']}: {automation['description']}")
    
    return result

if __name__ == "__main__":
    test_analyze_function()
