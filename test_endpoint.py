#!/usr/bin/env python3
"""
Script di test per verificare l'endpoint dei modelli LLM
"""

import requests
import json

def test_llm_models_endpoint():
    """Testa l'endpoint dei modelli LLM"""
    
    print("🔧 Testando l'endpoint dei modelli LLM...")
    
    try:
        # Test endpoint con tutti i modelli
        response = requests.get(
            'http://127.0.0.1:5000/api/llm/models',
            params={
                'include_free': 'true',
                'include_paid': 'true',
                'only_free_quota': 'false'
            },
            timeout=30
        )
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📋 Content-Type: {response.headers.get('Content-Type', 'N/A')}")
        
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            
            print(f"🎯 Numero totale di modelli: {len(models)}")
            
            if models:
                print("\n📝 Primi 10 modelli:")
                for i, model in enumerate(models[:10]):
                    model_id = model.get('id', 'N/A')
                    model_name = model.get('name', 'N/A')
                    is_free = model.get('is_free', False)
                    has_free_quota = model.get('has_free_quota', False)
                    
                    free_indicator = "🆓" if is_free else ("🎁" if has_free_quota else "💰")
                    
                    print(f"  {i+1:2d}. {free_indicator} {model_id} - {model_name}")
                
                # Conta i modelli per categoria
                free_models = [m for m in models if m.get('is_free', False)]
                quota_models = [m for m in models if m.get('has_free_quota', False)]
                
                print(f"\n📈 Statistiche:")
                print(f"  🆓 Modelli completamente gratuiti: {len(free_models)}")
                print(f"  🎁 Modelli con quote gratuite: {len(quota_models)}")
                print(f"  📊 Modelli totali: {len(models)}")
                
                # Test endpoint solo modelli gratuiti
                print("\n🆓 Testando endpoint solo modelli gratuiti...")
                response_free = requests.get(
                    'http://127.0.0.1:5000/api/llm/models',
                    params={
                        'include_free': 'true',
                        'include_paid': 'false',
                        'only_free_quota': 'true'
                    },
                    timeout=30
                )
                
                if response_free.status_code == 200:
                    data_free = response_free.json()
                    models_free = data_free.get('models', [])
                    print(f"  🆓 Modelli solo gratuiti: {len(models_free)}")
                else:
                    print(f"  ❌ Errore endpoint gratuiti: {response_free.status_code}")
                
            else:
                print("❌ Nessun modello trovato!")
        else:
            print(f"❌ Errore: {response.status_code}")
            print(f"📄 Risposta: {response.text[:500]}...")
            
    except Exception as e:
        print(f"❌ Errore nella richiesta: {e}")

if __name__ == "__main__":
    test_llm_models_endpoint()
