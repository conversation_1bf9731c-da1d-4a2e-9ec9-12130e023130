#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test dell'IntelligentEntityExtractor per app-roberto
"""

import pandas as pd
import sys

# Aggiungi il percorso corrente per gli import
sys.path.append('.')

try:
    from intelligent_entity_extractor import IntelligentEntityExtractor
except ImportError as e:
    print(f"❌ Errore nell'importare IntelligentEntityExtractor: {e}")
    sys.exit(1)

def create_test_data():
    """Crea dati di test ricchi di entità."""
    
    # Dati di test per attività con molte entità
    activity_data = pd.DataFrame({
        'ID Ticket': ['TK-12345', 'TK-12346', 'TK-12347', 'TK-12348'],
        'Descrizione Attività': [
            'Installazione software per cliente ABC',
            'Riparazione PC ufficio Milano',
            'Configurazione rete Sala Server',
            'Backup dati Studio Legale XYZ'
        ],
        '<PERSON><PERSON><PERSON><PERSON> As<PERSON>': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
        '<PERSON>lient<PERSON>': ['Azienda ABC S.r.l.', 'Studio XYZ', 'Ufficio Milano', 'Studio Legale XYZ'],
        'Data Inizio': ['15/01/2024', '16/01/2024', '17/01/2024', '18/01/2024'],
        'Data Fine': ['15/01/2024', '16/01/2024', '17/01/2024', '18/01/2024'],
        'Durata Ore': ['2:30', '1:45', '3:15', '4:00'],
        'Progetto': ['PRJ-001', 'PRJ-002', 'PRJ-001', 'PRJ-003'],
        'Email Tecnico': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
    })
    
    # Dati di test per timbrature
    timesheet_data = pd.DataFrame({
        'Codice Dipendente': ['MR001', 'LV002', 'AB003', 'MN004'],
        'Nome Dipendente': ['Mario Rossi', 'Luigi Verdi', 'Anna Bianchi', 'Marco Neri'],
        'Data': ['20/01/2024', '20/01/2024', '20/01/2024', '20/01/2024'],
        'Ora Entrata': ['08:30', '09:00', '08:45', '08:15'],
        'Ora Uscita': ['17:30', '18:00', '17:45', '17:00'],
        'Pausa Pranzo': ['12:30-13:30', '13:00-14:00', '12:45-13:45', '12:00-13:00'],
        'Ore Lavorate': ['8:00', '8:00', '8:00', '8:00'],
        'Sede': ['Ufficio Milano', 'Ufficio Roma', 'Ufficio Milano', 'Ufficio Torino']
    })
    
    # Dati di test per registro auto
    vehicle_data = pd.DataFrame({
        'Targa Veicolo': ['AB123CD', 'EF456GH', 'IJ789KL'],
        'Conducente': ['Mario Rossi', 'Luigi Verdi', 'Anna Bianchi'],
        'Data Viaggio': ['30/01/2024', '30/01/2024', '31/01/2024'],
        'Km Iniziali': ['15000 km', '22000 km', '8500 km'],
        'Km Finali': ['15080 km', '22150 km', '8620 km'],
        'Destinazione': ['Cliente ABC - Milano', 'Fornitore XYZ - Roma', 'Ufficio Postale - Torino'],
        'Costo Carburante': ['€ 25.50', '€ 35.00', '€ 18.75']
    })
    
    return {
        'attivita': activity_data,
        'timbrature': timesheet_data,
        'registro_auto': vehicle_data
    }

def test_intelligent_entity_extractor():
    """Test completo dell'IntelligentEntityExtractor."""
    print('🧪 Test IntelligentEntityExtractor')
    print('=' * 50)
    
    # Inizializza l'estrattore
    try:
        extractor = IntelligentEntityExtractor()
        print(f"✅ IntelligentEntityExtractor inizializzato")
    except Exception as e:
        print(f"❌ Errore nell'inizializzazione: {e}")
        return False
    
    # Crea dati di test
    test_datasets = create_test_data()
    
    results = {}
    
    for file_type, df in test_datasets.items():
        print(f'\n📊 Test per tipo: {file_type}')
        print(f'   Righe: {len(df)}, Colonne: {len(df.columns)}')
        
        try:
            # Esegui estrazione entità
            extraction_result = extractor.extract_entities(df, file_type)
            
            # Mostra risultati principali
            entities = extraction_result.get('entities', {})
            entity_stats = extraction_result.get('entity_statistics', {})
            confidence_scores = extraction_result.get('confidence_scores', {})
            
            print(f'   🔍 Tipi di entità trovati: {len(entities)}')
            
            # Dettagli per ogni tipo di entità
            for entity_type, entity_list in entities.items():
                count = len(entity_list)
                confidence = confidence_scores.get(entity_type, 0.0)
                print(f'      {entity_type}: {count} entità (confidenza: {confidence:.3f})')
                
                # Mostra alcune entità di esempio
                if entity_list:
                    examples = [e['value'] for e in entity_list[:3]]
                    print(f'         Esempi: {", ".join(examples)}')
            
            # Mostra mapping colonne
            column_mapping = extraction_result.get('column_mapping', {})
            if column_mapping:
                print(f'   📋 Mapping colonne:')
                for col, entity_types in column_mapping.items():
                    print(f'      {col} → {", ".join(entity_types)}')
            
            # Mostra raccomandazioni
            recommendations = extraction_result.get('recommendations', [])
            if recommendations:
                print(f'   💡 Raccomandazioni: {len(recommendations)}')
                for rec in recommendations[:2]:  # Mostra solo le prime 2
                    print(f'      - {rec}')
            
            results[file_type] = {
                'total_entities': sum(entity_stats.values()),
                'entity_types': len(entities),
                'avg_confidence': sum(confidence_scores.values()) / len(confidence_scores) if confidence_scores else 0.0,
                'entities': entities
            }
            
        except Exception as e:
            print(f'   ❌ Errore nell\'estrazione: {str(e)}')
            results[file_type] = {'error': str(e)}
    
    # Test estrazione entità master
    print(f'\n🏢 Test Estrazione Entità Master')
    print('-' * 30)
    
    try:
        # Combina tutti i dataset per test master
        all_data = pd.concat(test_datasets.values(), ignore_index=True)
        master_entities = extractor.extract_master_entities(all_data)
        
        for entity_type, entity_set in master_entities.items():
            print(f'   {entity_type}: {len(entity_set)} entità')
            if entity_set:
                examples = list(entity_set)[:3]
                print(f'      Esempi: {", ".join(examples)}')
        
    except Exception as e:
        print(f'   ❌ Errore nell\'estrazione master: {str(e)}')
    
    # Statistiche finali
    print(f'\n📈 STATISTICHE FINALI')
    print('=' * 30)
    
    total_entities = sum(r.get('total_entities', 0) for r in results.values() if 'total_entities' in r)
    total_types = sum(r.get('entity_types', 0) for r in results.values() if 'entity_types' in r)
    
    print(f'Entità totali estratte: {total_entities}')
    print(f'Tipi di entità trovati: {total_types}')
    
    # Confidenza media
    confidences = [r.get('avg_confidence', 0.0) for r in results.values() if 'avg_confidence' in r]
    avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0
    print(f'Confidenza media: {avg_confidence:.3f}')
    
    # Dettagli per tipo di file
    print(f'\n📋 DETTAGLI PER TIPO:')
    for file_type, result in results.items():
        if 'error' in result:
            print(f'❌ {file_type}: ERRORE - {result["error"]}')
        else:
            entities = result.get('total_entities', 0)
            types = result.get('entity_types', 0)
            confidence = result.get('avg_confidence', 0.0)
            print(f'✅ {file_type}: {entities} entità, {types} tipi (conf: {confidence:.3f})')
    
    return results

def test_specific_patterns():
    """Test di pattern specifici per verifica accuratezza."""
    print(f'\n🎯 Test Pattern Specifici')
    print('=' * 30)
    
    extractor = IntelligentEntityExtractor()
    
    # Test data con pattern specifici
    pattern_test_data = pd.DataFrame({
        'Ticket': ['TK-12345', 'ABC-9876', '#1234'],
        'Dipendente': ['Mario Rossi', 'MR001', '<EMAIL>'],
        'Cliente': ['Azienda ABC S.r.l.', 'Studio XYZ', '12345678901'],
        'Data': ['15/01/2024', '2024-01-16', '17 gennaio 2024'],
        'Ora': ['14:30', '2:45', '120m'],
        'Targa': ['AB123CD', 'XY999ZZ', 'invalid'],
        'Luogo': ['Sala Riunioni A', 'Ufficio Milano', 'Via Roma 123, MI']
    })
    
    result = extractor.extract_entities(pattern_test_data, "test_patterns")
    
    # Verifica pattern specifici
    entities = result.get('entities', {})
    for entity_type, entity_list in entities.items():
        print(f'{entity_type}: {len(entity_list)} entità trovate')
        for entity in entity_list:
            print(f'   "{entity["value"]}" (conf: {entity["confidence"]:.3f}, col: {entity["source_column"]})')
    
    return result

if __name__ == "__main__":
    try:
        results = test_intelligent_entity_extractor()
        pattern_results = test_specific_patterns()
        print(f"\n🎉 Test completato!")
    except Exception as e:
        print(f"❌ Errore generale nel test: {e}")
        import traceback
        traceback.print_exc()
