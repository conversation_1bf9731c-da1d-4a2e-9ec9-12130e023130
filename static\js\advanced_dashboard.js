/**
 * Script per la gestione della dashboard avanzata
 * Versione: 1.0.0
 */

// Stampa la versione nella console per verificare il caricamento
console.log('advanced_dashboard.js versione 1.0.0 caricato');

// Configurazione globale
const config = {
    dateRange: {
        start: null,
        end: null
    },
    filters: {
        technician: '',
        client: ''
    }
};

// Cache per i dati
const cache = {
    rawData: null,
    filteredData: null,
    stats: null
};

// Riferimenti ai widget
const widgets = {
    kpi: {},
    charts: {},
    tables: {}
};

/**
 * Inizializza la dashboard avanzata
 */
function initAdvancedDashboard() {
    console.log('Inizializzazione dashboard avanzata...');
    
    // Inizializza i filtri
    initFilters();
    
    // Carica i dati
    loadData();
}

/**
 * Inizializza i filtri
 */
function initFilters() {
    // Inizializza il selettore di intervallo date
    const dateRangePicker = $('#date-range');
    if (dateRangePicker.length) {
        dateRangePicker.daterangepicker({
            opens: 'left',
            autoApply: true,
            locale: {
                format: 'DD/MM/YYYY',
                separator: ' - ',
                applyLabel: 'Applica',
                cancelLabel: 'Annulla',
                fromLabel: 'Da',
                toLabel: 'A',
                customRangeLabel: 'Personalizzato',
                weekLabel: 'W',
                daysOfWeek: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve', 'Sa'],
                monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
                firstDay: 1
            },
            ranges: {
                'Oggi': [moment(), moment()],
                'Ieri': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Ultimi 7 giorni': [moment().subtract(6, 'days'), moment()],
                'Ultimi 30 giorni': [moment().subtract(29, 'days'), moment()],
                'Questo mese': [moment().startOf('month'), moment().endOf('month')],
                'Mese scorso': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            }
        });
        
        // Imposta l'intervallo predefinito (ultimi 30 giorni)
        dateRangePicker.data('daterangepicker').setStartDate(moment().subtract(29, 'days'));
        dateRangePicker.data('daterangepicker').setEndDate(moment());
        
        // Aggiorna la configurazione
        config.dateRange.start = moment().subtract(29, 'days').format('YYYY-MM-DD');
        config.dateRange.end = moment().format('YYYY-MM-DD');
        
        // Aggiungi l'event listener
        dateRangePicker.on('apply.daterangepicker', function(ev, picker) {
            config.dateRange.start = picker.startDate.format('YYYY-MM-DD');
            config.dateRange.end = picker.endDate.format('YYYY-MM-DD');
        });
    }
    
    // Aggiungi gli event listeners per i filtri
    const technicianFilter = document.getElementById('technician-filter');
    const clientFilter = document.getElementById('client-filter');
    const applyFiltersBtn = document.getElementById('apply-filters');
    const resetFiltersBtn = document.getElementById('reset-filters');
    
    if (technicianFilter) {
        technicianFilter.addEventListener('change', function() {
            config.filters.technician = this.value;
        });
    }
    
    if (clientFilter) {
        clientFilter.addEventListener('change', function() {
            config.filters.client = this.value;
        });
    }
    
    if (applyFiltersBtn) {
        applyFiltersBtn.addEventListener('click', function() {
            applyFilters();
        });
    }
    
    if (resetFiltersBtn) {
        resetFiltersBtn.addEventListener('click', function() {
            resetFilters();
        });
    }
}

/**
 * Carica i dati
 */
async function loadData() {
    try {
        console.log('Caricamento dati...');
        
        // Carica i dati elaborati
        const processedDataResponse = await fetch('/api/processed_data');
        const processedData = await processedDataResponse.json();
        
        if (processedData.error) {
            console.error('Errore nel caricamento dei dati elaborati:', processedData.error);
            showError(processedData.error);
            return;
        }
        
        // Salva i dati nella cache
        cache.rawData = processedData.data || [];
        cache.filteredData = [...cache.rawData];
        
        // Carica le statistiche
        const statsResponse = await fetch('/api/stats');
        const stats = await statsResponse.json();
        
        if (stats.error) {
            console.error('Errore nel caricamento delle statistiche:', stats.error);
        } else {
            cache.stats = stats;
        }
        
        // Inizializza i widget
        initWidgets();
        
    } catch (error) {
        console.error('Errore nel caricamento dei dati:', error);
        showError('Errore nel caricamento dei dati. Riprova più tardi.');
    }
}

/**
 * Inizializza i widget
 */
function initWidgets() {
    // Inizializza i widget KPI
    initKpiWidgets();
    
    // Inizializza i widget dei grafici
    initChartWidgets();
    
    // Inizializza il widget della tabella
    initTableWidget();
}

/**
 * Inizializza i widget KPI
 */
function initKpiWidgets() {
    // KPI 1: Ore Totali
    widgets.kpi.totalHours = new KpiWidget('kpi-total-hours', {
        title: 'Ore Totali',
        value: cache.stats?.total_hours || 0,
        label: 'Ore di attività registrate',
        icon: 'fas fa-clock',
        color: 'primary',
        suffix: ' ore',
        format: value => value.toFixed(1)
    });
    
    // KPI 2: Durata Media
    widgets.kpi.avgDuration = new KpiWidget('kpi-avg-duration', {
        title: 'Durata Media',
        value: cache.stats?.avg_duration || 0,
        label: 'Durata media delle sessioni',
        icon: 'fas fa-hourglass-half',
        color: 'info',
        suffix: ' ore',
        format: value => value.toFixed(2)
    });
    
    // KPI 3: Tecnici Attivi
    widgets.kpi.technicians = new KpiWidget('kpi-technicians', {
        title: 'Tecnici Attivi',
        value: cache.stats?.unique_technicians || 0,
        label: 'Numero di tecnici coinvolti',
        icon: 'fas fa-user-hard-hat',
        color: 'success'
    });
    
    // KPI 4: Clienti Serviti
    widgets.kpi.clients = new KpiWidget('kpi-clients', {
        title: 'Clienti Serviti',
        value: cache.stats?.unique_clients || 0,
        label: 'Numero di clienti serviti',
        icon: 'fas fa-building',
        color: 'warning'
    });
    
    // KPI 5: Efficienza
    widgets.kpi.efficiency = new KpiWidget('kpi-efficiency', {
        title: 'Efficienza',
        value: cache.stats?.efficiency || 0,
        label: 'Rapporto ore produttive/totali',
        icon: 'fas fa-tachometer-alt',
        color: 'danger',
        suffix: '%',
        format: value => Math.round(value)
    });
    
    // KPI 6: Carico di Lavoro
    widgets.kpi.workload = new KpiWidget('kpi-workload', {
        title: 'Carico di Lavoro',
        value: cache.stats?.total_hours || 0,
        label: 'Percentuale rispetto a 40 ore settimanali',
        icon: 'fas fa-weight-hanging',
        color: 'primary',
        suffix: '%',
        target: 40,
        format: value => Math.round((value / 40) * 100)
    });
}

/**
 * Inizializza i widget dei grafici
 */
function initChartWidgets() {
    // Chart 1: Attività nel Tempo
    widgets.charts.time = new ChartWidget('chart-time', {
        title: 'Attività nel Tempo',
        dataUrl: '/api/chart_data?type=line&x_column=data&y_column=durata&aggregation=sum',
        actions: [
            {
                name: 'refresh',
                title: 'Aggiorna',
                icon: 'fas fa-sync',
                handler: function() {
                    this.refresh();
                }
            }
        ]
    });
    
    // Chart 2: Distribuzione Tecnici
    widgets.charts.technicians = new ChartWidget('chart-technicians', {
        title: 'Distribuzione Tecnici',
        dataUrl: '/api/chart_data?type=pie&x_column=tecnico&aggregation=sum',
        actions: [
            {
                name: 'refresh',
                title: 'Aggiorna',
                icon: 'fas fa-sync',
                handler: function() {
                    this.refresh();
                }
            }
        ]
    });
    
    // Chart 3: Distribuzione Clienti
    widgets.charts.clients = new ChartWidget('chart-clients', {
        title: 'Distribuzione Clienti',
        dataUrl: '/api/chart_data?type=pie&x_column=cliente&aggregation=sum',
        actions: [
            {
                name: 'refresh',
                title: 'Aggiorna',
                icon: 'fas fa-sync',
                handler: function() {
                    this.refresh();
                }
            }
        ]
    });
    
    // Chart 4: Durata Sessioni
    widgets.charts.duration = new ChartWidget('chart-duration', {
        title: 'Durata Sessioni',
        dataUrl: '/api/chart_data?type=histogram&x_column=durata',
        actions: [
            {
                name: 'refresh',
                title: 'Aggiorna',
                icon: 'fas fa-sync',
                handler: function() {
                    this.refresh();
                }
            }
        ]
    });
    
    // Chart 5: Heatmap Attività
    widgets.charts.heatmap = new ChartWidget('chart-heatmap', {
        title: 'Heatmap Attività',
        dataUrl: '/api/chart_data?type=heatmap&x_column=giorno_settimana&y_column=ora_giorno',
        actions: [
            {
                name: 'refresh',
                title: 'Aggiorna',
                icon: 'fas fa-sync',
                handler: function() {
                    this.refresh();
                }
            }
        ]
    });
}

/**
 * Inizializza il widget della tabella
 */
function initTableWidget() {
    // Crea un elemento tabella
    const tableElement = document.createElement('table');
    tableElement.className = 'table table-striped table-hover table-sm';
    
    // Ottieni il widget
    const tableWidget = document.getElementById('table-recent');
    
    // Crea l'header della tabella
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');
    
    // Aggiungi le colonne
    const columns = ['Data', 'Tecnico', 'Cliente', 'Attività', 'Durata'];
    columns.forEach(column => {
        const th = document.createElement('th');
        th.textContent = column;
        headerRow.appendChild(th);
    });
    
    thead.appendChild(headerRow);
    tableElement.appendChild(thead);
    
    // Crea il body della tabella
    const tbody = document.createElement('tbody');
    
    // Aggiungi le righe (massimo 10)
    const maxRows = Math.min(10, cache.filteredData.length);
    for (let i = 0; i < maxRows; i++) {
        const row = document.createElement('tr');
        const item = cache.filteredData[i];
        
        // Aggiungi le celle
        const dataCell = document.createElement('td');
        dataCell.textContent = item.data || '';
        row.appendChild(dataCell);
        
        const tecnicoCell = document.createElement('td');
        tecnicoCell.textContent = item.tecnico || '';
        row.appendChild(tecnicoCell);
        
        const clienteCell = document.createElement('td');
        clienteCell.textContent = item.cliente || '';
        row.appendChild(clienteCell);
        
        const attivitaCell = document.createElement('td');
        attivitaCell.textContent = item.attivita || '';
        row.appendChild(attivitaCell);
        
        const durataCell = document.createElement('td');
        durataCell.textContent = item.durata ? `${item.durata} ore` : '';
        row.appendChild(durataCell);
        
        tbody.appendChild(row);
    }
    
    tableElement.appendChild(tbody);
    
    // Aggiungi la tabella al widget
    if (tableWidget) {
        // Crea la struttura del widget
        const header = document.createElement('div');
        header.className = 'widget-header';
        
        const title = document.createElement('h3');
        title.className = 'widget-title';
        title.textContent = 'Attività Recenti';
        header.appendChild(title);
        
        const body = document.createElement('div');
        body.className = 'widget-body';
        body.appendChild(tableElement);
        
        const footer = document.createElement('div');
        footer.className = 'widget-footer';
        footer.innerHTML = `<a href="${window.location.origin}/raw-data" class="text-decoration-none">Visualizza tutti i dati <i class="fas fa-arrow-right ms-1"></i></a>`;
        
        tableWidget.classList.add('widget', 'widget-xl', 'widget-table');
        tableWidget.appendChild(header);
        tableWidget.appendChild(body);
        tableWidget.appendChild(footer);
    }
}

/**
 * Applica i filtri
 */
function applyFilters() {
    console.log('Applicazione filtri:', config.filters);
    
    // Filtra i dati
    cache.filteredData = cache.rawData.filter(item => {
        // Filtra per data
        if (config.dateRange.start && config.dateRange.end) {
            const itemDate = new Date(item.data);
            const startDate = new Date(config.dateRange.start);
            const endDate = new Date(config.dateRange.end);
            
            if (itemDate < startDate || itemDate > endDate) {
                return false;
            }
        }
        
        // Filtra per tecnico
        if (config.filters.technician && item.tecnico !== config.filters.technician) {
            return false;
        }
        
        // Filtra per cliente
        if (config.filters.client && item.cliente !== config.filters.client) {
            return false;
        }
        
        return true;
    });
    
    // Aggiorna i widget
    updateWidgets();
}

/**
 * Resetta i filtri
 */
function resetFilters() {
    // Resetta i controlli
    const dateRangePicker = $('#date-range');
    if (dateRangePicker.length) {
        dateRangePicker.data('daterangepicker').setStartDate(moment().subtract(29, 'days'));
        dateRangePicker.data('daterangepicker').setEndDate(moment());
    }
    
    const technicianFilter = document.getElementById('technician-filter');
    if (technicianFilter) {
        technicianFilter.value = '';
    }
    
    const clientFilter = document.getElementById('client-filter');
    if (clientFilter) {
        clientFilter.value = '';
    }
    
    // Resetta la configurazione
    config.dateRange.start = moment().subtract(29, 'days').format('YYYY-MM-DD');
    config.dateRange.end = moment().format('YYYY-MM-DD');
    config.filters.technician = '';
    config.filters.client = '';
    
    // Resetta i dati filtrati
    cache.filteredData = [...cache.rawData];
    
    // Aggiorna i widget
    updateWidgets();
}

/**
 * Aggiorna i widget
 */
function updateWidgets() {
    // TODO: Implementare l'aggiornamento dei widget in base ai dati filtrati
}

/**
 * Mostra un errore
 */
function showError(message) {
    // TODO: Implementare la visualizzazione degli errori
}

// Inizializza la dashboard quando il documento è pronto
document.addEventListener('DOMContentLoaded', initAdvancedDashboard);
