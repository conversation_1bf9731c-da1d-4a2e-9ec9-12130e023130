#!/usr/bin/env python3
"""
Server Flask minimo per testare l'endpoint wizard
"""

from flask import Flask, request, jsonify, make_response
from datetime import datetime
import json

app = Flask(__name__)

@app.route('/api/wizard/test', methods=['GET', 'POST', 'OPTIONS'])
def test_wizard():
    """Endpoint di test per verificare la connettività"""
    print("=== TEST WIZARD ENDPOINT ===")
    print(f"🔍 Metodo: {request.method}")
    print(f"🔍 Headers: {dict(request.headers)}")
    print(f"🔍 Content-Type: {request.content_type}")
    
    if request.method == 'OPTIONS':
        response = make_response()
        response.headers.add("Access-Control-Allow-Origin", "*")
        response.headers.add('Access-Control-Allow-Headers', "*")
        response.headers.add('Access-Control-Allow-Methods', "*")
        return response
    
    response_data = {
        'success': True,
        'message': 'Test endpoint funzionante!',
        'method': request.method,
        'timestamp': datetime.now().isoformat()
    }
    
    flask_response = make_response(jsonify(response_data), 200)
    flask_response.headers.add("Access-Control-Allow-Origin", "*")
    return flask_response

@app.route('/api/wizard/complete', methods=['POST', 'OPTIONS'])
def complete_wizard():
    """Endpoint wizard completo"""
    print("=== API WIZARD COMPLETE ===")
    print(f"🔍 Metodo: {request.method}")
    print(f"🔍 Headers: {dict(request.headers)}")
    print(f"🔍 Content-Type: {request.content_type}")
    
    # Gestione CORS per richieste OPTIONS
    if request.method == 'OPTIONS':
        response = make_response()
        response.headers.add("Access-Control-Allow-Origin", "*")
        response.headers.add('Access-Control-Allow-Headers', "*")
        response.headers.add('Access-Control-Allow-Methods', "*")
        return response
    
    try:
        # Ottieni e valida dati
        data = request.get_json()
        if not data:
            print("❌ Dati di configurazione mancanti")
            response_data = {
                'success': False,
                'error': 'Dati di configurazione mancanti'
            }
            flask_response = make_response(jsonify(response_data), 400)
            flask_response.headers.add("Access-Control-Allow-Origin", "*")
            return flask_response

        print(f"✅ Dati ricevuti: {json.dumps(data, indent=2)}")

        # Simula elaborazione
        files = data.get('files', [])
        employees = data.get('employees', [])
        vehicles = data.get('vehicles', [])
        configuration = data.get('configuration', {})

        print(f"📁 File: {len(files)}")
        print(f"👥 Dipendenti: {len(employees)}")
        print(f"🚗 Veicoli: {len(vehicles)}")

        # Risposta di successo
        response_data = {
            'success': True,
            'message': 'Setup completato con successo! Benvenuto in App Roberto.',
            'redirect': '/dashboard',
            'data': {
                'files_processed': len(files),
                'employees_configured': len(employees),
                'vehicles_configured': len(vehicles),
                'completion_time': datetime.now().isoformat()
            }
        }
        
        flask_response = make_response(jsonify(response_data), 200)
        flask_response.headers.add("Access-Control-Allow-Origin", "*")
        return flask_response

    except Exception as e:
        print(f"❌ Errore: {str(e)}")
        response_data = {
            'success': False,
            'error': f'Errore completamento wizard: {str(e)}'
        }
        
        flask_response = make_response(jsonify(response_data), 500)
        flask_response.headers.add("Access-Control-Allow-Origin", "*")
        return flask_response

@app.route('/setup-wizard')
def setup_wizard():
    """Pagina setup wizard semplificata"""
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Setup Wizard</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
            button:hover { background: #0056b3; }
            .result { margin: 20px 0; padding: 15px; border-radius: 5px; }
            .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
            .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        </style>
    </head>
    <body>
        <h1>Test Setup Wizard</h1>
        <button onclick="testEndpoint()">Test Endpoint</button>
        <button onclick="completeSetup()">Complete Setup</button>
        <div id="result"></div>
        
        <script>
            async function testEndpoint() {
                try {
                    const response = await fetch('/api/wizard/test');
                    const result = await response.json();
                    showResult('Test riuscito: ' + JSON.stringify(result, null, 2), 'success');
                } catch (error) {
                    showResult('Errore test: ' + error.message, 'error');
                }
            }
            
            async function completeSetup() {
                try {
                    const setupData = {
                        files: [],
                        configuration: {},
                        employees: [],
                        vehicles: [],
                        analysis: {},
                        automations: {},
                        timestamp: new Date().toISOString()
                    };
                    
                    const response = await fetch('/api/wizard/complete', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(setupData)
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok && result.success) {
                        showResult('Setup completato: ' + JSON.stringify(result, null, 2), 'success');
                    } else {
                        showResult('Errore setup: ' + (result.error || 'Errore sconosciuto'), 'error');
                    }
                } catch (error) {
                    showResult('Errore setup: ' + error.message, 'error');
                }
            }
            
            function showResult(message, type) {
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = '<pre>' + message + '</pre>';
                resultDiv.className = 'result ' + type;
            }
        </script>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("🚀 Avvio server Flask minimo per test...")
    app.run(host='127.0.0.1', port=5000, debug=True)
