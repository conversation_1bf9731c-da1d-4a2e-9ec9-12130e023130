{"fase1": {"file_detection": {"total_files": 4, "successful_detections": 0, "average_confidence": 0.0, "results": [{"file": "apprilevazionepresenze-richieste-2025-05-01-2025-05-31 (3).xlsx", "error": "EnhancedFileDetector.detect_file_type() takes 2 positional arguments but 3 were given", "success": false}, {"file": "apprilevazionepresenze-timbrature-totali-base-2025-05-01-2025-05-31 (2).xlsx", "error": "EnhancedFileDetector.detect_file_type() takes 2 positional arguments but 3 were given", "success": false}, {"file": "export (9).xlsx", "error": "EnhancedFileDetector.detect_file_type() takes 2 positional arguments but 3 were given", "success": false}, {"file": "progetti_230525.xlsx", "error": "EnhancedFileDetector.detect_file_type() takes 2 positional arguments but 3 were given", "success": false}]}, "entity_extraction": {"total_files_tested": 3, "successful_extractions": 0, "total_entities": 0, "results": [{"file": "apprilevazionepresenze-richieste-2025-05-01-2025-05-31 (3).xlsx", "error": "IntelligentEntityExtractor.extract_entities() got an unexpected keyword argument 'file_path'", "success": false}, {"file": "apprilevazionepresenze-timbrature-totali-base-2025-05-01-2025-05-31 (2).xlsx", "error": "IntelligentEntityExtractor.extract_entities() got an unexpected keyword argument 'file_path'", "success": false}, {"file": "export (9).xlsx", "error": "IntelligentEntityExtractor.extract_entities() got an unexpected keyword argument 'file_path'", "success": false}]}, "data_standardization": {}, "performance": {}, "error": "'DataStandardizer' object has no attribute 'standardize_entities'"}, "fase2": {"database_connection": {"client_initialized": true, "connection_successful": true, "url_configured": true, "key_configured": true}, "advanced_manager": {"manager_initialized": true, "supabase_connected": true, "fuzzy_matching_available": false, "batch_processing_available": false}, "entity_processing": {"error": "'AdvancedDatabaseManager' object has no attribute 'save_processed_entities'"}, "performance": {}}, "fase3": {"cross_analysis_engine": {"engine_initialized": true, "database_connected": true, "analysis_methods_available": true}, "analysis_types": {"time_consistency": {"success": true, "processing_time_ms": 192, "discrepancies_found": 0, "records_analyzed": 0}, "activity_remote_correlation": {"success": true, "processing_time_ms": 198, "discrepancies_found": 0, "records_analyzed": 0}, "duplicates_overlaps": {"success": true, "processing_time_ms": 221, "discrepancies_found": 0, "records_analyzed": 0}, "productivity_analysis": {"success": true, "processing_time_ms": 193, "discrepancies_found": 0, "records_analyzed": 0}, "cost_analysis": {"success": true, "processing_time_ms": 165, "discrepancies_found": 0, "records_analyzed": 0}, "data_quality": {"success": true, "processing_time_ms": 233, "discrepancies_found": 0, "records_analyzed": 0}}, "dashboard_integration": {}, "performance": {}, "comprehensive_analysis": {"success": true, "total_processing_time_ms": 1019, "analyses_completed": 7, "total_discrepancies": 0}}, "fase4": {"llm_assistant": {"assistant_initialized": true, "api_key_configured": true, "client_available": true, "models_configured": true, "templates_loaded": true, "health_check": {"api_key_configured": true, "client_initialized": true, "models_available": 5, "templates_loaded": 5, "timestamp": "2025-05-24T02:35:18.461746", "llm_connection": true}, "llm_connection": true}, "intelligent_agents": {"orchestrator_initialized": true, "agents_count": 4, "active_agents": 4, "system_status": {"agents_count": 4, "active_agents": 4, "total_tasks_processed": 0, "total_success": 0, "total_errors": 0, "overall_success_rate": 0, "queue_size": 0, "is_running": false, "last_update": "2025-05-24T02:35:21.466819"}, "overall_success_rate": 0}, "automated_reporting": {}, "system_integration": {}, "error": "No module named 'weasy<PERSON>'"}, "integration": {"file_to_database": {"workflow_available": true, "simulated_success": true, "entities_processed": 25, "processing_time_ms": 450}, "database_to_analysis": {"success": true, "analyses_completed": 7, "processing_time_ms": 1015, "total_discrepancies": 0}, "analysis_to_llm": {"success": false, "processing_time_ms": 716, "confidence": 0.0, "suggestions_count": 1}, "complete_workflow": {"success": true, "total_processing_time_ms": 1022, "components_used": 1, "analyses_completed": 7}}, "performance": {"file_processing": {}, "database_operations": {}, "analysis_performance": {}, "llm_performance": {}, "memory_usage": {}, "error": "EnhancedFileDetector.detect_file_type() takes 2 positional arguments but 3 were given"}, "edge_cases": {"empty_files": {"error": "EnhancedFileDetector.detect_file_type() takes 2 positional arguments but 3 were given"}, "corrupted_data": {"error_handled": true, "error": "IntelligentEntityExtractor.extract_entities() got an unexpected keyword argument 'file_path'"}, "large_datasets": {"error": "'DataStandardizer' object has no attribute 'standardize_entities'"}, "network_failures": {"network_failure_handled": false, "graceful_degradation": true}, "concurrent_operations": {"concurrent_tasks_handled": true, "tasks_submitted": 3}}}