#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test per il modulo auth_routes.py.
"""

import json
import pytest
from flask import Flask

from auth import User, generate_token
from auth_routes import auth_bp

class TestAuthRoutes:
    """Test per le route di autenticazione."""

    def setup_method(self):
        """Setup per i test."""
        # Crea un'app Flask di test
        self.app = Flask(__name__)
        self.app.config['TESTING'] = True

        # Registra il blueprint di autenticazione
        self.app.register_blueprint(auth_bp)

        # Crea un client di test
        self.client = self.app.test_client()

    def test_login_success(self):
        """Test per il login con credenziali corrette."""
        # Effettua una richiesta di login con credenziali corrette
        response = self.client.post(
            '/auth/login',
            data=json.dumps({'username': 'admin', 'password': 'admin'}),
            content_type='application/json'
        )

        # Verifica che la risposta sia un successo
        assert response.status_code == 200

        # Verifica che la risposta contenga un token
        data = json.loads(response.data)
        assert 'token' in data
        assert 'user' in data
        assert data['user']['username'] == 'admin'
        assert data['user']['role'] == 'admin'

    def test_login_invalid_credentials(self):
        """Test per il login con credenziali non valide."""
        # Effettua una richiesta di login con credenziali non valide
        response = self.client.post(
            '/auth/login',
            data=json.dumps({'username': 'admin', 'password': 'wrong_password'}),
            content_type='application/json'
        )

        # Verifica che la risposta sia un errore 401
        assert response.status_code == 401

        # Verifica che la risposta contenga un messaggio di errore
        data = json.loads(response.data)
        assert 'error' in data
        assert data['error'] == 'Credenziali non valide'

    def test_login_missing_data(self):
        """Test per il login con dati mancanti."""
        # Effettua una richiesta di login senza dati ma con content type corretto
        response = self.client.post(
            '/auth/login',
            data=json.dumps({}),
            content_type='application/json'
        )

        # Verifica che la risposta sia un errore 400
        assert response.status_code == 400

        # Verifica che la risposta contenga un messaggio di errore
        data = json.loads(response.data)
        assert 'error' in data
        assert data['error'] == 'Dati mancanti'

    def test_login_missing_credentials(self):
        """Test per il login con credenziali mancanti."""
        # Effettua una richiesta di login senza username
        response = self.client.post(
            '/auth/login',
            data=json.dumps({'password': 'admin'}),
            content_type='application/json'
        )

        # Verifica che la risposta sia un errore 400
        assert response.status_code == 400

        # Verifica che la risposta contenga un messaggio di errore
        data = json.loads(response.data)
        assert 'error' in data
        assert data['error'] == 'Username e password richiesti'

        # Effettua una richiesta di login senza password
        response = self.client.post(
            '/auth/login',
            data=json.dumps({'username': 'admin'}),
            content_type='application/json'
        )

        # Verifica che la risposta sia un errore 400
        assert response.status_code == 400

        # Verifica che la risposta contenga un messaggio di errore
        data = json.loads(response.data)
        assert 'error' in data
        assert data['error'] == 'Username e password richiesti'

    def test_me_authenticated(self):
        """Test per l'endpoint /me con utente autenticato."""
        # Genera un token per un utente
        token = generate_token(2, "user", "user")

        # Effettua una richiesta all'endpoint /me con il token
        response = self.client.get(
            '/auth/me',
            headers={'Authorization': f'Bearer {token}'}
        )

        # Verifica che la risposta sia un successo
        assert response.status_code == 200

        # Verifica che la risposta contenga le informazioni dell'utente
        data = json.loads(response.data)
        assert 'user' in data
        assert data['user']['id'] == 2
        assert data['user']['username'] == 'user'
        assert data['user']['role'] == 'user'

    def test_me_unauthenticated(self):
        """Test per l'endpoint /me senza autenticazione."""
        # Effettua una richiesta all'endpoint /me senza token
        response = self.client.get('/auth/me')

        # Verifica che la risposta sia un errore 401
        assert response.status_code == 401

        # Verifica che la risposta contenga un messaggio di errore
        data = json.loads(response.data)
        assert 'error' in data
        assert data['error'] == 'Token mancante'

    def test_admin_as_admin(self):
        """Test per l'endpoint /admin come amministratore."""
        # Genera un token per un amministratore
        token = generate_token(1, "admin", "admin")

        # Effettua una richiesta all'endpoint /admin con il token
        response = self.client.get(
            '/auth/admin',
            headers={'Authorization': f'Bearer {token}'}
        )

        # Verifica che la risposta sia un successo
        assert response.status_code == 200

        # Verifica che la risposta contenga un messaggio di benvenuto
        data = json.loads(response.data)
        assert 'message' in data
        assert 'Benvenuto, amministratore admin' in data['message']

    def test_admin_as_user(self):
        """Test per l'endpoint /admin come utente normale."""
        # Genera un token per un utente normale
        token = generate_token(2, "user", "user")

        # Effettua una richiesta all'endpoint /admin con il token
        response = self.client.get(
            '/auth/admin',
            headers={'Authorization': f'Bearer {token}'}
        )

        # Verifica che la risposta sia un errore 403
        assert response.status_code == 403

        # Verifica che la risposta contenga un messaggio di errore
        data = json.loads(response.data)
        assert 'error' in data
        assert data['error'] == 'Accesso negato'

    def test_register_as_admin(self):
        """Test per l'endpoint /register come amministratore."""
        # Genera un token per un amministratore
        token = generate_token(1, "admin", "admin")

        # Effettua una richiesta all'endpoint /register con il token
        response = self.client.post(
            '/auth/register',
            data=json.dumps({
                'username': 'new_user',
                'password': 'password',
                'role': 'user'
            }),
            content_type='application/json',
            headers={'Authorization': f'Bearer {token}'}
        )

        # Verifica che la risposta sia un successo
        assert response.status_code == 201

        # Verifica che la risposta contenga le informazioni del nuovo utente
        data = json.loads(response.data)
        assert 'user' in data
        assert data['user']['username'] == 'new_user'
        assert data['user']['role'] == 'user'

    def test_register_as_user(self):
        """Test per l'endpoint /register come utente normale."""
        # Genera un token per un utente normale
        token = generate_token(2, "user", "user")

        # Effettua una richiesta all'endpoint /register con il token
        response = self.client.post(
            '/auth/register',
            data=json.dumps({
                'username': 'new_user',
                'password': 'password',
                'role': 'user'
            }),
            content_type='application/json',
            headers={'Authorization': f'Bearer {token}'}
        )

        # Verifica che la risposta sia un errore 403
        assert response.status_code == 403

        # Verifica che la risposta contenga un messaggio di errore
        data = json.loads(response.data)
        assert 'error' in data
        assert data['error'] == 'Accesso negato'
