<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Gestione Modalità Minimal - App Roberto</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        .status-card {
            border-left: 4px solid #007bff;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .system-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
            transition: all 0.3s ease;
        }

        .system-item:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .system-disabled {
            border-left: 4px solid #dc3545;
            background-color: #fff5f5;
        }

        .system-active {
            border-left: 4px solid #28a745;
            background-color: #f8fff8;
        }

        .action-button {
            margin: 5px;
            min-width: 200px;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .status-badge {
            font-size: 0.9em;
            padding: 6px 12px;
        }

        .impact-text {
            font-size: 0.85em;
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-cogs text-primary"></i>
                        Gestione Modalità Minimal
                    </h1>
                    <a href="/configuration" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Torna alla Configurazione
                    </a>
                </div>
                <p class="text-muted mt-2">
                    Gestisci l'attivazione e disattivazione della modalità minimal per ottimizzare le performance dell'app.
                </p>
            </div>
        </div>

        <!-- Status Panel -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card status-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle"></i>
                            Status Attuale
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="status-loading" class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Caricamento...</span>
                            </div>
                            <p class="mt-2">Caricamento status modalità minimal...</p>
                        </div>

                        <div id="status-content" class="wizard-hidden">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>📊 Informazioni Generali</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>Status:</strong> <span id="overall-status" class="badge"></span></li>
                                        <li><strong>Codice Minimal:</strong> <span id="code-status"></span></li>
                                        <li><strong>Sistemi Disabilitati:</strong> <span id="disabled-count"></span></li>
                                        <li><strong>Ultimo Aggiornamento:</strong> <span id="last-update"></span></li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>🎯 Impatto Performance</h6>
                                    <div id="performance-impact">
                                        <div class="progress mb-2">
                                            <div id="performance-bar" class="progress-bar" role="progressbar" title="Indicatore performance sistema" aria-label="Performance sistema"></div>
                                        </div>
                                        <small id="performance-text" class="text-muted"></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Controls Panel -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-sliders-h"></i>
                            Controlli Modalità Minimal
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="alert alert-warning" role="alert">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Attenzione:</strong> Dopo ogni modifica è necessario riavviare l'app per applicare le modifiche.
                        </div>

                        <div class="btn-group-vertical" role="group">
                            <button type="button" id="btn-enable" onclick="toggleMinimalMode(true)" class="btn btn-warning action-button">
                                <i class="fas fa-power-off"></i>
                                Attiva Modalità Minimal
                            </button>
                            <button type="button" id="btn-disable" onclick="toggleMinimalMode(false)" class="btn btn-success action-button">
                                <i class="fas fa-rocket"></i>
                                Disattiva Modalità Minimal (Performance Complete)
                            </button>
                            <button type="button" id="btn-toggle" onclick="toggleMinimalMode()" class="btn btn-info action-button">
                                <i class="fas fa-sync-alt"></i>
                                Toggle Automatico
                            </button>
                            <button type="button" id="btn-restore" onclick="restoreOriginalCode()" class="btn btn-secondary action-button">
                                <i class="fas fa-undo"></i>
                                Ripristina Codice Originale
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Systems Panel -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list"></i>
                            Sistemi Controllati dalla Modalità Minimal
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="systems-loading" class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Caricamento...</span>
                            </div>
                            <p class="mt-2">Caricamento sistemi...</p>
                        </div>

                        <div id="systems-content" class="wizard-hidden">
                            <div class="row" id="systems-list">
                                <!-- I sistemi verranno caricati dinamicamente qui -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        let currentStatus = null;

        // Carica status all'avvio
        document.addEventListener('DOMContentLoaded', function() {
            loadMinimalStatus();

            // Auto-refresh ogni 30 secondi
            setInterval(loadMinimalStatus, 30000);
        });

        async function loadMinimalStatus() {
            try {
                const response = await fetch('/api/minimal-mode/status');
                const result = await response.json();

                if (result.success) {
                    currentStatus = result.data;
                    updateStatusDisplay(result.data);
                    updateSystemsDisplay(result.data.systems_detail);
                } else {
                    showError('Errore caricamento status: ' + result.error);
                }

            } catch (error) {
                console.error('Errore caricamento status:', error);
                showError('Errore di connessione durante il caricamento dello status');
            }
        }

        function updateStatusDisplay(status) {
            // Nascondi loading e mostra contenuto
            document.getElementById('status-loading').classList.add('wizard-hidden');
            document.getElementById('status-content').classList.remove('wizard-hidden');

            // Aggiorna status generale
            const overallStatus = document.getElementById('overall-status');
            overallStatus.textContent = status.overall_status;
            overallStatus.className = 'badge ' + getStatusBadgeClass(status.overall_status);

            // Aggiorna altri campi
            document.getElementById('code-status').innerHTML = status.code_minimal_active ?
                '<i class="fas fa-check text-success"></i> Attivo' :
                '<i class="fas fa-times text-danger"></i> Inattivo';

            document.getElementById('disabled-count').textContent =
                `${status.disabled_systems_count}/${status.total_systems}`;

            document.getElementById('last-update').textContent =
                new Date(status.timestamp).toLocaleString('it-IT');

            // Aggiorna barra performance
            const performancePercentage = ((status.total_systems - status.disabled_systems_count) / status.total_systems) * 100;
            const performanceBar = document.getElementById('performance-bar');
            performanceBar.style.width = performancePercentage + '%';
            performanceBar.className = 'progress-bar ' + getPerformanceBarClass(performancePercentage);

            document.getElementById('performance-text').textContent =
                `Performance: ${performancePercentage.toFixed(0)}% (${status.total_systems - status.disabled_systems_count} sistemi attivi)`;
        }

        function updateSystemsDisplay(systems) {
            // Nascondi loading e mostra contenuto
            document.getElementById('systems-loading').classList.add('wizard-hidden');
            document.getElementById('systems-content').classList.remove('wizard-hidden');

            const systemsList = document.getElementById('systems-list');
            systemsList.innerHTML = '';

            for (const [var, system] of Object.entries(systems)) {
                const systemDiv = document.createElement('div');
                systemDiv.className = 'col-md-6 col-lg-4 mb-3';

                const statusIcon = system.disabled ? '❌' : '✅';
                const statusClass = system.disabled ? 'system-disabled' : 'system-active';
                const statusText = system.disabled ? 'DISABILITATO' : 'ATTIVO';
                const statusBadge = system.disabled ? 'badge bg-danger' : 'badge bg-success';

                systemDiv.innerHTML = `
                    <div class="system-item ${statusClass}">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="mb-0">${statusIcon} ${system.name}</h6>
                            <span class="${statusBadge}">${statusText}</span>
                        </div>
                        <p class="mb-2">${system.description}</p>
                        <div class="impact-text">
                            <i class="fas fa-info-circle"></i>
                            <strong>Impatto:</strong> ${system.impact}
                        </div>
                    </div>
                `;

                systemsList.appendChild(systemDiv);
            }
        }

        async function toggleMinimalMode(enable = null) {
            // Disabilita tutti i pulsanti durante l'operazione
            setButtonsLoading(true);

            try {
                const response = await fetch('/api/minimal-mode/toggle', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({enable: enable})
                });

                const result = await response.json();

                if (result.success) {
                    const action = result.data.action === 'enabled' ? 'attivata' : 'disattivata';
                    showSuccess(`Modalità minimal ${action} con successo!`,
                              'Riavvia l\'app per applicare le modifiche.');

                    // Ricarica status dopo 2 secondi
                    setTimeout(loadMinimalStatus, 2000);
                } else {
                    showError('Errore durante il toggle: ' + result.error);
                }

            } catch (error) {
                console.error('Errore toggle modalità minimal:', error);
                showError('Errore di connessione durante il toggle della modalità minimal');
            } finally {
                setButtonsLoading(false);
            }
        }

        async function restoreOriginalCode() {
            if (!confirm('Sei sicuro di voler ripristinare il codice originale? Questa operazione rimuoverà tutte le modifiche alla modalità minimal.')) {
                return;
            }

            setButtonsLoading(true);

            try {
                const response = await fetch('/api/minimal-mode/restore', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'}
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess('Codice originale ripristinato con successo!',
                              'Riavvia l\'app per applicare le modifiche.');
                    setTimeout(loadMinimalStatus, 2000);
                } else {
                    showError('Errore durante il ripristino: ' + result.error);
                }

            } catch (error) {
                console.error('Errore ripristino:', error);
                showError('Errore di connessione durante il ripristino');
            } finally {
                setButtonsLoading(false);
            }
        }

        function setButtonsLoading(loading) {
            const buttons = ['btn-enable', 'btn-disable', 'btn-toggle', 'btn-restore'];
            buttons.forEach(id => {
                const btn = document.getElementById(id);
                if (loading) {
                    btn.disabled = true;
                    btn.classList.add('loading');
                } else {
                    btn.disabled = false;
                    btn.classList.remove('loading');
                }
            });
        }

        function getStatusBadgeClass(status) {
            switch (status) {
                case 'FULL_MINIMAL': return 'bg-warning';
                case 'PARTIAL_MINIMAL': return 'bg-info';
                case 'FULL_PERFORMANCE': return 'bg-success';
                default: return 'bg-secondary';
            }
        }

        function getPerformanceBarClass(percentage) {
            if (percentage >= 80) return 'bg-success';
            if (percentage >= 60) return 'bg-warning';
            return 'bg-danger';
        }

        function showSuccess(title, message = '') {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.innerHTML = `
                <i class="fas fa-check-circle"></i>
                <strong>${title}</strong>
                ${message ? '<br>' + message : ''}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.row'));

            // Auto-remove dopo 5 secondi
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        function showError(message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-danger alert-dismissible fade show';
            alertDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Errore:</strong> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.querySelector('.container-fluid').insertBefore(alertDiv, document.querySelector('.row'));

            // Auto-remove dopo 8 secondi
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 8000);
        }
    </script>
</body>
</html>
