#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import re
from data_processor import DataProcessor

class AttendanceProcessor:
    """
    Classe specifica per l'elaborazione dei dati di presenze e timbrature.
    Estende le funzionalità di DataProcessor per gestire le specificità dei file di presenze.
    """

    def __init__(self):
        self.data_processor = DataProcessor()

        # Mappatura specifica per i campi di presenze
        self.attendance_fields = {
            'Data': 'data',
            'Dipendente': 'dipendente',
            'Nome': 'nome',
            'Cognome': 'cognome',
            'Entrata': 'entrata',
            'Uscita': 'uscita',
            'Ore Lavorate': 'ore_lavorate',
            'Straordinario': 'straordinario',
            'Permesso': 'permesso',
            'Tipo Permesso': 'tipo_permesso',
            'Note': 'note'
        }

        # Mappatura dei tipi di permesso
        self.permission_types = {
            'Malattia': 'malattia',
            'Ferie': 'ferie',
            'Permesso': 'permesso',
            'ROL': 'rol',
            'Maternità': 'maternita',
            'Altro': 'altro'
        }

    def process_attendance_file(self, file_path):
        """
        Elabora un file di presenze/timbrature e restituisce un DataFrame standardizzato
        """
        # Leggi il file
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path, sep=';', encoding='utf-8-sig')
        elif file_path.endswith(('.xlsx', '.xls')):
            df = pd.read_excel(file_path)
        else:
            raise ValueError("Formato file non supportato")

        # Standardizza i nomi delle colonne
        df = self._standardize_attendance_columns(df)

        # Elabora le date
        df = self._process_attendance_dates(df)

        # Elabora gli orari
        df = self._process_attendance_times(df)

        # Calcola metriche aggiuntive
        df = self._calculate_additional_metrics(df)

        return df

    def _standardize_attendance_columns(self, df):
        """
        Standardizza i nomi delle colonne specifiche di presenze
        """
        renamed_columns = {}

        for col in df.columns:
            if col in self.attendance_fields:
                renamed_columns[col] = self.attendance_fields[col]

        if renamed_columns:
            return df.rename(columns=renamed_columns)

        return df

    def _process_attendance_dates(self, df):
        """
        Elabora le colonne di date specifiche di presenze
        """
        if 'data' in df.columns:
            df['data'] = df['data'].apply(self.data_processor.parse_italian_date)

            # Aggiungi colonne utili per l'analisi
            df['giorno_settimana'] = df['data'].dt.day_name()
            df['mese'] = df['data'].dt.month
            df['anno'] = df['data'].dt.year
            df['settimana_anno'] = df['data'].dt.isocalendar().week

        return df

    def _process_attendance_times(self, df):
        """
        Elabora gli orari di entrata e uscita
        """
        time_columns = ['entrata', 'uscita']

        for col in time_columns:
            if col in df.columns:
                # Converti in formato orario temporaneamente per i calcoli
                time_series = pd.to_datetime(df[col], format='%H:%M', errors='coerce').dt.time

                # Salva gli oggetti time in una colonna temporanea per i calcoli
                df[f'{col}_obj'] = time_series

                # Converti gli oggetti time in stringhe per la serializzazione JSON
                df[col] = time_series.apply(lambda x: x.strftime('%H:%M') if pd.notna(x) else None)

        # Calcola ore lavorate se non presenti
        if 'entrata_obj' in df.columns and 'uscita_obj' in df.columns and 'ore_lavorate' not in df.columns:
            df['ore_lavorate'] = df.apply(
                lambda row: self._calculate_hours_worked(row['entrata_obj'], row['uscita_obj']),
                axis=1
            )

        # Rimuovi le colonne temporanee
        for col in time_columns:
            if f'{col}_obj' in df.columns:
                df.drop(f'{col}_obj', axis=1, inplace=True)

        return df

    def _calculate_hours_worked(self, entry_time, exit_time):
        """
        Calcola le ore lavorate tra entrata e uscita
        """
        if pd.isna(entry_time) or pd.isna(exit_time):
            return 0

        # Converti in datetime per il calcolo
        entry_dt = datetime.combine(datetime.today().date(), entry_time)
        exit_dt = datetime.combine(datetime.today().date(), exit_time)

        # Gestisci il caso in cui l'uscita sia il giorno successivo
        if exit_dt < entry_dt:
            exit_dt += timedelta(days=1)

        # Calcola la differenza in ore
        diff = exit_dt - entry_dt
        hours_worked = diff.total_seconds() / 3600

        # Sottrai la pausa pranzo se applicabile (es. 1 ora se si lavora più di 6 ore)
        if hours_worked > 6:
            hours_worked -= 1

        return round(hours_worked, 2)

    def _calculate_additional_metrics(self, df):
        """
        Calcola metriche aggiuntive utili per l'analisi
        """
        # Calcola ritardi
        if 'entrata' in df.columns:
            standard_entry = datetime.strptime('09:00', '%H:%M').time()

            # Converti le stringhe in oggetti time per il confronto
            def calculate_delay(entry_str):
                if pd.isna(entry_str):
                    return 0
                try:
                    entry_time = datetime.strptime(entry_str, '%H:%M').time()
                    if entry_time > standard_entry:
                        return (datetime.combine(datetime.today(), entry_time) -
                                datetime.combine(datetime.today(), standard_entry)).total_seconds() / 60
                    return 0
                except (ValueError, TypeError):
                    return 0

            df['ritardo'] = df['entrata'].apply(calculate_delay)

        # Calcola uscite anticipate
        if 'uscita' in df.columns:
            standard_exit = datetime.strptime('18:00', '%H:%M').time()

            # Converti le stringhe in oggetti time per il confronto
            def calculate_early_exit(exit_str):
                if pd.isna(exit_str):
                    return 0
                try:
                    exit_time = datetime.strptime(exit_str, '%H:%M').time()
                    if exit_time < standard_exit:
                        return (datetime.combine(datetime.today(), standard_exit) -
                                datetime.combine(datetime.today(), exit_time)).total_seconds() / 60
                    return 0
                except (ValueError, TypeError):
                    return 0

            df['uscita_anticipata'] = df['uscita'].apply(calculate_early_exit)

        # Calcola straordinari se non presenti
        if 'ore_lavorate' in df.columns and 'straordinario' not in df.columns:
            df['straordinario'] = df['ore_lavorate'].apply(lambda x: max(0, x - 8) if pd.notna(x) else 0)

        return df

    def generate_summary_stats(self, df):
        """
        Genera statistiche di riepilogo per i dati di presenze
        """
        stats = {}

        # Conteggio totale giorni
        stats['total_days'] = len(df)

        # Ore lavorate totali e medie
        if 'ore_lavorate' in df.columns:
            stats['total_hours'] = df['ore_lavorate'].sum()
            stats['avg_hours_per_day'] = df['ore_lavorate'].mean()

        # Straordinari totali
        if 'straordinario' in df.columns:
            stats['total_overtime'] = df['straordinario'].sum()

        # Ritardi
        if 'ritardo' in df.columns:
            stats['total_delays'] = df['ritardo'].sum()
            stats['days_with_delay'] = (df['ritardo'] > 0).sum()

        # Permessi
        if 'permesso' in df.columns:
            stats['total_permissions'] = df['permesso'].sum()

            if 'tipo_permesso' in df.columns:
                stats['permissions_by_type'] = df.groupby('tipo_permesso').size().to_dict()

        # Distribuzione per giorno della settimana
        if 'giorno_settimana' in df.columns and 'ore_lavorate' in df.columns:
            stats['hours_by_weekday'] = df.groupby('giorno_settimana')['ore_lavorate'].sum().to_dict()

        # Distribuzione per dipendente
        if 'dipendente' in df.columns and 'ore_lavorate' in df.columns:
            stats['hours_by_employee'] = df.groupby('dipendente')['ore_lavorate'].sum().to_dict()

        return stats
