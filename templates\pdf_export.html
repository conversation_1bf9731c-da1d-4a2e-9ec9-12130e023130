<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Esportazione Dati - {{ filename }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .header {
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .info-box {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .footer {
            margin-top: 30px;
            font-size: 12px;
            color: #777;
            text-align: center;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .note {
            font-style: italic;
            color: #666;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Esportazione Dati</h1>
        <h2>{{ filename }}</h2>
    </div>

    <div class="info-box">
        <p><strong>Tipo file:</strong> {{ file_type }}</p>
        <p><strong>Data esportazione:</strong> {{ timestamp }}</p>
    </div>

    {% if stats %}
    <h3>Statistiche</h3>
    <table>
        <thead>
            <tr>
                <th>Metrica</th>
                <th>Valore</th>
            </tr>
        </thead>
        <tbody>
            {% for key, value in stats.items() %}
            <tr>
                <td>{{ key }}</td>
                <td>{{ value }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% endif %}

    <h3>Dati</h3>
    {% if data %}
    <table>
        <thead>
            <tr>
                {% for col in columns %}
                <th>{{ col }}</th>
                {% endfor %}
            </tr>
        </thead>
        <tbody>
            {% for row in data[:100] %}
            <tr>
                {% for col in columns %}
                <td>{{ row[col]|default('') }}</td>
                {% endfor %}
            </tr>
            {% endfor %}
        </tbody>
    </table>
    
    {% if data|length > 100 %}
    <p class="note">Nota: Mostrate solo le prime 100 righe di {{ data|length }} totali.</p>
    {% endif %}
    {% else %}
    <p>Nessun dato disponibile.</p>
    {% endif %}

    <div class="footer">
        <p>Generato da App Roberto - {{ timestamp }}</p>
    </div>
</body>
</html>
