﻿<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="{% block description %}Sistema intelligente di analisi dati aziendali con AI integrata{% endblock %}">

    <!-- Theme color - implementato solo con JS in base al browser -->
    <script>
        // Aggiungi meta theme-color solo per i browser che lo supportano
        (function() {
            // Lista di browser che supportano theme-color (basati su User Agent)
            var supportedBrowsers = ['Chrome', 'Edge', 'Safari'];
            var isSupported = false;
            
            // Verifica se il browser attuale è supportato
            for (var i = 0; i < supportedBrowsers.length; i++) {
                if (navigator.userAgent.indexOf(supportedBrowsers[i]) !== -1) {
                    isSupported = true;
                    break;
                }
            }
            
            // Aggiungi i meta tag solo se il browser è supportato
            if (isSupported) {
                var lightThemeMeta = document.createElement('meta');
                lightThemeMeta.name = 'theme-color';
                lightThemeMeta.content = '#0d6efd';
                lightThemeMeta.id = 'theme-color-meta';
                lightThemeMeta.media = '(prefers-color-scheme: light)';
                
                var darkThemeMeta = document.createElement('meta');
                darkThemeMeta.name = 'theme-color';
                darkThemeMeta.content = '#121212';
                darkThemeMeta.id = 'theme-color-meta-dark';
                darkThemeMeta.media = '(prefers-color-scheme: dark)';
                
                document.head.appendChild(lightThemeMeta);
                document.head.appendChild(darkThemeMeta);
            }
        })();
    </script>
    <!-- Supporto schema colori per tutti i browser moderni -->
    <meta name="color-scheme" content="light dark">
    <!-- Microsoft Edge/IE -->
    <meta name="msapplication-navbutton-color" content="#0d6efd">
    <meta name="msapplication-TileColor" content="#0d6efd">
    <!-- iOS Safari -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <!-- Android/Chrome -->
    <title>{% block title %}Analisi Dati Aziendali{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='favicon.svg') }}">
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    <!-- Dark Theme CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dark-theme.css') }}">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Skip link per accessibilitÃ  -->
    <a href="#main-content" class="skip-link">Salta al contenuto principale</a>

    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary" role="navigation" aria-label="Navigazione principale">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-chart-line me-2" aria-hidden="true"></i>Analisi Dati Aziendali
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-label="Apri menu di navigazione" title="Apri menu di navigazione">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('index') %}active{% endif %}" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.path == url_for('dashboard') or request.path == url_for('advanced_dashboard') %}active{% endif %}" href="#" id="dashboardDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-magic me-1"></i>Dashboard
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="dashboardDropdown">
                            <li>
                                <a class="dropdown-item {% if request.path == url_for('dashboard') %}active{% endif %}" href="{{ url_for('dashboard') }}">
                                    <i class="fas fa-chart-pie me-1"></i>Standard
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item {% if request.path == url_for('advanced_dashboard') %}active{% endif %}" href="{{ url_for('advanced_dashboard') }}">
                                    <i class="fas fa-chart-line me-1"></i>Avanzata
                                </a>
                            </li>
                        </ul>
                    </li>
                    <!-- Dati Grezzi rimossi - sezione legacy -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('interactive_charts') %}active{% endif %}" href="{{ url_for('interactive_charts') }}">
                            <i class="fas fa-chart-bar me-1"></i>Grafici Interattivi
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('chat') %}active{% endif %}" href="{{ url_for('chat') }}">
                            <i class="fas fa-comments me-1"></i>Chat AI
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('intelligent_dashboard') %}active{% endif %}" href="{{ url_for('intelligent_dashboard') }}">
                            <i class="fas fa-brain me-1"></i>Dashboard Intelligente
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == '/agents-dashboard' %}active{% endif %}" href="/agents-dashboard">
                            <i class="fas fa-robot me-1"></i>Agenti AI
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('configuration') %}active{% endif %}" href="{{ url_for('configuration') }}">
                            <i class="fas fa-cogs me-1"></i>Configurazione
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.path == url_for('setup_wizard') or request.path == url_for('wizard_interattivo_page') %}active{% endif %}" href="#" id="wizardDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-magic me-1"></i>Wizard
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="wizardDropdown">
                            <li>
                                <a class="dropdown-item {% if request.path == url_for('setup_wizard') %}active{% endif %}" href="{{ url_for('setup_wizard') }}">
                                    <i class="fas fa-cog me-1"></i>Setup Wizard
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item {% if request.path == url_for('wizard_interattivo_page') %}active{% endif %}" href="{{ url_for('wizard_interattivo_page') }}">
                                    <i class="fas fa-upload me-1"></i>Wizard Interattivo
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="monitoringDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-chart-line me-1"></i>Monitoring
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="monitoringDropdown">
                            <li><a class="dropdown-item" href="/monitoring/health">
                                <i class="fas fa-heartbeat me-1"></i>Health Check
                            </a></li>
                            <li><a class="dropdown-item" href="/monitoring/metrics">
                                <i class="fas fa-tachometer-alt me-1"></i>Metriche Sistema
                            </a></li>
                            <li><a class="dropdown-item" href="/monitoring/status">
                                <i class="fas fa-info-circle me-1"></i>Status Generale
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="container mt-3">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category if category != 'error' else 'danger' }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Chiudi" title="Chiudi"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Main Content -->
    <main id="main-content" class="container my-4" role="main">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light py-3 mt-5">
        <div class="container text-center">
            <p class="mb-0">&copy; 2025 Bait Service Srl. Tutti i diritti riservati.</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Theme Manager JS (caricato per primo) -->
    <script src="{{ url_for('static', filename='js/theme-manager.js') }}"></script>

    <!-- UI/UX Enhancements JS -->
    <script src="{{ url_for('static', filename='js/ui-enhancements.js') }}"></script>

    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
