#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test per il modulo performance.py.
"""

import time
import pytest
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

from performance import Cache, cached, timed, RateLimiter, rate_limited, global_cache

class TestCache:
    """Test per la classe Cache."""

    def setup_method(self):
        """Setup per i test."""
        self.cache = Cache(max_size=3, default_ttl=60)

    def test_init(self):
        """Test per il costruttore."""
        assert self.cache.max_size == 3
        assert self.cache.default_ttl == 60
        assert self.cache.cache == {}

    def test_get_set(self):
        """Test per i metodi get e set."""
        # Imposta un valore nella cache
        self.cache.set("key1", "value1")

        # Verifica che il valore sia stato memorizzato
        assert self.cache.get("key1") == "value1"

        # Verifica che un valore non presente restituisca None
        assert self.cache.get("key2") is None

    def test_ttl(self):
        """Test per il tempo di vita degli elementi."""
        # Imposta un valore con TTL breve
        self.cache.set("key1", "value1", ttl=1)

        # Verifica che il valore sia presente
        assert self.cache.get("key1") == "value1"

        # Attendi che il valore scada
        time.sleep(1.1)

        # Verifica che il valore sia scaduto
        assert self.cache.get("key1") is None

    def test_max_size(self):
        """Test per la dimensione massima della cache."""
        # Imposta più valori della dimensione massima
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")
        self.cache.set("key3", "value3")
        self.cache.set("key4", "value4")

        # Verifica che la cache contenga solo gli ultimi 3 valori
        assert len(self.cache.cache) == 3
        assert self.cache.get("key1") is None  # Il più vecchio è stato rimosso
        assert self.cache.get("key2") == "value2"
        assert self.cache.get("key3") == "value3"
        assert self.cache.get("key4") == "value4"

    def test_delete(self):
        """Test per il metodo delete."""
        # Imposta un valore nella cache
        self.cache.set("key1", "value1")

        # Verifica che il valore sia presente
        assert self.cache.get("key1") == "value1"

        # Elimina il valore
        self.cache.delete("key1")

        # Verifica che il valore sia stato eliminato
        assert self.cache.get("key1") is None

    def test_clear(self):
        """Test per il metodo clear."""
        # Imposta alcuni valori nella cache
        self.cache.set("key1", "value1")
        self.cache.set("key2", "value2")

        # Verifica che i valori siano presenti
        assert self.cache.get("key1") == "value1"
        assert self.cache.get("key2") == "value2"

        # Svuota la cache
        self.cache.clear()

        # Verifica che la cache sia vuota
        assert self.cache.get("key1") is None
        assert self.cache.get("key2") is None
        assert len(self.cache.cache) == 0

class TestCachedDecorator:
    """Test per il decoratore cached."""

    def setup_method(self):
        """Setup per i test."""
        # Svuota la cache globale
        global_cache.clear()

        # Crea una funzione di test con contatore
        self.counter = 0

        @cached(ttl=60, key_prefix="test")
        def test_function(arg1, arg2=None):
            self.counter += 1
            return f"{arg1}_{arg2}"

        self.test_function = test_function

    def test_cached_decorator(self):
        """Test per il decoratore cached."""
        # Chiama la funzione la prima volta
        result1 = self.test_function("a", arg2="b")
        assert result1 == "a_b"
        assert self.counter == 1

        # Chiama la funzione con gli stessi argomenti
        result2 = self.test_function("a", arg2="b")
        assert result2 == "a_b"
        assert self.counter == 1  # Il contatore non è aumentato

        # Chiama la funzione con argomenti diversi
        result3 = self.test_function("c", arg2="d")
        assert result3 == "c_d"
        assert self.counter == 2  # Il contatore è aumentato

    def test_cached_decorator_ttl(self):
        """Test per il decoratore cached con TTL."""
        # Crea una funzione con TTL breve
        @cached(ttl=1, key_prefix="test_ttl")
        def test_function_ttl(arg):
            self.counter += 1
            return f"result_{arg}"

        # Chiama la funzione la prima volta
        result1 = test_function_ttl("x")
        assert result1 == "result_x"
        assert self.counter == 1

        # Chiama la funzione subito dopo
        result2 = test_function_ttl("x")
        assert result2 == "result_x"
        assert self.counter == 1  # Il contatore non è aumentato

        # Attendi che il valore scada
        time.sleep(1.1)

        # Chiama la funzione dopo la scadenza
        result3 = test_function_ttl("x")
        assert result3 == "result_x"
        assert self.counter == 2  # Il contatore è aumentato

class TestTimedDecorator:
    """Test per il decoratore timed."""

    def test_timed_decorator(self):
        """Test per il decoratore timed."""
        # Crea una funzione di test
        @timed
        def test_function(sleep_time):
            time.sleep(sleep_time)
            return "result"

        # Chiama la funzione con un tempo di sleep
        with patch("logging.Logger.info") as mock_info:
            result = test_function(0.1)

            # Verifica che la funzione abbia restituito il risultato corretto
            assert result == "result"

            # Verifica che il tempo di esecuzione sia stato registrato
            mock_info.assert_called_once()
            log_message = mock_info.call_args[0][0]
            assert "Function test_function took" in log_message
            assert "seconds to execute" in log_message

class TestRateLimiter:
    """Test per la classe RateLimiter."""

    def setup_method(self):
        """Setup per i test."""
        self.limiter = RateLimiter(max_calls=2, period=1)

    def test_init(self):
        """Test per il costruttore."""
        assert self.limiter.max_calls == 2
        assert self.limiter.period == 1
        assert self.limiter.calls == {}

    def test_is_allowed(self):
        """Test per il metodo is_allowed."""
        # La prima chiamata è consentita
        assert self.limiter.is_allowed("test_key") == True

        # La seconda chiamata è consentita
        assert self.limiter.is_allowed("test_key") == True

        # La terza chiamata non è consentita
        assert self.limiter.is_allowed("test_key") == False

        # Attendi che il periodo scada
        time.sleep(1.1)

        # La chiamata è di nuovo consentita
        assert self.limiter.is_allowed("test_key") == True

    def test_multiple_keys(self):
        """Test per il metodo is_allowed con chiavi diverse."""
        # Le chiamate con chiavi diverse sono indipendenti
        assert self.limiter.is_allowed("key1") == True
        assert self.limiter.is_allowed("key1") == True
        assert self.limiter.is_allowed("key1") == False

        assert self.limiter.is_allowed("key2") == True
        assert self.limiter.is_allowed("key2") == True
        assert self.limiter.is_allowed("key2") == False

class TestRateLimitedDecorator:
    """Test per il decoratore rate_limited."""

    def setup_method(self):
        """Setup per i test."""
        # Crea una funzione di test
        @rate_limited(max_calls=2, period=1, key_func=lambda *args, **kwargs: "test_key")
        def test_function():
            return "result"

        self.test_function = test_function

    def test_rate_limited_decorator(self):
        """Test per il decoratore rate_limited."""
        # Crea un'app Flask per il contesto di richiesta
        from flask import Flask
        app = Flask(__name__)

        with app.test_request_context('/', environ_base={'REMOTE_ADDR': '127.0.0.1'}):
            # La prima chiamata è consentita
            assert self.test_function() == "result"

            # La seconda chiamata è consentita
            assert self.test_function() == "result"

            # La terza chiamata non è consentita
            result = self.test_function()
            # Il decoratore restituisce una tupla (response, status_code)
            assert isinstance(result, tuple)
            response, status_code = result
            assert status_code == 429
            # response è un oggetto Response di Flask, accediamo ai dati JSON
            import json
            response_data = json.loads(response.get_data(as_text=True))
            assert response_data["error"] == "Troppe richieste"

            # Attendi che il periodo scada
            time.sleep(1.1)

            # La chiamata è di nuovo consentita
            assert self.test_function() == "result"
