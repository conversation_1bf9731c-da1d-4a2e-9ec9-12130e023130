# Piano di Test e Debug per APP-ROBERTO

## 0. Preparazione Iniziale

### 0.1 Backup dell'Applicazione

- Creare una copia completa della cartella APP-ROBERTO

  ```powershell
  Copy-Item -Path "APP-ROBERTO" -Destination "APP-ROBERTO_BACKUP" -Recurse
  ```

- Verificare che la copia sia completa e funzionante

### 0.2 Pulizia della Struttura dei File

- Creare una cartella `scripts_archiviati` per spostare i file .bat non necessari

  ```powershell
  New-Item -Path "APP-ROBERTO/scripts_archiviati" -ItemType Directory -Force
  ```

- Spostare i file .bat di prova nella cartella di archivio (mantenendo solo quelli essenziali)

  ```powershell
  # Esempio: mantenere solo i file essenziali
  $essentialBats = @("avvio_completo.bat", "create_clean_env.bat", "stop_app.bat")
  Get-ChildItem -Path "APP-ROBERTO" -Filter "*.bat" | 
  Where-Object { $essentialBats -notcontains $_.Name } | 
  ForEach-Object { Move-Item -Path $_.FullName -Destination "APP-ROBERTO/scripts_archiviati" }
  ```

## 1. Preparazione dell'Ambiente di Test

### 1.1 Configurazione dell'ambiente isolato

- Creare un ambiente virtuale dedicato ai test

  ```powershell
  cd APP-ROBERTO
  python -m venv test_env
  ```

- Attivare l'ambiente virtuale

  ```powershell
  .\test_env\Scripts\activate
  ```

- Installare le dipendenze da `requirements.txt`

  ```powershell
  pip install -r requirements.txt
  ```

- Installare strumenti di test aggiuntivi

  ```powershell
  pip install pytest pytest-cov
  ```

### 1.2 Configurazione del Controllo Versione

- Inizializzare Git se non già presente

  ```powershell
  git init
  ```

- Creare un branch dedicato ai test

  ```powershell
  git add .
  git commit -m "Stato iniziale prima dei test"
  git checkout -b test-debug
  ```

## 2. Test Unitari

### 2.1 Configurazione pytest

- Creare/verificare il file `pytest.ini`

  ```powershell
  New-Item -Path "pytest.ini" -ItemType File -Force
  ```

- Aggiungere configurazione base

  ```ini
  [pytest]
  testpaths = tests
  python_files = test_*.py
  python_classes = Test*
  python_functions = test_*
  ```

- Creare cartella per i test se non esiste

  ```powershell
  New-Item -Path "tests" -ItemType Directory -Force
  New-Item -Path "tests/__init__.py" -ItemType File -Force
  ```

### 2.2 Esecuzione dei test esistenti

- Eseguire i test con output dettagliato

  ```powershell
  pytest -v
  ```

- Analizzare i risultati e documentare gli errori

### 2.3 Analisi della copertura

- Eseguire i test con analisi della copertura

  ```powershell
  pytest --cov=. --cov-report=term-missing
  ```

- Identificare le aree con bassa copertura di test

## 3. Test Funzionali per Componenti Principali

### 3.1 Test del Sistema di Importazione Dati

- Creare dati di test per diversi formati
  - CSV con separatore `;`
  - File Excel (.xlsx)
  - File con dati malformati
- Verificare la gestione degli errori
- Verificare la validazione dei dati
- Documentare i risultati

### 3.2 Test dell'Elaborazione Dati

- Verificare la standardizzazione dei dati
- Verificare l'identificazione di anomalie
- Verificare la correzione automatica
- Documentare i risultati

### 3.3 Test dell'Interfaccia Utente

- Verificare il caricamento della pagina principale
- Verificare il funzionamento del drag-and-drop
- Verificare la visualizzazione dell'anteprima dei dati
- Verificare la generazione dei grafici
- Documentare i risultati

### 3.4 Test dell'API MCP

- Verificare l'avvio del server MCP
- Verificare le chiamate API principali
- Verificare la gestione degli errori
- Documentare i risultati

## 4. Processo di Debug Sistematico

### 4.1 Registro degli Errori

- Creare un file `debug_log.md` per registrare gli errori

  ```powershell
  New-Item -Path "debug_log.md" -ItemType File -Force
  ```

- Struttura per ogni errore:

  ```markdown
  ## Errore #1: [Breve descrizione]
  - **Componente**: [Nome del componente]
  - **Gravità**: [Alta/Media/Bassa]
  - **Riproduzione**: [Passi per riprodurre]
  - **Analisi**: [Analisi della causa]
  - **Soluzione proposta**: [Descrizione della soluzione]
  - **Stato**: [Risolto/In corso/Da verificare]
  ```

### 4.2 Prioritizzazione degli Errori

- Classificare gli errori per gravità
- Creare un piano di risoluzione in ordine di priorità

### 4.3 Risoluzione degli Errori

- Per ogni errore:
  1. Creare un test che riproduca il problema
  2. Implementare la correzione
  3. Verificare che il test passi
  4. Eseguire tutti i test per verificare che non ci siano regressioni
  5. Aggiornare il registro degli errori

## 5. Verifica delle Correzioni

### 5.1 Test di Regressione

- Eseguire tutti i test dopo ogni correzione

  ```powershell
  pytest -v
  ```

- Verificare che le funzionalità correlate non siano state compromesse

### 5.2 Test di Integrazione

- Verificare il flusso completo dell'applicazione
- Testare scenari reali di utilizzo
- Documentare i risultati

## 6. Documentazione

### 6.1 Aggiornamento della Documentazione

- Aggiornare README.md con le nuove informazioni
- Documentare eventuali nuovi requisiti o dipendenze
- Aggiornare la documentazione per gli utenti se necessario

### 6.2 Creazione di una Guida alla Risoluzione dei Problemi

- Creare un file `troubleshooting.md` con soluzioni ai problemi comuni

  ```powershell
  New-Item -Path "troubleshooting.md" -ItemType File -Force
  ```

## 7. Implementazione in Produzione

### 7.1 Merge delle Correzioni

- Tornare al branch principale

  ```powershell
  git checkout main
  ```

- Unire le correzioni

  ```powershell
  git merge test-debug
  ```

### 7.2 Creazione di uno Script di Avvio Ottimizzato

- Creare un nuovo script di avvio che incorpori tutte le correzioni

  ```powershell
  New-Item -Path "avvio_ottimizzato.bat" -ItemType File -Force
  ```

### 7.3 Test Finale

- Eseguire un test completo dell'applicazione con lo script ottimizzato
- Verificare che tutte le funzionalità siano operative
- Documentare eventuali problemi residui

## 8. Monitoraggio e Manutenzione

### 8.1 Piano di Monitoraggio

- Definire metriche di performance da monitorare
- Creare un piano di manutenzione periodica

### 8.2 Feedback degli Utenti

- Implementare un sistema per raccogliere feedback
- Pianificare revisioni periodiche basate sul feedback
