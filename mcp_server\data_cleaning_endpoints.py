#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Endpoints per la pulizia dei dati.
Questo modulo definisce gli endpoint API per l'identificazione e la correzione
di problemi comuni nei dati.
"""

import os
import json
import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from fastapi import APIRouter, HTTPException, Query, Path, Body
from pydantic import BaseModel, Field

# Configurazione del logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("data_cleaning_endpoints")

# Crea un router per gli endpoint di pulizia dei dati
router = APIRouter(prefix="/data-cleaning", tags=["Data Cleaning"])

# Cartelle per i file
UPLOAD_FOLDER = "uploads"
PROCESSED_FOLDER = os.path.join(UPLOAD_FOLDER, "processed")

# Modelli Pydantic per la validazione dei dati
class MissingValue(BaseModel):
    column: str
    row: int
    location: str


class FormatInconsistency(BaseModel):
    column: str
    row: int
    expected_format: str
    found_format: str
    critical: bool = False
    location: str


class DurationDiscrepancy(BaseModel):
    row: int
    declared_duration: str
    calculated_duration: str
    difference: int  # in minuti
    location: str


class Correction(BaseModel):
    type: str
    location: str
    description: str
    severity: str
    old_value: Optional[str] = None
    new_value: Optional[str] = None


class Issue(BaseModel):
    type: str
    location: str
    description: str
    severity: str
    expected_format: Optional[str] = None
    found_format: Optional[str] = None
    declared_duration: Optional[str] = None
    calculated_duration: Optional[str] = None
    difference: Optional[int] = None


# Funzioni di utilità
def load_file(file_id: str) -> pd.DataFrame:
    """
    Carica un file dal disco.
    
    Args:
        file_id: ID del file
        
    Returns:
        DataFrame con i dati del file
    """
    # Cerca prima nella cartella processed
    processed_path = os.path.join(PROCESSED_FOLDER, f"{file_id}.csv")
    if os.path.exists(processed_path):
        return pd.read_csv(processed_path)
    
    # Poi cerca nella cartella uploads
    upload_path = os.path.join(UPLOAD_FOLDER, f"{file_id}.csv")
    if os.path.exists(upload_path):
        return pd.read_csv(upload_path)
    
    # Se non trova il file, solleva un'eccezione
    raise HTTPException(status_code=404, detail=f"File non trovato: {file_id}")


def save_file(file_id: str, df: pd.DataFrame) -> str:
    """
    Salva un file sul disco.
    
    Args:
        file_id: ID del file
        df: DataFrame con i dati da salvare
        
    Returns:
        Percorso del file salvato
    """
    # Genera un nuovo ID per il file pulito
    cleaned_file_id = f"{file_id}_cleaned_{int(datetime.now().timestamp())}"
    
    # Salva il file nella cartella processed
    processed_path = os.path.join(PROCESSED_FOLDER, f"{cleaned_file_id}.csv")
    df.to_csv(processed_path, index=False)
    
    return cleaned_file_id


# Endpoint per l'identificazione dei valori mancanti
@router.get("/identify-missing-values/{file_id}", response_model=Dict[str, List[MissingValue]])
async def identify_missing_values(file_id: str, columns: Optional[List[str]] = Query(None)):
    """
    Identifica i valori mancanti nel dataset.
    
    Args:
        file_id: ID del file
        columns: Colonne da controllare (opzionale)
        
    Returns:
        Lista di valori mancanti
    """
    try:
        # Carica il file
        df = load_file(file_id)
        
        # Filtra le colonne se specificate
        if columns:
            df = df[columns]
        
        # Identifica i valori mancanti
        missing_values = []
        
        for col in df.columns:
            # Trova le righe con valori mancanti
            missing_rows = df[df[col].isna()].index.tolist()
            
            for row in missing_rows:
                missing_values.append(
                    MissingValue(
                        column=col,
                        row=row + 1,  # +1 perché gli indici partono da 0
                        location=f"{col}:{row + 1}"
                    )
                )
        
        return {"missing_values": missing_values}
    
    except Exception as e:
        logger.exception(f"Errore nell'identificazione dei valori mancanti: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore nell'identificazione dei valori mancanti: {str(e)}")


# Endpoint per l'identificazione dei formati inconsistenti
@router.get("/identify-format-inconsistencies/{file_id}", response_model=Dict[str, List[FormatInconsistency]])
async def identify_format_inconsistencies(file_id: str, columns: Optional[List[str]] = Query(None)):
    """
    Rileva formati inconsistenti nei dati.
    
    Args:
        file_id: ID del file
        columns: Colonne da controllare (opzionale)
        
    Returns:
        Lista di formati inconsistenti
    """
    try:
        # Carica il file
        df = load_file(file_id)
        
        # Filtra le colonne se specificate
        if columns:
            df = df[columns]
        
        # Identifica i formati inconsistenti
        inconsistencies = []
        
        # Controlla le colonne di date
        date_columns = [col for col in df.columns if "data" in col.lower() or "date" in col.lower()]
        for col in date_columns:
            # Trova il formato più comune
            formats = {}
            for value in df[col].dropna():
                # Semplificazione: controlla solo alcuni formati comuni
                if isinstance(value, str):
                    if "/" in value:
                        formats["dd/mm/yyyy"] = formats.get("dd/mm/yyyy", 0) + 1
                    elif "-" in value:
                        formats["yyyy-mm-dd"] = formats.get("yyyy-mm-dd", 0) + 1
                    elif "." in value:
                        formats["dd.mm.yyyy"] = formats.get("dd.mm.yyyy", 0) + 1
            
            if formats:
                expected_format = max(formats, key=formats.get)
                
                # Trova le righe con formati diversi
                for i, value in enumerate(df[col]):
                    if isinstance(value, str) and value:
                        found_format = None
                        if "/" in value:
                            found_format = "dd/mm/yyyy"
                        elif "-" in value:
                            found_format = "yyyy-mm-dd"
                        elif "." in value:
                            found_format = "dd.mm.yyyy"
                        
                        if found_format and found_format != expected_format:
                            inconsistencies.append(
                                FormatInconsistency(
                                    column=col,
                                    row=i + 1,
                                    expected_format=expected_format,
                                    found_format=found_format,
                                    critical=False,
                                    location=f"{col}:{i + 1}"
                                )
                            )
        
        # Controlla le colonne di durata
        duration_columns = [col for col in df.columns if "durata" in col.lower() or "duration" in col.lower()]
        for col in duration_columns:
            # Trova il formato più comune
            formats = {}
            for value in df[col].dropna():
                if isinstance(value, str):
                    if ":" in value:
                        formats["hh:mm"] = formats.get("hh:mm", 0) + 1
                    elif "h" in value.lower():
                        formats["Xh Ym"] = formats.get("Xh Ym", 0) + 1
                    elif "." in value:
                        formats["X.Y"] = formats.get("X.Y", 0) + 1
                    else:
                        try:
                            float(value)
                            formats["decimal"] = formats.get("decimal", 0) + 1
                        except:
                            pass
            
            if formats:
                expected_format = max(formats, key=formats.get)
                
                # Trova le righe con formati diversi
                for i, value in enumerate(df[col]):
                    if isinstance(value, str) and value:
                        found_format = None
                        if ":" in value:
                            found_format = "hh:mm"
                        elif "h" in value.lower():
                            found_format = "Xh Ym"
                        elif "." in value:
                            found_format = "X.Y"
                        else:
                            try:
                                float(value)
                                found_format = "decimal"
                            except:
                                pass
                        
                        if found_format and found_format != expected_format:
                            inconsistencies.append(
                                FormatInconsistency(
                                    column=col,
                                    row=i + 1,
                                    expected_format=expected_format,
                                    found_format=found_format,
                                    critical=False,
                                    location=f"{col}:{i + 1}"
                                )
                            )
        
        return {"inconsistencies": inconsistencies}
    
    except Exception as e:
        logger.exception(f"Errore nell'identificazione dei formati inconsistenti: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore nell'identificazione dei formati inconsistenti: {str(e)}")


# Endpoint per l'identificazione delle discrepanze di durata
@router.get("/identify-duration-discrepancies/{file_id}", response_model=Dict[str, List[DurationDiscrepancy]])
async def identify_duration_discrepancies(file_id: str):
    """
    Trova discrepanze tra durate dichiarate e calcolate.
    
    Args:
        file_id: ID del file
        
    Returns:
        Lista di discrepanze di durata
    """
    try:
        # Carica il file
        df = load_file(file_id)
        
        # Identifica le discrepanze di durata
        discrepancies = []
        
        # Cerca colonne di inizio e fine
        start_columns = [col for col in df.columns if "inizio" in col.lower() or "start" in col.lower()]
        end_columns = [col for col in df.columns if "fine" in col.lower() or "end" in col.lower()]
        duration_columns = [col for col in df.columns if "durata" in col.lower() or "duration" in col.lower()]
        
        if start_columns and end_columns and duration_columns:
            start_col = start_columns[0]
            end_col = end_columns[0]
            duration_col = duration_columns[0]
            
            for i, row in df.iterrows():
                start_time = row[start_col]
                end_time = row[end_col]
                declared_duration = row[duration_col]
                
                # Salta le righe con valori mancanti
                if pd.isna(start_time) or pd.isna(end_time) or pd.isna(declared_duration):
                    continue
                
                # Converti le stringhe in datetime
                try:
                    if isinstance(start_time, str) and isinstance(end_time, str):
                        # Prova diversi formati di data/ora
                        formats = ["%d/%m/%Y %H:%M", "%Y-%m-%d %H:%M", "%d.%m.%Y %H:%M"]
                        
                        start_dt = None
                        end_dt = None
                        
                        for fmt in formats:
                            try:
                                start_dt = datetime.strptime(start_time, fmt)
                                break
                            except:
                                pass
                        
                        for fmt in formats:
                            try:
                                end_dt = datetime.strptime(end_time, fmt)
                                break
                            except:
                                pass
                        
                        if start_dt and end_dt:
                            # Calcola la durata in minuti
                            calculated_duration_minutes = (end_dt - start_dt).total_seconds() / 60
                            
                            # Converti la durata dichiarata in minuti
                            declared_duration_minutes = 0
                            
                            if isinstance(declared_duration, str):
                                if ":" in declared_duration:
                                    # Formato hh:mm
                                    hours, minutes = declared_duration.split(":")
                                    declared_duration_minutes = int(hours) * 60 + int(minutes)
                                elif "h" in declared_duration.lower():
                                    # Formato Xh Ym
                                    parts = declared_duration.lower().replace("h", " ").replace("m", " ").split()
                                    hours = 0
                                    minutes = 0
                                    
                                    for i, part in enumerate(parts):
                                        if part.isdigit():
                                            if i == 0:
                                                hours = int(part)
                                            elif i == 1:
                                                minutes = int(part)
                                    
                                    declared_duration_minutes = hours * 60 + minutes
                                else:
                                    # Prova a convertire in decimale
                                    try:
                                        declared_duration_minutes = float(declared_duration) * 60
                                    except:
                                        continue
                            elif isinstance(declared_duration, (int, float)):
                                declared_duration_minutes = declared_duration * 60
                            
                            # Calcola la differenza
                            difference = int(declared_duration_minutes - calculated_duration_minutes)
                            
                            # Se la differenza è significativa (più di 5 minuti), registra la discrepanza
                            if abs(difference) > 5:
                                # Converti la durata calcolata in formato hh:mm
                                hours = int(calculated_duration_minutes // 60)
                                minutes = int(calculated_duration_minutes % 60)
                                calculated_duration_str = f"{hours}:{minutes:02d}"
                                
                                discrepancies.append(
                                    DurationDiscrepancy(
                                        row=i + 1,
                                        declared_duration=str(declared_duration),
                                        calculated_duration=calculated_duration_str,
                                        difference=difference,
                                        location=f"{duration_col}:{i + 1}"
                                    )
                                )
                except Exception as e:
                    logger.warning(f"Errore nell'analisi della riga {i}: {str(e)}")
        
        return {"discrepancies": discrepancies}
    
    except Exception as e:
        logger.exception(f"Errore nell'identificazione delle discrepanze di durata: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore nell'identificazione delle discrepanze di durata: {str(e)}")


# Endpoint per suggerire correzioni
@router.post("/suggest-corrections/{file_id}", response_model=Dict[str, List[Correction]])
async def suggest_corrections(file_id: str, issues: List[Issue] = Body(...)):
    """
    Genera suggerimenti per correggere i problemi identificati.
    
    Args:
        file_id: ID del file
        issues: Lista di problemi da correggere
        
    Returns:
        Lista di correzioni suggerite
    """
    try:
        # Carica il file
        df = load_file(file_id)
        
        # Genera suggerimenti di correzione
        corrections = []
        
        for issue in issues:
            if issue.type == "missing_value":
                # Suggerimento per valori mancanti
                column, row_str = issue.location.split(":")
                row = int(row_str) - 1
                
                # Suggerisci un valore basato sulla colonna
                suggested_value = None
                
                if "data" in column.lower() or "date" in column.lower():
                    # Per le date, suggerisci la data più comune
                    dates = df[column].dropna().tolist()
                    if dates:
                        suggested_value = max(set(dates), key=dates.count)
                
                elif "durata" in column.lower() or "duration" in column.lower():
                    # Per le durate, suggerisci la media
                    durations = []
                    for val in df[column].dropna():
                        try:
                            if isinstance(val, str) and ":" in val:
                                hours, minutes = val.split(":")
                                durations.append(int(hours) * 60 + int(minutes))
                            elif isinstance(val, (int, float)):
                                durations.append(val * 60)
                        except:
                            pass
                    
                    if durations:
                        avg_minutes = sum(durations) / len(durations)
                        hours = int(avg_minutes // 60)
                        minutes = int(avg_minutes % 60)
                        suggested_value = f"{hours}:{minutes:02d}"
                
                else:
                    # Per altre colonne, suggerisci il valore più comune
                    values = df[column].dropna().tolist()
                    if values:
                        suggested_value = max(set(values), key=values.count)
                
                if suggested_value is not None:
                    corrections.append(
                        Correction(
                            type="missing_value",
                            location=issue.location,
                            description=f"Inserire il valore '{suggested_value}' nella cella vuota",
                            severity="medium",
                            old_value="",
                            new_value=str(suggested_value)
                        )
                    )
            
            elif issue.type == "format_inconsistency":
                # Suggerimento per formati inconsistenti
                column, row_str = issue.location.split(":")
                row = int(row_str) - 1
                
                # Ottieni il valore attuale
                current_value = df.loc[row, column]
                
                # Suggerisci una correzione basata sul formato atteso
                corrected_value = None
                
                if issue.expected_format == "dd/mm/yyyy" and issue.found_format == "yyyy-mm-dd":
                    # Converti da yyyy-mm-dd a dd/mm/yyyy
                    try:
                        date_obj = datetime.strptime(current_value, "%Y-%m-%d")
                        corrected_value = date_obj.strftime("%d/%m/%Y")
                    except:
                        pass
                
                elif issue.expected_format == "yyyy-mm-dd" and issue.found_format == "dd/mm/yyyy":
                    # Converti da dd/mm/yyyy a yyyy-mm-dd
                    try:
                        date_obj = datetime.strptime(current_value, "%d/%m/%Y")
                        corrected_value = date_obj.strftime("%Y-%m-%d")
                    except:
                        pass
                
                elif issue.expected_format == "hh:mm" and issue.found_format in ["Xh Ym", "decimal", "X.Y"]:
                    # Converti in formato hh:mm
                    try:
                        minutes = 0
                        
                        if issue.found_format == "Xh Ym":
                            # Formato Xh Ym
                            parts = current_value.lower().replace("h", " ").replace("m", " ").split()
                            hours = 0
                            mins = 0
                            
                            for i, part in enumerate(parts):
                                if part.isdigit():
                                    if i == 0:
                                        hours = int(part)
                                    elif i == 1:
                                        mins = int(part)
                            
                            minutes = hours * 60 + mins
                        
                        elif issue.found_format in ["decimal", "X.Y"]:
                            # Formato decimale o X.Y
                            hours = float(current_value.replace(",", "."))
                            minutes = hours * 60
                        
                        hours_int = int(minutes // 60)
                        minutes_int = int(minutes % 60)
                        corrected_value = f"{hours_int}:{minutes_int:02d}"
                    except:
                        pass
                
                if corrected_value is not None:
                    corrections.append(
                        Correction(
                            type="format_inconsistency",
                            location=issue.location,
                            description=f"Convertire '{current_value}' nel formato {issue.expected_format}: '{corrected_value}'",
                            severity="medium",
                            old_value=str(current_value),
                            new_value=corrected_value
                        )
                    )
            
            elif issue.type == "duration_discrepancy":
                # Suggerimento per discrepanze di durata
                column, row_str = issue.location.split(":")
                row = int(row_str) - 1
                
                # Ottieni il valore attuale
                current_value = df.loc[row, column]
                
                # Suggerisci di utilizzare la durata calcolata
                corrections.append(
                    Correction(
                        type="duration_discrepancy",
                        location=issue.location,
                        description=f"Sostituire la durata dichiarata '{issue.declared_duration}' con la durata calcolata '{issue.calculated_duration}'",
                        severity="high" if abs(issue.difference) > 60 else "medium",
                        old_value=str(current_value),
                        new_value=issue.calculated_duration
                    )
                )
        
        return {"corrections": corrections}
    
    except Exception as e:
        logger.exception(f"Errore nella generazione dei suggerimenti di correzione: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore nella generazione dei suggerimenti di correzione: {str(e)}")


# Endpoint per applicare le correzioni
@router.post("/apply-corrections/{file_id}", response_model=Dict[str, str])
async def apply_corrections(file_id: str, corrections: List[Correction] = Body(...)):
    """
    Applica le correzioni suggerite ai dati.
    
    Args:
        file_id: ID del file
        corrections: Lista di correzioni da applicare
        
    Returns:
        ID del file pulito
    """
    try:
        # Carica il file
        df = load_file(file_id)
        
        # Applica le correzioni
        for correction in corrections:
            column, row_str = correction.location.split(":")
            row = int(row_str) - 1
            
            # Applica la correzione
            df.loc[row, column] = correction.new_value
        
        # Salva il file pulito
        cleaned_file_id = save_file(file_id, df)
        
        return {"cleaned_file_id": cleaned_file_id}
    
    except Exception as e:
        logger.exception(f"Errore nell'applicazione delle correzioni: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore nell'applicazione delle correzioni: {str(e)}")
