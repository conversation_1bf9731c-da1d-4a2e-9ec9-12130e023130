#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Modulo per l'autenticazione e l'autorizzazione.
"""

import os
import jwt
import logging
from datetime import datetime, timedelta
from functools import wraps
from flask import request, jsonify, current_app, g
from werkzeug.security import generate_password_hash, check_password_hash

# Configurazione del logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Classe per rappresentare un utente
class User:
    """Classe per rappresentare un utente."""
    
    def __init__(self, id, username, password_hash, role="user"):
        """
        Inizializza un utente.
        
        Args:
            id: ID dell'utente
            username: Nome utente
            password_hash: Hash della password
            role: <PERSON><PERSON><PERSON> dell'utente (default: "user")
        """
        self.id = id
        self.username = username
        self.password_hash = password_hash
        self.role = role
    
    def check_password(self, password):
        """
        Verifica la password.
        
        Args:
            password: Password da verificare
            
        Returns:
            True se la password è corretta, False altrimenti
        """
        return check_password_hash(self.password_hash, password)
    
    @staticmethod
    def hash_password(password):
        """
        Genera l'hash di una password.
        
        Args:
            password: Password da hashare
            
        Returns:
            Hash della password
        """
        return generate_password_hash(password)

# Dizionario per memorizzare gli utenti (in un'applicazione reale, si userebbe un database)
users = {
    "admin": User(1, "admin", User.hash_password("admin"), "admin"),
    "user": User(2, "user", User.hash_password("user"), "user")
}

def get_user_by_username(username):
    """
    Ottiene un utente dal suo nome utente.
    
    Args:
        username: Nome utente
        
    Returns:
        Utente se trovato, None altrimenti
    """
    return users.get(username)

def generate_token(user_id, username, role, expires_in=3600):
    """
    Genera un token JWT.
    
    Args:
        user_id: ID dell'utente
        username: Nome utente
        role: Ruolo dell'utente
        expires_in: Durata del token in secondi (default: 1 ora)
        
    Returns:
        Token JWT
    """
    payload = {
        "user_id": user_id,
        "username": username,
        "role": role,
        "exp": datetime.utcnow() + timedelta(seconds=expires_in)
    }
    
    secret_key = os.environ.get("JWT_SECRET_KEY", "default_secret_key")
    
    return jwt.encode(payload, secret_key, algorithm="HS256")

def verify_token(token):
    """
    Verifica un token JWT.
    
    Args:
        token: Token JWT da verificare
        
    Returns:
        Payload del token se valido, None altrimenti
    """
    try:
        secret_key = os.environ.get("JWT_SECRET_KEY", "default_secret_key")
        payload = jwt.decode(token, secret_key, algorithms=["HS256"])
        return payload
    except jwt.ExpiredSignatureError:
        logger.warning("Token scaduto")
        return None
    except jwt.InvalidTokenError:
        logger.warning("Token non valido")
        return None

def login_required(f):
    """
    Decoratore per richiedere l'autenticazione.
    
    Args:
        f: Funzione da decorare
        
    Returns:
        Funzione decorata
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = None
        
        # Ottieni il token dall'header Authorization
        if "Authorization" in request.headers:
            auth_header = request.headers["Authorization"]
            if auth_header.startswith("Bearer "):
                token = auth_header.split(" ")[1]
        
        # Verifica che il token sia presente
        if not token:
            return jsonify({"error": "Token mancante"}), 401
        
        # Verifica che il token sia valido
        payload = verify_token(token)
        if not payload:
            return jsonify({"error": "Token non valido o scaduto"}), 401
        
        # Memorizza le informazioni dell'utente in g
        g.user_id = payload["user_id"]
        g.username = payload["username"]
        g.role = payload["role"]
        
        return f(*args, **kwargs)
    
    return decorated_function

def admin_required(f):
    """
    Decoratore per richiedere l'autenticazione come amministratore.
    
    Args:
        f: Funzione da decorare
        
    Returns:
        Funzione decorata
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = None
        
        # Ottieni il token dall'header Authorization
        if "Authorization" in request.headers:
            auth_header = request.headers["Authorization"]
            if auth_header.startswith("Bearer "):
                token = auth_header.split(" ")[1]
        
        # Verifica che il token sia presente
        if not token:
            return jsonify({"error": "Token mancante"}), 401
        
        # Verifica che il token sia valido
        payload = verify_token(token)
        if not payload:
            return jsonify({"error": "Token non valido o scaduto"}), 401
        
        # Verifica che l'utente sia un amministratore
        if payload["role"] != "admin":
            return jsonify({"error": "Accesso negato"}), 403
        
        # Memorizza le informazioni dell'utente in g
        g.user_id = payload["user_id"]
        g.username = payload["username"]
        g.role = payload["role"]
        
        return f(*args, **kwargs)
    
    return decorated_function
