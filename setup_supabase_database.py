#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per configurare automaticamente il database Supabase.
Crea le tabelle e configura le policy di sicurezza.
"""

import os
import sys
import logging
from typing import Dict, Any, List
from datetime import datetime

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_supabase_database():
    """
    Configura il database Supabase con le tabelle e policy necessarie.
    """
    print("🚀 Setup Database Supabase per app-roberto")
    print("=" * 60)
    
    try:
        from supabase_integration import supabase_manager
        
        if not supabase_manager.is_connected:
            print("❌ Errore: Supabase non connesso")
            print("💡 Verifica le variabili d'ambiente SUPABASE_URL e SUPABASE_KEY")
            return False
        
        print("✅ Connessione Supabase attiva")
        
        # Leggi lo schema SQL
        schema_file = "supabase_schema.sql"
        if not os.path.exists(schema_file):
            print(f"❌ File schema non trovato: {schema_file}")
            return False
        
        with open(schema_file, 'r', encoding='utf-8') as f:
            schema_sql = f.read()
        
        print(f"📄 Schema SQL caricato da {schema_file}")
        
        # Nota: Supabase Python client non supporta direttamente l'esecuzione di SQL DDL
        # Dobbiamo usare l'API REST o eseguire manualmente nel dashboard
        print("⚠️ IMPORTANTE: Il client Python Supabase non supporta l'esecuzione diretta di SQL DDL")
        print("📋 Per completare il setup:")
        print("   1. Vai su https://app.supabase.com/project/zqjllwxqjxjhdkbcawfr/sql")
        print("   2. Copia e incolla il contenuto di supabase_schema.sql")
        print("   3. Esegui lo script SQL")
        print("   4. Riavvia questo script per verificare")
        
        # Verifica se le tabelle esistono già
        tables_to_check = [
            "file_uploads",
            "processed_data", 
            "user_sessions",
            "system_config",
            "employee_costs",
            "system_logs",
            "ai_analyses"
        ]
        
        print("\n🔍 Verifica tabelle esistenti...")
        existing_tables = []
        
        for table in tables_to_check:
            try:
                # Prova a fare una query semplice per verificare se la tabella esiste
                result = supabase_manager.client.table(table).select("*").limit(1).execute()
                existing_tables.append(table)
                print(f"   ✅ {table}: Esiste")
            except Exception as e:
                print(f"   ❌ {table}: Non esiste o non accessibile")
        
        if len(existing_tables) == len(tables_to_check):
            print("\n🎉 Tutte le tabelle sono presenti!")
            
            # Test inserimento dati di esempio
            return test_database_operations()
        else:
            print(f"\n⚠️ Trovate {len(existing_tables)}/{len(tables_to_check)} tabelle")
            print("💡 Esegui lo schema SQL nel dashboard Supabase")
            return False
            
    except ImportError:
        print("❌ Errore: Modulo supabase_integration non disponibile")
        return False
    except Exception as e:
        print(f"❌ Errore setup database: {str(e)}")
        return False

def test_database_operations():
    """
    Testa le operazioni base del database.
    """
    print("\n🧪 Test operazioni database...")
    
    try:
        from supabase_integration import supabase_manager
        
        # Test 1: Inserimento configurazione di sistema
        test_config = {
            "config_key": f"test_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "config_value": {"test": True, "timestamp": datetime.now().isoformat()},
            "description": "Configurazione di test"
        }
        
        result = supabase_manager.client.table("system_config").insert(test_config).execute()
        if result.data:
            print("   ✅ Test inserimento configurazione: OK")
            config_id = result.data[0]['id']
        else:
            print("   ❌ Test inserimento configurazione: FALLITO")
            return False
        
        # Test 2: Lettura configurazione
        result = supabase_manager.client.table("system_config").select("*").eq("id", config_id).execute()
        if result.data and len(result.data) > 0:
            print("   ✅ Test lettura configurazione: OK")
        else:
            print("   ❌ Test lettura configurazione: FALLITO")
            return False
        
        # Test 3: Aggiornamento configurazione
        updated_config = {
            "config_value": {"test": True, "updated": True, "timestamp": datetime.now().isoformat()},
            "updated_at": datetime.now().isoformat()
        }
        
        result = supabase_manager.client.table("system_config").update(updated_config).eq("id", config_id).execute()
        if result.data:
            print("   ✅ Test aggiornamento configurazione: OK")
        else:
            print("   ❌ Test aggiornamento configurazione: FALLITO")
            return False
        
        # Test 4: Inserimento file upload simulato
        test_file = {
            "filename": f"test_file_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
            "original_filename": "test_file.xlsx",
            "file_type": "attivita",
            "file_path": "/test/path/test_file.xlsx",
            "file_size": 1024,
            "status": "uploaded",
            "session_id": f"test_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        }
        
        result = supabase_manager.client.table("file_uploads").insert(test_file).execute()
        if result.data:
            print("   ✅ Test inserimento file upload: OK")
            file_id = result.data[0]['id']
        else:
            print("   ❌ Test inserimento file upload: FALLITO")
            return False
        
        # Test 5: Inserimento dati elaborati
        test_processed = {
            "file_upload_id": file_id,
            "data_type": "attivita",
            "processed_data": {
                "rows": 100,
                "columns": 10,
                "processing_time": 2.5
            },
            "statistics": {
                "total_hours": 150.5,
                "unique_employees": 5
            },
            "quality_report": {
                "completeness": 0.95,
                "accuracy": 0.88
            }
        }
        
        result = supabase_manager.client.table("processed_data").insert(test_processed).execute()
        if result.data:
            print("   ✅ Test inserimento dati elaborati: OK")
        else:
            print("   ❌ Test inserimento dati elaborati: FALLITO")
            return False
        
        # Test 6: Query complessa con JOIN
        try:
            result = supabase_manager.client.table("file_uploads").select(
                "*, processed_data(*)"
            ).eq("id", file_id).execute()
            
            if result.data and len(result.data) > 0:
                print("   ✅ Test query con JOIN: OK")
            else:
                print("   ⚠️ Test query con JOIN: Dati non trovati")
        except Exception as e:
            print(f"   ⚠️ Test query con JOIN: {str(e)}")
        
        # Test 7: Pulizia dati di test
        supabase_manager.client.table("processed_data").delete().eq("file_upload_id", file_id).execute()
        supabase_manager.client.table("file_uploads").delete().eq("id", file_id).execute()
        supabase_manager.client.table("system_config").delete().eq("id", config_id).execute()
        print("   ✅ Pulizia dati di test: OK")
        
        print("\n🎉 Tutti i test del database sono stati superati!")
        return True
        
    except Exception as e:
        print(f"   ❌ Errore durante i test: {str(e)}")
        return False

def create_sample_data():
    """
    Crea dati di esempio per testare il sistema.
    """
    print("\n📊 Creazione dati di esempio...")
    
    try:
        from supabase_integration import supabase_manager
        
        # Configurazioni di esempio
        sample_configs = [
            {
                "config_key": "default_hourly_rate",
                "config_value": 25.0,
                "description": "Tariffa oraria di default"
            },
            {
                "config_key": "vat_rate",
                "config_value": 22.0,
                "description": "Aliquota IVA standard"
            },
            {
                "config_key": "supported_file_types",
                "config_value": ["xlsx", "xls", "csv"],
                "description": "Tipi di file supportati"
            }
        ]
        
        for config in sample_configs:
            try:
                result = supabase_manager.client.table("system_config").upsert(config).execute()
                if result.data:
                    print(f"   ✅ Configurazione creata: {config['config_key']}")
            except Exception as e:
                print(f"   ⚠️ Errore configurazione {config['config_key']}: {str(e)}")
        
        # Costi dipendenti di esempio
        sample_employees = [
            {
                "employee_name": "Mario Rossi",
                "hourly_rate": 30.0,
                "vat_included": True,
                "vat_rate": 22.0,
                "notes": "Sviluppatore senior"
            },
            {
                "employee_name": "Giulia Bianchi",
                "hourly_rate": 25.0,
                "vat_included": True,
                "vat_rate": 22.0,
                "notes": "Analista dati"
            },
            {
                "employee_name": "Luca Verdi",
                "hourly_rate": 20.0,
                "vat_included": False,
                "vat_rate": 22.0,
                "notes": "Junior developer"
            }
        ]
        
        for employee in sample_employees:
            try:
                result = supabase_manager.client.table("employee_costs").upsert(employee).execute()
                if result.data:
                    print(f"   ✅ Dipendente creato: {employee['employee_name']}")
            except Exception as e:
                print(f"   ⚠️ Errore dipendente {employee['employee_name']}: {str(e)}")
        
        print("✅ Dati di esempio creati con successo!")
        return True
        
    except Exception as e:
        print(f"❌ Errore creazione dati di esempio: {str(e)}")
        return False

def main():
    """
    Funzione principale per il setup del database.
    """
    print("🔧 SETUP AUTOMATICO DATABASE SUPABASE")
    print("=" * 60)
    
    # Step 1: Setup database
    db_setup_success = setup_supabase_database()
    
    if db_setup_success:
        # Step 2: Crea dati di esempio
        sample_data_success = create_sample_data()
        
        if sample_data_success:
            print("\n🎉 SETUP COMPLETATO CON SUCCESSO!")
            print("💡 Il database Supabase è pronto per l'uso")
            
            # Mostra statistiche finali
            try:
                from supabase_integration import supabase_manager
                
                # Conta record nelle tabelle principali
                tables_stats = {}
                for table in ["system_config", "employee_costs", "file_uploads", "processed_data"]:
                    try:
                        result = supabase_manager.client.table(table).select("id", count="exact").execute()
                        tables_stats[table] = result.count if hasattr(result, 'count') else 0
                    except:
                        tables_stats[table] = "N/A"
                
                print("\n📊 STATISTICHE DATABASE:")
                for table, count in tables_stats.items():
                    print(f"   {table}: {count} record")
                    
            except Exception as e:
                print(f"⚠️ Errore recupero statistiche: {str(e)}")
        else:
            print("\n⚠️ Setup database completato, ma errore nella creazione dati di esempio")
    else:
        print("\n❌ SETUP NON COMPLETATO")
        print("💡 Segui le istruzioni per configurare manualmente il database")
    
    return db_setup_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
