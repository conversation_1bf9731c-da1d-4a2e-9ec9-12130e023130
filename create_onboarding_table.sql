-- Script SQL per creare la tabella onboarding_progress in Supabase
-- Eseguire questo script nel SQL Editor di Supabase

-- Crea la tabella onboarding_progress se non esiste
CREATE TABLE IF NOT EXISTS onboarding_progress (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    user_id VARCHAR(100) NOT NULL,
    progress_data JSONB NOT NULL DEFAULT '{}',
    current_step INTEGER DEFAULT 1,
    completed_steps INTEGER[] DEFAULT '{}',
    business_type VARCHAR(50) DEFAULT 'generic',
    confidence_score DECIMAL(5,2) DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Crea indici per migliorare le performance
CREATE INDEX IF NOT EXISTS idx_onboarding_progress_user_id ON onboarding_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_onboarding_progress_business_type ON onboarding_progress(business_type);
CREATE INDEX IF NOT EXISTS idx_onboarding_progress_created_at ON onboarding_progress(created_at);

-- Crea trigger per aggiornare automaticamente updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Applica il trigger alla tabella
DROP TRIGGER IF EXISTS update_onboarding_progress_updated_at ON onboarding_progress;
CREATE TRIGGER update_onboarding_progress_updated_at
    BEFORE UPDATE ON onboarding_progress
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Aggiungi commenti per documentazione
COMMENT ON TABLE onboarding_progress IS 'Tabella per salvare il progresso del wizard di onboarding intelligente';
COMMENT ON COLUMN onboarding_progress.user_id IS 'ID utente o session ID';
COMMENT ON COLUMN onboarding_progress.progress_data IS 'Dati completi del progresso in formato JSON';
COMMENT ON COLUMN onboarding_progress.current_step IS 'Step corrente del wizard (1-5)';
COMMENT ON COLUMN onboarding_progress.completed_steps IS 'Array degli step completati';
COMMENT ON COLUMN onboarding_progress.business_type IS 'Tipo di business rilevato (generic, it_services, consulting, etc.)';
COMMENT ON COLUMN onboarding_progress.confidence_score IS 'Punteggio di confidenza del rilevamento (0.0-1.0)';

-- Verifica che la tabella sia stata creata
SELECT 
    table_name, 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'onboarding_progress' 
ORDER BY ordinal_position;
