#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test di debug per la chat MCP - analisi dettagliata degli errori.
"""

import requests
import json

def test_chat_detailed():
    """Test dettagliato della chat per identificare il problema."""
    print("🔍 TEST DETTAGLIATO CHAT MCP")
    print("=" * 50)
    
    try:
        # Test 1: Verifica endpoint chat
        print("\n1️⃣ Test endpoint chat...")
        chat_data = {
            "message": "Test di connessione MCP"
        }
        
        print(f"📤 Invio richiesta a: http://127.0.0.1:5000/api/chat/send")
        print(f"📦 Dati: {json.dumps(chat_data, indent=2)}")
        
        response = requests.post(
            "http://127.0.0.1:5000/api/chat/send",
            json=chat_data,
            timeout=15
        )
        
        print(f"\n📊 Risposta HTTP:")
        print(f"   Status Code: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"\n📄 Contenuto risposta JSON:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                # Analizza la struttura della risposta
                print(f"\n🔍 Analisi struttura:")
                print(f"   Chiavi presenti: {list(result.keys())}")
                print(f"   Ha 'success': {'success' in result}")
                print(f"   Ha 'response': {'response' in result}")
                print(f"   Ha 'error': {'error' in result}")
                
                if 'success' in result:
                    print(f"   Valore 'success': {result['success']}")
                if 'error' in result:
                    print(f"   Valore 'error': {result['error']}")
                if 'response' in result:
                    print(f"   Valore 'response': {result['response'][:100]}...")
                    
                return result
                
            except json.JSONDecodeError as e:
                print(f"❌ Errore parsing JSON: {str(e)}")
                print(f"📄 Contenuto raw: {response.text[:500]}...")
                return None
        else:
            print(f"❌ Errore HTTP {response.status_code}")
            print(f"📄 Contenuto: {response.text[:500]}...")
            return None
            
    except Exception as e:
        print(f"❌ Errore nel test: {str(e)}")
        return None

def test_openrouter_availability():
    """Test disponibilità OpenRouter."""
    print("\n2️⃣ Test disponibilità OpenRouter...")
    
    try:
        # Test endpoint info app
        response = requests.get("http://127.0.0.1:5000/api/info", timeout=10)
        if response.status_code == 200:
            info = response.json()
            print(f"   📊 Info app: {json.dumps(info, indent=2)}")
        else:
            print(f"   ⚠️ Endpoint info non disponibile: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore test OpenRouter: {str(e)}")

def test_mcp_server_direct():
    """Test diretto del server MCP."""
    print("\n3️⃣ Test diretto server MCP...")
    
    try:
        # Test health
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        if response.status_code == 200:
            health = response.json()
            print(f"   ✅ Health MCP: {health}")
        else:
            print(f"   ❌ Health MCP fallito: {response.status_code}")
            
        # Test docs
        response = requests.get("http://127.0.0.1:8000/docs", timeout=5)
        if response.status_code == 200:
            print(f"   ✅ Docs MCP disponibili")
        else:
            print(f"   ❌ Docs MCP non disponibili: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore test MCP diretto: {str(e)}")

def test_session_data():
    """Test dati di sessione."""
    print("\n4️⃣ Test dati di sessione...")
    
    try:
        # Crea una sessione per mantenere i cookie
        session = requests.Session()
        
        # Prima vai alla home page per inizializzare la sessione
        response = session.get("http://127.0.0.1:5000/", timeout=10)
        print(f"   📊 Home page: {response.status_code}")
        
        # Poi prova la chat
        chat_data = {"message": "Test con sessione"}
        response = session.post(
            "http://127.0.0.1:5000/api/chat/send",
            json=chat_data,
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"   📄 Risposta con sessione: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"   ❌ Errore con sessione: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore test sessione: {str(e)}")

def main():
    """Esegue tutti i test di debug."""
    print("🚀 AVVIO TEST DEBUG CHAT MCP")
    print("=" * 50)
    
    # Test 1: Chat dettagliata
    result = test_chat_detailed()
    
    # Test 2: OpenRouter
    test_openrouter_availability()
    
    # Test 3: MCP diretto
    test_mcp_server_direct()
    
    # Test 4: Sessione
    test_session_data()
    
    print("\n" + "=" * 50)
    print("🎯 RISULTATO DEBUG")
    print("=" * 50)
    
    if result:
        print("✅ Endpoint chat risponde")
        print("🔍 Analizza i dettagli sopra per identificare il problema")
    else:
        print("❌ Endpoint chat non risponde correttamente")
        print("🔧 Verifica che l'applicazione sia avviata correttamente")
    
    return result is not None

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
