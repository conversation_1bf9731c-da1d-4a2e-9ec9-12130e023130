"""
Playwright PDF Generator - Moderno sostituto di WeasyPrint
Genera PDF da HTML usando Playwright (zero dipendenze GTK)
"""

import asyncio
import logging
import os
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Union
from playwright.async_api import async_playwright, Browser, Page

logger = logging.getLogger(__name__)

class PlaywrightPDFGenerator:
    """
    Generatore PDF moderno usando Playwright.
    Sostituisce WeasyPrint senza dipendenze GTK.
    """
    
    def __init__(self, headless: bool = True, browser_type: str = "chromium"):
        """
        Inizializza il generatore PDF.
        
        Args:
            headless: Esegui browser in modalità headless
            browser_type: Tipo di browser ('chromium', 'firefox', 'webkit')
        """
        self.headless = headless
        self.browser_type = browser_type
        self.browser: Optional[Browser] = None
        self._playwright = None
        
    async def __aenter__(self):
        """Context manager async entry."""
        await self.start()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Context manager async exit."""
        await self.close()
        
    async def start(self):
        """Avvia il browser."""
        try:
            self._playwright = await async_playwright().start()
            
            if self.browser_type == "chromium":
                self.browser = await self._playwright.chromium.launch(headless=self.headless)
            elif self.browser_type == "firefox":
                self.browser = await self._playwright.firefox.launch(headless=self.headless)
            elif self.browser_type == "webkit":
                self.browser = await self._playwright.webkit.launch(headless=self.headless)
            else:
                raise ValueError(f"Browser type non supportato: {self.browser_type}")
                
            logger.info(f"Browser {self.browser_type} avviato (headless={self.headless})")
            
        except Exception as e:
            logger.error(f"Errore avvio browser: {str(e)}")
            raise
            
    async def close(self):
        """Chiudi il browser."""
        try:
            if self.browser:
                await self.browser.close()
                logger.info("Browser chiuso")
                
            if self._playwright:
                await self._playwright.stop()
                
        except Exception as e:
            logger.error(f"Errore chiusura browser: {str(e)}")
            
    async def html_to_pdf(
        self,
        html_content: str,
        output_path: str,
        css_content: Optional[str] = None,
        page_options: Optional[Dict[str, Any]] = None,
        pdf_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Converte HTML in PDF.
        
        Args:
            html_content: Contenuto HTML da convertire
            output_path: Percorso file PDF di output
            css_content: CSS aggiuntivo (opzionale)
            page_options: Opzioni per la pagina (viewport, etc.)
            pdf_options: Opzioni per il PDF (formato, margini, etc.)
            
        Returns:
            Dict con risultato della conversione
        """
        if not self.browser:
            await self.start()
            
        try:
            # Opzioni default per la pagina
            default_page_options = {
                'viewport': {'width': 1200, 'height': 800}
            }
            page_opts = {**default_page_options, **(page_options or {})}
            
            # Opzioni default per il PDF
            default_pdf_options = {
                'format': 'A4',
                'margin': {
                    'top': '1cm',
                    'right': '1cm', 
                    'bottom': '1cm',
                    'left': '1cm'
                },
                'print_background': True,
                'prefer_css_page_size': True
            }
            pdf_opts = {**default_pdf_options, **(pdf_options or {})}
            
            # Crea nuova pagina
            page = await self.browser.new_page(**page_opts)
            
            try:
                # Aggiungi CSS se fornito
                if css_content:
                    await page.add_style_tag(content=css_content)
                
                # Carica HTML
                await page.set_content(html_content, wait_until='networkidle')
                
                # Genera PDF
                await page.pdf(path=output_path, **pdf_opts)
                
                # Verifica che il file sia stato creato
                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path)
                    logger.info(f"PDF generato: {output_path} ({file_size} bytes)")
                    
                    return {
                        'success': True,
                        'output_path': output_path,
                        'file_size': file_size,
                        'timestamp': datetime.now().isoformat()
                    }
                else:
                    raise FileNotFoundError(f"PDF non generato: {output_path}")
                    
            finally:
                await page.close()
                
        except Exception as e:
            logger.error(f"Errore generazione PDF: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            
    async def url_to_pdf(
        self,
        url: str,
        output_path: str,
        pdf_options: Optional[Dict[str, Any]] = None,
        wait_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Converte URL in PDF.
        
        Args:
            url: URL da convertire
            output_path: Percorso file PDF di output
            pdf_options: Opzioni per il PDF
            wait_options: Opzioni di attesa (timeout, wait_until)
            
        Returns:
            Dict con risultato della conversione
        """
        if not self.browser:
            await self.start()
            
        try:
            # Opzioni default di attesa
            default_wait_options = {
                'wait_until': 'networkidle',
                'timeout': 30000
            }
            wait_opts = {**default_wait_options, **(wait_options or {})}
            
            # Opzioni default per il PDF
            default_pdf_options = {
                'format': 'A4',
                'margin': {
                    'top': '1cm',
                    'right': '1cm',
                    'bottom': '1cm', 
                    'left': '1cm'
                },
                'print_background': True
            }
            pdf_opts = {**default_pdf_options, **(pdf_options or {})}
            
            # Crea nuova pagina
            page = await self.browser.new_page()
            
            try:
                # Naviga all'URL
                await page.goto(url, **wait_opts)
                
                # Genera PDF
                await page.pdf(path=output_path, **pdf_opts)
                
                # Verifica risultato
                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path)
                    logger.info(f"PDF da URL generato: {output_path} ({file_size} bytes)")
                    
                    return {
                        'success': True,
                        'output_path': output_path,
                        'file_size': file_size,
                        'url': url,
                        'timestamp': datetime.now().isoformat()
                    }
                else:
                    raise FileNotFoundError(f"PDF non generato: {output_path}")
                    
            finally:
                await page.close()
                
        except Exception as e:
            logger.error(f"Errore conversione URL to PDF: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'url': url,
                'timestamp': datetime.now().isoformat()
            }

# Funzioni di convenienza per compatibilità con WeasyPrint
async def generate_pdf_from_html(
    html_content: str,
    output_path: str,
    css_content: Optional[str] = None,
    **kwargs
) -> Dict[str, Any]:
    """
    Funzione di convenienza per generare PDF da HTML.
    Compatibile con l'interfaccia WeasyPrint.
    """
    async with PlaywrightPDFGenerator() as generator:
        return await generator.html_to_pdf(
            html_content=html_content,
            output_path=output_path,
            css_content=css_content,
            **kwargs
        )

async def generate_pdf_from_url(
    url: str,
    output_path: str,
    **kwargs
) -> Dict[str, Any]:
    """
    Funzione di convenienza per generare PDF da URL.
    """
    async with PlaywrightPDFGenerator() as generator:
        return await generator.url_to_pdf(
            url=url,
            output_path=output_path,
            **kwargs
        )

# Funzione sincrona per compatibilità
def generate_pdf_sync(
    html_content: str,
    output_path: str,
    css_content: Optional[str] = None,
    **kwargs
) -> Dict[str, Any]:
    """
    Versione sincrona per compatibilità con codice esistente.
    """
    return asyncio.run(generate_pdf_from_html(
        html_content=html_content,
        output_path=output_path,
        css_content=css_content,
        **kwargs
    ))
