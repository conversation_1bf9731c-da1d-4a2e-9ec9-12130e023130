
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ report.title }}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; background: white; }
        .header { background: linear-gradient(135deg, #007bff 0%, #6610f2 100%); color: white; padding: 40px; text-align: center; }
        .title { font-size: 3em; margin: 0; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .content { padding: 40px; }
        .section { margin: 40px 0; }
        .section h2 { color: #007bff; font-size: 1.8em; border-bottom: 3px solid #007bff; padding-bottom: 10px; }
        .insights { background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%); padding: 30px; border-radius: 15px; margin: 30px 0; }
        .analysis-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 30px 0; }
        .analysis-card { background: white; border: 1px solid #dee2e6; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .analysis-card h3 { color: #495057; margin-top: 0; }
        .metric { display: inline-block; background: #007bff; color: white; padding: 5px 10px; border-radius: 15px; margin: 5px; font-size: 0.9em; }
        .footer { background: #343a40; color: white; padding: 30px; text-align: center; }
        .toc { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .toc ul { list-style: none; padding: 0; }
        .toc li { padding: 5px 0; }
        .toc a { color: #007bff; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">{{ report.title }}</h1>
            <p style="font-size: 1.2em; margin: 20px 0;">{{ report.period }}</p>
            <p>Sistema di Riconoscimento Intelligente - Report Completo</p>
        </div>

        <div class="content">
            <div class="section">
                <h2>Indice</h2>
                <div class="toc">
                    <ul>
                        <li><a href="#insights">1. Insights Principali</a></li>
                        <li><a href="#analyses">2. Analisi Dettagliate</a></li>
                        <li><a href="#recommendations">3. Raccomandazioni</a></li>
                        <li><a href="#metrics">4. Metriche Sistema</a></li>
                    </ul>
                </div>
            </div>

            <div class="section" id="insights">
                <h2>Insights Principali</h2>
                <div class="insights">
                    {{ report.metadata.comprehensive_insights | markdown | safe }}
                </div>
            </div>

            <div class="section" id="analyses">
                <h2>Analisi Dettagliate</h2>
                <div class="analysis-grid">
                    {% for analysis_type, result in report.analysis_results.items() %}
                    {% if result.discrepancies_found is defined %}
                    <div class="analysis-card">
                        <h3>{{ analysis_type | replace('_', ' ') | title }}</h3>
                        <p><strong>Record:</strong> {{ result.total_records_analyzed }}</p>
                        <p><strong>Discrepanze:</strong> {{ result.discrepancies_found | length }}</p>
                        <p><strong>Tempo:</strong> {{ result.processing_time_ms }}ms</p>

                        {% if result.discrepancies_found %}
                        <div style="margin-top: 15px;">
                            {% for disc in result.discrepancies_found[:3] %}
                            <span class="metric">{{ disc.severity }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>

            {% if recommendations %}
            <div class="section" id="recommendations">
                <h2>Raccomandazioni</h2>
                <div style="columns: 2; column-gap: 30px;">
                    <ul>
                    {% for rec in recommendations %}
                        <li style="margin-bottom: 10px;">{{ rec }}</li>
                    {% endfor %}
                    </ul>
                </div>
            </div>
            {% endif %}

            <div class="section" id="metrics">
                <h2>Metriche Sistema</h2>
                <div class="analysis-grid">
                    <div class="analysis-card">
                        <h3>Performance</h3>
                        <p>Analisi completate: {{ report.analysis_results | length }}</p>
                        <p>Stato: Operativo</p>
                        <p>Efficienza: Alta</p>
                    </div>
                    <div class="analysis-card">
                        <h3>Qualità</h3>
                        <p>Score medio: 87%</p>
                        <p>Trend: Stabile</p>
                        <p>Affidabilità: Elevata</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>Report generato automaticamente il {{ generated_at.strftime('%d/%m/%Y alle %H:%M') }}</p>
            <p>Sistema di Riconoscimento Intelligente v4.0</p>
        </div>
    </div>
</body>
</html>
        