#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Configuration Manager per la gestione persistente delle configurazioni dell'applicazione.
Gestisce costi dipendenti, impostazioni IVA e altre configurazioni.
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# Configurazione del logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ConfigManager:
    """
    Gestore delle configurazioni dell'applicazione con persistenza su file JSON.
    Gestisce costi dipendenti, impostazioni IVA e configurazioni generali.
    """

    def __init__(self, config_file: str = "app_config.json"):
        self.config_file = config_file
        self.config_data = {}
        self.load_config()

    def load_config(self):
        """Carica la configurazione dal file JSON"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
                logger.info(f"✅ Configurazione caricata da {self.config_file}")
            else:
                # Crea configurazione di default
                self.config_data = self._create_default_config()
                self.save_config()
                logger.info(f"📝 Creata configurazione di default in {self.config_file}")
        except Exception as e:
            logger.error(f"❌ Errore caricamento configurazione: {e}")
            self.config_data = self._create_default_config()

    def save_config(self):
        """Salva la configurazione nel file JSON"""
        try:
            # Aggiungi timestamp ultimo aggiornamento
            self.config_data['last_updated'] = datetime.now().isoformat()
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=2, ensure_ascii=False)
            logger.info(f"💾 Configurazione salvata in {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"❌ Errore salvataggio configurazione: {e}")
            return False

    def _create_default_config(self) -> Dict[str, Any]:
        """Crea configurazione di default"""
        return {
            "version": "1.0",
            "created": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
            "employee_costs": {},
            "tax_settings": {
                "default_vat_included": True,
                "vat_rate": 22.0,
                "currency": "EUR"
            },
            "vehicle_settings": {
                "cost_per_km": 0.50,
                "fuel_cost_per_liter": 1.60,
                "vehicles": {
                    "Fiat Punto": {"fuel_consumption": 6.5, "daily_cost": 25.0},
                    "Peugeot 208": {"fuel_consumption": 5.8, "daily_cost": 30.0},
                    "Ford Fiesta": {"fuel_consumption": 6.2, "daily_cost": 28.0}
                }
            },
            "general_settings": {
                "company_name": "Roberto Srl",
                "working_hours_per_day": 8.0,
                "overtime_multiplier": 1.5
            }
        }

    # === GESTIONE COSTI DIPENDENTI ===

    def get_employee_cost(self, employee_name: str) -> Optional[Dict[str, Any]]:
        """Ottiene il costo orario di un dipendente"""
        return self.config_data.get("employee_costs", {}).get(employee_name)

    def set_employee_cost(self, employee_name: str, hourly_rate: float, 
                         vat_included: bool = True, notes: str = "") -> bool:
        """
        Imposta il costo orario di un dipendente
        
        Args:
            employee_name: Nome del dipendente
            hourly_rate: Costo orario
            vat_included: Se il costo include IVA
            notes: Note aggiuntive
        """
        try:
            if "employee_costs" not in self.config_data:
                self.config_data["employee_costs"] = {}
            
            self.config_data["employee_costs"][employee_name] = {
                "hourly_rate": float(hourly_rate),
                "vat_included": bool(vat_included),
                "notes": str(notes),
                "created": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat()
            }
            
            return self.save_config()
        except Exception as e:
            logger.error(f"❌ Errore impostazione costo dipendente {employee_name}: {e}")
            return False

    def remove_employee_cost(self, employee_name: str) -> bool:
        """Rimuove il costo di un dipendente"""
        try:
            if employee_name in self.config_data.get("employee_costs", {}):
                del self.config_data["employee_costs"][employee_name]
                return self.save_config()
            return True
        except Exception as e:
            logger.error(f"❌ Errore rimozione costo dipendente {employee_name}: {e}")
            return False

    def get_all_employee_costs(self) -> Dict[str, Dict[str, Any]]:
        """Ottiene tutti i costi dipendenti"""
        return self.config_data.get("employee_costs", {})

    def update_employee_cost(self, employee_name: str, **kwargs) -> bool:
        """Aggiorna parzialmente il costo di un dipendente"""
        try:
            if employee_name not in self.config_data.get("employee_costs", {}):
                return False
            
            employee_data = self.config_data["employee_costs"][employee_name]
            
            # Aggiorna solo i campi forniti
            for key, value in kwargs.items():
                if key in ["hourly_rate", "vat_included", "notes"]:
                    employee_data[key] = value
            
            employee_data["last_updated"] = datetime.now().isoformat()
            return self.save_config()
        except Exception as e:
            logger.error(f"❌ Errore aggiornamento costo dipendente {employee_name}: {e}")
            return False

    # === GESTIONE IMPOSTAZIONI IVA ===

    def get_tax_settings(self) -> Dict[str, Any]:
        """Ottiene le impostazioni IVA"""
        return self.config_data.get("tax_settings", {})

    def set_tax_settings(self, vat_rate: float = None, default_vat_included: bool = None, 
                        currency: str = None) -> bool:
        """Aggiorna le impostazioni IVA"""
        try:
            tax_settings = self.config_data.get("tax_settings", {})
            
            if vat_rate is not None:
                tax_settings["vat_rate"] = float(vat_rate)
            if default_vat_included is not None:
                tax_settings["default_vat_included"] = bool(default_vat_included)
            if currency is not None:
                tax_settings["currency"] = str(currency)
            
            self.config_data["tax_settings"] = tax_settings
            return self.save_config()
        except Exception as e:
            logger.error(f"❌ Errore aggiornamento impostazioni IVA: {e}")
            return False

    # === GESTIONE VEICOLI ===

    def get_vehicle_settings(self) -> Dict[str, Any]:
        """Ottiene le impostazioni veicoli"""
        return self.config_data.get("vehicle_settings", {})

    def set_vehicle_cost(self, vehicle_name: str, fuel_consumption: float, 
                        daily_cost: float) -> bool:
        """Imposta i costi di un veicolo"""
        try:
            if "vehicle_settings" not in self.config_data:
                self.config_data["vehicle_settings"] = {"vehicles": {}}
            if "vehicles" not in self.config_data["vehicle_settings"]:
                self.config_data["vehicle_settings"]["vehicles"] = {}
            
            self.config_data["vehicle_settings"]["vehicles"][vehicle_name] = {
                "fuel_consumption": float(fuel_consumption),
                "daily_cost": float(daily_cost),
                "last_updated": datetime.now().isoformat()
            }
            
            return self.save_config()
        except Exception as e:
            logger.error(f"❌ Errore impostazione costo veicolo {vehicle_name}: {e}")
            return False

    # === UTILITÀ ===

    def calculate_employee_cost(self, employee_name: str, hours: float, 
                               include_vat: bool = None) -> Dict[str, float]:
        """
        Calcola il costo totale per un dipendente
        
        Returns:
            Dict con costo_netto, iva, costo_totale
        """
        employee_data = self.get_employee_cost(employee_name)
        if not employee_data:
            return {"error": "Dipendente non trovato"}
        
        hourly_rate = employee_data["hourly_rate"]
        vat_included = employee_data["vat_included"]
        
        # Determina se includere IVA
        if include_vat is None:
            include_vat = vat_included
        
        # Calcola costo base
        base_cost = hourly_rate * hours
        
        # Calcola IVA
        vat_rate = self.get_tax_settings().get("vat_rate", 22.0) / 100
        
        if vat_included:
            # Il costo orario include già IVA
            costo_totale = base_cost
            costo_netto = base_cost / (1 + vat_rate)
            iva = costo_totale - costo_netto
        else:
            # Il costo orario è netto
            costo_netto = base_cost
            iva = base_cost * vat_rate
            costo_totale = costo_netto + iva
        
        return {
            "costo_netto": round(costo_netto, 2),
            "iva": round(iva, 2),
            "costo_totale": round(costo_totale, 2),
            "ore": hours,
            "costo_orario": hourly_rate,
            "iva_inclusa_nel_costo": vat_included
        }

    def get_employees_from_data(self, df) -> List[str]:
        """Estrae lista dipendenti unici da un DataFrame"""
        employee_columns = ['dipendente', 'employee', 'tecnico', 'technician', 'nome', 'name']
        
        employees = set()
        for col in employee_columns:
            if col in df.columns:
                unique_employees = df[col].dropna().unique()
                for emp in unique_employees:
                    if isinstance(emp, str) and emp.strip():
                        # Gestisci dipendenti multipli (es. "Mario/Luigi")
                        if '/' in emp:
                            for sub_emp in emp.split('/'):
                                employees.add(sub_emp.strip())
                        else:
                            employees.add(emp.strip())
        
        return sorted(list(employees))

    def backup_config(self, backup_suffix: str = None) -> str:
        """Crea backup della configurazione"""
        try:
            if backup_suffix is None:
                backup_suffix = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            backup_file = f"{self.config_file}.backup_{backup_suffix}"
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 Backup configurazione creato: {backup_file}")
            return backup_file
        except Exception as e:
            logger.error(f"❌ Errore creazione backup: {e}")
            return None

# Istanza globale per uso nell'applicazione
config_manager = ConfigManager()
