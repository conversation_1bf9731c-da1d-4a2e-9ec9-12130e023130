<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Agenti AI - Sistema di Riconoscimento Intelligente</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .agent-card {
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }
        .agent-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .status-healthy { border-left-color: #28a745; }
        .status-warning { border-left-color: #ffc107; }
        .status-error { border-left-color: #dc3545; }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .capability-badge {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }
        .task-progress {
            height: 8px;
            border-radius: 4px;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-robot me-2"></i>
                Dashboard Agenti AI
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">
                    <i class="fas fa-chart-line me-1"></i>
                    Dashboard Principale
                </a>
                <a class="nav-link" href="/intelligent-dashboard">
                    <i class="fas fa-brain me-1"></i>
                    Dashboard Intelligente
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3 mb-3">
                    <i class="fas fa-robot text-primary me-2"></i>
                    Dashboard Agenti AI
                </h1>
                <p class="text-muted">
                    Monitoraggio e gestione degli agenti intelligenti per automazione avanzata
                </p>
            </div>
        </div>

        <!-- System Status -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-robot fa-2x mb-2"></i>
                        <h4 class="mb-0">{{ data.overview.total_agents }}</h4>
                        <small>Agenti Attivi</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h4 class="mb-0">{{ data.overview.queue_size }}</h4>
                        <small>Task in Coda</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-play fa-2x mb-2"></i>
                        <h4 class="mb-0">{{ data.overview.running_tasks }}</h4>
                        <small>Task in Esecuzione</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-check fa-2x mb-2"></i>
                        <h4 class="mb-0">{{ data.overview.completed_tasks }}</h4>
                        <small>Task Completati</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Health Alert -->
        {% if data.system_status == 'error' %}
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Errore Sistema:</strong> {{ data.error }}
        </div>
        {% elif data.system_status == 'warning' %}
        <div class="alert alert-warning" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <strong>Attenzione:</strong> Alcuni agenti potrebbero non essere disponibili.
        </div>
        {% endif %}

        <!-- Agents Overview -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            Agenti Disponibili
                        </h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshAgents()">
                            <i class="fas fa-sync-alt me-1"></i>
                            Aggiorna
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row" id="agents-container">
                            <!-- Agenti verranno caricati qui via JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Agent Statistics -->
        {% if data.agents_stats %}
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            Statistiche Performance
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="performanceChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie me-2"></i>
                            Distribuzione Task
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="taskDistributionChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            Azioni Rapide
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <button class="btn btn-primary w-100" onclick="showDataCleaningModal()">
                                    <i class="fas fa-broom me-2"></i>
                                    Pulizia Dati
                                </button>
                            </div>
                            <div class="col-md-4 mb-3">
                                <button class="btn btn-success w-100" onclick="showExportModal()">
                                    <i class="fas fa-download me-2"></i>
                                    Esporta Dati
                                </button>
                            </div>
                            <div class="col-md-4 mb-3">
                                <button class="btn btn-info w-100" onclick="showHealthCheck()">
                                    <i class="fas fa-heartbeat me-2"></i>
                                    Health Check
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal per Data Cleaning -->
    <div class="modal fade" id="dataCleaningModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-broom me-2"></i>
                        Pulizia Dati
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="dataCleaningForm">
                        <div class="mb-3">
                            <label for="fileId" class="form-label">ID File</label>
                            <input type="text" class="form-control" id="fileId" required>
                        </div>
                        <div class="mb-3">
                            <label for="operation" class="form-label">Operazione</label>
                            <select class="form-select" id="operation">
                                <option value="full_cleaning_pipeline">Pipeline Completa</option>
                                <option value="missing_value_imputation">Imputazione Valori Mancanti</option>
                                <option value="outlier_detection">Rilevamento Outlier</option>
                                <option value="data_standardization">Standardizzazione Dati</option>
                                <option value="duplicate_resolution">Risoluzione Duplicati</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="priority" class="form-label">Priorità</label>
                            <select class="form-select" id="priority">
                                <option value="high">Alta</option>
                                <option value="medium" selected>Media</option>
                                <option value="low">Bassa</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annulla</button>
                    <button type="button" class="btn btn-primary" onclick="submitDataCleaning()">
                        <i class="fas fa-play me-1"></i>
                        Avvia Pulizia
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal per Export -->
    <div class="modal fade" id="exportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-download me-2"></i>
                        Esporta Dati
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="exportForm">
                        <div class="mb-3">
                            <label for="exportFileId" class="form-label">ID File</label>
                            <input type="text" class="form-control" id="exportFileId" required>
                        </div>
                        <div class="mb-3">
                            <label for="exportType" class="form-label">Tipo Export</label>
                            <select class="form-select" id="exportType">
                                <option value="standard">Standard</option>
                                <option value="filtered">Filtrato</option>
                                <option value="report">Report</option>
                                <option value="dashboard">Dashboard</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="format" class="form-label">Formato</label>
                            <select class="form-select" id="format">
                                <option value="xlsx">Excel (XLSX)</option>
                                <option value="csv">CSV</option>
                                <option value="json">JSON</option>
                                <option value="pdf">PDF</option>
                                <option value="xml">XML</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annulla</button>
                    <button type="button" class="btn btn-success" onclick="submitExport()">
                        <i class="fas fa-download me-1"></i>
                        Avvia Export
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Variabili globali
        let agentsData = [];
        let performanceChart = null;
        let taskDistributionChart = null;

        // Inizializzazione
        document.addEventListener('DOMContentLoaded', function() {
            loadAgents();
            initializeCharts();
            
            // Auto-refresh ogni 30 secondi
            setInterval(refreshAgents, 30000);
        });

        // Carica lista agenti
        async function loadAgents() {
            try {
                const response = await fetch('/api/agents/list');
                const data = await response.json();
                
                if (data.success) {
                    agentsData = data.agents;
                    renderAgents();
                    updateCharts();
                } else {
                    showAlert('Errore caricamento agenti: ' + data.error, 'danger');
                }
            } catch (error) {
                showAlert('Errore connessione: ' + error.message, 'danger');
            }
        }

        // Renderizza agenti
        function renderAgents() {
            const container = document.getElementById('agents-container');
            container.innerHTML = '';

            agentsData.forEach(agent => {
                const statusClass = agent.is_active ? 'status-healthy' : 'status-error';
                const statusIcon = agent.is_active ? 'fa-check-circle text-success' : 'fa-times-circle text-danger';
                
                const agentCard = `
                    <div class="col-md-6 mb-3">
                        <div class="card agent-card ${statusClass}">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-robot me-2"></i>
                                        ${agent.name.replace('_', ' ').toUpperCase()}
                                    </h6>
                                    <i class="fas ${statusIcon}"></i>
                                </div>
                                <p class="text-muted small mb-2">Tipo: ${agent.type}</p>
                                
                                <div class="row text-center">
                                    <div class="col-4">
                                        <small class="text-muted">Capacità</small>
                                        <div class="fw-bold">${agent.capabilities}</div>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-muted">Completati</small>
                                        <div class="fw-bold text-success">${agent.tasks_completed}</div>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-muted">Falliti</small>
                                        <div class="fw-bold text-danger">${agent.tasks_failed}</div>
                                    </div>
                                </div>
                                
                                ${agent.capabilities_list ? `
                                    <div class="mt-2">
                                        <small class="text-muted">Capacità:</small>
                                        <div class="mt-1">
                                            ${agent.capabilities_list.map(cap => 
                                                `<span class="badge capability-badge me-1">${cap}</span>`
                                            ).join('')}
                                        </div>
                                    </div>
                                ` : ''}
                                
                                ${agent.supported_formats ? `
                                    <div class="mt-2">
                                        <small class="text-muted">Formati:</small>
                                        <div class="mt-1">
                                            ${agent.supported_formats.map(format => 
                                                `<span class="badge bg-secondary me-1">${format}</span>`
                                            ).join('')}
                                        </div>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;
                
                container.innerHTML += agentCard;
            });
        }

        // Inizializza grafici
        function initializeCharts() {
            // Performance Chart
            const performanceCtx = document.getElementById('performanceChart');
            if (performanceCtx) {
                performanceChart = new Chart(performanceCtx, {
                    type: 'bar',
                    data: {
                        labels: [],
                        datasets: [{
                            label: 'Task Completati',
                            data: [],
                            backgroundColor: 'rgba(40, 167, 69, 0.8)',
                            borderColor: 'rgba(40, 167, 69, 1)',
                            borderWidth: 1
                        }, {
                            label: 'Task Falliti',
                            data: [],
                            backgroundColor: 'rgba(220, 53, 69, 0.8)',
                            borderColor: 'rgba(220, 53, 69, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            // Task Distribution Chart
            const taskCtx = document.getElementById('taskDistributionChart');
            if (taskCtx) {
                taskDistributionChart = new Chart(taskCtx, {
                    type: 'doughnut',
                    data: {
                        labels: [],
                        datasets: [{
                            data: [],
                            backgroundColor: [
                                'rgba(54, 162, 235, 0.8)',
                                'rgba(255, 99, 132, 0.8)',
                                'rgba(255, 205, 86, 0.8)',
                                'rgba(75, 192, 192, 0.8)'
                            ]
                        }]
                    },
                    options: {
                        responsive: true
                    }
                });
            }
        }

        // Aggiorna grafici
        function updateCharts() {
            if (performanceChart && agentsData.length > 0) {
                const labels = agentsData.map(agent => agent.name);
                const completed = agentsData.map(agent => agent.tasks_completed);
                const failed = agentsData.map(agent => agent.tasks_failed);

                performanceChart.data.labels = labels;
                performanceChart.data.datasets[0].data = completed;
                performanceChart.data.datasets[1].data = failed;
                performanceChart.update();
            }

            if (taskDistributionChart && agentsData.length > 0) {
                const labels = agentsData.map(agent => agent.name);
                const data = agentsData.map(agent => agent.tasks_completed + agent.tasks_failed);

                taskDistributionChart.data.labels = labels;
                taskDistributionChart.data.datasets[0].data = data;
                taskDistributionChart.update();
            }
        }

        // Refresh agenti
        function refreshAgents() {
            loadAgents();
        }

        // Mostra modal pulizia dati
        function showDataCleaningModal() {
            const modal = new bootstrap.Modal(document.getElementById('dataCleaningModal'));
            modal.show();
        }

        // Mostra modal export
        function showExportModal() {
            const modal = new bootstrap.Modal(document.getElementById('exportModal'));
            modal.show();
        }

        // Sottometti task pulizia dati
        async function submitDataCleaning() {
            const fileId = document.getElementById('fileId').value;
            const operation = document.getElementById('operation').value;
            const priority = document.getElementById('priority').value;

            if (!fileId) {
                showAlert('ID File richiesto', 'warning');
                return;
            }

            try {
                const response = await fetch('/api/agents/submit-task', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        agent_type: 'data_cleaning',
                        priority: priority,
                        task_data: {
                            file_id: fileId,
                            operation: operation
                        }
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    showAlert(`Task sottomesso con successo. ID: ${data.task_id}`, 'success');
                    bootstrap.Modal.getInstance(document.getElementById('dataCleaningModal')).hide();
                    document.getElementById('dataCleaningForm').reset();
                } else {
                    showAlert('Errore sottomissione task: ' + data.error, 'danger');
                }
            } catch (error) {
                showAlert('Errore connessione: ' + error.message, 'danger');
            }
        }

        // Sottometti task export
        async function submitExport() {
            const fileId = document.getElementById('exportFileId').value;
            const exportType = document.getElementById('exportType').value;
            const format = document.getElementById('format').value;

            if (!fileId) {
                showAlert('ID File richiesto', 'warning');
                return;
            }

            try {
                const response = await fetch('/api/agents/submit-task', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        agent_type: 'export_management',
                        priority: 'medium',
                        task_data: {
                            file_id: fileId,
                            export_type: exportType,
                            format: format
                        }
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    showAlert(`Task export sottomesso con successo. ID: ${data.task_id}`, 'success');
                    bootstrap.Modal.getInstance(document.getElementById('exportModal')).hide();
                    document.getElementById('exportForm').reset();
                } else {
                    showAlert('Errore sottomissione task: ' + data.error, 'danger');
                }
            } catch (error) {
                showAlert('Errore connessione: ' + error.message, 'danger');
            }
        }

        // Mostra health check
        async function showHealthCheck() {
            try {
                const response = await fetch('/api/agents/health');
                const data = await response.json();
                
                if (data.success) {
                    const health = data.health;
                    let message = `Status: ${health.orchestrator_status}\n`;
                    message += `Agenti: ${health.agents_count}\n`;
                    message += `Coda: ${health.queue_size}\n`;
                    message += `In esecuzione: ${health.running_tasks}\n`;
                    message += `Completati: ${health.completed_tasks}`;
                    
                    alert(message);
                } else {
                    showAlert('Errore health check: ' + data.error, 'danger');
                }
            } catch (error) {
                showAlert('Errore connessione: ' + error.message, 'danger');
            }
        }

        // Mostra alert
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);
            
            // Auto-remove dopo 5 secondi
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
