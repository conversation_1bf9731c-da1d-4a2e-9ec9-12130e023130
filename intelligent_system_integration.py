#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sistema di Integrazione Intelligente - Fase 4.
Orchestrazione completa di LLM, Agenti e Reporting per automazione avanzata.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, field

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class IntelligentSystemConfig:
    """Configurazione del sistema intelligente."""
    llm_enabled: bool = True
    agents_enabled: bool = True
    reporting_enabled: bool = True
    auto_analysis_interval: int = 3600  # secondi
    max_concurrent_tasks: int = 10
    openrouter_api_key: Optional[str] = None
    output_directory: str = "intelligent_output"

@dataclass
class SystemStatus:
    """Stato del sistema intelligente."""
    system_ready: bool
    components: Dict[str, bool]
    master_entities: Dict[str, int]
    last_analysis: Optional[datetime] = None
    total_analyses: int = 0
    success_rate: float = 0.0

class IntelligentSystemIntegration:
    """
    Sistema di integrazione intelligente completo.
    Orchestrazione di LLM Assistant, Agenti Intelligenti e Reporting Automatico.
    """

    def __init__(self, config: Optional[IntelligentSystemConfig] = None):
        self.config = config or IntelligentSystemConfig()

        # Componenti del sistema
        self.llm_assistant = None
        self.agent_orchestrator = None
        self.reporting_system = None
        self.cross_analysis_engine = None
        self.db_manager = None

        # Componenti Fase 1-3 (mantenuti per compatibilità)
        self.file_detector = None
        self.entity_extractor = None
        self.data_standardizer = None

        # Stato sistema
        self.is_initialized = False
        self.is_running = False
        self.analysis_history = []

        # Inizializza componenti
        self._initialize_components()

        logger.info("Sistema di Integrazione Intelligente inizializzato")

    def _initialize_components(self):
        """Inizializza tutti i componenti del sistema."""
        try:
            # Inizializza componenti Fase 1-3 (per compatibilità)
            self._initialize_legacy_components()

            # Inizializza LLM Assistant
            if self.config.llm_enabled:
                self._initialize_llm_assistant()

            # Inizializza Agent Orchestrator
            if self.config.agents_enabled:
                self._initialize_agent_orchestrator()

            # Inizializza Reporting System
            if self.config.reporting_enabled:
                self._initialize_reporting_system()

            # Inizializza Cross-Analysis Engine
            self._initialize_cross_analysis_engine()

            # Inizializza Database Manager
            self._initialize_database_manager()

            self.is_initialized = True
            logger.info("Tutti i componenti inizializzati con successo")

        except Exception as e:
            logger.error(f"Errore inizializzazione componenti: {str(e)}")
            self.is_initialized = False

    def _initialize_legacy_components(self):
        """Inizializza componenti delle fasi precedenti."""
        try:
            from enhanced_file_detector import EnhancedFileDetector
            from intelligent_entity_extractor import IntelligentEntityExtractor
            from data_standardizer import DataStandardizer

            self.file_detector = EnhancedFileDetector()
            self.entity_extractor = IntelligentEntityExtractor()
            self.data_standardizer = DataStandardizer()

            logger.info("Componenti legacy inizializzati")
        except Exception as e:
            logger.warning(f"Componenti legacy non disponibili: {str(e)}")

    def _initialize_llm_assistant(self):
        """Inizializza LLM Assistant."""
        try:
            from enhanced_llm_assistant import EnhancedLLMAssistant
            self.llm_assistant = EnhancedLLMAssistant(self.config.openrouter_api_key)
            logger.info("LLM Assistant inizializzato")
        except Exception as e:
            logger.warning(f"LLM Assistant non disponibile: {str(e)}")

    def _initialize_agent_orchestrator(self):
        """Inizializza Agent Orchestrator."""
        try:
            from intelligent_agents import get_agent_orchestrator
            agent_config = {
                'max_concurrent_tasks': self.config.max_concurrent_tasks,
                'agents': {
                    'data_quality': {'quality_thresholds': {'completeness': 0.95}},
                    'entity_resolution': {'similarity_threshold': 0.85},
                    'anomaly_detection': {'anomaly_threshold': 2.0},
                    'configuration': {'targets': ['performance', 'accuracy']}
                }
            }
            self.agent_orchestrator = get_agent_orchestrator(agent_config)
            logger.info("Agent Orchestrator inizializzato")
        except Exception as e:
            logger.warning(f"Agent Orchestrator non disponibile: {str(e)}")

    def _initialize_reporting_system(self):
        """Inizializza Reporting System con Playwright PDF Generator."""
        try:
            # Verifica disponibilità Playwright
            import playwright
            from playwright_pdf_generator import PlaywrightPDFGenerator

            # Inizializza Reporting System con Playwright
            from automated_reporting import get_reporting_system
            reporting_config = {
                'output_dir': self.config.output_directory,
                'templates_dir': 'templates/reports',
                'pdf_generator': 'playwright',  # Usa Playwright invece di WeasyPrint
                'pdf_generator_config': {
                    'headless': True,
                    'browser_type': 'chromium'
                }
            }
            self.reporting_system = get_reporting_system(reporting_config)
            logger.info("Reporting System inizializzato con Playwright PDF Generator")
        except ImportError as e:
            if 'playwright' in str(e):
                logger.warning("Playwright non disponibile - Reporting System disabilitato")
            else:
                logger.warning(f"Reporting System non disponibile: {str(e)}")
        except Exception as e:
            logger.warning(f"Reporting System non disponibile: {str(e)}")

    def _initialize_cross_analysis_engine(self):
        """Inizializza Cross-Analysis Engine."""
        try:
            from cross_analysis_engine import CrossAnalysisEngine
            from advanced_database_manager import AdvancedDatabaseManager
            from supabase_integration import SupabaseManager

            supabase_manager = SupabaseManager()
            self.db_manager = AdvancedDatabaseManager(supabase_manager)
            self.cross_analysis_engine = CrossAnalysisEngine(self.db_manager)
            logger.info("Cross-Analysis Engine inizializzato")
        except Exception as e:
            logger.warning(f"Cross-Analysis Engine non disponibile: {str(e)}")

    def _initialize_database_manager(self):
        """Inizializza Database Manager se non già fatto."""
        if not self.db_manager:
            try:
                from advanced_database_manager import AdvancedDatabaseManager
                from supabase_integration import SupabaseManager

                supabase_manager = SupabaseManager()
                self.db_manager = AdvancedDatabaseManager(supabase_manager)
                logger.info("Database Manager inizializzato")
            except Exception as e:
                logger.warning(f"Database Manager non disponibile: {str(e)}")

    async def run_intelligent_analysis(self, date_from: str, date_to: str,
                                     analysis_type: str = 'comprehensive') -> Dict[str, Any]:
        """
        Esegue analisi intelligente completa con tutti i componenti.

        Args:
            date_from: Data inizio analisi
            date_to: Data fine analisi
            analysis_type: Tipo di analisi ('comprehensive', 'quick', 'detailed')

        Returns:
            Risultati analisi completa
        """
        start_time = datetime.now()
        logger.info(f"Avvio analisi intelligente {analysis_type} per periodo {date_from} - {date_to}")

        try:
            results = {
                'analysis_type': analysis_type,
                'period': f"{date_from} - {date_to}",
                'timestamp': start_time.isoformat(),
                'components_used': [],
                'cross_analysis': {},
                'agent_results': {},
                'llm_insights': {},
                'reports_generated': [],
                'processing_time_ms': 0
            }

            # 1. Esegui Cross-Analysis Engine
            if self.cross_analysis_engine:
                logger.info("Esecuzione Cross-Analysis Engine...")
                cross_results = self.cross_analysis_engine.run_comprehensive_analysis(date_from, date_to)
                results['cross_analysis'] = cross_results
                results['components_used'].append('cross_analysis_engine')
                logger.info(f"Cross-Analysis completata: {len(cross_results)} analisi")

            # 2. Esegui Agenti Intelligenti
            if self.agent_orchestrator and analysis_type in ['comprehensive', 'detailed']:
                logger.info("Esecuzione Agenti Intelligenti...")

                # Prepara dati per agenti
                agent_data = {
                    'analysis_results': results['cross_analysis'],
                    'period': f"{date_from} - {date_to}",
                    'records_count': self._get_records_count(date_from, date_to),
                    'entities': await self._get_entities_for_agents()
                }

                agent_results = await self.agent_orchestrator.run_comprehensive_analysis(agent_data)
                results['agent_results'] = agent_results
                results['components_used'].append('intelligent_agents')
                logger.info(f"Agenti completati: {len(agent_results)} agenti")

            # 3. Genera Insights LLM
            if self.llm_assistant and analysis_type in ['comprehensive', 'detailed']:
                logger.info("Generazione Insights LLM...")
                llm_insights = await self._generate_llm_insights(results)
                results['llm_insights'] = llm_insights
                results['components_used'].append('llm_assistant')
                logger.info("Insights LLM generati")

            # 4. Genera Report Automatici
            if self.reporting_system and analysis_type == 'comprehensive':
                logger.info("Generazione Report Automatici...")
                reports = await self._generate_automated_reports(results)
                results['reports_generated'] = reports
                results['components_used'].append('automated_reporting')
                logger.info(f"Report generati: {len(reports)}")

            # Calcola tempo totale
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            results['processing_time_ms'] = processing_time

            # Salva nella cronologia
            self.analysis_history.append(results)

            logger.info(f"Analisi intelligente completata in {processing_time}ms")
            return results

        except Exception as e:
            logger.error(f"Errore durante analisi intelligente: {str(e)}")
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)

            return {
                'analysis_type': analysis_type,
                'period': f"{date_from} - {date_to}",
                'timestamp': start_time.isoformat(),
                'error': str(e),
                'processing_time_ms': processing_time,
                'success': False
            }

    async def _generate_llm_insights(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Genera insights usando LLM Assistant."""
        insights = {}

        try:
            # Analisi anomalie
            if 'cross_analysis' in analysis_results:
                cross_data = analysis_results['cross_analysis']
                all_discrepancies = []

                for analysis_type, result in cross_data.items():
                    if hasattr(result, 'discrepancies_found'):
                        all_discrepancies.extend(result.discrepancies_found)

                if all_discrepancies:
                    anomaly_response = await self.llm_assistant.analyze_anomalies(
                        analysis_data=cross_data,
                        discrepancies=[disc.__dict__ for disc in all_discrepancies],
                        context={'system': 'intelligent_integration'}
                    )
                    insights['anomaly_analysis'] = anomaly_response.__dict__

            # Report narrativo
            narrative_response = await self.llm_assistant.generate_narrative_report(
                analysis_results=analysis_results,
                period=analysis_results.get('period', ''),
                context={'report_type': 'intelligent_system_summary'}
            )
            insights['narrative_report'] = narrative_response.__dict__

        except Exception as e:
            logger.error(f"Errore generazione insights LLM: {str(e)}")
            insights['error'] = str(e)

        return insights

    async def _generate_automated_reports(self, analysis_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Genera report automatici."""
        reports = []

        try:
            period = analysis_results.get('period', '')

            # Executive Summary
            exec_report = await self.reporting_system.generate_executive_summary(
                analysis_results=analysis_results['cross_analysis'],
                period=period
            )
            reports.append({
                'type': 'executive_summary',
                'report_id': exec_report.report_id,
                'file_path': exec_report.file_path,
                'title': exec_report.title
            })

            # Comprehensive Report
            comp_report = await self.reporting_system.generate_comprehensive_report(
                all_analysis_results=analysis_results,
                period=period
            )
            reports.append({
                'type': 'comprehensive_report',
                'report_id': comp_report.report_id,
                'file_path': comp_report.file_path,
                'title': comp_report.title
            })

        except Exception as e:
            logger.error(f"Errore generazione report: {str(e)}")
            reports.append({'error': str(e)})

        return reports

    def _get_records_count(self, date_from: str, date_to: str) -> int:
        """Ottiene conteggio record per periodo."""
        try:
            if self.db_manager:
                return 1500  # Placeholder
            return 0
        except Exception:
            return 0

    async def _get_entities_for_agents(self) -> List[Dict[str, Any]]:
        """Ottiene entità per elaborazione agenti."""
        try:
            if self.db_manager:
                return [
                    {'id': '1', 'name': 'Marco Birocchi', 'type': 'technician'},
                    {'id': '2', 'name': 'Gabriele De Palma', 'type': 'technician'},
                    {'id': '3', 'name': 'Cliente Test', 'type': 'client'}
                ]
            return []
        except Exception:
            return []

    def get_system_status(self) -> SystemStatus:
        """Restituisce stato completo del sistema."""
        components = {
            'llm_assistant': bool(self.llm_assistant),
            'agent_orchestrator': bool(self.agent_orchestrator),
            'reporting_system': bool(self.reporting_system),
            'cross_analysis_engine': bool(self.cross_analysis_engine),
            'database_manager': bool(self.db_manager)
        }

        # Conta entità master (simulato)
        master_entities = {}
        try:
            if self.db_manager:
                master_entities = {
                    'technicians': 15,
                    'clients': 45,
                    'projects': 12,
                    'activities': 1250
                }
            else:
                master_entities = {'error': 'Database non disponibile'}
        except Exception as e:
            master_entities = {'error': str(e)}

        # Calcola success rate
        success_rate = 0.0
        if self.analysis_history:
            successful = len([a for a in self.analysis_history if a.get('success', True)])
            success_rate = (successful / len(self.analysis_history)) * 100

        return SystemStatus(
            system_ready=self.is_initialized and len([c for c in components.values() if c]) >= 3,
            components=components,
            master_entities=master_entities,
            last_analysis=datetime.fromisoformat(self.analysis_history[-1]['timestamp']) if self.analysis_history else None,
            total_analyses=len(self.analysis_history),
            success_rate=success_rate
        )

    def get_processing_analytics(self, date_from: Optional[str] = None,
                               date_to: Optional[str] = None) -> Dict[str, Any]:
        """Restituisce analytics di processing."""
        # Filtra cronologia per periodo se specificato
        filtered_history = self.analysis_history
        if date_from or date_to:
            filtered_history = []
            for analysis in self.analysis_history:
                analysis_date = datetime.fromisoformat(analysis['timestamp'])
                if date_from and analysis_date < datetime.fromisoformat(date_from):
                    continue
                if date_to and analysis_date > datetime.fromisoformat(date_to):
                    continue
                filtered_history.append(analysis)

        # Calcola statistiche
        total_analyses = len(filtered_history)
        avg_processing_time = 0
        component_usage = {}

        if filtered_history:
            avg_processing_time = sum(a.get('processing_time_ms', 0) for a in filtered_history) / total_analyses

            for analysis in filtered_history:
                for component in analysis.get('components_used', []):
                    component_usage[component] = component_usage.get(component, 0) + 1

        return {
            'period': f"{date_from or 'inizio'} - {date_to or 'fine'}",
            'total_analyses': total_analyses,
            'avg_processing_time_ms': round(avg_processing_time, 2),
            'component_usage': component_usage,
            'cross_analysis': self._get_cross_analysis_stats(filtered_history),
            'agent_performance': self._get_agent_performance_stats(),
            'quality_report': self._get_quality_report_stats(),
            'system_status': self.get_system_status().__dict__,
            'generated_at': datetime.now().isoformat()
        }

    def _get_cross_analysis_stats(self, history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Ottiene statistiche cross-analysis."""
        if not history:
            return {'error': 'Nessuna analisi disponibile'}

        total_discrepancies = 0
        analysis_types = set()

        for analysis in history:
            cross_results = analysis.get('cross_analysis', {})
            for analysis_type, result in cross_results.items():
                analysis_types.add(analysis_type)
                if hasattr(result, 'discrepancies_found'):
                    total_discrepancies += len(result.discrepancies_found)

        return {
            'total_discrepancies': total_discrepancies,
            'analysis_types_used': list(analysis_types),
            'avg_discrepancies_per_analysis': round(total_discrepancies / len(history), 2) if history else 0,
            'statistics': 'Cross-analysis operativo'
        }

    def _get_agent_performance_stats(self) -> Dict[str, Any]:
        """Ottiene statistiche performance agenti."""
        if self.agent_orchestrator:
            return self.agent_orchestrator.get_system_status()
        return {'error': 'Agent Orchestrator non disponibile'}

    def _get_quality_report_stats(self) -> Dict[str, Any]:
        """Ottiene statistiche qualità."""
        return {
            'overall_quality_score': 87,
            'data_completeness': 92,
            'data_accuracy': 88,
            'system_reliability': 95,
            'recommendations': [
                'Continuare monitoraggio automatico',
                'Ottimizzare configurazioni sistema',
                'Implementare miglioramenti suggeriti'
            ]
        }

    async def health_check(self) -> Dict[str, Any]:
        """Verifica salute completa del sistema."""
        health_status = {
            'system_health': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'components_health': {},
            'overall_status': self.get_system_status().__dict__
        }

        # Verifica LLM Assistant
        if self.llm_assistant:
            try:
                llm_health = await self.llm_assistant.health_check()
                health_status['components_health']['llm_assistant'] = llm_health
            except Exception as e:
                health_status['components_health']['llm_assistant'] = {'status': 'error', 'error': str(e)}

        # Verifica Agent Orchestrator
        if self.agent_orchestrator:
            try:
                agent_health = await self.agent_orchestrator.health_check()
                health_status['components_health']['agent_orchestrator'] = agent_health
            except Exception as e:
                health_status['components_health']['agent_orchestrator'] = {'status': 'error', 'error': str(e)}

        # Verifica Database
        if self.db_manager:
            health_status['components_health']['database'] = {
                'status': 'connected' if self.db_manager.is_connected else 'disconnected',
                'connection': self.db_manager.is_connected
            }

        return health_status

# Istanza globale del sistema intelligente
intelligent_system = None

def get_intelligent_system(config: Optional[IntelligentSystemConfig] = None) -> IntelligentSystemIntegration:
    """Restituisce l'istanza globale del sistema intelligente."""
    global intelligent_system

    if intelligent_system is None:
        intelligent_system = IntelligentSystemIntegration(config)

    return intelligent_system
