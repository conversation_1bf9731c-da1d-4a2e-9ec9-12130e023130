#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sistema di Automazione Intelligente per app-roberto.
Utilizza gli agenti AI per automatizzare processi complessi.
"""

import os
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import asyncio
import json

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AutomationRule:
    """
    Rappresenta una regola di automazione.
    """
    
    def __init__(self, rule_id: str, name: str, trigger: Dict[str, Any], 
                 actions: List[Dict[str, Any]], conditions: Optional[List[Dict[str, Any]]] = None):
        """
        Inizializza una regola di automazione.
        
        Args:
            rule_id: ID univoco della regola
            name: Nome descrittivo della regola
            trigger: Condizioni che attivano la regola
            actions: Azioni da eseguire quando la regola si attiva
            conditions: Condizioni aggiuntive da verificare
        """
        self.rule_id = rule_id
        self.name = name
        self.trigger = trigger
        self.actions = actions
        self.conditions = conditions or []
        self.is_active = True
        self.last_triggered = None
        self.execution_count = 0
        self.success_count = 0

class IntelligentAutomation:
    """
    Sistema di automazione intelligente che coordina agenti AI.
    """
    
    def __init__(self):
        """
        Inizializza il sistema di automazione.
        """
        self.rules = {}
        self.is_running = False
        self.monitoring_interval = 30  # secondi
        
        # Inizializza agenti
        try:
            from ai_agents_framework import agent_orchestrator
            self.orchestrator = agent_orchestrator
        except ImportError:
            logger.error("Framework agenti AI non disponibile")
            self.orchestrator = None
        
        # Carica regole predefinite
        self._load_default_rules()
    
    def _load_default_rules(self):
        """
        Carica le regole di automazione predefinite.
        """
        # Regola 1: Analisi automatica file caricati
        self.add_rule(AutomationRule(
            rule_id="auto_file_analysis",
            name="Analisi Automatica File Caricati",
            trigger={
                "type": "file_upload",
                "file_types": ["xlsx", "xls", "csv"]
            },
            actions=[
                {
                    "agent": "file_analysis",
                    "task": {
                        "operation": "analyze_file_enhanced",
                        "save_to_cloud": True
                    }
                }
            ]
        ))
        
        # Regola 2: Generazione report giornaliero
        self.add_rule(AutomationRule(
            rule_id="daily_report",
            name="Generazione Report Giornaliero",
            trigger={
                "type": "schedule",
                "schedule": "daily",
                "time": "18:00"
            },
            actions=[
                {
                    "agent": "data_processing",
                    "task": {
                        "operation": "analyze",
                        "data_source": "daily_activities"
                    }
                },
                {
                    "agent": "report_generation",
                    "task": {
                        "report_type": "daily_summary",
                        "include_charts": True
                    }
                }
            ]
        ))
        
        # Regola 3: Pulizia automatica file vecchi
        self.add_rule(AutomationRule(
            rule_id="cleanup_old_files",
            name="Pulizia File Vecchi",
            trigger={
                "type": "schedule",
                "schedule": "weekly",
                "day": "sunday",
                "time": "02:00"
            },
            actions=[
                {
                    "agent": "data_processing",
                    "task": {
                        "operation": "cleanup",
                        "older_than_days": 30
                    }
                }
            ]
        ))
        
        # Regola 4: Analisi qualità dati
        self.add_rule(AutomationRule(
            rule_id="data_quality_check",
            name="Controllo Qualità Dati",
            trigger={
                "type": "data_processed",
                "min_rows": 100
            },
            actions=[
                {
                    "agent": "data_processing",
                    "task": {
                        "operation": "quality_check",
                        "generate_report": True
                    }
                }
            ],
            conditions=[
                {
                    "type": "data_size",
                    "operator": "greater_than",
                    "value": 50
                }
            ]
        ))
        
        logger.info(f"Caricate {len(self.rules)} regole di automazione predefinite")
    
    def add_rule(self, rule: AutomationRule):
        """
        Aggiunge una regola di automazione.
        
        Args:
            rule: Regola da aggiungere
        """
        self.rules[rule.rule_id] = rule
        logger.info(f"Aggiunta regola di automazione: {rule.name}")
    
    def remove_rule(self, rule_id: str) -> bool:
        """
        Rimuove una regola di automazione.
        
        Args:
            rule_id: ID della regola da rimuovere
            
        Returns:
            bool: True se rimossa con successo
        """
        if rule_id in self.rules:
            del self.rules[rule_id]
            logger.info(f"Rimossa regola di automazione: {rule_id}")
            return True
        return False
    
    def get_rule(self, rule_id: str) -> Optional[AutomationRule]:
        """
        Ottiene una regola specifica.
        
        Args:
            rule_id: ID della regola
            
        Returns:
            AutomationRule: Regola trovata o None
        """
        return self.rules.get(rule_id)
    
    def list_rules(self) -> List[Dict[str, Any]]:
        """
        Lista tutte le regole di automazione.
        
        Returns:
            List: Lista delle regole
        """
        return [
            {
                "rule_id": rule.rule_id,
                "name": rule.name,
                "is_active": rule.is_active,
                "last_triggered": rule.last_triggered,
                "execution_count": rule.execution_count,
                "success_rate": rule.success_count / max(rule.execution_count, 1)
            }
            for rule in self.rules.values()
        ]
    
    async def trigger_event(self, event_type: str, event_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Attiva un evento che può scatenare regole di automazione.
        
        Args:
            event_type: Tipo di evento
            event_data: Dati dell'evento
            
        Returns:
            List: Risultati delle regole attivate
        """
        results = []
        
        for rule in self.rules.values():
            if not rule.is_active:
                continue
            
            # Verifica se l'evento attiva questa regola
            if self._matches_trigger(rule.trigger, event_type, event_data):
                # Verifica condizioni aggiuntive
                if self._check_conditions(rule.conditions, event_data):
                    logger.info(f"Attivazione regola: {rule.name}")
                    
                    # Esegui azioni
                    rule_results = await self._execute_rule_actions(rule, event_data)
                    results.extend(rule_results)
                    
                    # Aggiorna statistiche regola
                    rule.execution_count += 1
                    rule.last_triggered = datetime.now().isoformat()
                    
                    # Conta successi
                    if all(r.get("success", False) for r in rule_results):
                        rule.success_count += 1
        
        return results
    
    def _matches_trigger(self, trigger: Dict[str, Any], event_type: str, event_data: Dict[str, Any]) -> bool:
        """
        Verifica se un evento corrisponde al trigger di una regola.
        
        Args:
            trigger: Configurazione trigger della regola
            event_type: Tipo di evento
            event_data: Dati dell'evento
            
        Returns:
            bool: True se l'evento attiva il trigger
        """
        if trigger.get("type") != event_type:
            return False
        
        # Verifica condizioni specifiche del trigger
        if event_type == "file_upload":
            file_types = trigger.get("file_types", [])
            if file_types:
                file_extension = event_data.get("file_extension", "").lower()
                return file_extension in file_types
        
        elif event_type == "data_processed":
            min_rows = trigger.get("min_rows", 0)
            actual_rows = event_data.get("rows", 0)
            return actual_rows >= min_rows
        
        elif event_type == "schedule":
            # Per eventi schedulati, la logica di verifica sarebbe più complessa
            # Qui semplifichiamo
            return True
        
        return True
    
    def _check_conditions(self, conditions: List[Dict[str, Any]], event_data: Dict[str, Any]) -> bool:
        """
        Verifica le condizioni aggiuntive di una regola.
        
        Args:
            conditions: Lista delle condizioni da verificare
            event_data: Dati dell'evento
            
        Returns:
            bool: True se tutte le condizioni sono soddisfatte
        """
        for condition in conditions:
            condition_type = condition.get("type")
            operator = condition.get("operator")
            value = condition.get("value")
            
            if condition_type == "data_size":
                actual_size = event_data.get("data_size", 0)
                
                if operator == "greater_than" and actual_size <= value:
                    return False
                elif operator == "less_than" and actual_size >= value:
                    return False
                elif operator == "equals" and actual_size != value:
                    return False
        
        return True
    
    async def _execute_rule_actions(self, rule: AutomationRule, event_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Esegue le azioni di una regola.
        
        Args:
            rule: Regola da eseguire
            event_data: Dati dell'evento scatenante
            
        Returns:
            List: Risultati delle azioni
        """
        results = []
        
        if not self.orchestrator:
            logger.error("Orchestratore agenti non disponibile")
            return [{"success": False, "error": "Orchestratore non disponibile"}]
        
        for action in rule.actions:
            agent_name = action.get("agent")
            task_data = action.get("task", {})
            
            # Merge dei dati dell'evento nel task
            merged_task = {**task_data, **event_data}
            
            try:
                result = await self.orchestrator.execute_task(agent_name, merged_task)
                result["rule_id"] = rule.rule_id
                result["action"] = action
                results.append(result)
                
                logger.info(f"Azione eseguita per regola {rule.name}: {agent_name}")
                
            except Exception as e:
                error_result = {
                    "success": False,
                    "error": str(e),
                    "rule_id": rule.rule_id,
                    "action": action
                }
                results.append(error_result)
                logger.error(f"Errore esecuzione azione per regola {rule.name}: {str(e)}")
        
        return results
    
    async def start_monitoring(self):
        """
        Avvia il monitoraggio automatico per eventi schedulati.
        """
        self.is_running = True
        logger.info("Avviato monitoraggio automazione intelligente")
        
        while self.is_running:
            try:
                # Verifica eventi schedulati
                await self._check_scheduled_events()
                
                # Attendi prima del prossimo controllo
                await asyncio.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"Errore nel monitoraggio automazione: {str(e)}")
                await asyncio.sleep(self.monitoring_interval)
    
    def stop_monitoring(self):
        """
        Ferma il monitoraggio automatico.
        """
        self.is_running = False
        logger.info("Fermato monitoraggio automazione intelligente")
    
    async def _check_scheduled_events(self):
        """
        Verifica e attiva eventi schedulati.
        """
        current_time = datetime.now()
        
        for rule in self.rules.values():
            if not rule.is_active:
                continue
            
            trigger = rule.trigger
            if trigger.get("type") != "schedule":
                continue
            
            # Logica semplificata per eventi schedulati
            # In una implementazione reale, useresti una libreria come APScheduler
            schedule_type = trigger.get("schedule")
            
            if schedule_type == "daily":
                # Verifica se è il momento giusto per l'esecuzione giornaliera
                target_time = trigger.get("time", "00:00")
                # Implementazione semplificata
                pass
            
            elif schedule_type == "weekly":
                # Verifica se è il giorno e l'ora giusti per l'esecuzione settimanale
                target_day = trigger.get("day", "monday")
                target_time = trigger.get("time", "00:00")
                # Implementazione semplificata
                pass
    
    def get_automation_stats(self) -> Dict[str, Any]:
        """
        Ottiene statistiche del sistema di automazione.
        
        Returns:
            Dict: Statistiche di automazione
        """
        total_rules = len(self.rules)
        active_rules = sum(1 for rule in self.rules.values() if rule.is_active)
        total_executions = sum(rule.execution_count for rule in self.rules.values())
        total_successes = sum(rule.success_count for rule in self.rules.values())
        
        return {
            "total_rules": total_rules,
            "active_rules": active_rules,
            "inactive_rules": total_rules - active_rules,
            "total_executions": total_executions,
            "total_successes": total_successes,
            "overall_success_rate": total_successes / max(total_executions, 1),
            "is_monitoring": self.is_running,
            "monitoring_interval": self.monitoring_interval
        }

# Istanza globale del sistema di automazione
intelligent_automation = IntelligentAutomation()
