/**
 * Setup Wizard CSS - App Roberto
 * <PERSON>ili per il wizard di configurazione guidata
 * Versione: 1.0.0
 */

/* ===== THEME TOGGLE FIXED ===== */
.theme-toggle-fixed {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
}

.theme-toggle-fixed .btn {
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.theme-toggle-fixed .btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* ===== WIZARD CONTAINER ===== */
.wizard-container {
    max-width: 900px;
    width: 100%;
    margin: 0 auto;
    padding: 2rem;
}

/* ===== WIZARD HEADER ===== */
.wizard-header {
    margin-bottom: 2rem;
}

.wizard-logo {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* ===== PROGRESS BAR ===== */
.wizard-progress {
    margin-bottom: 2rem;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.progress-steps::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--border-color, #dee2e6);
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--bg-primary, #ffffff);
    border: 2px solid var(--border-color, #dee2e6);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    justify-content: center;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
    cursor: pointer;
}

.step i {
    font-size: 1.2rem;
    color: var(--text-muted, #6c757d);
    transition: color 0.3s ease;
}

.step span {
    font-size: 0.75rem;
    font-weight: 500;
    margin-top: 0.5rem;
    color: var(--text-muted, #6c757d);
    transition: color 0.3s ease;
}

.step.active {
    border-color: var(--accent-primary, #0d6efd);
    background-color: var(--accent-primary, #0d6efd);
    transform: scale(1.1);
}

.step.active i,
.step.active span {
    color: white;
}

.step.completed {
    border-color: var(--accent-success, #198754);
    background-color: var(--accent-success, #198754);
}

.step.completed i,
.step.completed span {
    color: white;
}

/* ===== WIZARD CONTENT ===== */
.wizard-content {
    min-height: 500px;
    position: relative;
}

.wizard-step {
    display: none;
    animation: fadeInUp 0.5s ease;
}

.wizard-step.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== CARDS ===== */
.card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-bottom: none;
    padding: 1.5rem 2rem;
}

/* ===== FEATURE ITEMS ===== */
.feature-item {
    padding: 1rem;
    border-radius: 10px;
    background-color: var(--bg-secondary, #f8f9fa);
    transition: background-color 0.3s ease;
}

.feature-item:hover {
    background-color: var(--bg-tertiary, #e9ecef);
}

/* ===== UPLOAD AREA ===== */
.upload-area {
    border: 3px dashed var(--border-color, #dee2e6);
    border-radius: 15px;
    padding: 3rem 2rem;
    text-align: center;
    background-color: var(--bg-secondary, #f8f9fa);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-area:hover {
    border-color: var(--accent-primary, #0d6efd);
    background-color: rgba(13, 110, 253, 0.05);
    transform: scale(1.02);
}

.upload-area.dragover {
    border-color: var(--accent-success, #198754);
    background-color: rgba(25, 135, 84, 0.1);
    transform: scale(1.05);
}

.upload-content {
    position: relative;
    z-index: 2;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(13, 110, 253, 0.1), transparent);
    transform: rotate(45deg);
    transition: transform 0.6s ease;
    z-index: 1;
}

.upload-area:hover::before {
    transform: rotate(45deg) translate(50%, 50%);
}

.supported-formats .badge {
    margin: 0.2rem;
    font-size: 0.8rem;
}

/* ===== FILE LIST ===== */
.file-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--border-color, #dee2e6);
    border-radius: 10px;
    margin-bottom: 0.5rem;
    background-color: var(--card-bg, #ffffff);
    transition: all 0.3s ease;
}

.file-item:hover {
    background-color: var(--bg-secondary, #f8f9fa);
    transform: translateX(5px);
}

.file-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.2rem;
    color: white;
}

.file-icon.excel { background-color: #217346; }
.file-icon.csv { background-color: #28a745; }
.file-icon.json { background-color: #ffc107; color: #000; }
.file-icon.txt { background-color: #6c757d; }

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: var(--text-primary, #212529);
}

.file-size {
    font-size: 0.875rem;
    color: var(--text-muted, #6c757d);
}

.file-actions {
    display: flex;
    gap: 0.5rem;
}

/* ===== DATA PREVIEW ===== */
.preview-content {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-color, #dee2e6);
    border-radius: 10px;
    padding: 1rem;
    background-color: var(--card-bg, #ffffff);
}

.preview-table {
    font-size: 0.875rem;
}

.preview-table th {
    background-color: var(--accent-primary, #0d6efd);
    color: white;
    border: none;
    padding: 0.75rem 0.5rem;
}

.preview-table td {
    padding: 0.5rem;
    border-bottom: 1px solid var(--border-light, #e9ecef);
}

/* ===== CONFIGURATION SECTIONS ===== */
.config-section,
.team-section,
.vehicle-section {
    background-color: var(--bg-secondary, #f8f9fa);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-light, #e9ecef);
}

.analysis-item,
.automation-item,
.employee-item,
.vehicle-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    border-radius: 8px;
    background-color: var(--card-bg, #ffffff);
    margin-bottom: 0.5rem;
    border: 1px solid var(--border-light, #e9ecef);
    transition: all 0.3s ease;
}

.analysis-item:hover,
.automation-item:hover,
.employee-item:hover,
.vehicle-item:hover {
    background-color: var(--bg-tertiary, #e9ecef);
    transform: translateX(3px);
}

.item-info {
    flex: 1;
}

.item-title {
    font-weight: 600;
    color: var(--text-primary, #212529);
    margin-bottom: 0.25rem;
}

.item-description {
    font-size: 0.875rem;
    color: var(--text-muted, #6c757d);
    margin: 0;
}

.item-actions {
    display: flex;
    gap: 0.5rem;
}

/* ===== COMPLETION ANIMATION ===== */
.completion-animation {
    animation: bounceIn 1s ease;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.setup-summary {
    background-color: var(--bg-secondary, #f8f9fa);
    border-radius: 10px;
    padding: 1.5rem;
    margin: 2rem 0;
    border: 1px solid var(--border-light, #e9ecef);
}

/* ===== WIZARD NAVIGATION ===== */
.wizard-navigation {
    padding-top: 2rem;
    border-top: 1px solid var(--border-color, #dee2e6);
}

.wizard-navigation .btn {
    min-width: 120px;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.wizard-navigation .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Navigation Layout */
.wizard-navigation .d-flex {
    position: relative;
}

.nav-left, .nav-right {
    flex: 0 0 auto;
}

.nav-center {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Restart Button */
#restart-btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #17a2b8, #138496);
    border-color: #17a2b8;
    color: white;
    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.2);
}

#restart-btn:hover {
    background: linear-gradient(135deg, #138496, #117a8b);
    border-color: #138496;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
    color: white;
}

#restart-btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
    color: white;
}

#restart-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.2);
}

#restart-btn i {
    font-size: 0.8rem;
}

/* Dark theme per restart button */
[data-theme="dark"] #restart-btn {
    background: linear-gradient(135deg, #4dabf7, #339af0);
    border-color: #4dabf7;
    color: var(--text-primary);
}

[data-theme="dark"] #restart-btn:hover {
    background: linear-gradient(135deg, #339af0, #228be6);
    border-color: #339af0;
    color: var(--text-primary);
}

[data-theme="dark"] #restart-btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(77, 171, 247, 0.25);
    color: var(--text-primary);
}

/* ===== UTILITY CLASSES ===== */
.wizard-hidden {
    display: none !important;
}

.wizard-progress-bar {
    width: 0%;
}

/* ===== TOAST CONTAINER POSITIONING ===== */
#toast-container {
    position: fixed;
    top: 20px;
    right: 80px;
    z-index: 9999;
    max-width: 350px;
}

/* Responsive toast positioning */
@media (max-width: 768px) {
    #toast-container {
        top: 80px;
        right: 20px;
        left: 20px;
        max-width: none;
    }
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .wizard-container {
        padding: 1rem;
    }

    .progress-steps {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .step {
        width: 50px;
        height: 50px;
    }

    .step i {
        font-size: 1rem;
    }

    .step span {
        font-size: 0.7rem;
    }

    .upload-area {
        padding: 2rem 1rem;
    }

    .card-body {
        padding: 1.5rem !important;
    }

    /* Theme toggle responsive */
    .theme-toggle-fixed {
        top: 15px;
        right: 15px;
    }

    .theme-toggle-fixed .btn {
        width: 40px;
        height: 40px;
    }
}

@media (max-width: 576px) {
    .wizard-navigation .btn {
        min-width: 100px;
        padding: 0.5rem 1rem;
    }

    .progress-steps .step span {
        display: none;
    }
}

/* ===== DARK THEME OVERRIDES ===== */

/* Container principale wizard */
[data-theme="dark"] .wizard-container {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

/* Header wizard */
[data-theme="dark"] .wizard-header {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

/* Steps del wizard */
[data-theme="dark"] .wizard-steps {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .step {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-secondary);
}

[data-theme="dark"] .step.active {
    background-color: var(--accent-primary);
    color: var(--text-primary);
}

[data-theme="dark"] .step.completed {
    background-color: var(--accent-success);
    color: var(--text-primary);
}

/* Content del wizard */
[data-theme="dark"] .wizard-content {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

[data-theme="dark"] .wizard-step {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-primary);
}

/* Upload area */
[data-theme="dark"] .upload-area {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .upload-area:hover {
    background-color: rgba(77, 171, 247, 0.1);
    border-color: var(--accent-primary);
}

[data-theme="dark"] .upload-area .upload-icon {
    color: var(--text-secondary);
}

[data-theme="dark"] .upload-area h4 {
    color: var(--text-primary);
}

[data-theme="dark"] .upload-area p {
    color: var(--text-secondary);
}

/* Feature items */
[data-theme="dark"] .feature-item {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .feature-item .feature-icon {
    color: var(--accent-primary);
}

[data-theme="dark"] .feature-item h5 {
    color: var(--text-primary);
}

[data-theme="dark"] .feature-item p {
    color: var(--text-secondary);
}

/* Sezioni configurazione */
[data-theme="dark"] .config-section,
[data-theme="dark"] .team-section,
[data-theme="dark"] .vehicle-section {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .config-section h5,
[data-theme="dark"] .team-section h5,
[data-theme="dark"] .vehicle-section h5 {
    color: var(--text-primary);
}

/* Setup summary */
[data-theme="dark"] .setup-summary {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .setup-summary h4 {
    color: var(--text-primary);
}

[data-theme="dark"] .setup-summary .summary-item {
    color: var(--text-secondary);
}

/* File list */
[data-theme="dark"] .file-item {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .file-name {
    color: var(--text-primary);
}

[data-theme="dark"] .file-size {
    color: var(--text-secondary);
}

/* Progress bar */
[data-theme="dark"] .wizard-progress {
    background-color: var(--bg-secondary);
}

[data-theme="dark"] .wizard-progress-bar {
    background-color: var(--accent-primary);
}

/* Navigation buttons */
[data-theme="dark"] .wizard-navigation {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
}

/* Theme toggle fixed */
[data-theme="dark"] .theme-toggle-fixed {
    background-color: var(--bg-tertiary);
    border-color: var(--border-color);
}

/* Text colors generali */
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
    color: var(--text-primary);
}

[data-theme="dark"] p {
    color: var(--text-secondary);
}

[data-theme="dark"] .text-muted {
    color: var(--text-muted) !important;
}

/* ===== PROGRESS BAR WIZARD ===== */
.wizard-progress-value {
    transition: width 0.5s ease;
}

.wizard-progress-initial {
    width: 20%;
}






