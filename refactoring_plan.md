# Piano di Refactoring: Sistema di Rilevamento File Content-Based

## 🎯 Obiettivo
Trasformare il sistema da **filename-based** a **content-based** per gestire file grezzi originali.

## 🚨 Problemi Attuali Identificati

### 1. Rilevamento File (app.py:263-315)
- **CRITICO**: <PERSON>li forzati basati su nome file hanno precedenza
- **PROBLEMA**: `'teamviewer'`, `'calendario'`, `'attivita'` hardcoded
- **IMPATTO**: File grezzi (`export.csv`, `connectionreport.xlsx`) non riconosciuti

### 2. Mappature Multiple (3 sistemi diversi)
- `data_processor.py` (linee 27-59) - Mappatura generica
- `column_mapper.py` (linee 20-94) - Mappatura MCP specifica  
- Processori specifici - Mappature hardcoded

### 3. Processori Rigidi
- **TeamViewer**: Assume colonne `'Utente'`, `'Computer'`, `'Inizio'`, `'Fine'`
- **Calendario**: Assume colonne `'Data'`, `'Ora inizio'`, `'Ora fine'`
- **Timbrature**: Assume colonne `'Data'`, `'Dipendente'`, `'Entrata'`

### 4. Dashboard/KPI Dipendenti
- Assumono strutture post-elaborazione specifiche
- Non gestiscono variazioni nei dati originali

## 📋 Piano di Refactoring (4 Fasi)

### **FASE 1: Analisi e Preparazione** 🔍
**Durata**: 1-2 giorni
**Obiettivo**: Mappare completamente il sistema attuale

#### 1.1 Inventario Completo
- [ ] Catalogare tutti i punti di dipendenza dal nome file
- [ ] Documentare tutte le mappature colonne esistenti
- [ ] Identificare tutti i processori e le loro dipendenze
- [ ] Mappare dashboard, KPI e grafici dipendenti

#### 1.2 Analisi File Reali
- [ ] Raccogliere esempi di file grezzi reali
- [ ] Analizzare strutture e variazioni delle colonne
- [ ] Identificare pattern comuni nei dati originali
- [ ] Documentare differenze vs file pre-elaborati

#### 1.3 Design Nuovo Sistema
- [ ] Progettare sistema di rilevamento content-based
- [ ] Definire architettura unificata per mappature
- [ ] Pianificare sistema di standardizzazione automatica
- [ ] Progettare backward compatibility

### **FASE 2: Nuovo Sistema di Rilevamento** 🔧
**Durata**: 2-3 giorni
**Obiettivo**: Implementare rilevamento basato su contenuto

#### 2.1 Enhanced File Detector
- [ ] Estendere `file_detector.py` con pattern content-based
- [ ] Implementare rilevamento fuzzy per colonne simili
- [ ] Aggiungere supporto per varianti linguistiche
- [ ] Implementare confidence scoring avanzato

#### 2.2 Unified Column Mapper
- [ ] Creare sistema unificato di mappatura colonne
- [ ] Implementare mappature multiple per tipo file
- [ ] Aggiungere supporto per sinonimi e varianti
- [ ] Implementare auto-learning da esempi

#### 2.3 Smart Standardization Engine
- [ ] Implementare standardizzazione automatica date/ore
- [ ] Aggiungere normalizzazione nomi persone/aziende
- [ ] Implementare pulizia identificatori/ticket
- [ ] Aggiungere validazione e correzione automatica

### **FASE 3: Refactoring Processori** ⚙️
**Durata**: 3-4 giorni
**Obiettivo**: Adattare tutti i processori per file grezzi

#### 3.1 Processori Flessibili
- [ ] Refactoring `teamviewer_processor.py` per file grezzi
- [ ] Refactoring `calendar_processor.py` per varianti
- [ ] Refactoring `attendance_processor.py` per formati diversi
- [ ] Implementare fallback per colonne mancanti

#### 3.2 Data Validation & Cleaning
- [ ] Implementare validazione robusta dei dati
- [ ] Aggiungere pulizia automatica valori inconsistenti
- [ ] Implementare gestione errori graceful
- [ ] Aggiungere reporting dettagliato problemi

#### 3.3 Backward Compatibility
- [ ] Mantenere supporto per file pre-elaborati
- [ ] Implementare detection automatica formato
- [ ] Aggiungere migration path per dati esistenti
- [ ] Testare compatibilità con file attuali

### **FASE 4: Dashboard e KPI Adattivi** 📊
**Durata**: 2-3 giorni
**Obiettivo**: Rendere dashboard flessibili per qualsiasi struttura

#### 4.1 Dashboard Dinamiche
- [ ] Refactoring dashboard per strutture variabili
- [ ] Implementare KPI adattivi basati su dati disponibili
- [ ] Aggiungere grafici auto-configuranti
- [ ] Implementare layout responsivo ai dati

#### 4.2 Agenti AI Flessibili
- [ ] Adattare agenti per strutture dati variabili
- [ ] Implementare context-aware analysis
- [ ] Aggiungere auto-discovery di pattern
- [ ] Implementare suggerimenti intelligenti

#### 4.3 Testing e Validazione
- [ ] Test completi con file reali
- [ ] Validazione performance con dataset grandi
- [ ] Test stress con formati edge-case
- [ ] Validazione accuratezza vs sistema attuale

## 🎯 Deliverables per Fase

### Fase 1
- Documento di analisi completa
- Inventario dipendenze
- Esempi file reali catalogati
- Design architettura nuovo sistema

### Fase 2  
- `enhanced_file_detector.py` - Rilevamento content-based
- `unified_column_mapper.py` - Sistema mappature unificato
- `smart_standardizer.py` - Engine standardizzazione
- Test suite per nuovo sistema

### Fase 3
- Processori refactored e flessibili
- Sistema validazione e pulizia dati
- Backward compatibility layer
- Migration tools per dati esistenti

### Fase 4
- Dashboard adattive e responsive
- KPI dinamici basati su dati disponibili
- Agenti AI context-aware
- Test suite completa end-to-end

## 🔄 Approccio Iterativo
- **Sviluppo incrementale** con test continui
- **Backward compatibility** mantenuta sempre
- **Rollback plan** per ogni fase
- **Validation** con file reali ad ogni step

## 📊 Metriche di Successo
- **100%** file grezzi riconosciuti correttamente
- **≥95%** accuratezza mappatura colonne automatica
- **≤5%** degradazione performance
- **100%** backward compatibility mantenuta
