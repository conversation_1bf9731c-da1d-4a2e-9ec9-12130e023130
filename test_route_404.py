#!/usr/bin/env python3
"""
Test specifico per diagnosticare l'errore 404 dell'endpoint POST /api/get-processed-data
"""

import requests
import json
import sys
import time

def test_specific_route():
    """Test specifico per l'endpoint problematico"""
    
    print("🔍 DIAGNOSI ERRORE 404: POST /api/get-processed-data")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test 1: Verifica connettività base
    print("1️⃣ Test connettività base...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   Homepage: {response.status_code} ({'✅' if response.status_code == 200 else '❌'})")
        if response.status_code != 200:
            print("   ❌ Applicazione non raggiungibile")
            return False
    except Exception as e:
        print(f"   ❌ Errore connettività: {str(e)}")
        return False
    
    # Test 2: Verifica endpoint di controllo
    print("\n2️⃣ Test endpoint di controllo...")
    control_endpoints = [
        ("GET", "/api/health"),
        ("GET", "/api/endpoints"),
    ]
    
    for method, path in control_endpoints:
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{path}", timeout=5)
            else:
                response = requests.post(f"{base_url}{path}", timeout=5)
            
            status = "✅" if response.status_code == 200 else "❌"
            print(f"   {method} {path}: {response.status_code} {status}")
            
        except Exception as e:
            print(f"   {method} {path}: ERROR - {str(e)}")
    
    # Test 3: Test specifico dell'endpoint problematico
    print("\n3️⃣ Test endpoint problematico...")
    
    # Test con metodo GET (dovrebbe dare 405 Method Not Allowed)
    print("   Test GET /api/get-processed-data (dovrebbe dare 405)...")
    try:
        response = requests.get(f"{base_url}/api/get-processed-data", timeout=5)
        print(f"      GET: {response.status_code} ({'✅ 405 Method Not Allowed' if response.status_code == 405 else '❌ Unexpected'})")
    except Exception as e:
        print(f"      GET: ERROR - {str(e)}")
    
    # Test con metodo POST (il nostro caso problematico)
    print("   Test POST /api/get-processed-data...")
    try:
        test_data = {"filename": "test.csv"}
        headers = {"Content-Type": "application/json"}
        
        response = requests.post(
            f"{base_url}/api/get-processed-data", 
            json=test_data,
            headers=headers,
            timeout=5
        )
        
        print(f"      POST: {response.status_code}")
        
        if response.status_code == 404:
            print("      ❌ ERRORE 404 - Route non registrata!")
            print("      📝 Possibili cause:")
            print("         - Errore di sintassi prima della definizione della route")
            print("         - Problema di importazione")
            print("         - Route definita ma non registrata")
            print("         - Conflitto con blueprint")
        elif response.status_code == 400:
            print("      ✅ Route registrata (400 = Bad Request - normale per dati test)")
            try:
                response_data = response.json()
                print(f"      📄 Risposta: {response_data}")
            except:
                pass
        elif response.status_code == 200:
            print("      ✅ Route registrata e funzionante!")
            try:
                response_data = response.json()
                print(f"      📄 Risposta: {response_data}")
            except:
                pass
        else:
            print(f"      ⚠️ Status code inaspettato: {response.status_code}")
            try:
                print(f"      📄 Risposta: {response.text}")
            except:
                pass
                
    except Exception as e:
        print(f"      POST: ERROR - {str(e)}")
    
    # Test 4: Verifica route simili
    print("\n4️⃣ Test route simili...")
    similar_routes = [
        ("GET", "/api/processed_data"),  # Route simile ma diversa
        ("GET", "/api/data"),
    ]
    
    for method, path in similar_routes:
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{path}", timeout=5)
            else:
                response = requests.post(f"{base_url}{path}", timeout=5)
            
            status = "✅" if response.status_code in [200, 404] else "❌"
            print(f"   {method} {path}: {response.status_code} {status}")
            
        except Exception as e:
            print(f"   {method} {path}: ERROR - {str(e)}")
    
    # Test 5: Verifica blueprint wizard
    print("\n5️⃣ Test blueprint wizard...")
    wizard_routes = [
        ("POST", "/api/wizard/complete"),
        ("GET", "/api/wizard/status"),
    ]
    
    for method, path in wizard_routes:
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{path}", timeout=5)
            else:
                response = requests.post(f"{base_url}{path}", json={}, timeout=5)
            
            status = "✅" if response.status_code in [200, 400, 500] else "❌"
            print(f"   {method} {path}: {response.status_code} {status}")
            
        except Exception as e:
            print(f"   {method} {path}: ERROR - {str(e)}")
    
    print("\n" + "=" * 60)
    print("📊 CONCLUSIONI")
    print("=" * 60)
    print("Se POST /api/get-processed-data restituisce 404:")
    print("1. La route non è registrata correttamente in Flask")
    print("2. Possibile errore di sintassi che impedisce la registrazione")
    print("3. Conflitto con blueprint o altre route")
    print("4. Problema nell'ordine di registrazione delle route")
    
    return True

if __name__ == "__main__":
    test_specific_route()
