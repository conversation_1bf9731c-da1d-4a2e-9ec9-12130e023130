@echo off
echo ===================================
echo Avvio applicazione con debug
echo ===================================
echo.

set CLEAN_ENV=clean_env

if not exist %CLEAN_ENV% (
    echo ERRORE: L'ambiente virtuale %CLEAN_ENV% non esiste.
    echo Eseguire prima create_clean_env.bat
    pause
    exit /b 1
)

echo Attivazione ambiente virtuale...
call %CLEAN_ENV%\Scripts\activate
if %errorlevel% neq 0 (
    echo ERRORE: Impossibile attivare l'ambiente virtuale.
    pause
    exit /b 1
)
echo Ambiente attivato: %VIRTUAL_ENV%
echo.

echo Verifica delle dipendenze principali...
python -c "import flask; print('Flask versione:', flask.__version__)"
if %errorlevel% neq 0 (
    echo ERRORE: Flask non è installato correttamente.
    pause
    exit /b 1
)

python -c "import pandas; print('Pandas versione:', pandas.__version__)"
if %errorlevel% neq 0 (
    echo ERRORE: Pandas non è installato correttamente.
    pause
    exit /b 1
)

python -c "import fastapi; print('FastAPI versione:', fastapi.__version__)"
if %errorlevel% neq 0 (
    echo ERRORE: FastAPI non è installato correttamente.
    pause
    exit /b 1
)

echo Verifica dei file principali...
if not exist app.py (
    echo ERRORE: File app.py non trovato.
    pause
    exit /b 1
)

if not exist mcp_server\run_server.py (
    echo ERRORE: File mcp_server\run_server.py non trovato.
    pause
    exit /b 1
)

echo Impostazione variabili d'ambiente...
set OPENROUTER_API_KEY=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
set APP_URL=http://localhost:5000
set MCP_URL=http://localhost:8000
set PYTHONPATH=%CD%
set FLASK_DEBUG=1

echo Variabili d'ambiente impostate:
echo OPENROUTER_API_KEY: %OPENROUTER_API_KEY%
echo APP_URL: %APP_URL%
echo MCP_URL: %MCP_URL%
echo PYTHONPATH: %PYTHONPATH%
echo.

echo Creazione cartella uploads se non esiste...
if not exist uploads mkdir uploads
echo.

echo Avvio del server MCP con debug...
echo NOTA: Il server verrà avviato nella finestra corrente per visualizzare eventuali errori.
echo Premi Ctrl+C per interrompere il server quando hai finito di verificare.
echo.
echo Comando: python mcp_server\run_server.py
echo.
cd mcp_server
python run_server.py
if %errorlevel% neq 0 (
    echo ERRORE: Il server MCP non si è avviato correttamente.
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo ===================================
echo Server MCP avviato correttamente!
echo Ora avvia l'applicazione principale in un'altra finestra con:
echo   python app.py
echo ===================================
echo.
pause
