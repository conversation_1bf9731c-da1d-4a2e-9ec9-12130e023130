#!/usr/bin/env python3
"""
Script di test per verificare il client OpenRouter corretto
"""

import sys
import os
sys.path.append('mcp_server')

from openrouter_client import OpenRouterClient
from dotenv import load_dotenv

def test_openrouter_client():
    """Testa il client OpenRouter corretto"""
    
    # Carica le variabili d'ambiente
    load_dotenv()
    
    print("🔧 Testando il client OpenRouter corretto...")
    
    # Crea il client
    client = OpenRouterClient()
    
    print(f"🔑 API Key presente: {bool(client.api_key)}")
    print(f"🌐 Client disponibile: {client.is_available}")
    
    if client.is_available:
        print("\n📝 Recuperando modelli...")
        models = client.get_models(include_free=True, include_paid=True)
        
        print(f"🎯 Numero totale di modelli recuperati: {len(models)}")
        
        if models:
            print("\n📝 Primi 10 modelli:")
            for i, model in enumerate(models[:10]):
                model_id = model.get('id', 'N/A')
                model_name = model.get('name', 'N/A')
                is_free = model.get('is_free', False)
                has_free_quota = model.get('has_free_quota', False)
                
                free_indicator = "🆓" if is_free else ("🎁" if has_free_quota else "💰")
                
                print(f"  {i+1:2d}. {free_indicator} {model_id} - {model_name}")
            
            # Conta i modelli per categoria
            free_models = [m for m in models if m.get('is_free', False)]
            quota_models = [m for m in models if m.get('has_free_quota', False)]
            
            print(f"\n📈 Statistiche:")
            print(f"  🆓 Modelli completamente gratuiti: {len(free_models)}")
            print(f"  🎁 Modelli con quote gratuite: {len(quota_models)}")
            print(f"  📊 Modelli totali: {len(models)}")
            
        else:
            print("❌ Nessun modello recuperato!")
    else:
        print("❌ Client non disponibile!")

if __name__ == "__main__":
    test_openrouter_client()
