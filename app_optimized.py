#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
App Roberto - <PERSON><PERSON> per Test Route
Carica solo i componenti essenziali per testare le route problematiche
"""

import os
import sys

# Carica le variabili d'ambiente dal file .env se presente
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("Variabili d'ambiente caricate dal file .env")
except ImportError:
    print("python-dotenv non installato. Le variabili d'ambiente devono essere impostate manualmente.")
except Exception as e:
    print(f"Errore nel caricamento delle variabili d'ambiente: {str(e)}")

# Importazioni Flask essenziali
from flask import Flask, jsonify
from datetime import datetime

# Configurazione dell'applicazione
app = Flask(__name__)
app.secret_key = 'bait_service_app_secret_key'

print("🔧 DEBUG: App Flask ottimizzata inizializzata")

# Simulazione AdvancedDatabaseManager per test
class MockAdvancedDatabaseManager:
    def __init__(self):
        self.is_connected = True
    
    def get_master_technicians(self):
        return [
            {
                'name': '<PERSON>',
                'hourly_rate': 25.0,
                'vat_included': True,
                'notes': 'Tecnico senior - Mock Supabase',
                'source': 'mock_supabase'
            },
            {
                'name': 'Luigi Verdi', 
                'hourly_rate': 20.0,
                'vat_included': True,
                'notes': 'Tecnico junior - Mock Supabase',
                'source': 'mock_supabase'
            }
        ]

# Inizializza mock database manager
app.db_manager = MockAdvancedDatabaseManager()
print(f"✅ MockAdvancedDatabaseManager inizializzato - Connesso: {app.db_manager.is_connected}")

# TEST ROUTE SEMPLICE
print("🔧 DEBUG: Registrando route di test /api/test-simple...")
@app.route('/api/test-simple', methods=['GET'])
def test_simple():
    return jsonify({"message": "Route di test funziona!", "status": "success"})

# API per configurazione dipendenti - NUOVO ENDPOINT SUPABASE-FIRST
print("🔧 DEBUG: Registrando route /api/config/employees...")
@app.route('/api/config/employees', methods=['GET'])
def get_config_employees():
    """
    API per ottenere la lista dei dipendenti configurati.
    Approccio Supabase-first con fallback intelligente.
    """
    import sys
    from datetime import datetime

    sys.stdout.write("=== API GET_CONFIG_EMPLOYEES SUPABASE-FIRST ===\n")
    sys.stdout.flush()

    try:
        employees = []
        data_source_used = 'unknown'

        # STRATEGIA SUPABASE-FIRST: Priorità assoluta a Supabase
        if hasattr(app, 'db_manager') and app.db_manager:
            try:
                sys.stdout.write("🚀 SUPABASE: Caricamento dipendenti da AdvancedDatabaseManager...\n")
                sys.stdout.flush()

                # Ottieni dipendenti da Supabase (mock)
                supabase_employees = app.db_manager.get_master_technicians()

                if supabase_employees and len(supabase_employees) > 0:
                    employees = supabase_employees
                    data_source_used = 'supabase'

                    sys.stdout.write(f"✅ SUPABASE: {len(employees)} dipendenti caricati con successo\n")
                    sys.stdout.flush()
                else:
                    sys.stdout.write("ℹ️ SUPABASE: Nessun dipendente trovato\n")
                    sys.stdout.flush()

            except Exception as e:
                sys.stdout.write(f"❌ SUPABASE: Errore caricamento dipendenti: {str(e)}\n")
                sys.stdout.flush()

        # FALLBACK: Dati demo se nessun dipendente disponibile
        if not employees:
            sys.stdout.write("🎭 DEMO: Generazione dipendenti demo...\n")
            sys.stdout.flush()

            data_source_used = 'demo'
            employees = [
                {
                    'name': 'Mario Rossi',
                    'hourly_rate': 25.0,
                    'vat_included': True,
                    'notes': 'Tecnico senior - Demo',
                    'source': 'demo'
                },
                {
                    'name': 'Luigi Verdi',
                    'hourly_rate': 20.0,
                    'vat_included': True,
                    'notes': 'Tecnico junior - Demo',
                    'source': 'demo'
                }
            ]

        # Log finale
        sys.stdout.write(f"👥 Config employees - Fonte: {data_source_used}, Dipendenti: {len(employees)}\n")
        sys.stdout.flush()

        return jsonify({
            'success': True,
            'employees': employees,
            'data_source': data_source_used,
            'count': len(employees),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        import traceback
        sys.stdout.write(f"❌ Errore API config/employees: {str(e)}\n")
        sys.stdout.write(traceback.format_exc())
        sys.stdout.flush()

        return jsonify({
            'success': False,
            'error': str(e),
            'employees': [],
            'data_source': 'error'
        }), 500

# Route di health check
@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({
        'status': 'healthy',
        'message': 'Server Flask ottimizzato funzionante',
        'timestamp': datetime.now().isoformat(),
        'db_connected': hasattr(app, 'db_manager') and app.db_manager.is_connected
    })

if __name__ == '__main__':
    print("🚀 Avvio server Flask ottimizzato...")
    print("📋 Route registrate:")
    print("   - /api/test-simple")
    print("   - /api/config/employees")
    print("   - /api/health")
    
    app.run(host='127.0.0.1', port=5002, debug=False)
