#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any

# Importazioni relative quando il modulo è importato come pacchetto
try:
    from .column_mapper import ColumnMapper
    from .duration_parser import DurationParser
# Importazioni assolute quando il modulo è eseguito direttamente
except ImportError:
    from column_mapper import ColumnMapper
    from duration_parser import DurationParser

# Configurazione del logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ActivityProcessor:
    """
    Classe per l'elaborazione principale dei dati di attività.
    Standardizza i nomi delle colonne, converte e valida le durate, e calcola metriche aggiuntive.
    """

    def __init__(self):
        self.column_mapper = ColumnMapper()
        self.duration_parser = DurationParser()

        # Formati data/ora supportati
        self.datetime_formats = [
            '%d/%m/%Y %H:%M',  # 31/12/2023 14:30
            '%d/%m/%Y %H.%M',  # 31/12/2023 14.30
            '%d-%m-%Y %H:%M',  # 31-12-2023 14:30
            '%Y-%m-%d %H:%M',  # 2023-12-31 14:30
            '%d/%m/%Y',        # 31/12/2023 (solo data)
            '%Y-%m-%d'         # 2023-12-31 (solo data)
        ]

    def process_activity_data(self, df_raw: pd.DataFrame, file_type: str) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        Esegue l'elaborazione principale dei dati di attività.

        Args:
            df_raw: DataFrame pandas con i dati grezzi
            file_type: Tipo di file (es. "attivita", "teamviewer", ecc.)

        Returns:
            Tuple contenente:
            - DataFrame elaborato
            - Dizionario con report di stato/discrepanze
        """
        if df_raw is None or df_raw.empty:
            logger.warning("DataFrame vuoto o None fornito al processore di attività")
            return pd.DataFrame(), {"error": "DataFrame vuoto o None"}

        # Inizializza il report di stato
        report = {
            "file_type": file_type,
            "rows_count": len(df_raw),
            "columns_count": len(df_raw.columns),
            "original_columns": df_raw.columns.tolist(),
            "processing_time": datetime.now().isoformat(),
            "discrepancies": [],
            "missing_values": {},
            "anomalies": []
        }

        try:
            # 1. Standardizza i nomi delle colonne
            df_mapped, applied_mapping, missing_columns = self.column_mapper.map_columns(df_raw, file_type)

            report["applied_mapping"] = applied_mapping
            report["missing_columns"] = missing_columns
            report["standard_columns"] = df_mapped.columns.tolist()

            # 2. Converti le colonne di data/ora
            df_processed = self._process_datetime_columns(df_mapped, file_type)

            # 3. Elabora le durate
            df_processed, duration_discrepancies = self._process_durations(df_processed, file_type)
            report["discrepancies"].extend(duration_discrepancies)

            # 4. Estrai informazioni aggiuntive
            df_processed = self._extract_additional_info(df_processed)

            # 5. Identifica valori mancanti o anomali
            missing_values, anomalies = self._identify_issues(df_processed)
            report["missing_values"] = missing_values
            report["anomalies"] = anomalies

            # 6. Calcola statistiche di base
            report["statistics"] = self._calculate_statistics(df_processed)

            return df_processed, report

        except Exception as e:
            logger.error(f"Errore nell'elaborazione dei dati: {str(e)}")
            return df_raw, {"error": str(e)}

    def _process_datetime_columns(self, df: pd.DataFrame, file_type: str) -> pd.DataFrame:
        """
        Converte le colonne di data/ora in oggetti datetime.

        Args:
            df: DataFrame pandas da elaborare
            file_type: Tipo di file

        Returns:
            DataFrame con colonne di data/ora convertite
        """
        df_processed = df.copy()

        # Identifica le colonne di data/ora in base al tipo di file
        datetime_columns = []

        if file_type in ["attivita", "teamviewer", "teamviewer_bait"]:
            datetime_columns = ["Data_Ora_Inizio", "Data_Ora_Fine"]
        elif file_type == "calendario":
            datetime_columns = ["Data", "Ora_Inizio", "Ora_Fine"]
        elif file_type in ["timbrature", "permessi"]:
            datetime_columns = ["Data", "Data_Inizio", "Data_Fine"]

        # Converti le colonne di data/ora
        for col in datetime_columns:
            if col in df_processed.columns:
                df_processed[col] = self._parse_datetime(df_processed[col])

        # Gestione speciale per il tipo "calendario" che ha data e ora separate
        if file_type == "calendario" and "Data" in df_processed.columns:
            if "Ora_Inizio" in df_processed.columns:
                df_processed["Data_Ora_Inizio"] = self._combine_date_time(df_processed["Data"], df_processed["Ora_Inizio"])

            if "Ora_Fine" in df_processed.columns:
                df_processed["Data_Ora_Fine"] = self._combine_date_time(df_processed["Data"], df_processed["Ora_Fine"])

        return df_processed

    def _parse_datetime(self, series: pd.Series) -> pd.Series:
        """
        Converte una serie di stringhe di data/ora in oggetti datetime.

        Args:
            series: Serie pandas con stringhe di data/ora

        Returns:
            Serie pandas con oggetti datetime
        """
        # Crea una copia della serie per non modificare l'originale
        result = series.copy()

        # Converti in stringa se non lo è già
        if not pd.api.types.is_string_dtype(result):
            result = result.astype(str)

        # Prova diversi formati di data/ora
        for fmt in self.datetime_formats:
            try:
                return pd.to_datetime(result, format=fmt, errors='coerce')
            except:
                continue

        # Se nessun formato funziona, usa il parser automatico
        return pd.to_datetime(result, errors='coerce')

    def _combine_date_time(self, date_series: pd.Series, time_series: pd.Series) -> pd.Series:
        """
        Combina serie di date e orari in una serie di datetime.

        Args:
            date_series: Serie pandas con date
            time_series: Serie pandas con orari

        Returns:
            Serie pandas con oggetti datetime combinati
        """
        # Converti in stringhe
        date_str = date_series.astype(str)
        time_str = time_series.astype(str)

        # Combina data e ora
        datetime_str = date_str + ' ' + time_str

        # Converti in datetime
        return pd.to_datetime(datetime_str, errors='coerce')

    def _process_durations(self, df: pd.DataFrame, file_type: str) -> Tuple[pd.DataFrame, List[Dict[str, Any]]]:
        """
        Elabora le durate delle attività.

        Args:
            df: DataFrame pandas da elaborare
            file_type: Tipo di file

        Returns:
            Tuple contenente:
            - DataFrame con durate elaborate
            - Lista di discrepanze trovate
        """
        df_processed = df.copy()
        discrepancies = []

        # Verifica se la colonna della durata è presente
        if "Durata_Attivita_Ore" in df_processed.columns:
            # Converti la colonna di durata in ore decimali
            df_processed["Durata_Attivita_Ore_Decimali"] = df_processed["Durata_Attivita_Ore"].apply(
                self.duration_parser.parse_duration
            )

            # Calcola la durata effettiva se sono presenti le colonne di inizio e fine
            if "Data_Ora_Inizio" in df_processed.columns and "Data_Ora_Fine" in df_processed.columns:
                # Calcola la durata effettiva
                df_processed["Durata_Calcolata_Ore"] = df_processed.apply(
                    lambda row: self._calculate_duration(row["Data_Ora_Inizio"], row["Data_Ora_Fine"]),
                    axis=1
                )

                # Confronta la durata dichiarata con quella calcolata
                df_processed["Durata_Discrepanza"] = df_processed.apply(
                    lambda row: self._check_duration_discrepancy(
                        row["Durata_Attivita_Ore_Decimali"],
                        row["Durata_Calcolata_Ore"]
                    ),
                    axis=1
                )

                # Raccogli le discrepanze
                discrepancies_df = df_processed[df_processed["Durata_Discrepanza"] == True].copy()

                for _, row in discrepancies_df.iterrows():
                    discrepancies.append({
                        "row_index": _,
                        "declared_duration": row["Durata_Attivita_Ore_Decimali"],
                        "calculated_duration": row["Durata_Calcolata_Ore"],
                        "difference": abs(row["Durata_Attivita_Ore_Decimali"] - row["Durata_Calcolata_Ore"]),
                        "start_time": row["Data_Ora_Inizio"].isoformat() if pd.notna(row["Data_Ora_Inizio"]) else None,
                        "end_time": row["Data_Ora_Fine"].isoformat() if pd.notna(row["Data_Ora_Fine"]) else None
                    })

        return df_processed, discrepancies

    def _calculate_duration(self, start_time: pd.Timestamp, end_time: pd.Timestamp) -> float:
        """
        Calcola la durata in ore tra due timestamp.

        Args:
            start_time: Timestamp di inizio
            end_time: Timestamp di fine

        Returns:
            Durata in ore decimali
        """
        if pd.isna(start_time) or pd.isna(end_time):
            return np.nan

        # Gestisci il caso in cui l'attività termina il giorno successivo
        if end_time < start_time:
            end_time = end_time + pd.Timedelta(days=1)

        # Calcola la differenza in ore
        duration_hours = (end_time - start_time).total_seconds() / 3600

        return round(duration_hours, 2)

    def _check_duration_discrepancy(self, declared_duration: float, calculated_duration: float) -> bool:
        """
        Verifica se c'è una discrepanza significativa tra la durata dichiarata e quella calcolata.

        Args:
            declared_duration: Durata dichiarata in ore
            calculated_duration: Durata calcolata in ore

        Returns:
            True se c'è una discrepanza significativa, False altrimenti
        """
        if pd.isna(declared_duration) or pd.isna(calculated_duration):
            return False

        # Tolleranza di 5 minuti (0.0833 ore)
        tolerance = 5 / 60

        return abs(declared_duration - calculated_duration) > tolerance

    def _extract_additional_info(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Estrae informazioni aggiuntive dalle colonne di data/ora.

        Args:
            df: DataFrame pandas da elaborare

        Returns:
            DataFrame con informazioni aggiuntive
        """
        df_processed = df.copy()

        # Estrai informazioni dalla colonna Data_Ora_Inizio
        if "Data_Ora_Inizio" in df_processed.columns:
            # Estrai data
            df_processed["Data_Inizio"] = df_processed["Data_Ora_Inizio"].dt.date

            # Estrai giorno della settimana
            df_processed["Giorno_Settimana"] = df_processed["Data_Ora_Inizio"].dt.day_name()

            # Estrai mese
            df_processed["Mese"] = df_processed["Data_Ora_Inizio"].dt.month

            # Estrai anno
            df_processed["Anno"] = df_processed["Data_Ora_Inizio"].dt.year

            # Estrai ora del giorno
            df_processed["Ora_Inizio"] = df_processed["Data_Ora_Inizio"].dt.hour

        return df_processed

    def _identify_issues(self, df: pd.DataFrame) -> Tuple[Dict[str, int], List[Dict[str, Any]]]:
        """
        Identifica valori mancanti o anomali nelle colonne chiave.

        Args:
            df: DataFrame pandas da elaborare

        Returns:
            Tuple contenente:
            - Dizionario con conteggio dei valori mancanti per colonna
            - Lista di anomalie trovate
        """
        # Identifica valori mancanti
        missing_values = {}
        for col in df.columns:
            missing_count = df[col].isna().sum()
            if missing_count > 0:
                missing_values[col] = int(missing_count)

        # Identifica anomalie
        anomalies = []

        # Verifica durate negative o eccessive
        if "Durata_Attivita_Ore_Decimali" in df.columns:
            # Durate negative
            negative_durations = df[df["Durata_Attivita_Ore_Decimali"] < 0]
            if not negative_durations.empty:
                for _, row in negative_durations.iterrows():
                    anomalies.append({
                        "type": "negative_duration",
                        "row_index": _,
                        "value": row["Durata_Attivita_Ore_Decimali"]
                    })

            # Durate eccessive (più di 24 ore)
            excessive_durations = df[df["Durata_Attivita_Ore_Decimali"] > 24]
            if not excessive_durations.empty:
                for _, row in excessive_durations.iterrows():
                    anomalies.append({
                        "type": "excessive_duration",
                        "row_index": _,
                        "value": row["Durata_Attivita_Ore_Decimali"]
                    })

        # Verifica date future
        if "Data_Ora_Inizio" in df.columns:
            future_dates = df[df["Data_Ora_Inizio"] > pd.Timestamp.now()]
            if not future_dates.empty:
                for _, row in future_dates.iterrows():
                    anomalies.append({
                        "type": "future_date",
                        "row_index": _,
                        "column": "Data_Ora_Inizio",
                        "value": row["Data_Ora_Inizio"].isoformat() if pd.notna(row["Data_Ora_Inizio"]) else None
                    })

        return missing_values, anomalies

    def _calculate_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Calcola statistiche di base sul DataFrame elaborato.

        Args:
            df: DataFrame pandas elaborato

        Returns:
            Dizionario con statistiche calcolate
        """
        stats = {
            "total_rows": len(df),
            "total_columns": len(df.columns)
        }

        # Statistiche specifiche per calendari
        if "Titolo_Evento" in df.columns:
            # Statistiche eventi calendario
            events = df["Titolo_Evento"].dropna()
            stats["total_events"] = len(events)
            stats["unique_events"] = len(events.unique())

            # Statistiche partecipanti
            if "Partecipanti" in df.columns:
                attendees = df["Partecipanti"].dropna()
                unique_attendees = set()
                for attendee_list in attendees:
                    if isinstance(attendee_list, str):
                        # Dividi per virgola o punto e virgola
                        attendees_split = attendee_list.replace(';', ',').split(',')
                        unique_attendees.update([a.strip() for a in attendees_split if a.strip()])
                stats["unique_attendees"] = len(unique_attendees)

            # Statistiche durata per calendari
            if "Data_Ora_Inizio" in df.columns and "Data_Ora_Fine" in df.columns:
                # Calcola durate per eventi calendario
                df_temp = df.copy()
                df_temp["Data_Ora_Inizio"] = pd.to_datetime(df_temp["Data_Ora_Inizio"], errors='coerce')
                df_temp["Data_Ora_Fine"] = pd.to_datetime(df_temp["Data_Ora_Fine"], errors='coerce')

                valid_events = df_temp.dropna(subset=["Data_Ora_Inizio", "Data_Ora_Fine"])
                if not valid_events.empty:
                    durations_minutes = (valid_events["Data_Ora_Fine"] - valid_events["Data_Ora_Inizio"]).dt.total_seconds() / 60
                    durations_minutes = durations_minutes[durations_minutes > 0]  # Solo durate positive

                    if not durations_minutes.empty:
                        stats["duration"] = {
                            "total_minutes": round(durations_minutes.sum(), 2),
                            "total_hours": round(durations_minutes.sum() / 60, 2),
                            "average_minutes": round(durations_minutes.mean(), 2),
                            "average_hours": round(durations_minutes.mean() / 60, 2),
                            "min_minutes": round(durations_minutes.min(), 2),
                            "max_minutes": round(durations_minutes.max(), 2)
                        }

        # Statistiche sulle durate (per file attività)
        elif "Durata_Attivita_Ore_Decimali" in df.columns:
            durations = df["Durata_Attivita_Ore_Decimali"].dropna()
            if not durations.empty:
                stats["duration"] = {
                    "total_hours": round(durations.sum(), 2),
                    "average_hours": round(durations.mean(), 2),
                    "min_hours": round(durations.min(), 2),
                    "max_hours": round(durations.max(), 2),
                    "median_hours": round(durations.median(), 2)
                }

        # Statistiche sui tecnici
        if "Tecnico" in df.columns:
            technicians = df["Tecnico"].dropna().unique()
            stats["technicians"] = {
                "count": len(technicians),
                "list": technicians.tolist()
            }

            # Ore per tecnico
            if "Durata_Attivita_Ore_Decimali" in df.columns:
                hours_by_technician = df.groupby("Tecnico")["Durata_Attivita_Ore_Decimali"].sum().to_dict()
                stats["hours_by_technician"] = {tech: round(hours, 2) for tech, hours in hours_by_technician.items()}

        # Statistiche sui clienti
        if "Cliente" in df.columns:
            clients = df["Cliente"].dropna().unique()
            stats["clients"] = {
                "count": len(clients),
                "list": clients.tolist()
            }

            # Ore per cliente
            if "Durata_Attivita_Ore_Decimali" in df.columns:
                hours_by_client = df.groupby("Cliente")["Durata_Attivita_Ore_Decimali"].sum().to_dict()
                stats["hours_by_client"] = {client: round(hours, 2) for client, hours in hours_by_client.items()}

        return stats
