{% extends "base.html" %}

{% block title %}Dashboard Intelligente - Sistema di Analisi Incrociata{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
<link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
<style>
    .analysis-card {
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }
    .analysis-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    .discrepancy-item {
        border-left: 4px solid;
        padding: 10px 15px;
        margin-bottom: 10px;
        border-radius: 0 5px 5px 0;
    }
    .discrepancy-critical { border-left-color: #dc3545; background-color: #f8d7da; }
    .discrepancy-high { border-left-color: #fd7e14; background-color: #fff3cd; }
    .discrepancy-medium { border-left-color: #ffc107; background-color: #fff3cd; }
    .discrepancy-low { border-left-color: #17a2b8; background-color: #d1ecf1; }

    .severity-badge {
        font-size: 0.75rem;
        padding: 4px 8px;
    }

    .analysis-progress {
        height: 8px;
        margin-bottom: 10px;
    }

    .recommendation-item {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 8px;
        border-left: 3px solid #007bff;
    }

    .entity-badge {
        background-color: #e9ecef;
        color: #495057;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 0.8rem;
        margin-right: 5px;
    }

    .analysis-summary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .metric-card {
        background: white;
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: #495057;
    }

    .metric-label {
        color: #6c757d;
        font-size: 0.9rem;
        margin-top: 5px;
    }

    .analysis-tabs .nav-link {
        border-radius: 20px 20px 0 0;
        margin-right: 5px;
    }

    .analysis-tabs .nav-link.active {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }

    .loading-spinner {
        display: none;
        text-align: center;
        padding: 20px;
    }

    .chart-container {
        height: 300px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<main class="container-fluid">
    <!-- Header Dashboard -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="analysis-summary">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-2">
                            <i class="fas fa-brain me-2"></i>Dashboard Intelligente
                        </h2>
                        <p class="mb-0">Sistema di Analisi Incrociata e Controllo Qualità Dati</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <button type="button" id="run-analysis-btn" class="btn btn-light btn-lg">
                            <i class="fas fa-play me-2"></i>Avvia Analisi Completa
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtri Periodo -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>Filtri Analisi
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="date-range" class="form-label">Periodo Analisi</label>
                            <input type="text" id="date-range" class="form-control" placeholder="Seleziona periodo">
                        </div>
                        <div class="col-md-3">
                            <label for="analysis-type" class="form-label">Tipo Analisi</label>
                            <select id="analysis-type" class="form-select">
                                <option value="comprehensive">Analisi Completa</option>
                                <option value="time_consistency">Coerenza Temporale</option>
                                <option value="activity_remote_correlation">Correlazione Attività-Remote</option>
                                <option value="duplicates_overlaps">Duplicati e Sovrapposizioni</option>
                                <option value="productivity_analysis">Analisi Produttività</option>
                                <option value="cost_analysis">Analisi Costi</option>
                                <option value="data_quality">Qualità Dati</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="severity-filter" class="form-label">Severità Minima</label>
                            <select id="severity-filter" class="form-select">
                                <option value="all">Tutte</option>
                                <option value="critical">Solo Critiche</option>
                                <option value="high">Alta e Critiche</option>
                                <option value="medium">Media e superiori</option>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="button" id="apply-filters-btn" class="btn btn-primary w-100">
                                <i class="fas fa-search me-1"></i>Applica
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Metriche Globali -->
    <div class="row mb-4" id="global-metrics">
        <div class="col-md-2">
            <div class="metric-card">
                <div class="metric-value" id="total-records">-</div>
                <div class="metric-label">Record Analizzati</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="metric-card">
                <div class="metric-value" id="total-discrepancies">-</div>
                <div class="metric-label">Discrepanze Trovate</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="metric-card">
                <div class="metric-value" id="critical-issues">-</div>
                <div class="metric-label">Problemi Critici</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="metric-card">
                <div class="metric-value" id="processing-time">-</div>
                <div class="metric-label">Tempo Elaborazione</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="metric-card">
                <div class="metric-value" id="data-quality-score">-</div>
                <div class="metric-label">Qualità Dati</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="metric-card">
                <div class="metric-value" id="system-health">-</div>
                <div class="metric-label">Stato Sistema</div>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div class="loading-spinner wizard-hidden" id="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Analisi in corso...</span>
        </div>
        <p class="mt-2">Esecuzione analisi incrociata in corso...</p>
    </div>

    <!-- Tabs Analisi -->
    <div class="row wizard-hidden" id="analysis-results">
        <div class="col-12">
            <ul class="nav nav-tabs analysis-tabs" id="analysis-tabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="summary-tab" data-bs-toggle="tab" data-bs-target="#summary" type="button" role="tab">
                        <i class="fas fa-chart-pie me-1"></i>Riepilogo
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="discrepancies-tab" data-bs-toggle="tab" data-bs-target="#discrepancies" type="button" role="tab">
                        <i class="fas fa-exclamation-triangle me-1"></i>Discrepanze
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="recommendations-tab" data-bs-toggle="tab" data-bs-target="#recommendations" type="button" role="tab">
                        <i class="fas fa-lightbulb me-1"></i>Raccomandazioni
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="analytics-tab" data-bs-toggle="tab" data-bs-target="#analytics" type="button" role="tab">
                        <i class="fas fa-chart-line me-1"></i>Analytics
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                        <i class="fas fa-cogs me-1"></i>Sistema
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="analysis-tab-content">
                <!-- Tab Riepilogo -->
                <div class="tab-pane fade show active" id="summary" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Riepilogo Analisi</h5>
                            <div id="summary-content">
                                <p class="text-muted">Esegui un'analisi per visualizzare il riepilogo.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tab Discrepanze -->
                <div class="tab-pane fade" id="discrepancies" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Discrepanze Identificate</h5>
                            <div id="discrepancies-content">
                                <p class="text-muted">Nessuna discrepanza da visualizzare.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tab Raccomandazioni -->
                <div class="tab-pane fade" id="recommendations" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Raccomandazioni Sistema</h5>
                            <div id="recommendations-content">
                                <p class="text-muted">Nessuna raccomandazione disponibile.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tab Analytics -->
                <div class="tab-pane fade" id="analytics" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Analytics Avanzate</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="chart-container" id="severity-chart"></div>
                                </div>
                                <div class="col-md-6">
                                    <div class="chart-container" id="analysis-type-chart"></div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="chart-container" id="timeline-chart"></div>
                                </div>
                                <div class="col-md-6">
                                    <div class="chart-container" id="entities-chart"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tab Sistema -->
                <div class="tab-pane fade" id="system" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Stato Sistema Intelligente</h5>
                            <div id="system-content">
                                <p class="text-muted">Caricamento stato sistema...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Dettagli Discrepanza -->
    <div class="modal fade" id="discrepancy-modal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Dettagli Discrepanza</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Chiudi" title="Chiudi finestra"></button>
                </div>
                <div class="modal-body" id="discrepancy-modal-body">
                    <!-- Contenuto dinamico -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Chiudi</button>
                    <button type="button" class="btn btn-primary" id="resolve-discrepancy-btn">Risolvi</button>
                </div>
            </div>
        </div>
    </div>
</main>
{% endblock %}

{% block extra_js %}
<!-- Carica Plotly per i grafici -->
<script src="https://cdn.plot.ly/plotly-2.24.1.min.js"></script>
<!-- Carica daterangepicker -->
<script src="https://cdn.jsdelivr.net/npm/moment/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<!-- Script dashboard intelligente -->
<script src="{{ url_for('static', filename='js/intelligent_dashboard.js') }}"></script>
{% endblock %}
