"""
Sistema di Persistenza Automatica per App Roberto
Gestisce il salvataggio automatico di file e dati in Supabase
"""

import os
import shutil
import uuid
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from werkzeug.utils import secure_filename

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AutomaticPersistenceManager:
    """
    Gestisce la persistenza automatica di file e dati.
    Integra upload, elaborazione e salvataggio in Supabase.
    """
    
    def __init__(self, supabase_manager=None, db_manager=None):
        """
        Inizializza il manager di persistenza automatica.
        
        Args:
            supabase_manager: Istanza di SupabaseManager
            db_manager: Istanza di AdvancedDatabaseManager
        """
        self.supabase_manager = supabase_manager
        self.db_manager = db_manager
        self.upload_folder = 'uploads'
        self.processed_folder = 'processed'
        self.temp_folder = 'temp'
        
        # Crea cartelle se non esistono
        self._ensure_folders_exist()
        
        logger.info("AutomaticPersistenceManager inizializzato")
    
    def _ensure_folders_exist(self):
        """Assicura che tutte le cartelle necessarie esistano."""
        folders = [self.upload_folder, self.processed_folder, self.temp_folder]
        for folder in folders:
            os.makedirs(folder, exist_ok=True)
    
    def save_uploaded_file_with_persistence(self, file, session_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Salva un file caricato con persistenza automatica in Supabase.
        
        Args:
            file: File caricato (Flask request.files)
            session_data: Dati di sessione opzionali
            
        Returns:
            Dict con informazioni del file salvato
        """
        try:
            import sys
            
            sys.stdout.write("=== PERSISTENZA AUTOMATICA FILE ===\n")
            sys.stdout.flush()
            
            # Genera ID univoco
            file_id = str(uuid.uuid4())
            timestamp = int(datetime.now().timestamp())
            
            # Nome file sicuro e univoco
            original_filename = file.filename
            secure_name = secure_filename(original_filename)
            base_name, ext = os.path.splitext(secure_name)
            unique_filename = f"{base_name}_{timestamp}_{file_id[:8]}{ext}"
            
            # Percorso file
            file_path = os.path.join(self.upload_folder, unique_filename)
            
            # Salva file fisicamente
            file.save(file_path)
            file_size = os.path.getsize(file_path)
            
            sys.stdout.write(f"✅ File salvato: {unique_filename} ({file_size} bytes)\n")
            sys.stdout.flush()
            
            # Prepara informazioni per Supabase
            file_info = {
                'filename': unique_filename,
                'original_filename': original_filename,
                'file_type': self._detect_file_type(original_filename),
                'file_path': file_path,
                'file_size': file_size,
                'status': 'uploaded',
                'session_id': session_data.get('session_id') if session_data else None,
                'upload_timestamp': datetime.now().isoformat(),
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }
            
            # Salva in Supabase se disponibile
            supabase_id = None
            if self.supabase_manager and self.supabase_manager.is_connected:
                try:
                    supabase_id = self.supabase_manager.save_file_upload(file_info)
                    if supabase_id:
                        sys.stdout.write(f"✅ File persistito in Supabase con ID: {supabase_id}\n")
                        file_info['supabase_id'] = supabase_id
                    else:
                        sys.stdout.write("⚠️ Errore nel salvare in Supabase\n")
                except Exception as e:
                    sys.stdout.write(f"⚠️ Errore Supabase: {str(e)}\n")
                sys.stdout.flush()
            
            # Risultato completo
            result = {
                'success': True,
                'file_id': file_id,
                'filename': unique_filename,
                'original_filename': original_filename,
                'file_path': file_path,
                'file_size': file_size,
                'supabase_id': supabase_id,
                'file_info': file_info,
                'upload_timestamp': datetime.now().isoformat()
            }
            
            sys.stdout.write(f"🎉 Persistenza automatica completata per: {original_filename}\n")
            sys.stdout.flush()
            
            return result
            
        except Exception as e:
            import traceback
            logger.error(f"Errore persistenza automatica: {str(e)}")
            logger.error(traceback.format_exc())
            
            return {
                'success': False,
                'error': str(e),
                'file_id': None,
                'filename': None,
                'original_filename': file.filename if file else None
            }
    
    def save_processed_data_with_persistence(self, file_id: str, processed_data: Dict[str, Any], 
                                           statistics: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Salva dati elaborati con persistenza automatica.
        
        Args:
            file_id: ID del file originale
            processed_data: Dati elaborati
            statistics: Statistiche opzionali
            
        Returns:
            Dict con risultato del salvataggio
        """
        try:
            import sys
            
            sys.stdout.write("=== PERSISTENZA DATI ELABORATI ===\n")
            sys.stdout.flush()
            
            # Genera percorso file elaborato
            processed_filename = f"processed_{file_id}_{int(datetime.now().timestamp())}.json"
            processed_path = os.path.join(self.processed_folder, processed_filename)
            
            # Salva dati elaborati su file
            with open(processed_path, 'w', encoding='utf-8') as f:
                json.dump(processed_data, f, ensure_ascii=False, indent=2)
            
            sys.stdout.write(f"✅ Dati elaborati salvati: {processed_filename}\n")
            sys.stdout.flush()
            
            # Salva in Supabase se disponibile
            supabase_id = None
            if self.supabase_manager and self.supabase_manager.is_connected:
                try:
                    # Trova il file upload originale
                    file_uploads = self.supabase_manager.get_file_uploads(limit=100)
                    original_upload = None
                    for upload in file_uploads:
                        if file_id in upload.get('filename', ''):
                            original_upload = upload
                            break
                    
                    if original_upload:
                        supabase_id = self.supabase_manager.save_processed_data(
                            file_upload_id=original_upload['id'],
                            data_type=original_upload.get('file_type', 'unknown'),
                            processed_data=processed_data,
                            statistics=statistics or {}
                        )
                        
                        if supabase_id:
                            sys.stdout.write(f"✅ Dati elaborati persistiti in Supabase con ID: {supabase_id}\n")
                        else:
                            sys.stdout.write("⚠️ Errore nel salvare dati elaborati in Supabase\n")
                    else:
                        sys.stdout.write("⚠️ File upload originale non trovato in Supabase\n")
                        
                except Exception as e:
                    sys.stdout.write(f"⚠️ Errore Supabase dati elaborati: {str(e)}\n")
                sys.stdout.flush()
            
            result = {
                'success': True,
                'processed_file_id': processed_filename,
                'processed_path': processed_path,
                'supabase_id': supabase_id,
                'records_count': len(processed_data) if isinstance(processed_data, list) else 1,
                'processing_timestamp': datetime.now().isoformat()
            }
            
            sys.stdout.write(f"🎉 Persistenza dati elaborati completata\n")
            sys.stdout.flush()
            
            return result
            
        except Exception as e:
            import traceback
            logger.error(f"Errore persistenza dati elaborati: {str(e)}")
            logger.error(traceback.format_exc())
            
            return {
                'success': False,
                'error': str(e),
                'processed_file_id': None
            }
    
    def cleanup_old_files(self, days_old: int = 7) -> Dict[str, Any]:
        """
        Pulisce file vecchi dalle cartelle temporanee.
        
        Args:
            days_old: Giorni di anzianità per la pulizia
            
        Returns:
            Dict con risultato della pulizia
        """
        try:
            import sys
            import time
            
            sys.stdout.write("=== PULIZIA FILE VECCHI ===\n")
            sys.stdout.flush()
            
            current_time = time.time()
            cutoff_time = current_time - (days_old * 24 * 60 * 60)
            
            cleaned_files = []
            total_size_freed = 0
            
            # Pulisci cartelle
            folders_to_clean = [self.temp_folder, self.processed_folder]
            
            for folder in folders_to_clean:
                if os.path.exists(folder):
                    for filename in os.listdir(folder):
                        file_path = os.path.join(folder, filename)
                        
                        if os.path.isfile(file_path):
                            file_mtime = os.path.getmtime(file_path)
                            
                            if file_mtime < cutoff_time:
                                file_size = os.path.getsize(file_path)
                                os.remove(file_path)
                                
                                cleaned_files.append({
                                    'filename': filename,
                                    'folder': folder,
                                    'size': file_size,
                                    'age_days': (current_time - file_mtime) / (24 * 60 * 60)
                                })
                                total_size_freed += file_size
            
            sys.stdout.write(f"✅ Pulizia completata: {len(cleaned_files)} file rimossi, {total_size_freed} bytes liberati\n")
            sys.stdout.flush()
            
            return {
                'success': True,
                'files_cleaned': len(cleaned_files),
                'total_size_freed': total_size_freed,
                'cleaned_files': cleaned_files
            }
            
        except Exception as e:
            logger.error(f"Errore pulizia file: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'files_cleaned': 0
            }
    
    def _detect_file_type(self, filename: str) -> str:
        """
        Rileva il tipo di file dall'estensione.
        
        Args:
            filename: Nome del file
            
        Returns:
            Tipo di file rilevato
        """
        ext = os.path.splitext(filename)[1].lower()
        
        if ext in ['.csv']:
            return 'csv'
        elif ext in ['.xlsx', '.xls']:
            return 'excel'
        elif ext in ['.json']:
            return 'json'
        elif ext in ['.pdf']:
            return 'pdf'
        else:
            return 'unknown'
    
    def get_persistence_status(self) -> Dict[str, Any]:
        """
        Ottiene lo stato del sistema di persistenza.
        
        Returns:
            Dict con stato del sistema
        """
        try:
            # Conta file nelle cartelle
            upload_count = len(os.listdir(self.upload_folder)) if os.path.exists(self.upload_folder) else 0
            processed_count = len(os.listdir(self.processed_folder)) if os.path.exists(self.processed_folder) else 0
            temp_count = len(os.listdir(self.temp_folder)) if os.path.exists(self.temp_folder) else 0
            
            # Stato Supabase
            supabase_connected = self.supabase_manager.is_connected if self.supabase_manager else False
            
            return {
                'supabase_connected': supabase_connected,
                'upload_files_count': upload_count,
                'processed_files_count': processed_count,
                'temp_files_count': temp_count,
                'folders_exist': {
                    'uploads': os.path.exists(self.upload_folder),
                    'processed': os.path.exists(self.processed_folder),
                    'temp': os.path.exists(self.temp_folder)
                },
                'last_check': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Errore stato persistenza: {str(e)}")
            return {
                'error': str(e),
                'last_check': datetime.now().isoformat()
            }
