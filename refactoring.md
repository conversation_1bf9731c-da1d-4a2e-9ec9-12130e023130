content = """# Piano di refactoring di app.py

Di seguito le **7 fasi** per rifattorizzare `app.py` e
risolvere i warning Pylint.

## 1. Organizzazione e deduplica degli import

- Raggruppa gli import in tre sezioni:

  - Standard library
  - Terze parti
  - Moduli locali
- Ordinale alfabeticamente.
- Rimuovi duplicati e import non utilizzati.

## 2. Risoluzione degli shadowing (W0621 / W0404)

- Individua variabili/parametri che ridefiniscono nomi
  importati (`sys`, `json`, `datetime`, `pd`, ecc.).
- Rinominale (es. `sys_mod`, `json_data`, `dt_now`,
  `pd_lib`).

## 3. Logging lazy formatting (W1203)

- Sostituisci le f-string nelle chiamate `logger.*` con
  placeholder `%s`:

  ```python




  ```

logger.debug("Valore: %s", valore)

**4. Gestione eccezioni esplicita (W0702)**

* Trasforma
  except: in except Exception as e:.
* Aggiungi
  logger.error(str(e)) se mancante.

**5. Rimozione di dead-code e disable inutili**

* Elimina
  funzioni, classi e variabili non referenziate.
* Rimuovi
  commenti # pylint: disable=… superflui.

**6. Conformità PEP-8**

* Nomi
  in snake_case.
* Indentazione
  a 4 spazi.
* Lunghezza
  linee max 88 caratteri.
* Spaziatura
  coerente.

**7. Verifica finale**

* Ricompila
  il file completo integrando tutte le fasi.
* Esegui
  Pylint: nessun warning residuo.
* Fornisci
  il file pulito app.py.

  """
