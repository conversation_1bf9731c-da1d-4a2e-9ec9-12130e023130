#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sistema di Agenti Intelligenti per Automazione Avanzata.
Agenti specializzati per task specifici del sistema di riconoscimento intelligente.
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
import json

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class AgentTask:
    """Task per un agente."""
    id: str
    agent_type: str
    data: Dict[str, Any]
    priority: int = 1  # 1=alta, 2=media, 3=bassa
    created_at: datetime = field(default_factory=datetime.now)
    deadline: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3

@dataclass
class AgentResult:
    """Risultato di un agente."""
    task_id: str
    agent_type: str
    success: bool
    result: Any
    metadata: Dict[str, Any]
    processing_time_ms: int
    timestamp: datetime = field(default_factory=datetime.now)

class BaseAgent(ABC):
    """Classe base per tutti gli agenti."""

    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        self.name = name
        self.config = config or {}
        self.is_active = True
        self.task_count = 0
        self.success_count = 0
        self.error_count = 0
        self.last_activity = None

        logger.info(f"Agente {self.name} inizializzato")

    @abstractmethod
    async def process_task(self, task: AgentTask) -> AgentResult:
        """Processa un task specifico."""
        pass

    async def execute(self, task: AgentTask) -> AgentResult:
        """Esegue un task con gestione errori e metriche."""
        start_time = datetime.now()
        self.task_count += 1
        self.last_activity = start_time

        try:
            logger.info(f"Agente {self.name} - Esecuzione task {task.id}")

            result = await self.process_task(task)

            if result.success:
                self.success_count += 1
            else:
                self.error_count += 1

            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)
            result.processing_time_ms = processing_time

            logger.info(f"Agente {self.name} - Task {task.id} completato in {processing_time}ms")
            return result

        except Exception as e:
            self.error_count += 1
            processing_time = int((datetime.now() - start_time).total_seconds() * 1000)

            logger.error(f"Agente {self.name} - Errore task {task.id}: {str(e)}")

            return AgentResult(
                task_id=task.id,
                agent_type=self.name,
                success=False,
                result=None,
                metadata={'error': str(e)},
                processing_time_ms=processing_time
            )

    def get_stats(self) -> Dict[str, Any]:
        """Restituisce statistiche dell'agente."""
        success_rate = (self.success_count / self.task_count * 100) if self.task_count > 0 else 0

        return {
            'name': self.name,
            'is_active': self.is_active,
            'task_count': self.task_count,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'success_rate': round(success_rate, 2),
            'last_activity': self.last_activity.isoformat() if self.last_activity else None
        }

class DataQualityAgent(BaseAgent):
    """Agente per controllo qualità dati continuo."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("DataQualityAgent", config)
        self.quality_thresholds = self.config.get('quality_thresholds', {
            'completeness': 0.95,
            'accuracy': 0.90,
            'consistency': 0.85,
            'timeliness': 0.80
        })

    async def process_task(self, task: AgentTask) -> AgentResult:
        """Processa controllo qualità dati."""
        data = task.data

        # Simula controlli qualità
        quality_checks = await self._perform_quality_checks(data)

        # Identifica problemi
        issues = self._identify_quality_issues(quality_checks)

        # Genera raccomandazioni
        recommendations = self._generate_quality_recommendations(issues)

        # Calcola score globale
        overall_score = self._calculate_overall_quality_score(quality_checks)

        result = {
            'quality_checks': quality_checks,
            'issues': issues,
            'recommendations': recommendations,
            'overall_score': overall_score,
            'passed_thresholds': overall_score >= 0.85
        }

        return AgentResult(
            task_id=task.id,
            agent_type=self.name,
            success=True,
            result=result,
            metadata={
                'checks_performed': len(quality_checks),
                'issues_found': len(issues),
                'score': overall_score
            },
            processing_time_ms=0  # Sarà impostato dal metodo execute
        )

    async def _perform_quality_checks(self, data: Dict[str, Any]) -> Dict[str, float]:
        """Esegue controlli di qualità sui dati."""
        # Simula controlli asincroni
        await asyncio.sleep(0.1)

        checks = {
            'completeness': 0.92,
            'accuracy': 0.88,
            'consistency': 0.90,
            'timeliness': 0.85,
            'validity': 0.93
        }

        # Aggiungi variabilità basata sui dati reali
        if 'records_count' in data:
            records = data['records_count']
            if records < 100:
                checks['completeness'] *= 0.95
            elif records > 1000:
                checks['completeness'] *= 1.02

        return checks

    def _identify_quality_issues(self, quality_checks: Dict[str, float]) -> List[Dict[str, Any]]:
        """Identifica problemi di qualità."""
        issues = []

        for metric, score in quality_checks.items():
            threshold = self.quality_thresholds.get(metric, 0.80)

            if score < threshold:
                severity = 'high' if score < threshold * 0.8 else 'medium'
                issues.append({
                    'metric': metric,
                    'score': score,
                    'threshold': threshold,
                    'severity': severity,
                    'description': f"{metric.title()} sotto soglia: {score:.2f} < {threshold:.2f}"
                })

        return issues

    def _generate_quality_recommendations(self, issues: List[Dict[str, Any]]) -> List[str]:
        """Genera raccomandazioni per migliorare la qualità."""
        recommendations = []

        for issue in issues:
            metric = issue['metric']

            if metric == 'completeness':
                recommendations.append("Implementare validazione campi obbligatori")
                recommendations.append("Aggiungere controlli di input più rigorosi")
            elif metric == 'accuracy':
                recommendations.append("Verificare algoritmi di parsing")
                recommendations.append("Implementare controlli di coerenza incrociata")
            elif metric == 'consistency':
                recommendations.append("Standardizzare formati di data e ora")
                recommendations.append("Normalizzare nomi e identificatori")
            elif metric == 'timeliness':
                recommendations.append("Ottimizzare frequenza di aggiornamento")
                recommendations.append("Implementare notifiche per dati obsoleti")

        return list(set(recommendations))  # Rimuovi duplicati

    def _calculate_overall_quality_score(self, quality_checks: Dict[str, float]) -> float:
        """Calcola score qualità globale."""
        if not quality_checks:
            return 0.0

        # Media ponderata
        weights = {
            'completeness': 0.25,
            'accuracy': 0.30,
            'consistency': 0.20,
            'timeliness': 0.15,
            'validity': 0.10
        }

        weighted_sum = 0
        total_weight = 0

        for metric, score in quality_checks.items():
            weight = weights.get(metric, 0.1)
            weighted_sum += score * weight
            total_weight += weight

        return weighted_sum / total_weight if total_weight > 0 else 0.0

class EntityResolutionAgent(BaseAgent):
    """Agente per risoluzione entità duplicate."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("EntityResolutionAgent", config)
        self.similarity_threshold = self.config.get('similarity_threshold', 0.85)

    async def process_task(self, task: AgentTask) -> AgentResult:
        """Processa risoluzione entità duplicate."""
        entities = task.data.get('entities', [])
        entity_type = task.data.get('entity_type', 'unknown')

        # Trova duplicati
        duplicates = await self._find_duplicates(entities)

        # Genera mapping di risoluzione
        resolution_mapping = self._generate_resolution_mapping(duplicates)

        # Calcola statistiche
        stats = self._calculate_resolution_stats(entities, duplicates)

        result = {
            'entity_type': entity_type,
            'total_entities': len(entities),
            'duplicate_groups': duplicates,
            'resolution_mapping': resolution_mapping,
            'stats': stats
        }

        return AgentResult(
            task_id=task.id,
            agent_type=self.name,
            success=True,
            result=result,
            metadata={
                'entities_processed': len(entities),
                'duplicate_groups': len(duplicates),
                'entities_to_merge': sum(len(group) - 1 for group in duplicates)
            },
            processing_time_ms=0
        )

    async def _find_duplicates(self, entities: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """Trova gruppi di entità duplicate."""
        await asyncio.sleep(0.05)  # Simula processing

        duplicates = []
        processed = set()

        for i, entity in enumerate(entities):
            if i in processed:
                continue

            group = [entity]

            for j, other_entity in enumerate(entities[i+1:], i+1):
                if j in processed:
                    continue

                similarity = self._calculate_similarity(entity, other_entity)

                if similarity >= self.similarity_threshold:
                    group.append(other_entity)
                    processed.add(j)

            if len(group) > 1:
                duplicates.append(group)
                processed.add(i)

        return duplicates

    def _calculate_similarity(self, entity1: Dict[str, Any], entity2: Dict[str, Any]) -> float:
        """Calcola similarità tra due entità."""
        # Implementazione semplificata
        name1 = entity1.get('name', '').lower()
        name2 = entity2.get('name', '').lower()

        if not name1 or not name2:
            return 0.0

        # Similarità basata su Levenshtein semplificato
        from difflib import SequenceMatcher
        return SequenceMatcher(None, name1, name2).ratio()

    def _generate_resolution_mapping(self, duplicates: List[List[Dict[str, Any]]]) -> Dict[str, str]:
        """Genera mapping per risoluzione duplicati."""
        mapping = {}

        for group in duplicates:
            # Scegli entità master (quella con più dati)
            master = max(group, key=lambda x: len([v for v in x.values() if v]))
            master_id = master.get('id', master.get('name', ''))

            for entity in group:
                entity_id = entity.get('id', entity.get('name', ''))
                if entity_id != master_id:
                    mapping[entity_id] = master_id

        return mapping

    def _calculate_resolution_stats(self, entities: List[Dict[str, Any]],
                                  duplicates: List[List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Calcola statistiche di risoluzione."""
        total_duplicates = sum(len(group) for group in duplicates)
        entities_to_merge = sum(len(group) - 1 for group in duplicates)

        return {
            'total_entities': len(entities),
            'duplicate_groups': len(duplicates),
            'total_duplicates': total_duplicates,
            'entities_to_merge': entities_to_merge,
            'reduction_percentage': round((entities_to_merge / len(entities) * 100), 2) if entities else 0
        }

class AnomalyDetectionAgent(BaseAgent):
    """Agente per identificazione pattern anomali."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("AnomalyDetectionAgent", config)
        self.anomaly_threshold = self.config.get('anomaly_threshold', 2.0)  # Deviazioni standard

    async def process_task(self, task: AgentTask) -> AgentResult:
        """Processa identificazione anomalie."""
        data = task.data
        analysis_type = data.get('analysis_type', 'general')

        # Identifica anomalie
        anomalies = await self._detect_anomalies(data)

        # Classifica per severità
        classified_anomalies = self._classify_anomalies(anomalies)

        # Genera insights
        insights = self._generate_insights(classified_anomalies)

        result = {
            'analysis_type': analysis_type,
            'anomalies': classified_anomalies,
            'insights': insights,
            'total_anomalies': len(anomalies),
            'critical_count': len([a for a in classified_anomalies if a['severity'] == 'critical'])
        }

        return AgentResult(
            task_id=task.id,
            agent_type=self.name,
            success=True,
            result=result,
            metadata={
                'data_points': data.get('records_count', 0),
                'anomalies_found': len(anomalies),
                'analysis_type': analysis_type
            },
            processing_time_ms=0
        )

    async def _detect_anomalies(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Rileva anomalie nei dati."""
        await asyncio.sleep(0.1)  # Simula processing

        anomalies = []

        # Simula rilevamento anomalie temporali
        if 'time_series' in data:
            anomalies.extend(self._detect_temporal_anomalies(data['time_series']))

        # Simula rilevamento anomalie di volume
        if 'volumes' in data:
            anomalies.extend(self._detect_volume_anomalies(data['volumes']))

        # Simula rilevamento pattern anomali
        if 'patterns' in data:
            anomalies.extend(self._detect_pattern_anomalies(data['patterns']))

        return anomalies

    def _detect_temporal_anomalies(self, time_series: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Rileva anomalie temporali."""
        anomalies = []

        # Simula rilevamento gap temporali
        for i in range(len(time_series) - 1):
            current = time_series[i]
            next_item = time_series[i + 1]

            # Simula gap detection
            if 'timestamp' in current and 'timestamp' in next_item:
                anomalies.append({
                    'type': 'temporal_gap',
                    'description': 'Gap temporale rilevato',
                    'data': {'current': current, 'next': next_item},
                    'confidence': 0.8
                })

        return anomalies[:3]  # Limita per demo

    def _detect_volume_anomalies(self, volumes: List[float]) -> List[Dict[str, Any]]:
        """Rileva anomalie di volume."""
        if len(volumes) < 3:
            return []

        # Calcola media e deviazione standard
        mean_vol = sum(volumes) / len(volumes)
        variance = sum((x - mean_vol) ** 2 for x in volumes) / len(volumes)
        std_dev = variance ** 0.5

        anomalies = []

        for i, volume in enumerate(volumes):
            z_score = abs(volume - mean_vol) / std_dev if std_dev > 0 else 0

            if z_score > self.anomaly_threshold:
                anomalies.append({
                    'type': 'volume_anomaly',
                    'description': f'Volume anomalo: {volume} (z-score: {z_score:.2f})',
                    'data': {'index': i, 'value': volume, 'z_score': z_score},
                    'confidence': min(0.9, z_score / 3.0)
                })

        return anomalies

    def _detect_pattern_anomalies(self, patterns: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Rileva pattern anomali."""
        anomalies = []

        # Simula rilevamento pattern inusuali
        pattern_counts = {}
        for pattern in patterns:
            pattern_type = pattern.get('type', 'unknown')
            pattern_counts[pattern_type] = pattern_counts.get(pattern_type, 0) + 1

        # Identifica pattern rari
        total_patterns = len(patterns)
        for pattern_type, count in pattern_counts.items():
            frequency = count / total_patterns

            if frequency < 0.05:  # Pattern che appare meno del 5%
                anomalies.append({
                    'type': 'rare_pattern',
                    'description': f'Pattern raro: {pattern_type} ({frequency:.1%})',
                    'data': {'pattern_type': pattern_type, 'count': count, 'frequency': frequency},
                    'confidence': 1.0 - frequency
                })

        return anomalies

    def _classify_anomalies(self, anomalies: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Classifica anomalie per severità."""
        classified = []

        for anomaly in anomalies:
            confidence = anomaly.get('confidence', 0.5)
            anomaly_type = anomaly.get('type', 'unknown')

            # Determina severità
            if confidence > 0.9 or anomaly_type == 'volume_anomaly':
                severity = 'critical'
            elif confidence > 0.7:
                severity = 'high'
            elif confidence > 0.5:
                severity = 'medium'
            else:
                severity = 'low'

            classified_anomaly = anomaly.copy()
            classified_anomaly['severity'] = severity
            classified.append(classified_anomaly)

        return classified

    def _generate_insights(self, anomalies: List[Dict[str, Any]]) -> List[str]:
        """Genera insights dalle anomalie."""
        insights = []

        # Conta per tipo
        type_counts = {}
        for anomaly in anomalies:
            anomaly_type = anomaly.get('type', 'unknown')
            type_counts[anomaly_type] = type_counts.get(anomaly_type, 0) + 1

        # Genera insights
        if type_counts.get('temporal_gap', 0) > 0:
            insights.append("Rilevati gap temporali nei dati - verificare processo di raccolta")

        if type_counts.get('volume_anomaly', 0) > 0:
            insights.append("Volumi anomali rilevati - possibili picchi o cali di attività")

        if type_counts.get('rare_pattern', 0) > 0:
            insights.append("Pattern rari identificati - potrebbero indicare casi speciali")

        # Insight generale
        critical_count = len([a for a in anomalies if a.get('severity') == 'critical'])
        if critical_count > 0:
            insights.append(f"{critical_count} anomalie critiche richiedono attenzione immediata")

        return insights

class ConfigurationAgent(BaseAgent):
    """Agente per ottimizzazione configurazioni."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("ConfigurationAgent", config)
        self.optimization_targets = self.config.get('targets', ['performance', 'accuracy', 'efficiency'])

    async def process_task(self, task: AgentTask) -> AgentResult:
        """Processa ottimizzazione configurazioni."""
        current_config = task.data.get('current_config', {})
        performance_metrics = task.data.get('performance_metrics', {})
        objectives = task.data.get('objectives', {})

        # Analizza configurazioni attuali
        analysis = await self._analyze_current_config(current_config, performance_metrics)

        # Genera ottimizzazioni
        optimizations = self._generate_optimizations(analysis, objectives)

        # Valuta impatto
        impact_assessment = self._assess_optimization_impact(optimizations)

        result = {
            'current_analysis': analysis,
            'optimizations': optimizations,
            'impact_assessment': impact_assessment,
            'implementation_priority': self._prioritize_optimizations(optimizations)
        }

        return AgentResult(
            task_id=task.id,
            agent_type=self.name,
            success=True,
            result=result,
            metadata={
                'config_params': len(current_config),
                'optimizations_suggested': len(optimizations),
                'targets': self.optimization_targets
            },
            processing_time_ms=0
        )

    async def _analyze_current_config(self, config: Dict[str, Any],
                                    metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Analizza configurazioni attuali."""
        await asyncio.sleep(0.05)

        analysis = {
            'performance_score': metrics.get('performance_score', 75),
            'efficiency_score': metrics.get('efficiency_score', 80),
            'bottlenecks': [],
            'strengths': [],
            'improvement_areas': []
        }

        # Simula analisi
        if analysis['performance_score'] < 80:
            analysis['bottlenecks'].append('Performance sotto target')
            analysis['improvement_areas'].append('Ottimizzazione algoritmi')

        if analysis['efficiency_score'] > 85:
            analysis['strengths'].append('Efficienza buona')

        return analysis

    def _generate_optimizations(self, analysis: Dict[str, Any],
                              objectives: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Genera ottimizzazioni suggerite."""
        optimizations = []

        # Ottimizzazioni basate su analisi
        if analysis['performance_score'] < 80:
            optimizations.append({
                'parameter': 'batch_size',
                'current_value': 100,
                'suggested_value': 250,
                'reason': 'Migliorare throughput processing',
                'expected_improvement': '15-20%'
            })

        if 'timeout' in objectives:
            optimizations.append({
                'parameter': 'connection_timeout',
                'current_value': 30,
                'suggested_value': 60,
                'reason': 'Ridurre timeout errors',
                'expected_improvement': '10-15%'
            })

        # Ottimizzazioni generiche
        optimizations.append({
            'parameter': 'cache_size',
            'current_value': 1000,
            'suggested_value': 2000,
            'reason': 'Migliorare hit rate cache',
            'expected_improvement': '5-10%'
        })

        return optimizations

    def _assess_optimization_impact(self, optimizations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Valuta impatto delle ottimizzazioni."""
        total_improvement = 0
        risk_level = 'low'

        for opt in optimizations:
            # Estrai miglioramento percentuale
            improvement_str = opt.get('expected_improvement', '0%')
            improvement = float(improvement_str.split('-')[0].replace('%', ''))
            total_improvement += improvement

        # Valuta rischio
        if len(optimizations) > 5:
            risk_level = 'medium'
        if total_improvement > 50:
            risk_level = 'high'

        return {
            'total_expected_improvement': f"{total_improvement:.1f}%",
            'risk_level': risk_level,
            'implementation_complexity': 'medium',
            'rollback_difficulty': 'easy'
        }

    def _prioritize_optimizations(self, optimizations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Prioritizza ottimizzazioni per implementazione."""
        # Ordina per impatto atteso
        prioritized = sorted(optimizations,
                           key=lambda x: float(x.get('expected_improvement', '0%').split('-')[0].replace('%', '')),
                           reverse=True)

        # Aggiungi priorità
        for i, opt in enumerate(prioritized):
            opt['priority'] = i + 1
            opt['implementation_order'] = 'immediate' if i < 2 else 'planned'

        return prioritized

class AgentOrchestrator:
    """Orchestratore per gestione e coordinamento agenti."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.agents: Dict[str, BaseAgent] = {}
        self.task_queue: List[AgentTask] = []
        self.results: List[AgentResult] = []
        self.is_running = False
        self.max_concurrent_tasks = self.config.get('max_concurrent_tasks', 5)

        # Inizializza agenti
        self._initialize_agents()

        logger.info("Agent Orchestrator inizializzato")

    def _initialize_agents(self):
        """Inizializza tutti gli agenti disponibili."""
        agent_configs = self.config.get('agents', {})

        # Inizializza agenti standard
        self.agents['data_quality'] = DataQualityAgent(agent_configs.get('data_quality'))
        self.agents['entity_resolution'] = EntityResolutionAgent(agent_configs.get('entity_resolution'))
        self.agents['anomaly_detection'] = AnomalyDetectionAgent(agent_configs.get('anomaly_detection'))
        self.agents['configuration'] = ConfigurationAgent(agent_configs.get('configuration'))

        logger.info(f"Inizializzati {len(self.agents)} agenti")

    async def submit_task(self, task: AgentTask) -> str:
        """Sottomette un task alla coda."""
        self.task_queue.append(task)
        logger.info(f"Task {task.id} aggiunto alla coda (tipo: {task.agent_type})")
        return task.id

    async def process_tasks(self) -> List[AgentResult]:
        """Processa tutti i task in coda."""
        if not self.task_queue:
            return []

        self.is_running = True
        results = []

        try:
            # Ordina task per priorità
            self.task_queue.sort(key=lambda x: (x.priority, x.created_at))

            # Processa task in batch
            while self.task_queue and self.is_running:
                batch = self.task_queue[:self.max_concurrent_tasks]
                self.task_queue = self.task_queue[self.max_concurrent_tasks:]

                # Esegui batch in parallelo
                batch_results = await self._process_task_batch(batch)
                results.extend(batch_results)

                # Salva risultati
                self.results.extend(batch_results)

        finally:
            self.is_running = False

        logger.info(f"Processati {len(results)} task")
        return results

    async def _process_task_batch(self, tasks: List[AgentTask]) -> List[AgentResult]:
        """Processa un batch di task in parallelo."""
        coroutines = []

        for task in tasks:
            agent = self.agents.get(task.agent_type)
            if agent and agent.is_active:
                coroutines.append(agent.execute(task))
            else:
                # Crea risultato di errore per agente non disponibile
                error_result = AgentResult(
                    task_id=task.id,
                    agent_type=task.agent_type,
                    success=False,
                    result=None,
                    metadata={'error': f'Agente {task.agent_type} non disponibile'},
                    processing_time_ms=0
                )
                coroutines.append(asyncio.coroutine(lambda: error_result)())

        if coroutines:
            return await asyncio.gather(*coroutines)

        return []

    async def run_comprehensive_analysis(self, data: Dict[str, Any]) -> Dict[str, AgentResult]:
        """Esegue analisi completa con tutti gli agenti."""
        logger.info("Avvio analisi completa con tutti gli agenti")

        # Crea task per ogni agente
        tasks = []

        # Task qualità dati
        tasks.append(AgentTask(
            id=f"quality_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            agent_type='data_quality',
            data=data,
            priority=1
        ))

        # Task risoluzione entità (se ci sono entità)
        if 'entities' in data:
            tasks.append(AgentTask(
                id=f"entities_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                agent_type='entity_resolution',
                data=data,
                priority=2
            ))

        # Task rilevamento anomalie
        tasks.append(AgentTask(
            id=f"anomalies_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            agent_type='anomaly_detection',
            data=data,
            priority=1
        ))

        # Task ottimizzazione configurazione
        if 'current_config' in data:
            tasks.append(AgentTask(
                id=f"config_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                agent_type='configuration',
                data=data,
                priority=3
            ))

        # Sottometti task
        for task in tasks:
            await self.submit_task(task)

        # Processa task
        results = await self.process_tasks()

        # Organizza risultati per tipo agente
        organized_results = {}
        for result in results:
            organized_results[result.agent_type] = result

        logger.info(f"Analisi completa completata: {len(organized_results)} agenti")
        return organized_results

    def get_agent_stats(self) -> Dict[str, Dict[str, Any]]:
        """Restituisce statistiche di tutti gli agenti."""
        stats = {}
        for agent_name, agent in self.agents.items():
            stats[agent_name] = agent.get_stats()
        return stats

    def get_system_status(self) -> Dict[str, Any]:
        """Restituisce stato del sistema di agenti."""
        total_tasks = sum(agent.task_count for agent in self.agents.values())
        total_success = sum(agent.success_count for agent in self.agents.values())
        total_errors = sum(agent.error_count for agent in self.agents.values())

        success_rate = (total_success / total_tasks * 100) if total_tasks > 0 else 0

        return {
            'agents_count': len(self.agents),
            'active_agents': len([a for a in self.agents.values() if a.is_active]),
            'total_tasks_processed': total_tasks,
            'total_success': total_success,
            'total_errors': total_errors,
            'overall_success_rate': round(success_rate, 2),
            'queue_size': len(self.task_queue),
            'is_running': self.is_running,
            'last_update': datetime.now().isoformat()
        }

    def stop_processing(self):
        """Ferma il processing dei task."""
        self.is_running = False
        logger.info("Processing agenti fermato")

    def clear_queue(self):
        """Svuota la coda dei task."""
        cleared_count = len(self.task_queue)
        self.task_queue.clear()
        logger.info(f"Coda svuotata: {cleared_count} task rimossi")

    def get_recent_results(self, limit: int = 10) -> List[AgentResult]:
        """Restituisce i risultati più recenti."""
        return sorted(self.results, key=lambda x: x.timestamp, reverse=True)[:limit]

    async def health_check(self) -> Dict[str, Any]:
        """Verifica salute del sistema di agenti."""
        health_status = {
            'orchestrator_status': 'healthy',
            'agents_health': {},
            'system_metrics': self.get_system_status(),
            'timestamp': datetime.now().isoformat()
        }

        # Verifica salute di ogni agente
        for agent_name, agent in self.agents.items():
            agent_health = {
                'is_active': agent.is_active,
                'task_count': agent.task_count,
                'success_rate': (agent.success_count / agent.task_count * 100) if agent.task_count > 0 else 0,
                'last_activity': agent.last_activity.isoformat() if agent.last_activity else None
            }

            # Determina stato salute
            if not agent.is_active:
                agent_health['status'] = 'inactive'
            elif agent.task_count == 0:
                agent_health['status'] = 'idle'
            elif agent_health['success_rate'] < 80:
                agent_health['status'] = 'degraded'
            else:
                agent_health['status'] = 'healthy'

            health_status['agents_health'][agent_name] = agent_health

        return health_status

# Factory per creazione agenti personalizzati
class AgentFactory:
    """Factory per creazione di agenti personalizzati."""

    @staticmethod
    def create_agent(agent_type: str, config: Optional[Dict[str, Any]] = None) -> BaseAgent:
        """Crea un agente del tipo specificato."""
        agent_classes = {
            'data_quality': DataQualityAgent,
            'entity_resolution': EntityResolutionAgent,
            'anomaly_detection': AnomalyDetectionAgent,
            'configuration': ConfigurationAgent
        }

        agent_class = agent_classes.get(agent_type)
        if not agent_class:
            raise ValueError(f"Tipo di agente non supportato: {agent_type}")

        return agent_class(config)

    @staticmethod
    def get_available_agent_types() -> List[str]:
        """Restituisce i tipi di agenti disponibili."""
        return ['data_quality', 'entity_resolution', 'anomaly_detection', 'configuration']

# Istanza globale dell'orchestratore
agent_orchestrator = None

def get_agent_orchestrator(config: Optional[Dict[str, Any]] = None) -> AgentOrchestrator:
    """Restituisce l'istanza globale dell'orchestratore."""
    global agent_orchestrator

    if agent_orchestrator is None:
        agent_orchestrator = AgentOrchestrator(config)

    return agent_orchestrator
