# 🧙‍♂️ WIZARD INTERATTIVO - DOCUMENTAZIONE COMPLETA

## 📋 PANORAMICA

Il **Wizard Interattivo** è un sistema avanzato di caricamento e validazione file che implementa tutti i 6 task richiesti nel prompt originale. Fornisce un'interfaccia guidata passo-passo per caricare, validare, normalizzare e inserire dati in Supabase con controlli di coerenza automatici.

## 🎯 TASK IMPLEMENTATI

### ✅ **TASK 1: Analisi Intelligente del File**
- **Rilevamento automatico formato**: CSV, Excel (.xlsx, .xls)
- **Analisi struttura dati**: Colonne, tipi di dati, campioni
- **Rilevamento tipo file**: Attività, Timbrature, Utilizzo Veicoli, ecc.
- **Validazione formato**: Controlli di integrità e coerenza

### ✅ **TASK 2: Feedback Interattivo con Domande**
- **Domande di validazione**: "Il formato è corretto?", "Le colonne sono giuste?"
- **Interfaccia user-friendly**: Radio button, dropdown, form interattivi
- **Validazione in tempo reale**: Feedback immediato sulle scelte
- **Possibilità di modifica**: Correzione mappature colonne

### ✅ **TASK 3: Wizard di Caricamento File Interattivo**
- **4 step guidati**: Caricamento → Validazione → Normalizzazione → Inserimento
- **Indicatori di progresso**: Step indicator visuale
- **Drag & drop**: Interfaccia moderna per caricamento file
- **Anteprima dati**: Visualizzazione campioni prima dell'inserimento

### ✅ **TASK 4: Normalizzazione e Mappatura dei Dati**
- **Riconoscimento entità**: Dipendenti, Clienti, Progetti, Veicoli
- **Mappatura intelligente**: Associazione automatica a entità esistenti
- **Creazione nuove entità**: Gestione entità non presenti nel database
- **Interfaccia di mappatura**: Controllo completo sulle associazioni

### ✅ **TASK 5: Inserimento Dati in Supabase (Monitorato)**
- **Inserimento batch**: Ottimizzazione performance con batch da 100 record
- **Progress bar**: Monitoraggio visuale del progresso
- **Gestione errori**: Retry automatico e logging dettagliato
- **Conferma finale**: Riepilogo prima dell'inserimento definitivo

### ✅ **TASK 6: Controlli di Coerenza e Reportistica Iniziale**
- **Controlli automatici**: Coerenza Dipendente-Attività-Auto
- **Validazione assenze**: Verifica conflitti Assenze-Attività
- **Controlli permessi**: Coerenza Permessi-Attività
- **Score di coerenza**: Calcolo automatico percentuale coerenza
- **Statistiche finali**: Costi/impegno, produttività, utilizzo veicoli

## 🚀 COME UTILIZZARE IL WIZARD

### **Step 1: Accesso al Wizard**
1. Vai alla navbar principale
2. Clicca su "Wizard" → "Wizard Interattivo"
3. Oppure naviga direttamente a `/wizard-interattivo`

### **Step 2: Caricamento File**
1. **Drag & Drop**: Trascina il file nell'area di caricamento
2. **Selezione manuale**: Clicca "Seleziona File" per aprire il browser
3. **Formati supportati**: CSV, Excel (.xlsx, .xls)
4. **Dimensione massima**: 50MB

### **Step 3: Validazione Colonne**
1. **Anteprima dati**: Visualizza le prime 5 righe del file
2. **Mappatura colonne**: Verifica e modifica i tipi di colonna rilevati
3. **Domande di validazione**: Rispondi alle domande di controllo
4. **Conferma**: Clicca "Conferma Validazione" per procedere

### **Step 4: Normalizzazione Entità**
1. **Entità rilevate**: Visualizza dipendenti, clienti, progetti trovati
2. **Mappatura**: Scegli se mappare a entità esistenti o crearne di nuove
3. **Controllo**: Verifica le associazioni proposte
4. **Conferma**: Clicca "Conferma Normalizzazione"

### **Step 5: Inserimento Dati**
1. **Riepilogo**: Controlla il summary dell'operazione
2. **Conferma inserimento**: Clicca "Conferma Inserimento in Supabase"
3. **Monitoraggio**: Osserva la progress bar durante l'inserimento
4. **Controlli coerenza**: Attendi i controlli automatici post-inserimento

## 🔧 ARCHITETTURA TECNICA

### **Frontend (JavaScript)**
```javascript
// Stato globale del wizard
let wizardState = {
    currentStep: 1,
    totalSteps: 4,
    fileId: null,
    analysisResult: null,
    validationResult: null,
    normalizationResult: null,
    insertionResult: null
};
```

### **Backend (Python/Flask)**
```python
# API Endpoints principali
/api/wizard/interactive/start      # POST - Avvia analisi file
/api/wizard/interactive/validate   # POST - Valida colonne
/api/wizard/interactive/normalize  # POST - Normalizza entità
/api/wizard/interactive/insert     # POST - Inserisce dati
```

### **Database (Supabase)**
```sql
-- Tabelle di destinazione
normalized_activities      -- Attività normalizzate
normalized_vehicle_usage   -- Utilizzo veicoli normalizzato
normalized_timesheets      -- Timbrature normalizzate
processed_data             -- Dati generici processati
```

## 📊 CONTROLLI DI COERENZA

### **1. Coerenza Dipendente-Attività-Auto**
- **Verifica**: Dipendente con auto deve avere attività registrate
- **Controllo orari**: Sovrapposizione temporale utilizzo veicolo/attività
- **Severità**: Alta (problemi critici)

### **2. Coerenza Dipendente-Assenze-Attività**
- **Verifica**: Dipendente assente non può avere attività
- **Tipi assenza**: Permesso, Malattia, Ferie
- **Severità**: Alta (conflitti critici)

### **3. Coerenza Permessi-Attività**
- **Verifica**: Permessi approvati vs attività registrate
- **Controllo stato**: Solo permessi approvati
- **Severità**: Media (incongruenze minori)

### **Score di Coerenza**
```python
# Calcolo score (0-100%)
score = 100 - (weighted_issues / max_possible_issues * 100)

# Pesi per severità
severity_weights = {'high': 3, 'medium': 2, 'low': 1}
```

## 📈 STATISTICHE GENERATE

### **1. Costi/Impegno**
- Ore lavorate per progetto
- Ore lavorate per cliente  
- Ore lavorate per dipendente
- Top 5 progetti per ore
- Top 5 clienti per ore

### **2. Produttività Dipendenti**
- Attività totali per dipendente
- Ore medie per attività
- Dipendente più produttivo
- Dipendente più attivo

### **3. Clienti e Ticket**
- Numero attività per cliente
- Andamento mensile
- Statistiche temporali
- Trend di crescita

### **4. Utilizzo Veicoli**
- Chilometri percorsi totali
- Giorni di utilizzo
- Veicolo più utilizzato
- Statistiche per veicolo

## 🛠️ CONFIGURAZIONE E SETUP

### **Dipendenze Python**
```python
# Aggiunte per il wizard interattivo
from coherence_checker import CoherenceChecker
import pandas as pd
import json
from datetime import datetime, timedelta
```

### **File Creati**
- `templates/wizard_interattivo.html` - Interfaccia web completa
- `coherence_checker.py` - Sistema controlli di coerenza
- `WIZARD_INTERATTIVO_DOCUMENTAZIONE.md` - Questa documentazione

### **Modifiche a File Esistenti**
- `app.py` - Aggiunte API wizard e controlli coerenza
- `templates/base.html` - Aggiunto link navbar

## 🔍 DEBUGGING E TROUBLESHOOTING

### **Log di Sistema**
Il wizard produce log dettagliati per ogni operazione:
```
✅ === AVVIO CONTROLLI DI COERENZA ===
🚗 Controllo coerenza Dipendente-Attività-Auto...
   ✅ 150 record controllati, 3 problemi trovati
🏠 Controllo coerenza Dipendente-Assenze-Attività...
   ✅ 45 assenze controllate, 1 conflitti trovati
📝 Controllo coerenza Permessi-Attività...
   ✅ 23 permessi controllati, 0 conflitti trovati
📊 Generazione statistiche finali...
   ✅ Statistiche generate con successo
✅ Controlli completati. Score coerenza: 87.3%
```

### **Errori Comuni**
1. **File troppo grande**: Limite 50MB
2. **Formato non supportato**: Solo CSV, Excel
3. **Database non disponibile**: Verifica connessione Supabase
4. **Entità non trovate**: Controlla mappature nel step 3

### **Risoluzione Problemi**
- Controlla i log della console browser (F12)
- Verifica la connessione a Supabase
- Assicurati che le tabelle di destinazione esistano
- Controlla i permessi di scrittura sul database

## 🎉 RISULTATI ATTESI

Al completamento del wizard, avrai:

1. **✅ Dati inseriti** in Supabase nelle tabelle appropriate
2. **📊 Report di coerenza** con score percentuale
3. **📈 Statistiche complete** su produttività e utilizzo risorse
4. **🔍 Lista problemi** da risolvere (se presenti)
5. **💡 Raccomandazioni** per migliorare la qualità dei dati

## 🚀 PROSSIMI SVILUPPI

- **Wizard batch**: Caricamento multipli file simultanei
- **Template personalizzati**: Configurazione mappature predefinite
- **Export report**: Esportazione risultati in PDF/Excel
- **Notifiche email**: Alert automatici per problemi critici
- **Dashboard coerenza**: Monitoraggio continuo qualità dati

---

**🎯 Il Wizard Interattivo rappresenta la soluzione completa per il caricamento e validazione dati richiesta nel prompt, implementando tutti i 6 task con un'interfaccia moderna e controlli di qualità avanzati.**
