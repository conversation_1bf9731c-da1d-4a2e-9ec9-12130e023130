#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sistema di Agenti AI Avanzato - <PERSON><PERSON> (Fase 6)
Sistema agentico basato su LangChain/LangGraph per automazione intelligente e analisi avanzate.
"""

import asyncio
import logging
import json
import uuid
from typing import Dict, List, Any, Optional, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import threading
from abc import ABC, abstractmethod

# Import LangChain/LangGraph
try:
    from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
    from langchain_core.tools import tool
    from langchain_openai import ChatOpenAI
    from langchain.memory import ConversationBufferMemory
    from langchain.agents import AgentExecutor, create_react_agent
    from langchain import hub
    from langgraph.checkpoint.memory import MemorySaver
    from langgraph.prebuilt import create_react_agent as create_langgraph_agent
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    # Warning rimosso - <PERSON><PERSON>hain è ora installato e disponibile

# Import moduli esistenti
try:
    from intelligent_cache_system import intelligent_cache
    from performance_profiler import performance_profiler, profile
    from auto_tuner import auto_tuner
    from supabase_integration import SupabaseManager
except ImportError as e:
    logging.warning(f"Alcuni moduli non disponibili: {e}")

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgentStatus(Enum):
    """Stati degli agenti."""
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    ERROR = "error"
    PAUSED = "paused"

class AgentType(Enum):
    """Tipi di agenti disponibili."""
    DATA_CLEANING = "data_cleaning"
    BUSINESS_ANALYSIS = "business_analysis"
    WORKFLOW_AUTOMATION = "workflow_automation"
    RECOMMENDATION = "recommendation"

@dataclass
class AgentTask:
    """Task per un agente."""
    task_id: str
    agent_type: AgentType
    input_data: Dict[str, Any]
    priority: int = 5  # 1-10, 10 = massima priorità
    timeout_seconds: int = 300
    human_in_loop: bool = False
    created_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class AgentResult:
    """Risultato di un'esecuzione agente."""
    task_id: str
    agent_type: AgentType
    status: AgentStatus
    result_data: Dict[str, Any]
    execution_time: float
    confidence_score: float
    error_message: Optional[str] = None
    created_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

class BaseAgent(ABC):
    """Classe base per tutti gli agenti AI."""

    def __init__(self, agent_type: AgentType, llm_model: str = "gpt-4"):
        self.agent_type = agent_type
        self.agent_id = str(uuid.uuid4())
        self.status = AgentStatus.IDLE
        self.llm_model = llm_model
        self.memory = ConversationBufferMemory(return_messages=True) if LANGCHAIN_AVAILABLE else None
        self.tools = self._initialize_tools()
        self.performance_metrics = {
            "tasks_completed": 0,
            "avg_execution_time": 0.0,
            "success_rate": 0.0,
            "avg_confidence": 0.0
        }

        # Inizializza LLM se disponibile
        if LANGCHAIN_AVAILABLE:
            try:
                self.llm = ChatOpenAI(
                    model=llm_model,
                    temperature=0.1,
                    max_tokens=2000
                )
                self.agent_executor = self._create_agent_executor()
            except Exception as e:
                logger.warning(f"Errore inizializzazione LLM: {e}")
                self.llm = None
                self.agent_executor = None
        else:
            self.llm = None
            self.agent_executor = None

        logger.info(f"Agente {self.agent_type.value} inizializzato (ID: {self.agent_id})")

    @abstractmethod
    def _initialize_tools(self) -> List[Callable]:
        """Inizializza gli strumenti specifici dell'agente."""
        pass

    @abstractmethod
    async def _execute_task_logic(self, task: AgentTask) -> Dict[str, Any]:
        """Logica specifica di esecuzione del task."""
        pass

    def _create_agent_executor(self) -> Optional[Any]:
        """Crea executor per l'agente."""
        if not LANGCHAIN_AVAILABLE or not self.llm:
            return None

        try:
            # Placeholder per agent executor
            # In un sistema reale, qui si creerebbe l'AgentExecutor di LangChain
            return {"type": "mock_executor", "tools": self.tools}
        except Exception as e:
            logger.error(f"Errore creazione agent executor: {e}")
            return None

    @profile(name="agent_execute_task")
    async def execute_task(self, task: AgentTask) -> AgentResult:
        """
        Esegue un task assegnato all'agente.

        Args:
            task: Task da eseguire

        Returns:
            Risultato dell'esecuzione
        """
        start_time = datetime.now()
        self.status = AgentStatus.RUNNING

        try:
            logger.info(f"Agente {self.agent_type.value} inizia task {task.task_id}")

            # Esegui logica specifica dell'agente
            result_data = await self._execute_task_logic(task)

            # Calcola metriche
            execution_time = (datetime.now() - start_time).total_seconds()
            confidence_score = result_data.get("confidence_score", 0.8)

            # Aggiorna metriche performance
            self._update_performance_metrics(execution_time, confidence_score, True)

            # Crea risultato
            result = AgentResult(
                task_id=task.task_id,
                agent_type=self.agent_type,
                status=AgentStatus.COMPLETED,
                result_data=result_data,
                execution_time=execution_time,
                confidence_score=confidence_score
            )

            self.status = AgentStatus.IDLE
            logger.info(f"Agente {self.agent_type.value} completato task {task.task_id} "
                       f"in {execution_time:.2f}s (confidence: {confidence_score:.2f})")

            return result

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            self._update_performance_metrics(execution_time, 0.0, False)

            error_result = AgentResult(
                task_id=task.task_id,
                agent_type=self.agent_type,
                status=AgentStatus.ERROR,
                result_data={"error": str(e)},
                execution_time=execution_time,
                confidence_score=0.0,
                error_message=str(e)
            )

            self.status = AgentStatus.IDLE
            logger.error(f"Errore agente {self.agent_type.value} task {task.task_id}: {e}")

            return error_result

    def _update_performance_metrics(self, execution_time: float, confidence: float, success: bool):
        """Aggiorna metriche di performance dell'agente."""
        metrics = self.performance_metrics

        # Aggiorna contatori
        total_tasks = metrics["tasks_completed"]
        metrics["tasks_completed"] += 1

        # Aggiorna tempo medio
        current_avg_time = metrics["avg_execution_time"]
        metrics["avg_execution_time"] = (
            (current_avg_time * total_tasks + execution_time) / metrics["tasks_completed"]
        )

        # Aggiorna confidence media
        current_avg_confidence = metrics["avg_confidence"]
        metrics["avg_confidence"] = (
            (current_avg_confidence * total_tasks + confidence) / metrics["tasks_completed"]
        )

        # Aggiorna success rate (semplificato)
        if success:
            current_successes = metrics["success_rate"] * total_tasks
            metrics["success_rate"] = (current_successes + 1) / metrics["tasks_completed"]
        else:
            current_successes = metrics["success_rate"] * total_tasks
            metrics["success_rate"] = current_successes / metrics["tasks_completed"]

    def get_status(self) -> Dict[str, Any]:
        """Ottiene stato corrente dell'agente."""
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type.value,
            "status": self.status.value,
            "llm_model": self.llm_model,
            "tools_count": len(self.tools),
            "performance_metrics": self.performance_metrics,
            "memory_available": self.memory is not None,
            "executor_available": self.agent_executor is not None
        }

class AgentOrchestrator:
    """
    Orchestratore centrale per la gestione degli agenti AI.
    Gestisce l'esecuzione, la prioritizzazione e il monitoraggio degli agenti.
    """

    def __init__(self, supabase_manager: Optional[Any] = None):
        self.supabase_manager = supabase_manager

        # Registry degli agenti
        self.agents: Dict[AgentType, BaseAgent] = {}
        self.task_queue: List[AgentTask] = []
        self.active_tasks: Dict[str, AgentTask] = {}
        self.completed_tasks: Dict[str, AgentResult] = {}

        # Configurazione
        self.MAX_CONCURRENT_TASKS = 3
        self.TASK_TIMEOUT_DEFAULT = 300  # 5 minuti

        # Threading
        self.orchestrator_active = False
        self.orchestrator_thread = None
        self.lock = threading.RLock()

        # Metriche globali
        self.global_metrics = {
            "total_tasks_processed": 0,
            "total_execution_time": 0.0,
            "avg_task_time": 0.0,
            "success_rate": 0.0,
            "agents_active": 0
        }

        logger.info("AgentOrchestrator inizializzato")

    def register_agent(self, agent: BaseAgent):
        """Registra un agente nel sistema."""
        with self.lock:
            self.agents[agent.agent_type] = agent
            logger.info(f"Agente {agent.agent_type.value} registrato")

    def submit_task(self, task: AgentTask) -> str:
        """
        Sottomette un task per l'esecuzione.

        Args:
            task: Task da eseguire

        Returns:
            ID del task sottomesso
        """
        with self.lock:
            # Verifica che l'agente sia disponibile
            if task.agent_type not in self.agents:
                raise ValueError(f"Agente {task.agent_type.value} non disponibile")

            # Aggiungi alla coda con priorità
            self.task_queue.append(task)
            self.task_queue.sort(key=lambda t: t.priority, reverse=True)

            logger.info(f"Task {task.task_id} sottomesso per agente {task.agent_type.value}")

            # Avvia orchestratore se non attivo
            if not self.orchestrator_active:
                self.start_orchestrator()

            return task.task_id

    def start_orchestrator(self):
        """Avvia l'orchestratore di task."""
        if self.orchestrator_active:
            return

        self.orchestrator_active = True
        self.orchestrator_thread = threading.Thread(
            target=self._orchestrator_loop,
            daemon=True
        )
        self.orchestrator_thread.start()
        logger.info("🎭 Orchestratore agenti avviato")

    def stop_orchestrator(self):
        """Ferma l'orchestratore di task."""
        self.orchestrator_active = False
        if self.orchestrator_thread:
            self.orchestrator_thread.join(timeout=5)
        logger.info("⏹️ Orchestratore agenti fermato")

    def _orchestrator_loop(self):
        """Loop principale dell'orchestratore."""
        logger.info("🔄 Loop orchestratore avviato")

        while self.orchestrator_active:
            try:
                # Processa task in coda
                self._process_task_queue()

                # Verifica task timeout
                self._check_task_timeouts()

                # Aggiorna metriche
                self._update_global_metrics()

                # Attendi prima del prossimo ciclo
                import time
                time.sleep(1)

            except Exception as e:
                logger.error(f"Errore nel loop orchestratore: {e}")
                import time
                time.sleep(5)

        logger.info("🔄 Loop orchestratore terminato")

    def _process_task_queue(self):
        """Processa la coda dei task."""
        with self.lock:
            # Verifica se possiamo eseguire più task
            if len(self.active_tasks) >= self.MAX_CONCURRENT_TASKS:
                return

            # Prendi task con priorità più alta
            if not self.task_queue:
                return

            task = self.task_queue.pop(0)
            agent = self.agents.get(task.agent_type)

            if not agent or agent.status != AgentStatus.IDLE:
                # Rimetti in coda se agente non disponibile
                self.task_queue.insert(0, task)
                return

            # Avvia task
            self.active_tasks[task.task_id] = task

            # Esegui task in un thread separato per evitare problemi con event loop
            import threading
            task_thread = threading.Thread(
                target=self._execute_task_sync,
                args=(agent, task),
                daemon=True
            )
            task_thread.start()

    def _execute_task_sync(self, agent: BaseAgent, task: AgentTask):
        """Esegue un task in modo sincrono in un thread separato."""
        try:
            # Crea un nuovo event loop per questo thread
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # Esegui il task asincrono nel nuovo loop
                result = loop.run_until_complete(agent.execute_task(task))

                with self.lock:
                    # Rimuovi da task attivi
                    if task.task_id in self.active_tasks:
                        del self.active_tasks[task.task_id]

                    # Aggiungi ai completati
                    self.completed_tasks[task.task_id] = result

                    # Mantieni solo ultimi 1000 task completati
                    if len(self.completed_tasks) > 1000:
                        oldest_tasks = sorted(
                            self.completed_tasks.keys(),
                            key=lambda k: self.completed_tasks[k].created_at
                        )[:100]
                        for old_task_id in oldest_tasks:
                            del self.completed_tasks[old_task_id]
            finally:
                loop.close()

        except Exception as e:
            logger.error(f"Errore esecuzione task {task.task_id}: {e}")

            with self.lock:
                if task.task_id in self.active_tasks:
                    del self.active_tasks[task.task_id]

    async def _execute_task_async(self, agent: BaseAgent, task: AgentTask):
        """Esegue un task in modo asincrono (mantenuto per compatibilità)."""
        try:
            result = await agent.execute_task(task)

            with self.lock:
                # Rimuovi da task attivi
                if task.task_id in self.active_tasks:
                    del self.active_tasks[task.task_id]

                # Aggiungi ai completati
                self.completed_tasks[task.task_id] = result

                # Mantieni solo ultimi 1000 task completati
                if len(self.completed_tasks) > 1000:
                    oldest_tasks = sorted(
                        self.completed_tasks.keys(),
                        key=lambda k: self.completed_tasks[k].created_at
                    )[:100]
                    for old_task_id in oldest_tasks:
                        del self.completed_tasks[old_task_id]

        except Exception as e:
            logger.error(f"Errore esecuzione task {task.task_id}: {e}")

            with self.lock:
                if task.task_id in self.active_tasks:
                    del self.active_tasks[task.task_id]

    def _check_task_timeouts(self):
        """Verifica e gestisce timeout dei task."""
        current_time = datetime.now()

        with self.lock:
            timed_out_tasks = []

            for task_id, task in self.active_tasks.items():
                elapsed = (current_time - task.created_at).total_seconds()
                if elapsed > task.timeout_seconds:
                    timed_out_tasks.append(task_id)

            for task_id in timed_out_tasks:
                task = self.active_tasks[task_id]
                logger.warning(f"Task {task_id} timeout dopo {task.timeout_seconds}s")

                # Crea risultato di timeout
                timeout_result = AgentResult(
                    task_id=task_id,
                    agent_type=task.agent_type,
                    status=AgentStatus.ERROR,
                    result_data={"error": "Task timeout"},
                    execution_time=task.timeout_seconds,
                    confidence_score=0.0,
                    error_message="Task timeout"
                )

                # Sposta a completati
                del self.active_tasks[task_id]
                self.completed_tasks[task_id] = timeout_result

                # Reset agente
                agent = self.agents.get(task.agent_type)
                if agent:
                    agent.status = AgentStatus.IDLE

    def _update_global_metrics(self):
        """Aggiorna metriche globali del sistema."""
        with self.lock:
            total_tasks = len(self.completed_tasks)
            if total_tasks == 0:
                return

            # Calcola metriche aggregate
            total_time = sum(r.execution_time for r in self.completed_tasks.values())
            successful_tasks = sum(
                1 for r in self.completed_tasks.values()
                if r.status == AgentStatus.COMPLETED
            )

            self.global_metrics.update({
                "total_tasks_processed": total_tasks,
                "total_execution_time": total_time,
                "avg_task_time": total_time / total_tasks if total_tasks > 0 else 0.0,
                "success_rate": successful_tasks / total_tasks if total_tasks > 0 else 0.0,
                "agents_active": len([a for a in self.agents.values() if a.status == AgentStatus.RUNNING])
            })

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Ottiene stato di un task."""
        with self.lock:
            # Verifica se in coda
            for task in self.task_queue:
                if task.task_id == task_id:
                    return {
                        "task_id": task_id,
                        "status": "queued",
                        "position_in_queue": self.task_queue.index(task) + 1,
                        "agent_type": task.agent_type.value
                    }

            # Verifica se attivo
            if task_id in self.active_tasks:
                task = self.active_tasks[task_id]
                return {
                    "task_id": task_id,
                    "status": "running",
                    "agent_type": task.agent_type.value,
                    "elapsed_time": (datetime.now() - task.created_at).total_seconds()
                }

            # Verifica se completato
            if task_id in self.completed_tasks:
                result = self.completed_tasks[task_id]
                return {
                    "task_id": task_id,
                    "status": result.status.value,
                    "agent_type": result.agent_type.value,
                    "execution_time": result.execution_time,
                    "confidence_score": result.confidence_score,
                    "error_message": result.error_message,
                    "result_data": result.result_data
                }

            return None

    def get_system_status(self) -> Dict[str, Any]:
        """Ottiene stato completo del sistema agenti."""
        with self.lock:
            return {
                "orchestrator_active": self.orchestrator_active,
                "agents_registered": len(self.agents),
                "tasks_in_queue": len(self.task_queue),
                "active_tasks": len(self.active_tasks),
                "completed_tasks": len(self.completed_tasks),
                "global_metrics": self.global_metrics,
                "agents_status": {
                    agent_type.value: agent.get_status()
                    for agent_type, agent in self.agents.items()
                }
            }

    def get_task_result(self, task_id: str) -> Optional[AgentResult]:
        """Ottiene risultato di un task completato."""
        with self.lock:
            return self.completed_tasks.get(task_id)

    def cancel_task(self, task_id: str) -> bool:
        """Cancella un task dalla coda."""
        with self.lock:
            # Rimuovi dalla coda se presente
            for i, task in enumerate(self.task_queue):
                if task.task_id == task_id:
                    del self.task_queue[i]
                    logger.info(f"Task {task_id} cancellato dalla coda")
                    return True

            # Non possiamo cancellare task attivi (per ora)
            if task_id in self.active_tasks:
                logger.warning(f"Impossibile cancellare task attivo {task_id}")
                return False

            return False

# Istanza globale dell'orchestratore
agent_orchestrator = AgentOrchestrator()
