import pandas as pd
import os
import sys

def fix_csv_file(input_file, output_file):
    try:
        # Prova a leggere il file con diversi parametri
        try:
            # Prova con il parser C più veloce
            df = pd.read_csv(input_file, encoding='utf-8-sig')
        except Exception as e1:
            try:
                # Prova con il parser Python più flessibile
                df = pd.read_csv(input_file, encoding='utf-8-sig', engine='python')
            except Exception as e2:
                try:
                    # Prova con gestione esplicita delle virgolette
                    df = pd.read_csv(input_file, encoding='utf-8-sig', quoting=1, engine='python')
                except Exception as e3:
                    # Ultimo tentativo: leggi come testo e correggi manualmente
                    with open(input_file, 'r', encoding='utf-8-sig') as f:
                        lines = f.readlines()
                    
                    # Correggi le righe problematiche
                    fixed_lines = []
                    for i, line in enumerate(lines):
                        # Sostituisci caratteri problematici
                        fixed_line = line.replace('→', '->')
                        fixed_lines.append(fixed_line)
                    
                    # Scrivi il file corretto
                    with open(output_file, 'w', encoding='utf-8-sig') as f:
                        f.writelines(fixed_lines)
                    
                    print(f"File corretto manualmente e salvato come {output_file}")
                    return
        
        # Se siamo arrivati qui, la lettura è riuscita
        # Salva il file corretto
        df.to_csv(output_file, index=False, quoting=1)
        print(f"File CSV corretto e salvato come {output_file}")
    
    except Exception as e:
        print(f"Errore durante la correzione del file: {str(e)}")

if __name__ == "__main__":
    input_file = 'uploads/export_1747854974_0e167b48.csv'
    output_file = 'uploads/export_1747854974_0e167b48_fixed.csv'
    
    fix_csv_file(input_file, output_file)
