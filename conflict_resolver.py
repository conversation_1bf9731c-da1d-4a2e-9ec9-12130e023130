#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Conflict Resolver - Sistema di rilevamento e risoluzione conflitti per app-roberto.
Gestisce conflitti di sincronizzazione con strategie intelligenti di merge e risoluzione.
"""

import logging
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import difflib

# Import dei moduli esistenti
try:
    from supabase_integration import SupabaseManager
except ImportError as e:
    logging.warning(f"Alcuni moduli non disponibili: {e}")

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ConflictType(Enum):
    """Tipi di conflitti."""
    CONCURRENT_MODIFICATION = "concurrent_modification"
    VERSION_MISMATCH = "version_mismatch"
    DATA_INCONSISTENCY = "data_inconsistency"
    SCHEMA_CONFLICT = "schema_conflict"
    BUSINESS_RULE_VIOLATION = "business_rule_violation"

class ResolutionStrategy(Enum):
    """Strategie di risoluzione conflitti."""
    LATEST_WINS = "latest_wins"
    MANUAL_REVIEW = "manual_review"
    INTELLIGENT_MERGE = "intelligent_merge"
    FIELD_PRIORITY = "field_priority"
    BUSINESS_RULES = "business_rules"

@dataclass
class ConflictInfo:
    """Informazioni su un conflitto."""
    conflict_id: str
    conflict_type: ConflictType
    table_name: str
    record_id: int
    local_version: Dict[str, Any]
    remote_version: Dict[str, Any]
    conflicting_fields: List[str]
    detected_at: datetime
    resolution_strategy: Optional[ResolutionStrategy] = None
    resolved_at: Optional[datetime] = None
    resolved_version: Optional[Dict[str, Any]] = None
    resolution_notes: str = ""

class ConflictResolver:
    """
    Sistema di risoluzione conflitti che:
    - Rileva conflitti di sincronizzazione
    - Applica strategie intelligenti di risoluzione
    - Gestisce merge automatico e manuale
    - Mantiene cronologia delle risoluzioni
    - Supporta regole di business personalizzate
    """

    def __init__(self, supabase_manager: Optional[SupabaseManager] = None):
        self.supabase_manager = supabase_manager or SupabaseManager()

        # Configurazione risoluzione conflitti
        self.DEFAULT_STRATEGY = ResolutionStrategy.INTELLIGENT_MERGE
        self.AUTO_RESOLVE_THRESHOLD = 0.8  # Soglia per risoluzione automatica
        self.CONFLICT_TIMEOUT_HOURS = 24    # Timeout per conflitti non risolti

        # Priorità dei campi per risoluzione
        self.FIELD_PRIORITIES = {
            "id": 10,                    # ID sempre prioritario
            "created_at": 9,             # Data creazione importante
            "updated_at": 8,             # Data aggiornamento importante
            "status": 7,                 # Status business critical
            "version": 6,                # Versioning importante
            "user_id": 5,                # Ownership importante
            "data": 4,                   # Dati generici
            "metadata": 3,               # Metadati meno critici
            "cache": 1                   # Cache meno importante
        }

        # Regole di business per risoluzione
        self.BUSINESS_RULES = {
            "file_uploads": {
                "immutable_fields": ["id", "created_at", "original_filename"],
                "priority_fields": ["status", "file_type", "file_size"],
                "merge_strategy": "latest_wins_with_validation"
            },
            "processed_data": {
                "immutable_fields": ["id", "file_upload_id", "created_at"],
                "priority_fields": ["data_type", "processed_data", "statistics"],
                "merge_strategy": "intelligent_merge"
            },
            "employee_costs": {
                "immutable_fields": ["id", "employee_name"],
                "priority_fields": ["hourly_rate", "vat_included", "is_active"],
                "merge_strategy": "business_rules"
            }
        }

        # Cache conflitti attivi
        self.active_conflicts = {}
        self.resolution_history = []

        # Statistiche risoluzione
        self.resolution_stats = {
            "total_conflicts": 0,
            "auto_resolved": 0,
            "manual_resolved": 0,
            "unresolved": 0,
            "resolution_success_rate": 0.0
        }

        logger.info("ConflictResolver inizializzato")

    def detect_conflict(self, table_name: str, record_id: int,
                       local_version: Dict[str, Any], remote_version: Dict[str, Any]) -> Optional[ConflictInfo]:
        """
        Rileva conflitti tra versioni locale e remota di un record.

        Args:
            table_name: Nome della tabella
            record_id: ID del record
            local_version: Versione locale del record
            remote_version: Versione remota del record

        Returns:
            Informazioni sul conflitto se rilevato
        """
        try:
            # Verifica se c'è effettivamente un conflitto
            conflicting_fields = self._identify_conflicting_fields(local_version, remote_version)

            if not conflicting_fields:
                return None

            # Determina tipo di conflitto
            conflict_type = self._determine_conflict_type(local_version, remote_version, conflicting_fields)

            # Crea informazioni conflitto
            conflict_id = f"{table_name}_{record_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            conflict_info = ConflictInfo(
                conflict_id=conflict_id,
                conflict_type=conflict_type,
                table_name=table_name,
                record_id=record_id,
                local_version=local_version,
                remote_version=remote_version,
                conflicting_fields=conflicting_fields,
                detected_at=datetime.now()
            )

            # Aggiungi ai conflitti attivi
            self.active_conflicts[conflict_id] = conflict_info
            self.resolution_stats["total_conflicts"] += 1

            logger.warning(f"🔥 Conflitto rilevato: {conflict_id} ({conflict_type.value})")
            return conflict_info

        except Exception as e:
            logger.error(f"❌ Errore rilevamento conflitto: {str(e)}")
            return None

    def resolve_conflict(self, conflict_info: ConflictInfo,
                        strategy: Optional[ResolutionStrategy] = None) -> Dict[str, Any]:
        """
        Risolve un conflitto usando la strategia specificata.

        Args:
            conflict_info: Informazioni sul conflitto
            strategy: Strategia di risoluzione (opzionale)

        Returns:
            Risultato della risoluzione
        """
        logger.info(f"🔧 Risoluzione conflitto: {conflict_info.conflict_id}")

        resolution_result = {
            "conflict_id": conflict_info.conflict_id,
            "strategy_used": strategy or self.DEFAULT_STRATEGY,
            "success": False,
            "resolved_version": None,
            "resolution_notes": "",
            "auto_resolved": False
        }

        try:
            # Usa strategia specificata o default
            resolution_strategy = strategy or self._select_optimal_strategy(conflict_info)

            # Applica strategia di risoluzione
            if resolution_strategy == ResolutionStrategy.LATEST_WINS:
                resolved_version = self._resolve_latest_wins(conflict_info)
            elif resolution_strategy == ResolutionStrategy.INTELLIGENT_MERGE:
                resolved_version = self._resolve_intelligent_merge(conflict_info)
            elif resolution_strategy == ResolutionStrategy.FIELD_PRIORITY:
                resolved_version = self._resolve_field_priority(conflict_info)
            elif resolution_strategy == ResolutionStrategy.BUSINESS_RULES:
                resolved_version = self._resolve_business_rules(conflict_info)
            else:  # MANUAL_REVIEW
                return self._prepare_manual_review(conflict_info)

            # Valida risoluzione
            if self._validate_resolution(conflict_info, resolved_version):
                # Aggiorna conflitto
                conflict_info.resolution_strategy = resolution_strategy
                conflict_info.resolved_at = datetime.now()
                conflict_info.resolved_version = resolved_version

                # Aggiorna risultato
                resolution_result["success"] = True
                resolution_result["resolved_version"] = resolved_version
                resolution_result["auto_resolved"] = True
                resolution_result["resolution_notes"] = f"Risolto con strategia {resolution_strategy.value}"

                # Rimuovi dai conflitti attivi
                if conflict_info.conflict_id in self.active_conflicts:
                    del self.active_conflicts[conflict_info.conflict_id]

                # Aggiungi alla cronologia
                self.resolution_history.append(conflict_info)
                self.resolution_stats["auto_resolved"] += 1

                logger.info(f"✅ Conflitto risolto automaticamente: {conflict_info.conflict_id}")
            else:
                resolution_result["resolution_notes"] = "Risoluzione fallita nella validazione"
                logger.warning(f"⚠️ Risoluzione fallita per: {conflict_info.conflict_id}")

        except Exception as e:
            resolution_result["resolution_notes"] = f"Errore: {str(e)}"
            logger.error(f"❌ Errore risoluzione conflitto: {str(e)}")

        return resolution_result

    def resolve_all_pending_conflicts(self) -> Dict[str, Any]:
        """
        Risolve tutti i conflitti pendenti.

        Returns:
            Risultato della risoluzione batch
        """
        logger.info(f"🔄 Risoluzione batch di {len(self.active_conflicts)} conflitti")

        batch_result = {
            "total_conflicts": len(self.active_conflicts),
            "resolved": 0,
            "failed": 0,
            "manual_review_required": 0,
            "results": []
        }

        # Copia lista conflitti per evitare modifiche durante iterazione
        conflicts_to_resolve = list(self.active_conflicts.values())

        for conflict_info in conflicts_to_resolve:
            try:
                result = self.resolve_conflict(conflict_info)
                batch_result["results"].append(result)

                if result["success"]:
                    batch_result["resolved"] += 1
                elif result.get("manual_review_required", False):
                    batch_result["manual_review_required"] += 1
                else:
                    batch_result["failed"] += 1

            except Exception as e:
                logger.error(f"Errore risoluzione conflitto {conflict_info.conflict_id}: {e}")
                batch_result["failed"] += 1

        logger.info(f"✅ Risoluzione batch completata: {batch_result['resolved']} risolti, "
                   f"{batch_result['failed']} falliti, {batch_result['manual_review_required']} manuali")

        return batch_result

    def _identify_conflicting_fields(self, local_version: Dict[str, Any],
                                   remote_version: Dict[str, Any]) -> List[str]:
        """Identifica i campi in conflitto tra due versioni."""
        conflicting_fields = []

        # Confronta tutti i campi presenti in entrambe le versioni
        all_fields = set(local_version.keys()) | set(remote_version.keys())

        for field in all_fields:
            local_value = local_version.get(field)
            remote_value = remote_version.get(field)

            # Ignora campi automatici che possono differire
            if field in ['updated_at', 'last_sync', 'sync_hash']:
                continue

            # Confronta valori
            if local_value != remote_value:
                conflicting_fields.append(field)

        return conflicting_fields

    def _determine_conflict_type(self, local_version: Dict[str, Any],
                               remote_version: Dict[str, Any],
                               conflicting_fields: List[str]) -> ConflictType:
        """Determina il tipo di conflitto."""

        # Verifica conflitto di versione
        local_version_num = local_version.get("version", 1)
        remote_version_num = remote_version.get("version", 1)

        if local_version_num != remote_version_num:
            return ConflictType.VERSION_MISMATCH

        # Verifica modifiche concorrenti
        local_updated = local_version.get("updated_at")
        remote_updated = remote_version.get("updated_at")

        if local_updated and remote_updated:
            try:
                local_time = datetime.fromisoformat(str(local_updated).replace('Z', '+00:00'))
                remote_time = datetime.fromisoformat(str(remote_updated).replace('Z', '+00:00'))

                # Se modifiche entro 5 minuti, è modifica concorrente
                if abs((local_time - remote_time).total_seconds()) < 300:
                    return ConflictType.CONCURRENT_MODIFICATION
            except:
                pass

        # Verifica inconsistenza dati
        critical_fields = ['id', 'status', 'data_type']
        if any(field in conflicting_fields for field in critical_fields):
            return ConflictType.DATA_INCONSISTENCY

        # Default: modifica concorrente
        return ConflictType.CONCURRENT_MODIFICATION

    def _select_optimal_strategy(self, conflict_info: ConflictInfo) -> ResolutionStrategy:
        """Seleziona la strategia ottimale per risolvere un conflitto."""

        # Strategia basata sul tipo di conflitto
        if conflict_info.conflict_type == ConflictType.VERSION_MISMATCH:
            return ResolutionStrategy.LATEST_WINS

        elif conflict_info.conflict_type == ConflictType.CONCURRENT_MODIFICATION:
            # Se pochi campi in conflitto, usa merge intelligente
            if len(conflict_info.conflicting_fields) <= 3:
                return ResolutionStrategy.INTELLIGENT_MERGE
            else:
                return ResolutionStrategy.FIELD_PRIORITY

        elif conflict_info.conflict_type == ConflictType.DATA_INCONSISTENCY:
            return ResolutionStrategy.BUSINESS_RULES

        else:
            return ResolutionStrategy.MANUAL_REVIEW

    def _resolve_latest_wins(self, conflict_info: ConflictInfo) -> Dict[str, Any]:
        """Risolve conflitto con strategia 'latest wins'."""
        local_updated = conflict_info.local_version.get("updated_at")
        remote_updated = conflict_info.remote_version.get("updated_at")

        if local_updated and remote_updated:
            try:
                local_time = datetime.fromisoformat(str(local_updated).replace('Z', '+00:00'))
                remote_time = datetime.fromisoformat(str(remote_updated).replace('Z', '+00:00'))

                if local_time > remote_time:
                    winner = conflict_info.local_version.copy()
                    conflict_info.resolution_notes = "Locale vince (più recente)"
                else:
                    winner = conflict_info.remote_version.copy()
                    conflict_info.resolution_notes = "Remoto vince (più recente)"

                return winner
            except:
                pass

        # Fallback: usa versione locale
        conflict_info.resolution_notes = "Locale vince (fallback)"
        return conflict_info.local_version.copy()

    def _resolve_intelligent_merge(self, conflict_info: ConflictInfo) -> Dict[str, Any]:
        """Risolve conflitto con merge intelligente."""
        merged = conflict_info.remote_version.copy()  # Base: versione remota
        merge_notes = []

        for field in conflict_info.conflicting_fields:
            local_value = conflict_info.local_version.get(field)
            remote_value = conflict_info.remote_version.get(field)

            # Logica di merge intelligente
            if local_value is None:
                # Mantieni valore remoto
                merge_notes.append(f"{field}: mantiene remoto (locale None)")
            elif remote_value is None:
                # Usa valore locale
                merged[field] = local_value
                merge_notes.append(f"{field}: usa locale (remoto None)")
            elif isinstance(local_value, str) and isinstance(remote_value, str):
                # Merge stringhe: usa la più lunga se significativamente diversa
                if len(local_value) > len(remote_value) * 1.2:
                    merged[field] = local_value
                    merge_notes.append(f"{field}: usa locale (più dettagliato)")
                else:
                    merge_notes.append(f"{field}: mantiene remoto")
            elif isinstance(local_value, (int, float)) and isinstance(remote_value, (int, float)):
                # Merge numeri: usa il maggiore se positivo
                if local_value > 0 and local_value > remote_value:
                    merged[field] = local_value
                    merge_notes.append(f"{field}: usa locale (valore maggiore)")
                else:
                    merge_notes.append(f"{field}: mantiene remoto")
            else:
                # Default: usa priorità campo
                field_priority = self.FIELD_PRIORITIES.get(field, 5)
                if field_priority >= 7:  # Campo ad alta priorità
                    merged[field] = local_value
                    merge_notes.append(f"{field}: usa locale (alta priorità)")
                else:
                    merge_notes.append(f"{field}: mantiene remoto (bassa priorità)")

        # Aggiorna timestamp e note
        merged['updated_at'] = datetime.now().isoformat()
        merged['merged_at'] = datetime.now().isoformat()
        conflict_info.resolution_notes = "; ".join(merge_notes)

        return merged

    def _resolve_field_priority(self, conflict_info: ConflictInfo) -> Dict[str, Any]:
        """Risolve conflitto basandosi sulla priorità dei campi."""
        merged = conflict_info.remote_version.copy()
        priority_notes = []

        for field in conflict_info.conflicting_fields:
            field_priority = self.FIELD_PRIORITIES.get(field, 5)
            local_value = conflict_info.local_version.get(field)

            # Se campo ad alta priorità, usa versione locale
            if field_priority >= 7:
                merged[field] = local_value
                priority_notes.append(f"{field}: locale (priorità {field_priority})")
            else:
                priority_notes.append(f"{field}: remoto (priorità {field_priority})")

        merged['updated_at'] = datetime.now().isoformat()
        conflict_info.resolution_notes = "; ".join(priority_notes)

        return merged

    def _resolve_business_rules(self, conflict_info: ConflictInfo) -> Dict[str, Any]:
        """Risolve conflitto applicando regole di business."""
        table_rules = self.BUSINESS_RULES.get(conflict_info.table_name, {})
        immutable_fields = table_rules.get("immutable_fields", [])
        priority_fields = table_rules.get("priority_fields", [])

        merged = conflict_info.remote_version.copy()
        business_notes = []

        for field in conflict_info.conflicting_fields:
            local_value = conflict_info.local_version.get(field)

            if field in immutable_fields:
                # Campi immutabili: mantieni versione originale (remota)
                business_notes.append(f"{field}: immutabile, mantiene remoto")
            elif field in priority_fields:
                # Campi prioritari: usa versione locale
                merged[field] = local_value
                business_notes.append(f"{field}: prioritario, usa locale")
            else:
                # Applica logica di merge standard
                if local_value is not None:
                    merged[field] = local_value
                    business_notes.append(f"{field}: usa locale (non None)")
                else:
                    business_notes.append(f"{field}: mantiene remoto")

        merged['updated_at'] = datetime.now().isoformat()
        conflict_info.resolution_notes = "; ".join(business_notes)

        return merged

    def _prepare_manual_review(self, conflict_info: ConflictInfo) -> Dict[str, Any]:
        """Prepara conflitto per revisione manuale."""
        return {
            "conflict_id": conflict_info.conflict_id,
            "strategy_used": ResolutionStrategy.MANUAL_REVIEW,
            "success": False,
            "manual_review_required": True,
            "conflict_details": {
                "type": conflict_info.conflict_type.value,
                "fields": conflict_info.conflicting_fields,
                "local_version": conflict_info.local_version,
                "remote_version": conflict_info.remote_version
            },
            "resolution_notes": "Richiede revisione manuale"
        }

    def _validate_resolution(self, conflict_info: ConflictInfo, resolved_version: Dict[str, Any]) -> bool:
        """Valida che la risoluzione sia corretta."""
        try:
            # Verifica che tutti i campi obbligatori siano presenti
            required_fields = ['id']
            for field in required_fields:
                if field not in resolved_version:
                    return False

            # Verifica che l'ID non sia cambiato
            original_id = conflict_info.local_version.get('id') or conflict_info.remote_version.get('id')
            if resolved_version.get('id') != original_id:
                return False

            # Verifica regole di business specifiche per tabella
            table_rules = self.BUSINESS_RULES.get(conflict_info.table_name, {})
            immutable_fields = table_rules.get("immutable_fields", [])

            for field in immutable_fields:
                original_value = conflict_info.remote_version.get(field)
                resolved_value = resolved_version.get(field)
                if original_value != resolved_value:
                    logger.warning(f"Campo immutabile modificato: {field}")
                    return False

            return True

        except Exception as e:
            logger.error(f"Errore validazione risoluzione: {e}")
            return False

    def get_conflict_statistics(self) -> Dict[str, Any]:
        """Restituisce statistiche sui conflitti."""
        stats = self.resolution_stats.copy()

        # Calcola tasso di successo
        total_resolved = stats["auto_resolved"] + stats["manual_resolved"]
        if stats["total_conflicts"] > 0:
            stats["resolution_success_rate"] = (total_resolved / stats["total_conflicts"]) * 100

        # Aggiungi informazioni sui conflitti attivi
        stats["active_conflicts"] = len(self.active_conflicts)
        stats["resolution_history_size"] = len(self.resolution_history)

        return stats

# Istanza globale
conflict_resolver = ConflictResolver()
