#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Modulo di integrazione con Supabase per app-roberto.
Gestisce la connessione al database PostgreSQL e le operazioni CRUD.
"""

import os
import logging
from typing import Optional, Dict, List, Any
from datetime import datetime
import pandas as pd

try:
    from supabase import create_client, Client
    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False
    logging.warning("Supabase non disponibile. Installare con: pip install supabase")

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SupabaseManager:
    """
    Gestore per le operazioni Supabase.
    Fornisce un'interfaccia unificata per interagire con il database PostgreSQL.
    """

    def __init__(self, url: Optional[str] = None, key: Optional[str] = None, use_service_key: bool = True):
        """
        Inizializza il client Supabase.

        Args:
            url: URL del progetto Supabase (opzionale, può essere impostato tramite variabile d'ambiente)
            key: Chiave API Supabase (opzionale, può essere impostata tramite variabile d'ambiente)
            use_service_key: Se utilizzare service_role key per operazioni privilegiate (default: True)
        """
        self.url = url or os.environ.get("SUPABASE_URL")

        # Usa service key per operazioni privilegiate, anon key per operazioni utente
        if use_service_key:
            self.key = key or os.environ.get("SUPABASE_SERVICE_KEY") or os.environ.get("SUPABASE_KEY")
        else:
            self.key = key or os.environ.get("SUPABASE_KEY")

        self.client: Optional[Client] = None
        self.is_connected = False

        if not SUPABASE_AVAILABLE:
            logger.error("Supabase non è disponibile. Installare con: pip install supabase")
            return

        if not self.url or not self.key:
            logger.warning("URL o chiave Supabase non configurati. Alcune funzionalità potrebbero non essere disponibili.")
            return

        try:
            self.client = create_client(self.url, self.key)
            self.is_connected = True
            key_type = "service_role" if use_service_key and "SUPABASE_SERVICE_KEY" in os.environ else "anon"
            logger.info(f"Connessione a Supabase stabilita con successo (key: {key_type})")
        except Exception as e:
            logger.error(f"Errore nella connessione a Supabase: {str(e)}")
            self.is_connected = False

    def test_connection(self) -> bool:
        """
        Testa la connessione al database.

        Returns:
            bool: True se la connessione è attiva, False altrimenti
        """
        if not self.is_connected or not self.client:
            return False

        try:
            # Test semplice: prova a fare una query sulla tabella file_uploads
            result = self.client.table("file_uploads").select("id").limit(1).execute()
            return True
        except Exception as e:
            logger.error(f"Test connessione fallito: {str(e)}")
            return False

    def create_tables_if_not_exist(self) -> bool:
        """
        Crea le tabelle necessarie se non esistono.

        Returns:
            bool: True se le tabelle sono state create/esistono, False altrimenti
        """
        if not self.is_connected or not self.client:
            logger.error("Connessione Supabase non disponibile")
            return False

        try:
            # Schema per le tabelle principali
            tables_sql = [
                """
                CREATE TABLE IF NOT EXISTS file_uploads (
                    id SERIAL PRIMARY KEY,
                    filename VARCHAR(255) NOT NULL,
                    original_filename VARCHAR(255) NOT NULL,
                    file_type VARCHAR(50) NOT NULL,
                    file_path TEXT NOT NULL,
                    upload_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    session_id VARCHAR(100),
                    mcp_file_id VARCHAR(100),
                    file_size BIGINT,
                    status VARCHAR(20) DEFAULT 'uploaded',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                """,
                """
                CREATE TABLE IF NOT EXISTS processed_data (
                    id SERIAL PRIMARY KEY,
                    file_upload_id INTEGER REFERENCES file_uploads(id),
                    data_type VARCHAR(50) NOT NULL,
                    processed_data JSONB,
                    statistics JSONB,
                    quality_report JSONB,
                    processing_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                """,
                """
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id SERIAL PRIMARY KEY,
                    session_id VARCHAR(100) UNIQUE NOT NULL,
                    user_data JSONB,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                """,
                """
                CREATE TABLE IF NOT EXISTS system_config (
                    id SERIAL PRIMARY KEY,
                    config_key VARCHAR(100) UNIQUE NOT NULL,
                    config_value JSONB,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                """
            ]

            # Esegui le query di creazione tabelle
            for sql in tables_sql:
                try:
                    # Nota: Supabase Python client non supporta direttamente SQL DDL
                    # Dovremmo usare l'API REST o configurare le tabelle tramite dashboard
                    logger.info("Le tabelle dovrebbero essere create tramite Supabase Dashboard o SQL Editor")
                except Exception as e:
                    logger.error(f"Errore nella creazione tabella: {str(e)}")

            return True

        except Exception as e:
            logger.error(f"Errore nella creazione delle tabelle: {str(e)}")
            return False

    def save_file_upload(self, file_info: Dict[str, Any]) -> Optional[int]:
        """
        Salva le informazioni di un file caricato.

        Args:
            file_info: Dizionario con le informazioni del file

        Returns:
            int: ID del record inserito, None se errore
        """
        if not self.is_connected or not self.client:
            logger.error("Connessione Supabase non disponibile")
            return None

        try:
            result = self.client.table("file_uploads").insert(file_info).execute()
            if result.data:
                return result.data[0]['id']
            return None
        except Exception as e:
            logger.error(f"Errore nel salvare file upload: {str(e)}")
            return None

    def get_file_uploads(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Recupera la lista dei file caricati.

        Args:
            limit: Numero massimo di record da recuperare

        Returns:
            List: Lista dei file caricati
        """
        if not self.is_connected or not self.client:
            logger.error("Connessione Supabase non disponibile")
            return []

        try:
            result = self.client.table("file_uploads").select("*").order("created_at", desc=True).limit(limit).execute()
            return result.data if result.data else []
        except Exception as e:
            logger.error(f"Errore nel recuperare file uploads: {str(e)}")
            return []

    def save_processed_data(self, file_upload_id: int, data_type: str,
                          processed_data: Dict[str, Any],
                          statistics: Optional[Dict[str, Any]] = None,
                          quality_report: Optional[Dict[str, Any]] = None) -> Optional[int]:
        """
        Salva i dati elaborati.

        Args:
            file_upload_id: ID del file upload di riferimento
            data_type: Tipo di dati elaborati
            processed_data: Dati elaborati
            statistics: Statistiche opzionali
            quality_report: Report qualità opzionale

        Returns:
            int: ID del record inserito, None se errore
        """
        if not self.is_connected or not self.client:
            logger.error("Connessione Supabase non disponibile")
            return None

        try:
            data = {
                "file_upload_id": file_upload_id,
                "data_type": data_type,
                "processed_data": processed_data,
                "statistics": statistics,
                "quality_report": quality_report
            }
            result = self.client.table("processed_data").insert(data).execute()
            if result.data:
                return result.data[0]['id']
            return None
        except Exception as e:
            logger.error(f"Errore nel salvare dati elaborati: {str(e)}")
            return None

    def get_processed_data(self, file_upload_id: int) -> Optional[Dict[str, Any]]:
        """
        Recupera i dati elaborati per un file.

        Args:
            file_upload_id: ID del file upload

        Returns:
            Dict: Dati elaborati, None se non trovati
        """
        if not self.is_connected or not self.client:
            logger.error("Connessione Supabase non disponibile")
            return None

        try:
            result = self.client.table("processed_data").select("*").eq("file_upload_id", file_upload_id).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Errore nel recuperare dati elaborati: {str(e)}")
            return None

    # ==========================================
    # METODI PER GESTIONE COSTI DIPENDENTI
    # ==========================================

    def save_employee_cost(self, employee_data: Dict[str, Any]) -> Optional[int]:
        """
        Salva o aggiorna i costi di un dipendente.

        Args:
            employee_data: Dati del dipendente (nome, tariffa oraria, IVA, etc.)

        Returns:
            int: ID del record inserito/aggiornato, None se errore
        """
        if not self.is_connected or not self.client:
            logger.error("Connessione Supabase non disponibile")
            return None

        try:
            # Usa upsert per inserire o aggiornare
            result = self.client.table("employee_costs").upsert(employee_data).execute()
            if result.data:
                return result.data[0]['id']
            return None
        except Exception as e:
            logger.error(f"Errore nel salvare costi dipendente: {str(e)}")
            return None

    def get_employee_costs(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """
        Recupera i costi dei dipendenti.

        Args:
            active_only: Se recuperare solo dipendenti attivi

        Returns:
            List: Lista dei costi dipendenti
        """
        if not self.is_connected or not self.client:
            logger.error("Connessione Supabase non disponibile")
            return []

        try:
            query = self.client.table("employee_costs").select("*")
            if active_only:
                query = query.eq("is_active", True)
            result = query.order("employee_name").execute()
            return result.data if result.data else []
        except Exception as e:
            logger.error(f"Errore nel recuperare costi dipendenti: {str(e)}")
            return []

    def get_employee_cost_by_name(self, employee_name: str) -> Optional[Dict[str, Any]]:
        """
        Recupera i costi di un dipendente specifico.

        Args:
            employee_name: Nome del dipendente

        Returns:
            Dict: Dati del dipendente, None se non trovato
        """
        if not self.is_connected or not self.client:
            logger.error("Connessione Supabase non disponibile")
            return None

        try:
            result = self.client.table("employee_costs").select("*").eq("employee_name", employee_name).eq("is_active", True).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Errore nel recuperare costi dipendente {employee_name}: {str(e)}")
            return None

    # ==========================================
    # METODI PER GESTIONE CONFIGURAZIONI
    # ==========================================

    def get_config(self, config_key: str, default_value: Any = None) -> Any:
        """
        Recupera una configurazione di sistema.

        Args:
            config_key: Chiave della configurazione
            default_value: Valore di default se non trovato

        Returns:
            Any: Valore della configurazione
        """
        if not self.is_connected or not self.client:
            logger.warning("Connessione Supabase non disponibile, uso valore default")
            return default_value

        try:
            result = self.client.table("system_config").select("config_value").eq("config_key", config_key).execute()
            if result.data:
                return result.data[0]['config_value']
            return default_value
        except Exception as e:
            logger.error(f"Errore nel recuperare configurazione {config_key}: {str(e)}")
            return default_value

    def set_config(self, config_key: str, config_value: Any, description: str = "") -> bool:
        """
        Imposta una configurazione di sistema.

        Args:
            config_key: Chiave della configurazione
            config_value: Valore della configurazione
            description: Descrizione opzionale

        Returns:
            bool: True se salvato con successo
        """
        if not self.is_connected or not self.client:
            logger.error("Connessione Supabase non disponibile")
            return False

        try:
            config_data = {
                "config_key": config_key,
                "config_value": config_value,
                "description": description
            }
            result = self.client.table("system_config").upsert(config_data).execute()
            return bool(result.data)
        except Exception as e:
            logger.error(f"Errore nel salvare configurazione {config_key}: {str(e)}")
            return False

    # ==========================================
    # METODI PER LOGGING E ANALISI AI
    # ==========================================

    def log_system_event(self, level: str, message: str, component: str = "", details: Dict[str, Any] = None) -> bool:
        """
        Registra un evento di sistema.

        Args:
            level: Livello del log (INFO, WARNING, ERROR, etc.)
            message: Messaggio del log
            component: Componente che ha generato il log
            details: Dettagli aggiuntivi in formato JSON

        Returns:
            bool: True se salvato con successo
        """
        if not self.is_connected or not self.client:
            # Fallback al logging standard se Supabase non disponibile
            logger.log(getattr(logging, level.upper(), logging.INFO), f"[{component}] {message}")
            return False

        try:
            log_data = {
                "log_level": level.upper(),
                "message": message,
                "component": component,
                "details": details or {}
            }
            result = self.client.table("system_logs").insert(log_data).execute()
            return bool(result.data)
        except Exception as e:
            logger.error(f"Errore nel salvare log: {str(e)}")
            return False

    def save_ai_analysis(self, file_upload_id: int, analysis_type: str,
                        ai_insights: Dict[str, Any], recommendations: Dict[str, Any] = None,
                        quality_score: float = None, model_used: str = "",
                        processing_time_ms: int = None) -> Optional[int]:
        """
        Salva i risultati di un'analisi AI.

        Args:
            file_upload_id: ID del file analizzato
            analysis_type: Tipo di analisi eseguita
            ai_insights: Insights generati dall'AI
            recommendations: Raccomandazioni opzionali
            quality_score: Punteggio di qualità (0-1)
            model_used: Modello AI utilizzato
            processing_time_ms: Tempo di elaborazione in millisecondi

        Returns:
            int: ID del record inserito, None se errore
        """
        if not self.is_connected or not self.client:
            logger.error("Connessione Supabase non disponibile")
            return None

        try:
            analysis_data = {
                "file_upload_id": file_upload_id,
                "analysis_type": analysis_type,
                "ai_insights": ai_insights,
                "recommendations": recommendations or {},
                "quality_score": quality_score,
                "model_used": model_used,
                "processing_time_ms": processing_time_ms
            }
            result = self.client.table("ai_analyses").insert(analysis_data).execute()
            if result.data:
                return result.data[0]['id']
            return None
        except Exception as e:
            logger.error(f"Errore nel salvare analisi AI: {str(e)}")
            return None

    # ==========================================
    # METODI PER ANALISI INCROCIATA
    # ==========================================

    def get_cross_analysis_data(self, data_types: List[str] = None,
                               date_from: str = None, date_to: str = None) -> Dict[str, Any]:
        """
        Recupera dati per analisi incrociata.

        Args:
            data_types: Tipi di dati da includere nell'analisi
            date_from: Data di inizio (formato YYYY-MM-DD)
            date_to: Data di fine (formato YYYY-MM-DD)

        Returns:
            Dict: Dati aggregati per l'analisi incrociata
        """
        if not self.is_connected or not self.client:
            logger.error("Connessione Supabase non disponibile")
            return {}

        try:
            # Query base per i dati elaborati
            query = self.client.table("processed_data").select("""
                *,
                file_uploads!inner(
                    filename,
                    file_type,
                    upload_timestamp,
                    created_at
                )
            """)

            # Filtri opzionali
            if data_types:
                query = query.in_("data_type", data_types)
            if date_from:
                query = query.gte("file_uploads.created_at", date_from)
            if date_to:
                query = query.lte("file_uploads.created_at", date_to)

            result = query.order("created_at", desc=True).execute()

            # Organizza i dati per tipo
            organized_data = {}
            for record in result.data or []:
                data_type = record['data_type']
                if data_type not in organized_data:
                    organized_data[data_type] = []
                organized_data[data_type].append(record)

            return organized_data

        except Exception as e:
            logger.error(f"Errore nell'analisi incrociata: {str(e)}")
            return {}

    def get_employee_productivity_stats(self, employee_name: str = None,
                                      date_from: str = None, date_to: str = None) -> Dict[str, Any]:
        """
        Calcola statistiche di produttività per i dipendenti.

        Args:
            employee_name: Nome specifico del dipendente (opzionale)
            date_from: Data di inizio analisi
            date_to: Data di fine analisi

        Returns:
            Dict: Statistiche di produttività
        """
        if not self.is_connected or not self.client:
            logger.error("Connessione Supabase non disponibile")
            return {}

        try:
            # Recupera dati di attività e costi
            activity_data = self.get_cross_analysis_data(
                data_types=['attivita', 'timbrature'],
                date_from=date_from,
                date_to=date_to
            )

            employee_costs = self.get_employee_costs()

            # Calcola statistiche (implementazione semplificata)
            stats = {
                "total_employees": len(employee_costs),
                "activity_records": len(activity_data.get('attivita', [])),
                "timesheet_records": len(activity_data.get('timbrature', [])),
                "analysis_period": f"{date_from or 'N/A'} - {date_to or 'N/A'}"
            }

            return stats

        except Exception as e:
            logger.error(f"Errore nel calcolo statistiche produttività: {str(e)}")
            return {}

# Istanza globale del manager Supabase
supabase_manager = SupabaseManager()
