# 📋 REVISIONE FINALE - PROMPT GUIDA PER DEBUG E INTEGRAZIONE DATI

## 🎯 **OBIETTIVO GENERALE**

L'obiettivo di questo progetto è creare un sistema robusto per l'ingestione, la normalizzazione e l'analisi di dati provenienti da diverse fonti CSV/Excel, al fine di ottenere statistiche complete su costi, impegno, produttività dei dipendenti, andamento delle attività e gestione dei ticket. L'approccio deve essere interattivo, permettendo all'utente di convalidare e correggere i dati estratti prima dell'inserimento nel database Supabase.

## 🔧 **MODALITÀ DI LAVORO RICHIESTA**

Augment Code deve operare in una modalità di "debug guidato e interattivo" su tutte le funzioni del progetto, con un focus sull'ingestione e la normalizzazione dei dati. Non deve modificare la struttura esistente di template o funzioni complesse che non siano direttamente correlate al processo di importazione e validazione dei dati.

---

## 🏗️ **PRINCIPI FONDAMENTALI DI DEBUG E COLLABORAZIONE**

### **1. 🔄 Iterazione Guidata e Persistenza degli Errori Corretti**

- Augment Code deve presentare gli errori e le incongruenze, attendere l'input dell'utente per la correzione, applicare la correzione e assicurarsi che lo stesso errore non si ripresenti al riavvio dell'applicazione o alla successiva esecuzione della funzione. L'obiettivo è eliminare gli errori in modo persistente, non solo temporaneo.
- Se, ad esempio, si riscontra un bug nella funzionalità "Chat AI" e l'utente fornisce istruzioni per correggerlo, la correzione deve essere implementata in modo che la "Chat AI" funzioni correttamente anche dopo il riavvio dell'applicazione.

### **2. 📚 Verifica di Librerie e Best Practice (Utilizzo di Context 7)**

- Durante il processo di debug e ottimizzazione, Augment Code deve consultare regolarmente il "Context 7" (riferimento a una base di conoscenza implicita o esplicita di best practice, librerie aggiornate, e soluzioni comuni) per verificare l'esistenza o meno di librerie aggiornate, pratiche di codifica migliori o soluzioni a problemi noti.
- Se viene identificata una libreria deprecata, una vulnerabilità di sicurezza, o una pratica meno efficiente, Augment Code deve segnalarlo all'utente e proporre un aggiornamento o una modifica, sempre con la possibilità di revisione e conferma da parte dell'utente.
- L'utente si aspetta che il codice sia mantenuto aggiornato e aderente alle migliori pratiche.

### **3. 🔒 Sicurezza e Reversibilità delle Modifiche**

- È ASSOLUTAMENTE FONDAMENTALE che le modifiche apportate da Augment Code non arrechino danni al codice esistente.
- L'utente deve avere SEMPRE la possibilità di tornare indietro a una versione funzionante del codice. Questo implica che ogni modifica significativa o serie di modifiche debba essere trattata come una transazione reversibile, o che Augment Code suggerisca esplicitamente all'utente di effettuare un backup o di utilizzare un sistema di controllo versione (es. Git). Augment Code dovrebbe informare l'utente su come annullare le modifiche se necessario.
- Augment Code deve operare in modo incrementale, proponendo modifiche specifiche e attendendo la conferma dell'utente prima di applicarle.

---

## 📋 **TASK DETTAGLIATI**

### **TASK 1: 🔍 Analisi Preliminare e Configurazione Iniziale**

#### **Comprendi il Progetto**
- Analizza il codice Python fornito (app.py) e i file di configurazione per comprendere l'architettura attuale, le dipendenze, la logica di connessione a Supabase e la gestione delle API. 
- Presta particolare attenzione alle sezioni relative all'importazione dati e alla gestione degli errori, come evidenziato nel file errori app_py.pdf.

#### **Identifica le Funzioni Chiave**
- Individua le funzioni responsabili della lettura dei file, dell'estrazione dei dati, della mappatura delle colonne e dell'interazione con Supabase. 
- Contemporaneamente, identifica le funzioni che gestiscono le diverse funzionalità dell'applicazione (es. "Chat AI", navigazione tra le pagine, ecc.) per il debug funzionale.

#### **Preparazione all'Interazione**
- Riconosci che l'utente desidera un controllo granulare sul processo di importazione e sul debug delle funzionalità dell'app. 
- Preparati a implementare o rafforzare una logica di "wizard" interattivo per entrambi gli ambiti.

### **TASK 2: 🐛 Debug Funzionale dell'Applicazione (UI/UX e Logica Applicativa)**

#### **Monitoraggio Interattivo delle Funzionalità**
- Quando l'utente interagisce con l'applicazione (es. clicca su un pulsante, naviga tra le pagine, utilizza una funzionalità specifica come la "Chat AI"), Augment Code deve monitorare eventuali errori o malfunzionamenti.

#### **Diagnosi e Proposta di Correzione**
- Se viene rilevato un errore (es. una funzionalità non risponde, si verifica un'eccezione, un link non funziona), Augment Code deve diagnosticarne la causa.
- Proponi all'utente una soluzione chiara e concisa. Chiedi: "Ho rilevato un errore nella funzionalità X (descrizione dell'errore). La causa probabile è Y. Proporrei la seguente modifica per correggerlo: [codice/descrizione modifica]. Sei d'accordo ad applicarla?"

#### **Applicazione Persistente delle Correzioni**
- Una volta che l'utente approva una correzione, applicala al codice.
- **Cruciale**: Dopo aver applicato la correzione, verifica che l'errore non si ripresenti al riavvio dell'applicazione o alla successiva esecuzione della stessa funzionalità. Se l'errore persiste, segnalalo e inizia un nuovo ciclo di diagnosi e correzione.

### **TASK 3: 🧙‍♂️ Implementazione del "Wizard" di Caricamento File (Sezione Critica)**

Questa è la parte più importante per l'ingestione dati. Augment Code deve simulare o migliorare una sezione di "wizard" che permetta all'utente di caricare un file alla volta e convalidare l'estrazione.

#### **Richiesta Caricamento File**
- Quando viene avviata la procedura di importazione, Augment Code deve chiedere all'utente di caricare un file specifico (es. "timbrature.csv", "attivita.csv", "registro_auto.csv", etc.).

#### **Analisi Iniziale del File**
Una volta caricato il file, esegui una prima analisi:
- **Formato**: Rileva automaticamente il formato del file (CSV, Excel).
- **Delimitatore**: Se CSV, tenta di rilevare il delimitatore corretto (es. `,`, `;`).
- **Header**: Identifica la riga dell'header (se presente). Nota che per registro_auto_230525.CSV l'header potrebbe non essere sulla prima riga (estimatedRowsAboveHeader: 2).
- **Prime Righe**: Mostra le prime 5-10 righe del file all'utente.

#### **Proposta di Colonne Rilevate**
- Basandoti sull'analisi iniziale, proponi all'utente un elenco delle colonne rilevate e la loro potenziale interpretazione (es. "Colonna 'Nome Dipendente' rilevata come 'Nome' di tipo stringa").

#### **Feedback Utente - Convalida e Modifica**
- Chiedi all'utente: "Va bene come ho estratto i dati?"
- Chiedi: "È giusto che abbia rilevato le colonne in questo modo?"
- Chiedi: "Vuoi dirmi di modificare qualcosa (es. il delimitatore, la riga dell'header, i nomi delle colonne, il tipo di dato di una colonna)? In caso affermativo, specifica le modifiche."
- Permetti all'utente di rinominare le colonne rilevate per uniformarle a un nome standardizzato interno, se necessario.
- Permetti all'utente di specificare o correggere il tipo di dato per ciascuna colonna (es. data, numero, testo).

#### **Ciclo di Revisione**
- Ripeti il feedback e la richiesta di modifica fino a quando l'utente non conferma che l'estrazione e la mappatura delle colonne sono corrette per il file corrente.

### **TASK 4: 🔄 Normalizzazione e Mappatura dei Dati**

Una volta che l'utente ha convalidato l'estrazione delle colonne per un file specifico:

#### **Definizione di Entità Comuni**
Riconosci che ci sono entità comuni tra i diversi file che devono essere mappate e normalizzate:
- **Nomi Dipendenti**: Devono essere mappati a un ID dipendente univoco.
- **Nomi Clienti**: Devono essere mappati a un ID cliente univoco.
- **Nomi Auto/Targhe**: Devono essere mappati a un ID veicolo univoco.
- **Nomi Progetti**: Devono essere mappati a un ID progetto univoco.

#### **Processo di Normalizzazione Guidato**
Per ogni entità comune rilevata in un file, Augment Code deve:
- Verificare se l'entità (es. "Nome Dipendente: Mario Rossi") esiste già nel database Supabase (nella tabella master_dipendenti, master_clienti, ecc.).
- Se l'entità non esiste, chiederne la creazione all'utente, magari proponendo un ID.
- Se l'entità esiste, usarne l'ID corrispondente.
- Questo processo deve essere interattivo, permettendo all'utente di correggere o confermare le mappature.

#### **Gestione Dati Specifici per File**
Per ogni tipo di file:

**📅 Timbrature** (`apprilevazionepresenze-timbrature-totali-base-2025-05-01-2025-05-31 (2).xlsx` - `timbrature.csv`):
- **Colonne chiave**: Data, Nome Dipendente, Ora Entrata, Ora Uscita, Causa Assenza (Permesso, Malattia, Ferie).
- **Obiettivo**: Registrare le presenze/assenze giornaliere dei dipendenti.

**📝 Richieste** (`apprilevazionepresenze-richieste-2025-05-01-2025-05-31 (3).xlsx` - `richieste.csv`):
- **Colonne chiave**: Data Richiesta, Tipo Richiesta (Permesso, Ferie), Dipendente, Stato.
- **Obiettivo**: Gestire le richieste di assenza.

**⚡ Attività** (`export (9).xlsx` - `Attività - Completo.csv`):
- **Colonne chiave**: Data, Ora Inizio, Ora Fine, Dipendente, Cliente, Progetto, Descrizione Attività, Tipo di Intervento (Remoto, On-site).
- **Obiettivo**: Dettagliare le attività svolte.

**🚗 Registro Auto** (`registro_auto_230525.CSV`):
- **Colonne chiave**: Data, Ora Prelievo, Ora Riconsegna, Targa Veicolo, Dipendente Utilizzatore, Chilometri Percorsi, Destinazione.
- **Obiettivo**: Monitorare l'utilizzo dei veicoli aziendali.

**📊 Progetti** (`progetti_230525.xlsx` - `Progetti Attivi.csv`):
- **Colonne chiave**: ID Progetto, Nome Progetto, Cliente, Data Inizio, Data Fine, Stato.
- **Obiettivo**: Mantenere un elenco dei progetti attivi.

**🔗 Connection Report** (`connectionreport (6).csv`, `connectionreport (7).csv`, `tmp-174803235854260.csv`):
- **Colonne chiave**: Data/Ora Connessione, Utente (Tecnico), Cliente Connesso, Durata Connessione, IP Remoto.
- **Obiettivo**: Tracciare le sessioni di assistenza remota. Questi file possono avere strutture diverse ma dati simili.

### **TASK 5: 💾 Inserimento Dati in Supabase (Monitorato)**

#### **Conferma Inserimento**
- Prima di procedere all'inserimento, chiedi all'utente: "I dati sono stati estratti, normalizzati e convalidati. Sei pronto per inserirli nel database Supabase?"

#### **Gestione Errori Supabase**
- Presta attenzione agli errori di Supabase, come quello riportato in errori app_py.pdf ("Could not find a relationship between 'normalized_activities' and 'master_technicians'"). 
- Se si verifica un errore di relazione o di inserimento, segnalalo chiaramente all'utente e proponi soluzioni (es. "La tabella master_technicians non sembra contenere l'ID dipendente mappato. Vuoi creare questa entrata o correggere la mappatura?").

#### **Feedback Inserimento**
- Comunica chiaramente lo stato dell'inserimento (es. "X record inseriti con successo nella tabella Y", "Z record falliti, vedi dettagli...").

### **TASK 6: ✅ Controlli di Coerenza e Reportistica Iniziale (Post-Inserimento)**

Dopo aver inserito i dati, esegui controlli di coerenza interattivi:

#### **🔍 Coerenza Dipendente-Attività-Auto**
- "Voglio verificare se un dipendente che risulta aver preso la macchina in un tal giorno deve avere un'attività registrata in quel giorno, in quelle ore e da quel cliente."
- **Azione**: Query Supabase per incrociare i dati di registro_auto e attività. Se si trovano incongruenze, segnalale all'utente (es. "Il dipendente X ha utilizzato l'auto il GG/MM/AAAA ma non ha attività registrate per quel giorno/ora. Vuoi investigare?").

#### **🔍 Coerenza Dipendente-Assenze-Attività**
- "Se un dipendente è assente (per permesso, malattia, ferie) non può avere attività registrate in quel giorno."
- **Azione**: Incrocia i dati di timbrature (assenze) e attività. Segnala le incongruenze.

#### **🔍 Coerenza Permessi-Attività**
- "Verifica che i permessi siano coerenti con le attività."
- **Azione**: Incrocia richieste e attività.

#### **📈 Generazione Statistiche (Obiettivo Finale)**
Una volta che i dati sono stati convalidati e incrociati, Augment Code deve essere in grado di generare statistiche:
- **💰 Costi/Impegno**: Ore lavorate per progetto/cliente/dipendente.
- **⚡ Produttività Dipendenti**: Attività per dipendente, tempo medio per attività.
- **👥 Clienti e Ticket**: Numero di ticket/attività per cliente, andamento nel tempo.
- **🚗 Utilizzo Auto**: Chilometri percorsi, giorni di utilizzo.

---

## 🎯 **PRINCIPI OPERATIVI CHIAVE**

### **✅ SEMPRE FARE**
1. **Consultare Context 7** prima di modifiche significative
2. **Chiedere conferma** prima di applicare correzioni
3. **Verificare persistenza** delle correzioni dopo riavvio
4. **Proporre backup/versioning** per modifiche importanti
5. **Operare incrementalmente** con feedback continuo

### **❌ MAI FARE**
1. **Modificare** senza consenso dell'utente
2. **Danneggiare** codice esistente funzionante
3. **Ignorare** errori di Supabase o relazioni
4. **Procedere** senza validazione utente
5. **Applicare** correzioni temporanee non persistenti

---

**📋 Questo documento deve essere consultato ad ogni sessione di debug per garantire coerenza e qualità del processo di sviluppo.**
