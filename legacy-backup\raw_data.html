{% extends "base.html" %}

{% block title %}<PERSON><PERSON> - <PERSON><PERSON>{% endblock %}

{% block content %}
<main class="row mb-4">
    <section class="col-12">
        <article class="card shadow">
            <header class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-table me-2"></i><PERSON><PERSON></h4>
            </header>
            <div class="card-body">
                {% if not data %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Nessun dato disponibile. Carica un file dalla pagina principale per visualizzare i dati.
                </div>
                {% else %}
                <div class="alert alert-info d-flex justify-content-between align-items-center">
                    <div id="data-source-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Visualizzazione dati dal file: <strong>{{ filename }}</strong>
                        <span class="badge {% if file_type == 'unknown' or file_type == 'generico' %}bg-warning{% else %}bg-success{% endif %} text-white ms-2">
                            <i class="fas {% if file_type == 'attivita' %}fa-tasks{% elif 'teamviewer' in file_type %}fa-desktop{% elif file_type == 'calendario' %}fa-calendar{% elif file_type == 'timbrature' %}fa-clock{% elif file_type == 'permessi' %}fa-calendar-check{% else %}fa-file{% endif %} me-1"></i>
                            Tipo: {{ file_type }}
                        </span>
                        {% if session.get('mcp_file_id') %}
                        <span class="badge bg-info text-white ms-2" title="Elaborato con MCP">
                            <i class="fas fa-server me-1"></i>MCP
                        </span>
                        {% endif %}
                    </div>
                    <div class="d-flex">
                        {% if session.get('mcp_file_id') %}
                        <div class="me-3">
                            <label for="data-source" class="visually-hidden">Tipo di dati</label>
                            <select class="form-select form-select-sm" id="data-source" aria-label="Seleziona tipo di dati">
                                <option value="raw">Dati grezzi</option>
                                <option value="processed">Dati elaborati (MCP)</option>
                            </select>
                        </div>
                        <div class="form-check form-switch me-3 d-flex align-items-center">
                            <input class="form-check-input" type="checkbox" id="highlight-anomalies" checked>
                            <label class="form-check-label ms-2" for="highlight-anomalies">Evidenzia anomalie</label>
                        </div>
                        {% endif %}
                        <div class="export-buttons">
                            <div class="btn-group" role="group" aria-label="Esporta dati">
                                <button type="button" class="btn btn-success" id="export-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-file-export me-1"></i>Esporta
                                </button>
                                <div class="dropdown-menu dropdown-menu-end" aria-labelledby="export-dropdown">
                                    <h6 class="dropdown-header">Scegli formato</h6>
                                    <button class="dropdown-item" type="button" id="export-excel">
                                        <i class="fas fa-file-excel me-2 text-success"></i>Excel
                                    </button>
                                    <button class="dropdown-item" type="button" id="export-csv">
                                        <i class="fas fa-file-csv me-2 text-primary"></i>CSV
                                    </button>
                                    <button class="dropdown-item" type="button" id="export-json">
                                        <i class="fas fa-file-code me-2 text-warning"></i>JSON
                                    </button>
                                    <div class="dropdown-divider"></div>
                                    <button class="dropdown-item" type="button" id="export-pdf">
                                        <i class="fas fa-file-pdf me-2 text-danger"></i>PDF
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filtri avanzati -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <button class="btn btn-link text-decoration-none p-0" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
                            <i class="fas fa-filter me-1"></i>Filtri Avanzati
                        </button>
                    </div>
                    <div class="collapse" id="filterCollapse">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="column-filter" class="form-label">Colonna</label>
                                    <select class="form-select" id="column-filter">
                                        <option value="">Tutte le colonne</option>
                                        {% for column in columns %}
                                        <option value="{{ column }}">{{ column }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="operator-filter" class="form-label">Operatore</label>
                                    <select class="form-select" id="operator-filter">
                                        <option value="contains">Contiene</option>
                                        <option value="equals">Uguale a</option>
                                        <option value="starts">Inizia con</option>
                                        <option value="ends">Finisce con</option>
                                        <option value="greater">Maggiore di</option>
                                        <option value="less">Minore di</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="value-filter" class="form-label">Valore</label>
                                    <input type="text" class="form-control" id="value-filter" placeholder="Valore da filtrare">
                                </div>
                                <div class="col-md-2 mb-3 d-flex align-items-end">
                                    <button type="button" class="btn btn-primary w-100" id="apply-column-filter">
                                        <i class="fas fa-search me-1"></i>Applica
                                    </button>
                                </div>
                            </div>
                            <div class="d-flex justify-content-end">
                                <button type="button" class="btn btn-outline-secondary" id="reset-filters">
                                    <i class="fas fa-redo me-1"></i>Reset
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tabella dati -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="data-table">
                        <thead class="table-dark">
                            <tr>
                                {% for column in columns %}
                                <th class="sortable" data-column="{{ column }}">
                                    {{ column }}
                                    <span class="sort-icon ms-1"><i class="fas fa-sort"></i></span>
                                </th>
                                {% endfor %}
                            </tr>
                        </thead>
                        <tbody>
                            <!-- I dati verranno caricati dinamicamente via JavaScript -->
                            <tr>
                                <td colspan="{{ columns|length }}" class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Caricamento...</span>
                                    </div>
                                    <p class="mt-2">Caricamento dati in corso...</p>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Legenda anomalie -->
                <div class="anomaly-legend mt-3" id="anomaly-legend">
                    <h6 class="mb-2"><i class="fas fa-info-circle me-2"></i>Legenda Anomalie</h6>
                    <div class="d-flex flex-wrap">
                        <div class="anomaly-legend-item">
                            <span class="anomaly-legend-color anomaly-legend-missing"></span>
                            <span>Valore mancante</span>
                        </div>
                        <div class="anomaly-legend-item">
                            <span class="anomaly-legend-color anomaly-legend-format"></span>
                            <span>Formato non valido</span>
                        </div>
                        <div class="anomaly-legend-item">
                            <span class="anomaly-legend-color anomaly-legend-outlier"></span>
                            <span>Valore anomalo</span>
                        </div>
                    </div>
                </div>

                <!-- Paginazione -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="d-flex align-items-center">
                        <label for="page-size" class="me-2">Righe per pagina:</label>
                        <select class="form-select form-select-sm page-size-select" id="page-size">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                    </div>
                    <nav aria-label="Navigazione pagine">
                        <ul class="pagination" id="pagination">
                            <!-- La paginazione verrà generata dinamicamente via JavaScript -->
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </article>
    </section>
</main>
{% endblock %}

{% block extra_css %}
<style>
    .sortable {
        cursor: pointer;
    }
    .sort-icon {
        display: inline-block;
        width: 1em;
    }
    .sort-asc .sort-icon i:before {
        content: "\f0de"; /* fa-sort-up */
    }
    .sort-desc .sort-icon i:before {
        content: "\f0dd"; /* fa-sort-down */
    }
    .table-responsive {
        max-height: 600px;
    }
    #data-table th {
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .page-size-select {
        width: auto;
    }

    .export-buttons .dropdown-item:hover {
        background-color: #f8f9fa;
    }

    .export-buttons .dropdown-item:active {
        background-color: #e9ecef;
        color: #212529;
    }

    /* Stili per le anomalie */
    .anomaly {
        position: relative;
    }

    .anomaly-missing {
        background-color: rgba(255, 0, 0, 0.1) !important;
    }

    .anomaly-format {
        background-color: rgba(255, 165, 0, 0.1) !important;
    }

    .anomaly-outlier {
        background-color: rgba(255, 255, 0, 0.1) !important;
    }

    /* Legenda anomalie */
    .anomaly-legend {
        margin-top: 1rem;
        padding: 0.5rem;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        background-color: #f8f9fa;
    }

    .anomaly-legend-item {
        display: inline-flex;
        align-items: center;
        margin-right: 1rem;
    }

    .anomaly-legend-color {
        display: inline-block;
        width: 1rem;
        height: 1rem;
        margin-right: 0.5rem;
        border: 1px solid #dee2e6;
    }

    .anomaly-legend-missing {
        background-color: rgba(255, 0, 0, 0.1);
    }

    .anomaly-legend-format {
        background-color: rgba(255, 165, 0, 0.1);
    }

    .anomaly-legend-outlier {
        background-color: rgba(255, 255, 0, 0.1);
    }
</style>
{% endblock %}

{% block extra_js %}
<!-- Carica lo script per la gestione dei dati grezzi -->
<script src="{{ url_for('static', filename='js/raw_data.js') }}?v=2.0.0"></script>
{% endblock %}


