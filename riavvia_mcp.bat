@echo off
chcp 65001 >nul
title Riavvio Server MCP

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    RIAVVIO SERVER MCP                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📍 Directory di lavoro: %CD%
echo.

REM Verifica se siamo nella directory corretta
if not exist "mcp_server\run_server.py" (
    echo ❌ ERRORE: Directory mcp_server non trovata
    echo 💡 Assicurati di essere nella directory del progetto app-roberto
    pause
    exit /b 1
)

echo 🔍 Verifica stato server MCP...
python -c "import requests; r = requests.get('http://127.0.0.1:8000/health', timeout=2); print('✅ Server MCP già attivo')" 2>nul
if %errorlevel% equ 0 (
    echo ✅ Server MCP già in esecuzione
    echo 💡 Se hai problemi di connessione, chiudi manualmente il server e riprova
    pause
    exit /b 0
)

echo 🚀 Avvio server MCP...
echo.

REM Cambia directory e avvia il server
cd mcp_server
start "MCP Server" python run_server.py

echo ⏳ Attesa avvio server (5 secondi)...
timeout /t 5 /nobreak >nul

echo 🔍 Test connessione...
python -c "import requests; r = requests.get('http://127.0.0.1:8000/health', timeout=5); print('✅ Server MCP operativo:', r.json()['status'])" 2>nul
if %errorlevel% equ 0 (
    echo.
    echo ═══════════════════════════════════════════════════════════════
    echo  ✅ SERVER MCP AVVIATO CON SUCCESSO!
    echo  🌐 URL: http://127.0.0.1:8000
    echo  📊 Status: Operativo
    echo ═══════════════════════════════════════════════════════════════
) else (
    echo.
    echo ❌ Server MCP non risponde
    echo 💡 Controlla la finestra del server per eventuali errori
)

echo.
echo 💡 Premi un tasto per chiudere...
pause >nul
