-- Ta<PERSON> per il progresso dell'onboarding intelligente
-- Eseguire questo script nel SQL Editor di Supabase

-- <PERSON><PERSON> la tabella onboarding_progress
CREATE TABLE IF NOT EXISTS onboarding_progress (
    id SERIAL PRIMARY KEY,
    uuid UUID DEFAULT uuid_generate_v4() UNIQUE,
    user_id VARCHAR(100) NOT NULL,
    progress_data JSONB NOT NULL,
    current_step INTEGER DEFAULT 1,
    completed_steps INTEGER[] DEFAULT '{}',
    business_type VARCHAR(50) DEFAULT 'generic',
    confidence_score DECIMAL(3,2) DEFAULT 0.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Crea indici per performance
CREATE INDEX IF NOT EXISTS idx_onboarding_progress_user_id ON onboarding_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_onboarding_progress_business_type ON onboarding_progress(business_type);
CREATE INDEX IF NOT EXISTS idx_onboarding_progress_created_at ON onboarding_progress(created_at);

-- Crea trigger per aggiornare updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_onboarding_progress_updated_at 
    BEFORE UPDATE ON onboarding_progress 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Policy di sicurezza (RLS)
ALTER TABLE onboarding_progress ENABLE ROW LEVEL SECURITY;

-- Policy per permettere a tutti di leggere/scrivere (per ora)
-- In produzione, limitare in base all'utente autenticato
CREATE POLICY "Allow all operations on onboarding_progress" ON onboarding_progress
    FOR ALL USING (true);

-- Commenti per documentazione
COMMENT ON TABLE onboarding_progress IS 'Tabella per tracciare il progresso dell''onboarding intelligente degli utenti';
COMMENT ON COLUMN onboarding_progress.user_id IS 'ID dell''utente (può essere session_id o user_id)';
COMMENT ON COLUMN onboarding_progress.progress_data IS 'Dati completi del progresso in formato JSON';
COMMENT ON COLUMN onboarding_progress.current_step IS 'Step corrente dell''onboarding (1-5)';
COMMENT ON COLUMN onboarding_progress.completed_steps IS 'Array degli step completati';
COMMENT ON COLUMN onboarding_progress.business_type IS 'Tipo di business rilevato (generic, timbrature, teamviewer, etc.)';
COMMENT ON COLUMN onboarding_progress.confidence_score IS 'Punteggio di confidenza del rilevamento (0.0-1.0)';
