#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP Watchdog - Monitora e riavvia automaticamente il server MCP se si ferma.
"""

import time
import requests
import subprocess
import os
import sys
import threading
from datetime import datetime
import logging

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mcp_watchdog.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('mcp_watchdog')

class MCPWatchdog:
    """Watchdog per monitorare e riavviare il server MCP."""

    def __init__(self, mcp_url="http://127.0.0.1:8000", check_interval=30):
        self.mcp_url = mcp_url
        self.check_interval = check_interval
        self.mcp_process = None
        self.running = False
        self.restart_count = 0
        self.max_restarts = 10

    def check_mcp_health(self):
        """Verifica se il server MCP è operativo."""
        try:
            response = requests.get(f"{self.mcp_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'ok':
                    return True
            return False
        except Exception as e:
            logger.debug(f"Health check fallito: {str(e)}")
            return False

    def start_mcp_server(self):
        """Avvia il server MCP."""
        try:
            logger.info("🚀 Avvio server MCP...")

            # Cambia directory al mcp_server
            mcp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'mcp_server')

            # Avvia il server MCP
            self.mcp_process = subprocess.Popen(
                [sys.executable, 'run_server.py'],
                cwd=mcp_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
            )

            # Aspetta che il server si avvii
            time.sleep(5)

            # Verifica che sia operativo
            for i in range(10):  # Prova per 10 secondi
                if self.check_mcp_health():
                    logger.info("✅ Server MCP avviato con successo")
                    self.restart_count += 1
                    return True
                time.sleep(1)

            logger.error("❌ Server MCP non risponde dopo l'avvio")
            return False

        except Exception as e:
            logger.error(f"❌ Errore nell'avvio del server MCP: {str(e)}")
            return False

    def stop_mcp_server(self):
        """Ferma il server MCP."""
        if self.mcp_process:
            try:
                logger.info("🛑 Fermando server MCP...")
                self.mcp_process.terminate()
                self.mcp_process.wait(timeout=10)
                logger.info("✅ Server MCP fermato")
            except Exception as e:
                logger.error(f"❌ Errore nel fermare il server MCP: {str(e)}")
                try:
                    self.mcp_process.kill()
                except:
                    pass
            finally:
                self.mcp_process = None

    def monitor_loop(self):
        """Loop principale di monitoraggio."""
        logger.info("🔍 Avvio monitoraggio MCP Watchdog")
        logger.info(f"📍 URL MCP: {self.mcp_url}")
        logger.info(f"⏱️ Intervallo controllo: {self.check_interval} secondi")

        while self.running:
            try:
                if self.check_mcp_health():
                    logger.debug("✅ Server MCP operativo")
                else:
                    logger.warning("⚠️ Server MCP non risponde")

                    if self.restart_count >= self.max_restarts:
                        logger.error(f"❌ Raggiunto limite massimo di riavvii ({self.max_restarts})")
                        logger.error("🛑 Fermando watchdog per evitare loop infiniti")
                        break

                    logger.info("🔄 Tentativo di riavvio server MCP...")
                    self.stop_mcp_server()
                    time.sleep(2)

                    if self.start_mcp_server():
                        logger.info("🎉 Server MCP riavviato con successo")
                    else:
                        logger.error("❌ Riavvio server MCP fallito")

                time.sleep(self.check_interval)

            except KeyboardInterrupt:
                logger.info("🛑 Interruzione richiesta dall'utente")
                break
            except Exception as e:
                logger.error(f"❌ Errore nel loop di monitoraggio: {str(e)}")
                time.sleep(5)

        self.stop_mcp_server()
        logger.info("🏁 MCP Watchdog terminato")

    def start(self):
        """Avvia il watchdog."""
        self.running = True

        # Verifica se il server è già in esecuzione
        if not self.check_mcp_health():
            logger.info("🚀 Server MCP non in esecuzione, avvio...")
            if not self.start_mcp_server():
                logger.error("❌ Impossibile avviare il server MCP")
                return False
        else:
            logger.info("✅ Server MCP già in esecuzione")

        # Avvia il monitoraggio in un thread separato
        monitor_thread = threading.Thread(target=self.monitor_loop)
        monitor_thread.daemon = True
        monitor_thread.start()

        return True

    def stop(self):
        """Ferma il watchdog."""
        self.running = False
        self.stop_mcp_server()

def main():
    """Funzione principale."""
    print("🐕 MCP WATCHDOG - Monitoraggio Server MCP")
    print("=" * 50)

    watchdog = MCPWatchdog()

    try:
        if watchdog.start():
            print("✅ Watchdog avviato con successo")
            print("🔍 Monitoraggio in corso...")
            print("💡 Premi Ctrl+C per fermare")

            # Mantieni il programma in esecuzione
            while watchdog.running:
                time.sleep(1)
        else:
            print("❌ Impossibile avviare il watchdog")
            return 1

    except KeyboardInterrupt:
        print("\n🛑 Fermando watchdog...")
        watchdog.stop()
        print("✅ Watchdog fermato")

    return 0

if __name__ == "__main__":
    exit(main())
