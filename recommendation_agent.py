#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Recommendation Agent - Agente AI per sistemi di raccomandazione intelligenti.
Fornisce raccomandazioni personalizzate basate su ML, collaborative filtering e content-based filtering.
"""

import asyncio
import logging
import json
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import statistics

# Import librerie ML
try:
    from sklearn.metrics.pairwise import cosine_similarity, euclidean_distances
    from sklearn.cluster import KMeans
    from sklearn.decomposition import PCA
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logging.warning("Scikit-learn non disponibile - funzionalità ML limitate")

# Import LangChain tools
try:
    from langchain_core.tools import tool
    from langchain_core.messages import HumanMessage
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False

# Import moduli esistenti
try:
    from agent_system import BaseAgent, AgentType, AgentTask
    from intelligent_cache_system import intelligent_cache
    from performance_profiler import profile
except ImportError as e:
    logging.warning(f"Alcuni moduli non disponibili: {e}")

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RecommendationType(Enum):
    """Tipi di raccomandazione."""
    COLLABORATIVE = "collaborative"
    CONTENT_BASED = "content_based"
    HYBRID = "hybrid"
    POPULARITY = "popularity"
    KNOWLEDGE_BASED = "knowledge_based"

class RecommendationContext(Enum):
    """Contesti di raccomandazione."""
    BUSINESS_PROCESS = "business_process"
    WORKFLOW_OPTIMIZATION = "workflow_optimization"
    RESOURCE_ALLOCATION = "resource_allocation"
    SKILL_DEVELOPMENT = "skill_development"
    TOOL_SELECTION = "tool_selection"

@dataclass
class UserProfile:
    """Profilo utente per raccomandazioni."""
    user_id: str
    preferences: Dict[str, float]  # feature -> weight
    behavior_history: List[Dict[str, Any]]
    skills: List[str]
    role: str
    department: str
    experience_level: str  # beginner, intermediate, advanced
    last_activity: datetime

@dataclass
class RecommendationItem:
    """Item da raccomandare."""
    item_id: str
    title: str
    description: str
    category: str
    features: Dict[str, float]
    tags: List[str]
    popularity_score: float
    quality_score: float
    created_at: datetime

@dataclass
class Recommendation:
    """Singola raccomandazione."""
    item_id: str
    user_id: str
    score: float
    confidence: float
    reasoning: str
    recommendation_type: RecommendationType
    context: RecommendationContext
    metadata: Dict[str, Any]
    generated_at: datetime

@dataclass
class RecommendationResult:
    """Risultato completo di raccomandazione."""
    user_id: str
    recommendations: List[Recommendation]
    total_items_considered: int
    algorithm_used: str
    execution_time: float
    context: RecommendationContext
    personalization_score: float

class RecommendationAgent(BaseAgent):
    """
    Agente AI specializzato in sistemi di raccomandazione intelligenti.

    Capacità:
    - Collaborative filtering basato su similarità utenti/item
    - Content-based filtering con analisi features
    - Hybrid recommendations combinando multiple tecniche
    - Real-time personalization con ML
    - Context-aware recommendations
    - A/B testing per ottimizzazione algoritmi
    """

    def __init__(self, llm_model: str = "gpt-4"):
        super().__init__(AgentType.RECOMMENDATION, llm_model)

        # Storage dati raccomandazione
        self.user_profiles: Dict[str, UserProfile] = {}
        self.items: Dict[str, RecommendationItem] = {}
        self.user_item_matrix: Optional[np.ndarray] = None
        self.item_features_matrix: Optional[np.ndarray] = None

        # Configurazione algoritmi
        self.ALGORITHM_CONFIG = {
            "collaborative_threshold": 0.1,  # Soglia similarità
            "content_similarity_threshold": 0.2,
            "min_interactions": 5,  # Minimo interazioni per collaborative
            "max_recommendations": 10,
            "diversity_factor": 0.3,  # Bilanciamento diversità/accuratezza
            "novelty_weight": 0.2  # Peso novità nelle raccomandazioni
        }

        # Metriche performance
        self.recommendation_metrics = {
            "total_recommendations": 0,
            "avg_confidence": 0.0,
            "algorithm_usage": {},
            "context_distribution": {},
            "user_satisfaction": 0.0
        }

        # Inizializza dati demo
        self._initialize_demo_data()

        logger.info("RecommendationAgent inizializzato")

    def _initialize_tools(self) -> List:
        """Inizializza strumenti specifici per raccomandazioni."""
        tools = []

        if LANGCHAIN_AVAILABLE:
            @tool
            def generate_user_recommendations(user_id: str, context: str, num_recommendations: int = 5) -> str:
                """Genera raccomandazioni personalizzate per un utente."""
                try:
                    result = asyncio.run(self._generate_recommendations_for_user(
                        user_id, RecommendationContext(context), num_recommendations
                    ))
                    return json.dumps(asdict(result))
                except Exception as e:
                    return f"Errore generazione raccomandazioni: {e}"

            @tool
            def analyze_user_preferences(user_id: str, behavior_data: str) -> str:
                """Analizza preferenze utente da dati comportamentali."""
                try:
                    behavior = json.loads(behavior_data)
                    preferences = self._analyze_user_preferences(user_id, behavior)
                    return json.dumps(preferences)
                except Exception as e:
                    return f"Errore analisi preferenze: {e}"

            @tool
            def find_similar_users(user_id: str, similarity_threshold: float = 0.5) -> str:
                """Trova utenti simili per collaborative filtering."""
                try:
                    similar_users = self._find_similar_users(user_id, similarity_threshold)
                    return json.dumps(similar_users)
                except Exception as e:
                    return f"Errore ricerca utenti simili: {e}"

            @tool
            def evaluate_recommendation_performance(time_period: str) -> str:
                """Valuta performance del sistema di raccomandazione."""
                try:
                    metrics = self._evaluate_recommendation_performance(time_period)
                    return json.dumps(metrics)
                except Exception as e:
                    return f"Errore valutazione performance: {e}"

            tools.extend([
                generate_user_recommendations,
                analyze_user_preferences,
                find_similar_users,
                evaluate_recommendation_performance
            ])

        return tools

    @profile(name="recommendation_execute_task")
    async def _execute_task_logic(self, task: AgentTask) -> Dict[str, Any]:
        """
        Esegue la logica di raccomandazione.

        Args:
            task: Task con parametri di raccomandazione

        Returns:
            Risultato delle raccomandazioni con items e metriche
        """
        input_data = task.input_data
        operation_type = input_data.get("operation_type", "generate_recommendations")

        logger.info(f"Iniziando operazione raccomandazione: {operation_type}")

        if operation_type == "generate_recommendations":
            return await self._generate_recommendations_operation(input_data)
        elif operation_type == "analyze_preferences":
            return await self._analyze_preferences_operation(input_data)
        elif operation_type == "update_user_profile":
            return await self._update_user_profile_operation(input_data)
        elif operation_type == "evaluate_algorithms":
            return await self._evaluate_algorithms_operation(input_data)
        else:
            raise ValueError(f"Operazione non supportata: {operation_type}")

    async def _generate_recommendations_operation(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Genera raccomandazioni per utente."""
        user_id = input_data.get("user_id", "user_001")
        context = RecommendationContext(input_data.get("context", "business_process"))
        num_recommendations = input_data.get("num_recommendations", 5)
        algorithm_type = input_data.get("algorithm_type", "hybrid")

        # Genera raccomandazioni
        result = await self._generate_recommendations_for_user(user_id, context, num_recommendations, algorithm_type)

        # Aggiorna metriche
        self._update_recommendation_metrics(result)

        return {
            "operation_type": "generate_recommendations",
            "user_id": user_id,
            "context": context.value,
            "algorithm_used": result.algorithm_used,
            "recommendations": [asdict(rec) for rec in result.recommendations],
            "total_items_considered": result.total_items_considered,
            "execution_time": result.execution_time,
            "personalization_score": result.personalization_score,
            "confidence_score": statistics.mean([r.confidence for r in result.recommendations]) if result.recommendations else 0.0
        }

    async def _analyze_preferences_operation(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analizza preferenze utente."""
        user_id = input_data.get("user_id", "user_001")
        behavior_data = input_data.get("behavior_data", [])

        # Analizza preferenze
        preferences = await self._analyze_user_preferences_advanced(user_id, behavior_data)

        # Aggiorna profilo utente
        if user_id in self.user_profiles:
            self.user_profiles[user_id].preferences.update(preferences)
            self.user_profiles[user_id].last_activity = datetime.now()

        return {
            "operation_type": "analyze_preferences",
            "user_id": user_id,
            "preferences": preferences,
            "preference_strength": sum(abs(v) for v in preferences.values()),
            "top_preferences": sorted(preferences.items(), key=lambda x: abs(x[1]), reverse=True)[:5],
            "confidence_score": 0.88
        }

    async def _update_user_profile_operation(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Aggiorna profilo utente."""
        user_id = input_data.get("user_id")
        profile_updates = input_data.get("profile_updates", {})

        if user_id not in self.user_profiles:
            # Crea nuovo profilo
            self.user_profiles[user_id] = UserProfile(
                user_id=user_id,
                preferences={},
                behavior_history=[],
                skills=[],
                role="employee",
                department="general",
                experience_level="intermediate",
                last_activity=datetime.now()
            )

        # Aggiorna profilo
        profile = self.user_profiles[user_id]
        for key, value in profile_updates.items():
            if hasattr(profile, key):
                setattr(profile, key, value)

        profile.last_activity = datetime.now()

        return {
            "operation_type": "update_user_profile",
            "user_id": user_id,
            "profile_updated": True,
            "profile_summary": {
                "preferences_count": len(profile.preferences),
                "skills_count": len(profile.skills),
                "role": profile.role,
                "experience_level": profile.experience_level
            },
            "confidence_score": 0.95
        }

    async def _evaluate_algorithms_operation(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Valuta performance algoritmi di raccomandazione."""
        time_period = input_data.get("time_period", "7_days")

        # Simula valutazione algoritmi
        algorithm_performance = {
            "collaborative": {"precision": 0.78, "recall": 0.65, "f1": 0.71, "coverage": 0.82},
            "content_based": {"precision": 0.72, "recall": 0.68, "f1": 0.70, "coverage": 0.95},
            "hybrid": {"precision": 0.85, "recall": 0.72, "f1": 0.78, "coverage": 0.88},
            "popularity": {"precision": 0.60, "recall": 0.85, "f1": 0.70, "coverage": 1.0}
        }

        # Calcola metriche aggregate
        best_algorithm = max(algorithm_performance.items(), key=lambda x: x[1]["f1"])
        avg_precision = statistics.mean([alg["precision"] for alg in algorithm_performance.values()])

        return {
            "operation_type": "evaluate_algorithms",
            "time_period": time_period,
            "algorithm_performance": algorithm_performance,
            "best_algorithm": best_algorithm[0],
            "best_f1_score": best_algorithm[1]["f1"],
            "average_precision": avg_precision,
            "total_algorithms_tested": len(algorithm_performance),
            "confidence_score": 0.92
        }

    async def _generate_recommendations_for_user(self, user_id: str, context: RecommendationContext,
                                                num_recommendations: int, algorithm_type: str = "hybrid") -> RecommendationResult:
        """Genera raccomandazioni per un utente specifico."""
        start_time = datetime.now()

        # Verifica profilo utente
        if user_id not in self.user_profiles:
            await self._create_default_user_profile(user_id)

        user_profile = self.user_profiles[user_id]

        # Seleziona algoritmo
        if algorithm_type == "collaborative":
            recommendations = await self._collaborative_filtering(user_id, context, num_recommendations)
        elif algorithm_type == "content_based":
            recommendations = await self._content_based_filtering(user_id, context, num_recommendations)
        elif algorithm_type == "popularity":
            recommendations = await self._popularity_based_filtering(context, num_recommendations)
        else:  # hybrid
            recommendations = await self._hybrid_filtering(user_id, context, num_recommendations)

        # Calcola personalization score
        personalization_score = self._calculate_personalization_score(recommendations, user_profile)

        execution_time = (datetime.now() - start_time).total_seconds()

        return RecommendationResult(
            user_id=user_id,
            recommendations=recommendations,
            total_items_considered=len(self.items),
            algorithm_used=algorithm_type,
            execution_time=execution_time,
            context=context,
            personalization_score=personalization_score
        )

    async def _collaborative_filtering(self, user_id: str, context: RecommendationContext,
                                     num_recommendations: int) -> List[Recommendation]:
        """Implementa collaborative filtering."""
        recommendations = []

        if not SKLEARN_AVAILABLE:
            return await self._fallback_recommendations(user_id, context, num_recommendations)

        # Trova utenti simili
        similar_users = await self._find_similar_users_advanced(user_id)

        # Genera raccomandazioni basate su utenti simili
        item_scores = {}
        for similar_user_id, similarity in similar_users[:5]:  # Top 5 utenti simili
            similar_profile = self.user_profiles.get(similar_user_id)
            if similar_profile:
                for item_id, score in similar_profile.preferences.items():
                    if item_id not in item_scores:
                        item_scores[item_id] = 0
                    item_scores[item_id] += score * similarity

        # Ordina per score e crea raccomandazioni
        sorted_items = sorted(item_scores.items(), key=lambda x: x[1], reverse=True)

        for i, (item_id, score) in enumerate(sorted_items[:num_recommendations]):
            if item_id in self.items:
                recommendations.append(Recommendation(
                    item_id=item_id,
                    user_id=user_id,
                    score=score,
                    confidence=min(0.95, 0.6 + (score / 10)),
                    reasoning=f"Raccomandato basato su utenti con preferenze simili (score: {score:.2f})",
                    recommendation_type=RecommendationType.COLLABORATIVE,
                    context=context,
                    metadata={"similar_users_count": len(similar_users), "rank": i + 1},
                    generated_at=datetime.now()
                ))

        return recommendations

    async def _content_based_filtering(self, user_id: str, context: RecommendationContext,
                                     num_recommendations: int) -> List[Recommendation]:
        """Implementa content-based filtering."""
        recommendations = []
        user_profile = self.user_profiles[user_id]

        # Calcola similarità tra preferenze utente e features degli item
        item_scores = {}
        for item_id, item in self.items.items():
            score = 0
            for feature, weight in user_profile.preferences.items():
                if feature in item.features:
                    score += weight * item.features[feature]

            # Aggiungi bonus per qualità e popolarità
            score += item.quality_score * 0.2
            score += item.popularity_score * 0.1

            item_scores[item_id] = score

        # Ordina e crea raccomandazioni
        sorted_items = sorted(item_scores.items(), key=lambda x: x[1], reverse=True)

        for i, (item_id, score) in enumerate(sorted_items[:num_recommendations]):
            item = self.items[item_id]
            recommendations.append(Recommendation(
                item_id=item_id,
                user_id=user_id,
                score=score,
                confidence=min(0.90, 0.5 + (score / 15)),
                reasoning=f"Raccomandato basato su preferenze contenuto (score: {score:.2f})",
                recommendation_type=RecommendationType.CONTENT_BASED,
                context=context,
                metadata={"content_match": score, "quality_score": item.quality_score, "rank": i + 1},
                generated_at=datetime.now()
            ))

        return recommendations

    async def _popularity_based_filtering(self, context: RecommendationContext,
                                        num_recommendations: int) -> List[Recommendation]:
        """Implementa popularity-based filtering."""
        recommendations = []

        # Ordina per popolarità
        sorted_items = sorted(self.items.items(), key=lambda x: x[1].popularity_score, reverse=True)

        for i, (item_id, item) in enumerate(sorted_items[:num_recommendations]):
            recommendations.append(Recommendation(
                item_id=item_id,
                user_id="anonymous",
                score=item.popularity_score,
                confidence=0.75,
                reasoning=f"Raccomandato per alta popolarità (score: {item.popularity_score:.2f})",
                recommendation_type=RecommendationType.POPULARITY,
                context=context,
                metadata={"popularity_rank": i + 1, "quality_score": item.quality_score},
                generated_at=datetime.now()
            ))

        return recommendations

    async def _hybrid_filtering(self, user_id: str, context: RecommendationContext,
                              num_recommendations: int) -> List[Recommendation]:
        """Implementa hybrid filtering combinando multiple tecniche."""
        # Ottieni raccomandazioni da diversi algoritmi
        collaborative_recs = await self._collaborative_filtering(user_id, context, num_recommendations)
        content_recs = await self._content_based_filtering(user_id, context, num_recommendations)
        popularity_recs = await self._popularity_based_filtering(context, num_recommendations // 2)

        # Combina e pesa le raccomandazioni
        combined_scores = {}

        # Peso collaborative filtering
        for rec in collaborative_recs:
            combined_scores[rec.item_id] = combined_scores.get(rec.item_id, 0) + rec.score * 0.4

        # Peso content-based filtering
        for rec in content_recs:
            combined_scores[rec.item_id] = combined_scores.get(rec.item_id, 0) + rec.score * 0.4

        # Peso popularity
        for rec in popularity_recs:
            combined_scores[rec.item_id] = combined_scores.get(rec.item_id, 0) + rec.score * 0.2

        # Crea raccomandazioni finali
        recommendations = []
        sorted_items = sorted(combined_scores.items(), key=lambda x: x[1], reverse=True)

        for i, (item_id, score) in enumerate(sorted_items[:num_recommendations]):
            if item_id in self.items:
                recommendations.append(Recommendation(
                    item_id=item_id,
                    user_id=user_id,
                    score=score,
                    confidence=min(0.95, 0.7 + (score / 20)),
                    reasoning=f"Raccomandazione ibrida combinando multiple tecniche (score: {score:.2f})",
                    recommendation_type=RecommendationType.HYBRID,
                    context=context,
                    metadata={"hybrid_score": score, "rank": i + 1},
                    generated_at=datetime.now()
                ))

        return recommendations

    async def _fallback_recommendations(self, user_id: str, context: RecommendationContext,
                                      num_recommendations: int) -> List[Recommendation]:
        """Raccomandazioni fallback quando ML non è disponibile."""
        recommendations = []

        # Usa raccomandazioni basate su popolarità come fallback
        sorted_items = sorted(self.items.items(), key=lambda x: x[1].popularity_score, reverse=True)

        for i, (item_id, item) in enumerate(sorted_items[:num_recommendations]):
            recommendations.append(Recommendation(
                item_id=item_id,
                user_id=user_id,
                score=item.popularity_score,
                confidence=0.60,
                reasoning="Raccomandazione fallback basata su popolarità",
                recommendation_type=RecommendationType.POPULARITY,
                context=context,
                metadata={"fallback": True, "rank": i + 1},
                generated_at=datetime.now()
            ))

        return recommendations

    async def _create_default_user_profile(self, user_id: str):
        """Crea profilo utente di default."""
        self.user_profiles[user_id] = UserProfile(
            user_id=user_id,
            preferences={
                "efficiency": 0.8,
                "automation": 0.6,
                "collaboration": 0.7,
                "innovation": 0.5
            },
            behavior_history=[],
            skills=["general"],
            role="employee",
            department="general",
            experience_level="intermediate",
            last_activity=datetime.now()
        )

    async def _find_similar_users_advanced(self, user_id: str) -> List[Tuple[str, float]]:
        """Trova utenti simili usando cosine similarity."""
        if not SKLEARN_AVAILABLE:
            return []

        user_profile = self.user_profiles[user_id]
        similar_users = []

        # Crea vettore preferenze utente target
        all_features = set()
        for profile in self.user_profiles.values():
            all_features.update(profile.preferences.keys())

        target_vector = np.array([user_profile.preferences.get(feature, 0) for feature in all_features])

        # Calcola similarità con altri utenti
        for other_user_id, other_profile in self.user_profiles.items():
            if other_user_id == user_id:
                continue

            other_vector = np.array([other_profile.preferences.get(feature, 0) for feature in all_features])

            # Calcola cosine similarity
            similarity = cosine_similarity([target_vector], [other_vector])[0][0]

            if similarity > self.ALGORITHM_CONFIG["collaborative_threshold"]:
                similar_users.append((other_user_id, similarity))

        return sorted(similar_users, key=lambda x: x[1], reverse=True)

    async def _analyze_user_preferences_advanced(self, user_id: str, behavior_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """Analizza preferenze utente da dati comportamentali."""
        preferences = {}

        # Analizza comportamenti
        for behavior in behavior_data:
            action = behavior.get("action", "")
            item_id = behavior.get("item_id", "")
            rating = behavior.get("rating", 0)

            if item_id in self.items:
                item = self.items[item_id]

                # Aggiorna preferenze basate su features dell'item
                for feature, value in item.features.items():
                    if feature not in preferences:
                        preferences[feature] = 0

                    # Peso basato su azione e rating
                    weight = 1.0
                    if action == "like":
                        weight = 1.5
                    elif action == "dislike":
                        weight = -1.0
                    elif action == "view":
                        weight = 0.5

                    preferences[feature] += weight * value * (rating / 5.0 if rating > 0 else 1.0)

        # Normalizza preferenze
        if preferences:
            max_pref = max(abs(v) for v in preferences.values())
            if max_pref > 0:
                preferences = {k: v / max_pref for k, v in preferences.items()}

        return preferences

    def _calculate_personalization_score(self, recommendations: List[Recommendation],
                                       user_profile: UserProfile) -> float:
        """Calcola score di personalizzazione."""
        if not recommendations:
            return 0.0

        # Fattori di personalizzazione
        preference_match = 0.0
        diversity_score = 0.0
        novelty_score = 0.0

        # Calcola match con preferenze
        for rec in recommendations:
            if rec.item_id in self.items:
                item = self.items[rec.item_id]
                for feature, user_pref in user_profile.preferences.items():
                    if feature in item.features:
                        preference_match += abs(user_pref * item.features[feature])

        preference_match /= len(recommendations)

        # Calcola diversità (varietà di categorie)
        categories = set()
        for rec in recommendations:
            if rec.item_id in self.items:
                categories.add(self.items[rec.item_id].category)

        diversity_score = len(categories) / len(recommendations)

        # Calcola novità (items meno popolari)
        avg_popularity = statistics.mean([
            self.items[rec.item_id].popularity_score
            for rec in recommendations
            if rec.item_id in self.items
        ])
        novelty_score = 1.0 - (avg_popularity / 100.0)  # Assume popularity 0-100

        # Combina fattori
        personalization_score = (
            preference_match * 0.5 +
            diversity_score * 0.3 +
            novelty_score * 0.2
        )

        return min(1.0, personalization_score)

    def _update_recommendation_metrics(self, result: RecommendationResult):
        """Aggiorna metriche del sistema di raccomandazione."""
        self.recommendation_metrics["total_recommendations"] += len(result.recommendations)

        # Aggiorna confidence media
        if result.recommendations:
            avg_confidence = statistics.mean([r.confidence for r in result.recommendations])
            current_avg = self.recommendation_metrics["avg_confidence"]
            total_recs = self.recommendation_metrics["total_recommendations"]

            self.recommendation_metrics["avg_confidence"] = (
                (current_avg * (total_recs - len(result.recommendations)) +
                 avg_confidence * len(result.recommendations)) / total_recs
            )

        # Aggiorna usage algoritmi
        algorithm = result.algorithm_used
        if algorithm not in self.recommendation_metrics["algorithm_usage"]:
            self.recommendation_metrics["algorithm_usage"][algorithm] = 0
        self.recommendation_metrics["algorithm_usage"][algorithm] += 1

        # Aggiorna distribuzione contesti
        context = result.context.value
        if context not in self.recommendation_metrics["context_distribution"]:
            self.recommendation_metrics["context_distribution"][context] = 0
        self.recommendation_metrics["context_distribution"][context] += 1

    def _initialize_demo_data(self):
        """Inizializza dati demo per testing."""
        # Crea items demo
        demo_items = [
            RecommendationItem(
                item_id="tool_001",
                title="Automated Data Processor",
                description="Tool per elaborazione automatica dati",
                category="automation",
                features={"efficiency": 0.9, "automation": 0.95, "complexity": 0.3},
                tags=["automation", "data", "efficiency"],
                popularity_score=85.0,
                quality_score=0.92,
                created_at=datetime.now() - timedelta(days=30)
            ),
            RecommendationItem(
                item_id="process_001",
                title="Collaborative Workflow",
                description="Processo di workflow collaborativo",
                category="workflow",
                features={"collaboration": 0.95, "efficiency": 0.8, "innovation": 0.7},
                tags=["collaboration", "workflow", "team"],
                popularity_score=78.0,
                quality_score=0.88,
                created_at=datetime.now() - timedelta(days=15)
            ),
            RecommendationItem(
                item_id="training_001",
                title="Advanced Analytics Course",
                description="Corso avanzato di analytics",
                category="training",
                features={"innovation": 0.9, "complexity": 0.8, "efficiency": 0.6},
                tags=["training", "analytics", "advanced"],
                popularity_score=72.0,
                quality_score=0.95,
                created_at=datetime.now() - timedelta(days=7)
            )
        ]

        for item in demo_items:
            self.items[item.item_id] = item

        # Crea profili utente demo
        demo_users = [
            UserProfile(
                user_id="user_001",
                preferences={"efficiency": 0.9, "automation": 0.8, "collaboration": 0.6},
                behavior_history=[],
                skills=["data_analysis", "automation"],
                role="analyst",
                department="operations",
                experience_level="advanced",
                last_activity=datetime.now()
            ),
            UserProfile(
                user_id="user_002",
                preferences={"collaboration": 0.95, "innovation": 0.8, "efficiency": 0.7},
                behavior_history=[],
                skills=["project_management", "collaboration"],
                role="manager",
                department="projects",
                experience_level="intermediate",
                last_activity=datetime.now()
            )
        ]

        for user in demo_users:
            self.user_profiles[user.user_id] = user

    # Metodi di supporto per tools
    def _analyze_user_preferences(self, user_id: str, behavior: List[Dict[str, Any]]) -> Dict[str, float]:
        """Metodo di supporto per analisi preferenze."""
        return {"efficiency": 0.8, "automation": 0.6}

    def _find_similar_users(self, user_id: str, threshold: float) -> List[Dict[str, Any]]:
        """Metodo di supporto per ricerca utenti simili."""
        return [{"user_id": "user_002", "similarity": 0.75}]

    def _evaluate_recommendation_performance(self, period: str) -> Dict[str, Any]:
        """Metodo di supporto per valutazione performance."""
        return {"precision": 0.85, "recall": 0.72, "f1": 0.78}

# Istanza globale dell'agente
recommendation_agent = RecommendationAgent()
