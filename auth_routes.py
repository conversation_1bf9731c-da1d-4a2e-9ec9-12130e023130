#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Route per l'autenticazione.
"""

import os
import logging
from flask import Blueprint, request, jsonify, g

from auth import User, get_user_by_username, generate_token, login_required, admin_required

# Configurazione del logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Crea un blueprint per le route di autenticazione
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

@auth_bp.route('/login', methods=['POST'])
def login():
    """
    Endpoint per il login.
    
    Returns:
        Token JWT se le credenziali sono corrette, errore altrimenti
    """
    # Ottieni le credenziali dalla richiesta
    data = request.get_json()
    if not data:
        return jsonify({"error": "<PERSON><PERSON> man<PERSON>"}), 400
    
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({"error": "Username e password richiesti"}), 400
    
    # Verifica le credenziali
    user = get_user_by_username(username)
    if not user or not user.check_password(password):
        return jsonify({"error": "Credenziali non valide"}), 401
    
    # Genera un token JWT
    token = generate_token(user.id, user.username, user.role)
    
    # Restituisci il token
    return jsonify({
        "token": token,
        "user": {
            "id": user.id,
            "username": user.username,
            "role": user.role
        }
    })

@auth_bp.route('/me', methods=['GET'])
@login_required
def me():
    """
    Endpoint per ottenere le informazioni dell'utente autenticato.
    
    Returns:
        Informazioni dell'utente
    """
    return jsonify({
        "user": {
            "id": g.user_id,
            "username": g.username,
            "role": g.role
        }
    })

@auth_bp.route('/admin', methods=['GET'])
@admin_required
def admin():
    """
    Endpoint per gli amministratori.
    
    Returns:
        Messaggio di benvenuto per gli amministratori
    """
    return jsonify({
        "message": f"Benvenuto, amministratore {g.username}!"
    })

@auth_bp.route('/register', methods=['POST'])
@admin_required
def register():
    """
    Endpoint per registrare un nuovo utente.
    Solo gli amministratori possono registrare nuovi utenti.
    
    Returns:
        Informazioni del nuovo utente
    """
    # Ottieni i dati dalla richiesta
    data = request.get_json()
    if not data:
        return jsonify({"error": "Dati mancanti"}), 400
    
    username = data.get('username')
    password = data.get('password')
    role = data.get('role', 'user')
    
    if not username or not password:
        return jsonify({"error": "Username e password richiesti"}), 400
    
    # Verifica che l'username non sia già in uso
    if get_user_by_username(username):
        return jsonify({"error": "Username già in uso"}), 400
    
    # Verifica che il ruolo sia valido
    if role not in ['user', 'admin']:
        return jsonify({"error": "Ruolo non valido"}), 400
    
    # Genera l'hash della password
    password_hash = User.hash_password(password)
    
    # Crea un nuovo utente
    # In un'applicazione reale, si salverebbe l'utente in un database
    # Per semplicità, lo aggiungiamo al dizionario degli utenti
    from auth import users
    user_id = max(user.id for user in users.values()) + 1
    users[username] = User(user_id, username, password_hash, role)
    
    # Restituisci le informazioni del nuovo utente
    return jsonify({
        "user": {
            "id": user_id,
            "username": username,
            "role": role
        }
    }), 201
