#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
App Flask semplificata per testare le route problematiche
"""

from flask import Flask, jsonify
from datetime import datetime

# Crea app Flask semplice
app = Flask(__name__)
app.secret_key = 'test_key'

print("🔧 DEBUG: Creando app Flask semplificata...")

# TEST ROUTE SEMPLICE
print("🔧 DEBUG: Registrando route di test /api/test-simple...")
@app.route('/api/test-simple', methods=['GET'])
def test_simple():
    return jsonify({"message": "Route di test funziona!", "status": "success"})

# API per configurazione dipendenti - NUOVO ENDPOINT SUPABASE-FIRST
print("🔧 DEBUG: Registrando route /api/config/employees...")
@app.route('/api/config/employees', methods=['GET'])
def get_config_employees():
    """
    API per ottenere la lista dei dipendenti configurati.
    Versione semplificata per test.
    """
    print("=== API GET_CONFIG_EMPLOYEES CHIAMATA ===")
    
    try:
        # Dati demo semplici
        employees = [
            {
                'name': '<PERSON>',
                'hourly_rate': 25.0,
                'vat_included': True,
                'notes': 'Tecnico senior - Test',
                'source': 'test'
            },
            {
                'name': 'Luigi Verdi',
                'hourly_rate': 20.0,
                'vat_included': True,
                'notes': 'Tecnico junior - Test',
                'source': 'test'
            }
        ]

        print(f"👥 Config employees - Test: {len(employees)} dipendenti")

        return jsonify({
            'success': True,
            'employees': employees,
            'data_source': 'test',
            'count': len(employees),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ Errore API config/employees: {str(e)}")

        return jsonify({
            'success': False,
            'error': str(e),
            'employees': [],
            'data_source': 'error'
        }), 500

# API per configurazione veicoli - NUOVO ENDPOINT SUPABASE-FIRST
print("🔧 DEBUG: Registrando route /api/config/vehicles...")
@app.route('/api/config/vehicles', methods=['GET'])
def get_config_vehicles():
    """
    API per ottenere la lista dei veicoli configurati.
    Versione semplificata per test.
    """
    print("=== API GET_CONFIG_VEHICLES CHIAMATA ===")
    
    try:
        # Dati demo semplici
        vehicles = [
            {
                'name': 'Fiat Ducato',
                'fuel_consumption': 8.5,
                'daily_cost': 45.0,
                'notes': 'Furgone principale - Test',
                'source': 'test'
            },
            {
                'name': 'Ford Transit',
                'fuel_consumption': 7.8,
                'daily_cost': 40.0,
                'notes': 'Furgone compatto - Test',
                'source': 'test'
            }
        ]

        print(f"🚗 Config vehicles - Test: {len(vehicles)} veicoli")

        return jsonify({
            'success': True,
            'vehicles': vehicles,
            'data_source': 'test',
            'count': len(vehicles),
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ Errore API config/vehicles: {str(e)}")

        return jsonify({
            'success': False,
            'error': str(e),
            'vehicles': [],
            'data_source': 'error'
        }), 500

# Route di health check
@app.route('/api/health', methods=['GET'])
def health_check():
    return jsonify({
        'status': 'healthy',
        'message': 'Server Flask semplificato funzionante',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("🚀 Avvio server Flask semplificato...")
    print("📋 Route registrate:")
    print("   - /api/test-simple")
    print("   - /api/config/employees")
    print("   - /api/config/vehicles")
    print("   - /api/health")
    
    app.run(host='127.0.0.1', port=5001, debug=False)
