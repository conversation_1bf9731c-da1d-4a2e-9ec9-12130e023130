@echo off
setlocal enabledelayedexpansion
echo ===================================
echo Avvio dell'applicazione app-roberto con variabili d'ambiente
echo ===================================
echo.

REM Verifica se l'ambiente virtuale esiste
if not exist venv (
    echo Creazione dell'ambiente virtuale...
    python -m venv venv
    echo Ambiente virtuale creato.
    echo.
)

REM Attiva l'ambiente virtuale
echo Attivazione dell'ambiente virtuale...
call venv\Scripts\activate
echo Ambiente virtuale attivato.
echo.

REM Installa le dipendenze necessarie
echo Installazione delle dipendenze necessarie...
pip install python-dotenv fastapi
echo.

REM Risoluzione definitiva del problema con plotly e ~lotly
echo Risoluzione definitiva del problema con plotly...

REM Rimuovi manualmente la directory del pacchetto problematico ~lotly
if exist venv\Lib\site-packages\~lotly (
    echo Rimozione manuale del pacchetto ~lotly...
    rd /s /q venv\Lib\site-packages\~lotly 2>nul
)

REM Rimuovi anche eventuali file di metadati associati
if exist venv\Lib\site-packages\~lotly-* (
    echo Rimozione file di metadati ~lotly...
    del /q venv\Lib\site-packages\~lotly-* 2>nul
)

REM Pulisci la cache di pip per evitare problemi di installazione
echo Pulizia della cache di pip...
pip cache purge 2>nul

REM Rimuovi completamente plotly e tutti i suoi metadati
echo Rimozione completa di plotly e dei suoi metadati...
pip uninstall -y plotly 2>nul

REM Rimuovi manualmente tutti i file relativi a plotly
if exist venv\Lib\site-packages\plotly (
    echo Rimozione manuale della directory plotly...
    rd /s /q venv\Lib\site-packages\plotly 2>nul
)
if exist venv\Lib\site-packages\plotly-* (
    echo Rimozione file di metadati plotly...
    del /q venv\Lib\site-packages\plotly-* 2>nul
)

REM Reinstalla plotly con l'opzione --force-reinstall e --no-cache-dir
echo Reinstallazione pulita di plotly...
pip install --force-reinstall --no-cache-dir plotly==5.16.1

REM Installa le altre dipendenze
echo Installazione delle altre dipendenze...
pip install -r requirements.txt
echo Dipendenze installate.

REM Verifica che plotly sia stato installato correttamente e che non ci siano pacchetti ~lotly
echo Verifica dell'installazione di plotly...
pip show plotly > nul 2>&1
if %errorlevel% neq 0 (
    echo ATTENZIONE: Problemi con l'installazione di plotly. Tentativo di riparazione finale...
    pip install --force-reinstall --no-cache-dir plotly==5.16.1
    echo.
)

REM Verifica che non ci siano più pacchetti ~lotly
pip list 2>&1 | findstr "~lotly" > nul
if %errorlevel% equ 0 (
    echo ATTENZIONE: Il pacchetto ~lotly è ancora presente. Tentativo di riparazione finale...

    REM Riparazione più aggressiva: reinstalla pip
    echo Aggiornamento di pip per risolvere problemi di metadati...
    python -m pip install --upgrade pip

    REM Rimuovi nuovamente il pacchetto problematico
    if exist venv\Lib\site-packages\~lotly (
        rd /s /q venv\Lib\site-packages\~lotly 2>nul
    )
    if exist venv\Lib\site-packages\~lotly-* (
        del /q venv\Lib\site-packages\~lotly-* 2>nul
    )

    REM Reinstalla plotly una volta ancora
    pip install --force-reinstall --no-cache-dir plotly==5.16.1

    REM Verifica se il problema persiste
    pip list 2>&1 | findstr "~lotly" > nul
    if %errorlevel% equ 0 (
        echo.
        echo ===================================
        echo ATTENZIONE: Il problema con il pacchetto ~lotly persiste.
        echo Per risolvere definitivamente il problema, è necessario
        echo ricreare completamente l'ambiente virtuale.
        echo.
        echo Esegui il comando: reset_environment.bat
        echo ===================================
        echo.
    )
) else (
    echo Pacchetto plotly installato correttamente e nessun pacchetto ~lotly trovato.
)
echo.

REM Crea la cartella uploads se non esiste
if not exist uploads (
    echo Creazione della cartella uploads...
    mkdir uploads
    echo Cartella uploads creata.
    echo.
)

REM Leggi le variabili d'ambiente dal file .env
echo Lettura delle variabili d'ambiente dal file .env...
for /f "tokens=*" %%a in (.env) do (
    set "line=%%a"
    if not "!line:~0,1!"=="#" (
        if not "!line!"=="" (
            set "%%a"
        )
    )
)

REM Imposta manualmente le variabili d'ambiente principali
set OPENROUTER_API_KEY=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
set APP_URL=http://localhost:5001
set MCP_URL=http://localhost:8000

echo Variabili d'ambiente impostate:
echo OPENROUTER_API_KEY: %OPENROUTER_API_KEY%
echo APP_URL: %APP_URL%
echo MCP_URL: %MCP_URL%
echo.

REM Avvia il server MCP in una nuova finestra
echo Avvio del server MCP...
start "MCP Server" cmd /k "call venv\Scripts\activate && cd mcp_server && python run_server.py"
echo Server MCP avviato in una nuova finestra.
echo.

REM Attendi 5 secondi per dare tempo al server MCP di avviarsi
echo Attesa di 5 secondi per l'avvio del server MCP...
ping 127.0.0.1 -n 6 > nul
echo.

REM Avvia l'applicazione principale
echo Avvio dell'applicazione principale...
start "App Roberto" cmd /k "call venv\Scripts\activate && python app.py"
echo Applicazione principale avviata in una nuova finestra.
echo.

echo ===================================
echo Applicazione avviata con successo!
echo - Flask: http://localhost:5000
echo - MCP: http://localhost:8000
echo ===================================
echo.
echo Premi un tasto per chiudere questa finestra...
pause > nul
