#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional, Any

# Configurazione del logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FileTypeDetector:
    """
    Classe per il rilevamento automatico del tipo di file in base alla struttura delle colonne.
    Utilizza un sistema di firme (pattern di colonne attese) per identificare i diversi tipi di file.
    """

    def __init__(self):
        # Definizione delle firme per i diversi tipi di file
        # Ogni firma è un dizionario con:
        # - required_columns: colonne che devono essere presenti
        # - optional_columns: colonne che potrebbero essere presenti
        # - weight: peso di ogni colonna per il calcolo del punteggio
        self.FILE_SIGNATURES = {
            "attivita": {
                "required_columns": [
                    "Contratto",
                    "Id Ticket",
                    "Iniziata il",
                    "Conclusa il",
                    "Durata"
                ],
                "optional_columns": [
                    "Creato da",
                    "Descrizione",
                    "Stato",
                    "Tipo"
                ],
                "weight": {
                    "Contratto": 1.0,
                    "Id Ticket": 1.0,
                    "Iniziata il": 1.5,
                    "Conclusa il": 1.5,
                    "Durata": 1.5,
                    "Creato da": 0.8,
                    "Descrizione": 0.5,
                    "Stato": 0.5,
                    "Tipo": 0.5
                }
            },
            "teamviewer": {
                "required_columns": [
                    "Assegnatario",
                    "Nome",
                    "Inizio",
                    "Fine",
                    "Durata"
                ],
                "optional_columns": [
                    "Codice",
                    "Tipo di sessione",
                    "Gruppo",
                    "Note",
                    "Classificazione",
                    "Commenti del cliente"
                ],
                "weight": {
                    "Assegnatario": 1.5,
                    "Nome": 1.0,
                    "Inizio": 1.5,
                    "Fine": 1.5,
                    "Durata": 1.5,
                    "Codice": 0.8,
                    "Tipo di sessione": 1.0,
                    "Gruppo": 0.8,
                    "Note": 0.3,
                    "Classificazione": 0.5,
                    "Commenti del cliente": 0.3
                }
            },
            "calendario": {
                "required_columns": [
                    "SUMMARY",
                    "DTSTART",
                    "DTEND"
                ],
                "optional_columns": [
                    "ATTENDEE",
                    "LOCATION",
                    "PRIORITY",
                    "DUE",
                    "URL",
                    "CALENDAR",
                    "UID",
                    "ORGANIZER",
                    "CATEGORIES",
                    "DURATION"
                ],
                "weight": {
                    "SUMMARY": 1.5,
                    "DTSTART": 1.5,
                    "DTEND": 1.5,
                    "ATTENDEE": 1.0,
                    "LOCATION": 0.8,
                    "PRIORITY": 0.5,
                    "DUE": 0.3,
                    "URL": 0.3,
                    "CALENDAR": 0.3,
                    "UID": 0.3,
                    "ORGANIZER": 0.5,
                    "CATEGORIES": 0.3,
                    "DURATION": 0.5
                }
            },
            "timbrature": {
                "required_columns": [
                    "Data",
                    "Entrata",
                    "Uscita",
                    "Dipendente"
                ],
                "optional_columns": [
                    "Ore lavorate",
                    "Note"
                ],
                "weight": {
                    "Data": 1.5,
                    "Entrata": 1.5,
                    "Uscita": 1.5,
                    "Dipendente": 1.0,
                    "Ore lavorate": 0.8,
                    "Note": 0.3
                }
            },
            "permessi": {
                "required_columns": [
                    "Data inizio",
                    "Data fine",
                    "Dipendente",
                    "Tipo permesso"
                ],
                "optional_columns": [
                    "Ore",
                    "Approvato da",
                    "Note"
                ],
                "weight": {
                    "Data inizio": 1.5,
                    "Data fine": 1.5,
                    "Dipendente": 1.0,
                    "Tipo permesso": 1.5,
                    "Ore": 0.8,
                    "Approvato da": 0.5,
                    "Note": 0.3
                }
            },
            "registro_auto": {
                "required_columns": [
                    "Dipendente",
                    "Data",
                    "Auto",
                    "Presa Data e Ora",
                    "Cliente"
                ],
                "optional_columns": [
                    "Riconsegna Data e Ora",
                    "Ore"
                ],
                "weight": {
                    "Dipendente": 1.5,
                    "Data": 1.5,
                    "Auto": 1.0,
                    "Presa Data e Ora": 1.5,
                    "Riconsegna Data e Ora": 1.0,
                    "Cliente": 1.0,
                    "Ore": 0.8
                }
            }
        }

        # Soglia minima di punteggio per considerare un match valido
        self.MIN_MATCH_THRESHOLD = 0.6

    def detect_file_type(self, df: pd.DataFrame) -> Tuple[str, float, Dict[str, float]]:
        """
        Analizza un DataFrame e determina il tipo di file in base alle colonne presenti.

        Args:
            df: DataFrame pandas da analizzare

        Returns:
            Tuple contenente:
            - Il tipo di file rilevato (o "unknown" se non riconosciuto)
            - Il punteggio di confidenza (da 0 a 1)
            - Un dizionario con i punteggi per ogni tipo di file
        """
        if df is None or df.empty:
            logger.warning("DataFrame vuoto o None fornito al rilevatore di tipo file")
            return "unknown", 0.0, {}

        # Normalizza i nomi delle colonne (rimuovi spazi extra, converti in lowercase)
        df_columns = [self._normalize_column_name(col) for col in df.columns]

        # Calcola i punteggi per ogni tipo di file
        scores = {}
        for file_type, signature in self.FILE_SIGNATURES.items():
            score = self._calculate_match_score(df_columns, signature)
            scores[file_type] = score
            logger.debug(f"Punteggio per {file_type}: {score:.2f}")

        # Trova il tipo di file con il punteggio più alto
        if not scores:
            return "unknown", 0.0, {}

        best_match = max(scores.items(), key=lambda x: x[1])
        file_type, score = best_match

        # Verifica se il punteggio supera la soglia minima
        if score < self.MIN_MATCH_THRESHOLD:
            logger.info(f"Nessun tipo di file riconosciuto con sufficiente confidenza. Punteggio massimo: {score:.2f} per {file_type}")
            return "unknown", score, scores

        logger.info(f"Tipo di file rilevato: {file_type} con confidenza {score:.2f}")
        return file_type, score, scores

    def _normalize_column_name(self, column_name: str) -> str:
        """
        Normalizza il nome di una colonna per il confronto.

        Args:
            column_name: Nome della colonna da normalizzare

        Returns:
            Nome della colonna normalizzato
        """
        if not isinstance(column_name, str):
            return str(column_name).lower().strip()
        return column_name.lower().strip()

    def _calculate_match_score(self, df_columns: List[str], signature: Dict[str, Any]) -> float:
        """
        Calcola un punteggio di corrispondenza tra le colonne del DataFrame e una firma.

        Args:
            df_columns: Lista dei nomi delle colonne normalizzati
            signature: Dizionario contenente la firma del tipo di file

        Returns:
            Punteggio di corrispondenza (da 0 a 1)
        """
        # Normalizza i nomi delle colonne nella firma
        required_columns = [self._normalize_column_name(col) for col in signature["required_columns"]]
        optional_columns = [self._normalize_column_name(col) for col in signature["optional_columns"]]
        weights = {self._normalize_column_name(col): weight for col, weight in signature["weight"].items()}

        # Calcola il punteggio massimo possibile
        max_score = sum(weights.values())

        # Calcola il punteggio effettivo
        actual_score = 0.0

        # Verifica la presenza delle colonne richieste e opzionali
        for col in required_columns + optional_columns:
            normalized_df_columns = [self._normalize_column_name(c) for c in df_columns]
            if col in normalized_df_columns:
                actual_score += weights.get(col, 0.0)
            elif col in required_columns:
                # Penalizza fortemente la mancanza di colonne richieste
                actual_score -= weights.get(col, 0.0)

        # Normalizza il punteggio (da 0 a 1)
        if max_score > 0:
            normalized_score = max(0.0, min(1.0, actual_score / max_score))
        else:
            normalized_score = 0.0

        return normalized_score

    def get_column_mapping_suggestions(self, df: pd.DataFrame, detected_type: str) -> Dict[str, str]:
        """
        Suggerisce una mappatura tra le colonne del DataFrame e le colonne standard
        per il tipo di file rilevato.

        Args:
            df: DataFrame pandas da analizzare
            detected_type: Tipo di file rilevato

        Returns:
            Dizionario con la mappatura suggerita {colonna_df: colonna_standard}
        """
        if detected_type not in self.FILE_SIGNATURES or detected_type == "unknown":
            return {}

        signature = self.FILE_SIGNATURES[detected_type]
        standard_columns = signature["required_columns"] + signature["optional_columns"]

        # Inizializza la mappatura
        mapping = {}

        # Per ogni colonna standard, cerca la migliore corrispondenza nel DataFrame
        for std_col in standard_columns:
            best_match = None
            best_score = 0.0

            for df_col in df.columns:
                # Calcola un punteggio di similarità tra i nomi delle colonne
                similarity = self._calculate_column_similarity(std_col, df_col)

                if similarity > best_score and similarity > 0.7:  # Soglia di similarità
                    best_match = df_col
                    best_score = similarity

            if best_match:
                mapping[best_match] = std_col

        return mapping

    def _calculate_column_similarity(self, col1: str, col2: str) -> float:
        """
        Calcola un punteggio di similarità tra due nomi di colonne.

        Args:
            col1: Primo nome di colonna
            col2: Secondo nome di colonna

        Returns:
            Punteggio di similarità (da 0 a 1)
        """
        # Normalizza i nomi delle colonne
        col1 = self._normalize_column_name(col1)
        col2 = self._normalize_column_name(col2)

        # Caso più semplice: corrispondenza esatta
        if col1 == col2:
            return 1.0

        # Caso: una colonna è contenuta nell'altra
        if col1 in col2 or col2 in col1:
            return 0.8

        # Implementazione semplice di similarità basata su caratteri comuni
        # In un'implementazione reale, si potrebbe usare una metrica più sofisticata
        # come la distanza di Levenshtein o la similarità di Jaccard
        common_chars = set(col1) & set(col2)
        all_chars = set(col1) | set(col2)

        if not all_chars:
            return 0.0

        return len(common_chars) / len(all_chars)
