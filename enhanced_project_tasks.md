# Piano di Implementazione per il Riconoscimento e l'Elaborazione dei File di Attività (Versione Avanzata con Server MCP)

## FASE 1: Setup e Riconoscimento File

### Task 1.1: Preparazione Ambiente Backend (aggiornato)

* **Task 1.1.1: Aggiornamento dipendenze Backend (FastAPI, uvicorn, pandas, openpyxl, httpx)**
  * **Scopo:** Assicurare che il backend abbia tutte le librerie necessarie per l'upload e le chiamate HTTP esterne.
  * **Azione:** Attiva l'ambiente virtuale e installa/aggiorna `fastapi`, `uvicorn`, `pandas`, `openpyxl`, `httpx` (o `requests` se preferito).
* **Task 1.1.2: Creazione file requirements.txt Backend**
  * **Scopo:** Documentare le dipendenze aggiornate.
  * **Azione:** Genera il file `requirements.txt` (es. `pip freeze > requirements.txt`).

### Task 1.2: Implementazione del Sistema di Riconoscimento del Tipo di File nel Backend principale

* **Task 1.2.1: Creare un modulo `file_detector.py` con la classe `FileTypeDetector`**
  * **Scopo:** Identificare il tipo di file (es. "File Attività") in base alla sua struttura.
  * **Azione:** Implementare un dizionario `FILE_SIGNATURES` con le firme (pattern di colonne attese) per i tipi di file supportati (iniziando con il file di attività attuale). Sviluppare il metodo `detect_file_type(df)` che analizza le colonne del DataFrame e assegna punteggi di corrispondenza alle firme definite.
  * **Note:** Il riconoscimento si baserà sulla presenza/assenza e sui nomi delle colonne chiave (`Contratto`, `Id Ticket`, `Iniziata il`, `Conclusa il`, `Durata`, `Creato da`).
* **Task 1.2.2: Integrare il rilevatore di tipo file nell'endpoint di upload del Backend**
  * **Scopo:** Utilizzare il rilevatore subito dopo il caricamento del file.
  * **Azione:** Modificare l'endpoint `/uploadfile/` in `main.py` per utilizzare `FileTypeDetector`. Aggiungere log per mostrare il tipo di file rilevato. Gestire i casi in cui il tipo di file non viene riconosciuto, restituendo un errore appropriato al frontend.

### Task 1.3: Endpoint API Backend per ottenere la struttura del file

* **Task 1.3.1: Modifica dell'endpoint `/file-info/{file_id}` (o creazione nuovo)**
  * **Scopo:** Fornire al frontend i metadati del file caricato (nomi colonne, tipo riconosciuto) per permettere all'utente di selezionare/confermare.
  * **Azione:** Estendi questo endpoint per includere il tipo di file riconosciuto da `FileTypeDetector` e la lista esatta delle colonne presenti nel file temporaneo.

## FASE 2: Server MCP (Micro-Capability Platform) - Core di Analisi e Elaborazione Dati

### Task 2.1: Setup dell'Infrastruttura del Server MCP

* **Task 2.1.1: Creazione sottodirectory e inizializzazione progetto Python per MCP**
  * **Scopo:** Preparare un ambiente dedicato per il server MCP.
  * **Azione:** All'interno della directory radice del progetto, crea una sottodirectory `mcp_server`. Inizializza un ambiente virtuale Python e installa le dipendenze necessarie (`fastapi`, `uvicorn`, `pandas`, `openpyxl`).
* **Task 2.1.2: Creazione Dockerfile per MCP**
  * **Scopo:** Containerizzare il server MCP per l'orchestrazione con Docker Compose.
  * **Azione:** Crea un `Dockerfile` nella directory `mcp_server` per containerizzare l'applicazione Python.

### Task 2.2: Implementazione del Parser di Durate nel Server MCP

* **Task 2.2.1: Creare un modulo `duration_parser.py` con la classe `DurationParser` nell'MCP**
  * **Scopo:** Gestire la conversione e validazione dei formati di durata.
  * **Azione:** Implementare il metodo `parse_duration(duration_str)` che supporta formati `HH:MM` e decimali (con virgola/punto). Aggiungere gestione di casi speciali (0:00, valori vuoti, valori negativi).
* **Task 2.2.2: Implementare la validazione delle durate nell'MCP**
  * **Scopo:** Verificare la coerenza tra durate calcolate e dichiarate.
  * **Azione:** Sviluppare il metodo `validate_duration(start_time, end_time, duration_parsed)` che verifica la coerenza tra `Iniziata il`, `Conclusa il` e la `Durata` parsata. Aggiungere funzionalità per segnalare (ma non correggere automaticamente in questa fase) le discrepanze.

### Task 2.3: Implementazione del Sistema di Mappatura delle Colonne nel Server MCP

* **Task 2.3.1: Creare un modulo `column_mapper.py` con la classe `ColumnMapper` nell'MCP**
  * **Scopo:** Standardizzare i nomi delle colonne dei file importati.
  * **Azione:** Implementare un dizionario `COLUMN_MAPPINGS` con le mappature standard (es. `Iniziata il` -> `Data_Ora_Inizio`, `Durata` -> `Durata_Attivita_Ore_Decimali`). Sviluppare il metodo `map_columns(df, file_type)` che applica queste mappature, gestisce le colonne mancanti (es. riempiendo con NaN) e rileva colonne simili.

### Task 2.4: Elaborazione Dati di Attività nel Server MCP

* **Task 2.4.1: Creare un modulo `activity_processor.py` con la classe `ActivityProcessor` nell'MCP**
  * **Scopo:** Eseguire l'elaborazione principale dei dati.
  * **Azione:** Implementare il metodo `process_activity_data(df_raw, file_type)` che:
    * Utilizza `ColumnMapper` per standardizzare i nomi.
    * Utilizza `DurationParser` per convertire e validare la colonna `Durata`.
    * Converte le colonne `Iniziata il` e `Conclusa il` in oggetti datetime.
    * Calcola la durata effettiva (`Conclusa il` - `Iniziata il`) e confrontala con la colonna `Durata` fornita per identificare discrepanze.
    * Estrae informazioni aggiuntive (giorno della settimana, mese, anno, ora del giorno).
    * Identifica valori mancanti o anomali in colonne chiave.
    * Restituisce un DataFrame elaborato e un report di stato/discrepanze.

### Task 2.5: Esposizione API del Server MCP

* **Task 2.5.1: Implementazione del file `main.py` per FastAPI nel server MCP**
  * **Scopo:** Definire il punto di ingresso per le API del server MCP.
  * **Azione:** Inizializzare FastAPI nel `mcp_server/main.py`.
* **Task 2.5.2: Endpoint API MCP per elaborazione file**
  * **Scopo:** Permettere al backend principale di richiedere l'elaborazione dei dati.
  * **Azione:** Creare un endpoint POST (es. `/process-file/{file_id}`) che riceva l'ID di un file temporaneo (gestito dal backend principale), legga il file (es. da una shared volume o riceva il contenuto), lo processi con `ActivityProcessor` e restituisca il DataFrame elaborato (serializzato) e le informazioni sulle discrepanze.

## FASE 3: Orchestrazione e Integrazione Backend-MCP

### Task 3.1: Configurazione `docker-compose.yml` (aggiornato)

* **Scopo:** Definire e orchestrare il servizio MCP insieme a frontend e backend.
* **Azione:** Aggiungi un nuovo servizio (`mcp_server`) nel `docker-compose.yml`, mappando le porte e, se necessario, configurando un volume condiviso per i file temporanei tra backend e MCP.

### Task 3.2: Integrazione del Backend con il Server MCP

* **Task 3.2.1: Modifica dell'endpoint di upload nel Backend principale**
  * **Scopo:** Dopo aver salvato il file e riconosciuto il tipo, il backend deve invocare l'MCP per l'elaborazione.
  * **Azione:** Nell'endpoint `/uploadfile/` del backend principale, dopo il salvataggio temporaneo e il riconoscimento del tipo di file, effettuare una chiamata HTTP interna al nuovo endpoint `/process-file/{file_id}` del server MCP. Salvare la risposta elaborata dall'MCP (es. in memoria o in un database temporaneo associato all'ID del file).
* **Task 3.2.2: Endpoint API Backend per ottenere dati elaborati e report di analisi**
  * **Scopo:** Il frontend richiederà i dati elaborati e le analisi all'MCP tramite il backend principale.
  * **Azione:** Creare o aggiornare endpoint nel backend principale (es. `/processed-data/{file_id}` e `/analysis-report/{file_id}`) che recuperino i dati elaborati e il report di stato salvati dopo la chiamata all'MCP e li forniscano al frontend.

## FASE 4: Visualizzazione Dati e UI Avanzata (Frontend)

### Task 4.1: Miglioramento della Visualizzazione Dati Grezzi

* **Task 4.1.1: Implementazione componente Tabella Interattiva (Frontend)**
  * **Scopo:** Mostrare i dati elaborati in formato tabellare.
  * **Azione:** Utilizza una libreria come `react-table`. Il componente deve ricevere i dati dall'endpoint `/processed-data/{file_id}` del backend.
* **Task 4.1.2: Aggiungere funzionalità di paginazione, ordinamento e filtro alla tabella**
  * **Scopo:** Permettere all'utente di esplorare i dati efficacemente.
  * **Azione:** Configura la libreria di tabelle per supportare queste funzionalità.
* **Task 4.1.3: Implementare evidenziazione celle basata su risultati analisi (es. discrepanze, valori mancanti)**
  * **Scopo:** Visualizzare i risultati delle analisi direttamente nella tabella dati.
  * **Azione:** Modifica il componente tabella per applicare stili (es. colore di sfondo, icone) alle celle in base al report di discrepanze/analisi ricevuto dal backend (dall'MCP).

### Task 4.2: Implementazione dei Grafici Interattivi

* **Task 4.2.1: Installazione libreria di grafici nel Frontend**
  * **Scopo:** Aggiungere capacità di visualizzazione grafica.
  * **Azione:** Installa una libreria come `Chart.js`, `Recharts` (per React) o `D3.js`.
* **Task 4.2.2: Implementare grafici base con dati da un foglio elaborato**
  * **Scopo:** Visualizzare le distribuzioni o le tendenze dei dati.
  * **Azione:** Aggiungi logica nel Frontend per preparare i dati per grafici (es. barre per ore per tecnico/azienda, linee per andamento temporale).
* **Task 4.2.3: Creare componente UI per selezione tipo grafico e colonne**
  * **Scopo:** Permettere all'utente di scegliere quali dati visualizzare e come.
  * **Azione:** Aggiungi elementi UI (menu a tendina, checkbox) per selezionare il tipo di grafico e le colonne da includere.

### Task 4.3: Creazione di una Dashboard con KPI

* **Task 4.3.1: Implementazione layout a griglia per la Dashboard**
  * **Scopo:** Creare un'area dove gli utenti possono disporre widget.
  * **Azione:** Utilizza CSS Grid o una libreria di layout a griglia (es. `react-grid-layout`).
* **Task 4.3.2: Creare componenti "widget" riutilizzabili (Tabella, Grafico)**
  * **Scopo:** Incapsulare le funzionalità di visualizzazione in blocchi che possono essere aggiunti alla dashboard.
  * **Azione:** Rendi i componenti Tabella e Grafico riutilizzabili come widget.
* **Task 4.3.3: Implementare calcolo e visualizzazione di KPI specifici**
  * **Scopo:** Mostrare metriche chiave.
  * **Azione:** Calcola KPI (ore totali, media ore per attività, numero di ticket unici, ecc.) e mostra in componenti visivi dedicati nella dashboard.

## FASE 5: Integrazione OpenRouter e Chat LLM (Riadattata per MCP)

### Task 5.1: Integrazione Backend-OpenRouter

* **Task 5.1.1: Installazione libreria client per API HTTP nel Backend (se non già fatto)**
  * **Scopo:** Preparare il backend per effettuare chiamate API esterne.
  * **Azione:** Assicurati che `httpx` o `requests` siano installati.
* **Task 5.1.2: Implementazione client base per API OpenRouter nel Backend**
  * **Scopo:** Scrivere il codice per interagire con l'API di OpenRouter.
  * **Azione:** Crea un modulo o una classe nel backend per gestire le chiamate all'API di OpenRouter, inclusa la configurazione della chiave API.
* **Task 5.1.3: Endpoint API Backend per listare i modelli disponibili su OpenRouter**
  * **Scopo:** Permettere al frontend di mostrare all'utente quali modelli LLM sono selezionabili.
  * **Azione:** Crea un endpoint GET (es. `/llm/models`) che usi il client OpenRouter per recuperare la lista dei modelli disponibili.

### Task 5.2: Chat UI e Interazione Frontend

* **Task 5.2.1: Creazione componente UI Chat nel Frontend**
  * **Scopo:** Implementare l'interfaccia visiva della chat.
  * **Azione:** Crea un componente Frontend con un'area per i messaggi, un input text e un pulsante di invio.
* **Task 5.2.2: Implementazione logica Frontend per inviare/ricevere messaggi dalla chat**
  * **Scopo:** Gestire la comunicazione con il backend e visualizzare le risposte.
  * **Azione:** Aggiungi un gestore di eventi al pulsante di invio che prenda il testo dall'input e lo invii all'endpoint `/chat/send` del backend. Gestisci la risposta e aggiungila all'area dei messaggi.
* **Task 5.2.3: Implementazione selezione modello LLM nel Frontend**
  * **Scopo:** Permettere all'utente di scegliere il modello da OpenRouter.
  * **Azione:** Usa l'endpoint `/llm/models` per popolare un menu a tendina o una lista.

### Task 5.3: Logica di Interazione LLM con Dati (Delegata all'MCP)

* **Task 5.3.1: Logica Backend per serializzare i dati e inoltrare le richieste LLM all'MCP (Nuovo Task)**
  * **Scopo:** Il backend principale inoltra la richiesta di chat all'MCP, includendo l'ID del file elaborato.
  * **Azione:** L'endpoint `/chat/send` del backend principale preparerà un messaggio per l'MCP (che includerà la query dell'utente e l'ID del file corrente) e invierà una richiesta a un nuovo endpoint dell'MCP.
* **Task 5.3.2: Endpoint API MCP per la gestione delle richieste LLM con contesto dati (Nuovo Task)**
  * **Scopo:** L'MCP riceve la richiesta di chat, recupera i dati elaborati e formula il prompt per l'LLM.
  * **Azione:** Crea un endpoint POST nell'MCP (es. `/llm-query`) che:
    * Riceva la query dell'utente e l'ID del file.
    * Recuperi il DataFrame elaborato (dalla sua cache o da un volume condiviso).
    * Utilizzi funzioni interne per **serializzare i dati analizzati** (es. statistiche aggregate, sample di dati, elenco discrepanze) in un formato conciso e comprensibile per l'LLM (testo descrittivo, JSON snippet).
    * **Crea un prompt template per l'LLM** che combini istruzioni chiare con i dati serializzati (es. "Analizza questi dati: [dati_serializzati]. Rispondi alla seguente domanda: [query_utente]").
    * Inoltri la richiesta all'API OpenRouter (tramite il client OpenRouter, che potrebbe essere anche nell'MCP o esposto da un altro servizio se necessario per la scalabilità).
    * Restituisca la risposta dell'LLM al backend principale.
* **Task 5.3.3: Gestione della risposta dell'LLM nel Backend principale**
  * **Scopo:** Ricevere ed elaborare la risposta dall'LLM (tramite l'MCP).
  * **Azione:** Nell'endpoint `/chat/send` del backend principale, ricevi la risposta dall'MCP e preparala per essere inviata al frontend.

## FASE 6: Funzionalità Agentiche e Avanzate

### Task 6.1: Definizione e Implementazione Agenti (con interazione MCP)

* **Task 6.1.1: Definizione concettuale dei primi Agenti AI (aggiornato)**
  * **Scopo:** Progettare il comportamento e le capacità degli agenti che useranno l'MCP come strumento.
  * **Azione:** Crea un documento `agent_specs.md` che descriva il primo agente (es. Agente Pulizia Dati): il suo scopo, gli input, gli output e quali "strumenti" (endpoint MCP come "identificazione valori mancanti", "suggerimento correzioni") utilizzerà.
* **Task 6.1.2: Implementazione struttura base per un Framework Agenti nel Backend (o MCP)**
  * **Scopo:** Creare il codice che orchestrerà gli agenti.
  * **Azione:** Decidere se il framework agentico risiede nel backend principale o nell'MCP (o in un terzo servizio). Inizialmente, potrebbe essere nel backend principale che invoca API dell'MCP per le operazioni sui dati. Implementa una struttura base che possa caricare definizioni di agenti e coordinare le loro azioni.
* **Task 6.1.3: Creazione del primo Agente (es. Agente Pulizia Dati - Logica Backend/MCP)**
  * **Scopo:** Implementare la logica del primo agente.
  * **Azione:** Scrivi il codice Python per l'agente. Questo agente utilizzerà gli endpoint dell'MCP per identificare e suggerire correzioni per i problemi comuni nei dati (valori mancanti, formati inconsistenti, discrepanze di durata).
* **Task 6.1.4: Endpoint API Backend per invocare gli Agenti**
  * **Scopo:** Permettere al frontend o ad altri sistemi di avviare gli agenti.
  * **Azione:** Crea un endpoint POST (es. `/agents/run/{agent_name}`) che riceva i parametri necessari e avvii l'esecuzione dell'agente specificato, il quale a sua volta interagirà con l'MCP.
* **Task 6.1.5: Componente UI Frontend per interagire con gli Agenti**
  * **Scopo:** Fornire un'interfaccia per avviare gli agenti e visualizzare il loro stato/risultati.
  * **Azione:** Crea un'area nell'UI dove l'utente può selezionare un agente, fornire input e vedere l'output o lo stato di avanzamento.

### Task 6.2: Esportazione Dati

* **Task 6.2.1: Implementazione funzione Backend per esportare dati in formato XLSX/CSV (con dati elaborati da MCP)**
  * **Scopo:** Permettere agli utenti di scaricare i dati elaborati o i risultati.
  * **Azione:** L'endpoint di esportazione nel backend principale richiederà i dati elaborati all'MCP (o userà quelli in cache) e li scriverà in formato XLSX o CSV.
* **Task 6.2.2: Endpoint API Backend per scaricare file esportati**
  * **Scopo:** Inviare il file generato al browser dell'utente.
  * **Azione:** Crea un endpoint (es. `/export/{file_format}/{file_id}`) che restituisca il file richiesto.
* **Task 6.2.3: Implementazione logica Frontend per richiedere e gestire il download file**
  * **Scopo:** Permettere all'utente di avviare l'esportazione dal browser.
  * **Azione:** Aggiungi pulsanti nell'UI che chiamino gli endpoint di esportazione del backend e gestiscano il download del file.

## FASE 7: Testing, Sicurezza e Ottimizzazione

### Task 7.1: Testing (Backend e MCP)

* **Task 7.1.1: Configurazione framework di testing per Backend e MCP (es. pytest)**
  * **Scopo:** Preparare l'ambiente per i test automatici.
  * **Azione:** Installa `pytest` e crea directory `tests/` separate per backend e mcp_server.
* **Task 7.1.2: Scrittura test unitari per le funzioni di parsing file (MCP)**
  * **Scopo:** Verificare che la lettura e l'elaborazione dei file funzionino correttamente nell'MCP.
  * **Azione:** Scrivi test che usino file di esempio per verificare `FileTypeDetector`, `DurationParser`, `ColumnMapper` e `ActivityProcessor`.
* **Task 7.1.3: Scrittura test di integrazione per gli endpoint API Backend (upload, file-info, chat)**
  * **Scopo:** Verificare che gli endpoint API del backend principale e la loro interazione con l'MCP e OpenRouter funzionino.
  * **Azione:** Scrivi test che simulino le richieste HTTP al backend.
* **Task 7.1.4: Scrittura test di integrazione per gli endpoint API MCP**
  * **Scopo:** Verificare che gli endpoint API dell'MCP funzionino come previsto.
  * **Azione:** Scrivi test che simulino le richieste HTTP all'MCP.

### Task 7.2: Sicurezza e Autenticazione

* **Task 7.2.1: Ricerca e scelta di una strategia di Autenticazione/Autorizzazione**
  * **Scopo:** Decidere come gli utenti accederanno e quali permessi avranno.
  * **Azione:** Ricerca framework o approcci comuni (es. OAuth2, JWT) e scegli quello più adatto.
* **Task 7.2.2: Implementazione base del sistema di Autenticazione nel Backend**
  * **Scopo:** Permettere agli utenti di registrarsi e/o effettuare il login.
  * **Azione:** Implementa gli endpoint e la logica base per la registrazione e l'autenticazione degli utenti.
* **Task 7.2.3: Protezione degli endpoint API con Autenticazione/Autorizzazione**
  * **Scopo:** Assicurarsi che solo gli utenti autorizzati possano accedere alle funzionalità.
  * **Azione:** Applica i decoratori o la logica di autenticazione/autorizzazione agli endpoint API critici in entrambi i servizi (backend e MCP se l'MCP espone API pubbliche, altrimenti solo nel backend).

### Task 7.3: Ottimizzazione delle Performance

* **Task 7.3.1: Implementazione caching per i risultati dell'analisi nel MCP**
  * **Scopo:** Evitare di rifare calcoli costosi per gli stessi file/fogli.
  * **Azione:** Usa un meccanismo di caching (es. cache in memoria, Redis) nell'MCP per memorizzare i risultati delle analisi.
* **Task 7.3.2: Ottimizzazione dell'uso di Pandas per dataset grandi (MCP)**
  * **Scopo:** Migliorare le performance quando si lavora con file molto grandi.
  * **Azione:** Rivedi il codice che usa pandas per assicurarti che sia efficiente (es. scegliendo i tipi di dati corretti, evitando loop inefficienti).
* **Task 7.3.3: Implementazione lazy loading per componenti Frontend pesanti**
  * **Scopo:** Ridurre il tempo di caricamento iniziale dell'applicazione.
  * **Azione:** Configura il routing o l'importazione dei componenti Frontend per caricare i moduli solo quando necessario.

## Priorità e Dipendenze (Aggiornate)

* **FASE 1 (Setup e Riconoscimento File):** Alta priorità, nessuna dipendenza (necessaria per iniziare).
* **FASE 2 (Server MCP):** Altissima priorità (core del sistema), inizia in parallelo con FASE 1 o subito dopo.
* **FASE 3 (Orchestrazione Backend-MCP):** Alta priorità, dipende da FASE 1 e FASE 2.
* **FASE 4 (Visualizzazione Dati):** Media priorità, dipende da FASE 3.
* **FASE 5 (Integrazione LLM):** Media priorità, dipende da FASE 3 e FASE 4 (per avere dati da interrogare).
* **FASE 6 (Agenti e Esportazione):** Bassa-Media priorità, dipende da FASE 2 e FASE 3 (per avere funzionalità di analisi).
* **FASE 7 (Testing, Sicurezza, Ottimizzazione):** Continua e trasversale a tutte le fasi, con maggiore enfasi verso la fine.

## Tempistiche Stimate (Da Rivedere con l'agente se necessario)

Le tempistiche stimate sono indicative e si basano sull'organizzazione attuale. La suddivisione in un MCP potrebbe aumentare leggermente le prime fasi di setup ma dovrebbe velocizzare e rendere più robusto lo sviluppo delle fasi successive.

* **FASE 1:** 2-3 giorni
* **FASE 2:** 4-6 giorni (è il cuore logico)
* **FASE 3:** 2-3 giorni
* **FASE 4:** 3-4 giorni
* **FASE 5:** 3-4 giorni
* **FASE 6:** 4-5 giorni
* **FASE 7:** (Continuo) 3-4 giorni dedicati specificamente

**Tempo totale stimato: 21-29 giorni lavorativi** (L'aumento rispetto alla stima precedente è dovuto alla maggiore granularità e complessità intrinseca della separazione in MCP, ma il risultato sarà più robusto e scalabile).
