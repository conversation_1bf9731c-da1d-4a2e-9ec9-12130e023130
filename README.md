# 🚀 App Roberto - Sistema di Analisi Dati Aziendali Intelligente

**Sistema enterprise-grade di analisi dati aziendali** che utilizza intelligenza artificiale avanzata, database persistente Supabase e riconoscimento intelligente per processare e analizzare file CSV contenenti dati di attività, timbrature, sessioni remote e altri dati aziendali.

## 🎯 **STATO ATTUALE DEL PROGETTO**

### ✅ **COMPLETATO CON SUCCESSO:**
- 🔧 **Timeout MCP risolto** - Connessione stabile porta 8000
- 🧪 **Test automatici** - 100% superati
- 🧠 **Sistema riconoscimento intelligente** - 7 fasi complete
- 🤖 **Chat AI** - Funzionante con OpenRouter
- 📊 **Dashboard intelligente** - Operativa con grafici

### 🔄 **IN CORSO DI REVISIONE:**
- 🗄️ **Database Supabase** - Correzione API keys
- 📁 **Persistenza file** - Eliminazione pulizia automatica
- 🎨 **Tema scuro** - Implementazione completa
- ⚙️ **Configurazione guidata** - Setup intelligente

📋 **Piano dettagliato**: Vedi `PIANO_REVISIONE_OPERATIVITA.md`

## Funzionalità Principali

### 🚀 **FASE 1 REFACTORING COMPLETATA** ✅

- **Parser CSV Robusto**: Gestione automatica errori di tokenizzazione con 6 strategie di parsing
- **Real File Analyzer**: Riconoscimento automatico tipi file con confidenza 67-100%
- **Server MCP (Model Context Protocol)**: Elaborazione distribuita con integrazione LLM
- **Integrazione AI**: Query naturali, data cleaning automatico, correzioni intelligenti
- Importazione dati da file CSV ed Excel con gestione errori avanzata
- Elaborazione e standardizzazione automatica dei dati
- Visualizzazione di dashboard con KPI e grafici interattivi
- Generazione di report specifici
- Esportazione dei dati in vari formati

## Requisiti di Sistema

- Python 3.8+
- Flask 2.3.3+
- Pandas 2.1.0+
- Plotly 5.16.1+
- Altri requisiti specificati in `requirements.txt`

## Installazione

1. Clona il repository:

   ```bash
   git clone https://github.com/Fiore0312/app-roberto.git
   cd app-roberto
   ```

2. Crea e attiva un ambiente virtuale:

   ```bash
   python -m venv venv
   venv\Scripts\activate  # Windows
   source venv/bin/activate  # Linux/Mac
   ```

3. Installa le dipendenze:

   ```bash
   pip install -r requirements.txt
   ```

4. Avvia l'applicazione:

   ```bash
   python app.py
   ```

5. Avvia l'applicazione completa (con server MCP):

   ```bash
   # Avvio automatico completo (raccomandato)
   avvio_completo.bat
   ```

   Oppure avvio manuale:

   ```bash
   # Solo app principale
   python app.py
   ```

6. Accedi all'applicazione:

   ```
   http://127.0.0.1:5001
   ```

   **Nota**: L'applicazione ora utilizza la porta **5001** come principale per evitare conflitti su Windows.

## 🤖 **Server MCP (Model Context Protocol)**

Il server MCP fornisce funzionalità avanzate di elaborazione dati con integrazione AI.

### Avvio Server MCP

```bash
# Avvio automatico (incluso in avvio_completo.bat)
cd mcp_server
python main.py
```

Il server MCP sarà disponibile su `http://localhost:8000`

### API Endpoints Principali

#### 📊 **Elaborazione Dati**
- `POST /upload-file/` - Caricamento file
- `POST /process-file/` - Elaborazione file con mappatura colonne automatica
- `GET /file-summary/{file_id}` - Riepilogo statistiche elaborate

#### 🤖 **Integrazione LLM**
- `POST /llm-query/` - Query AI sui dati con contesto automatico
- `POST /identify-issues/{file_id}` - Identificazione automatica problemi nei dati
- `POST /suggest-corrections/{file_id}` - Suggerimenti correzioni intelligenti

#### 🔧 **Utilità**
- `GET /health` - Stato server
- `GET /processed-data/{file_id}` - Dati elaborati JSON-compatibili
- `GET /export/{file_id}/{format}` - Esportazione (csv, excel, json)

### Esempio Utilizzo LLM

```python
import requests

# Query AI sui dati
response = requests.post("http://localhost:8000/llm-query/", json={
    "file_id": "calendario_123",
    "query": "Analizza i pattern degli eventi e dimmi quali sono i giorni più occupati",
    "model_id": "anthropic/claude-3-haiku"
})

print(response.json()["response"])
```

## 📊 **Parser CSV Robusto**

Il sistema include un parser CSV robusto che gestisce automaticamente file problematici:

### Funzionalità
- **6 Strategie di Parsing**: Prova automaticamente diversi separatori e encoding
- **Gestione Errori**: Risolve errori come "Expected 1 fields in line 4, saw 2"
- **Rilevamento Automatico**: Identifica il tipo di file dal nome (calendario, teamviewer, ecc.)
- **Fallback Intelligente**: Se il parser robusto fallisce, usa quello standard

### Test di Performance

```bash
# Esegui test confronto MCP vs Locale
test_mcp.bat
```

#### Risultati Test (File Calendario 74 eventi)

| Metodo | Velocità | Eventi | Colonne | Funzionalità |
|--------|----------|--------|---------|--------------|
| **Locale** | 0.15s | 74 ✅ | 31 | Parser robusto, veloce |
| **MCP** | 2-3s | 74 ✅ | 26 | AI, cache, API RESTful |

#### Vantaggi MCP
- 🤖 **Analisi AI**: Query naturali sui dati
- 🔧 **Data Cleaning**: Identificazione e correzione automatica problemi
- 📈 **Statistiche Avanzate**: Partecipanti unici, eventi sovrapposti
- 🌐 **API RESTful**: Integrazione con sistemi esterni

#### Vantaggi Locale
- ⚡ **Velocità**: 10x più veloce per elaborazione base
- 🔧 **Parser Robusto**: Gestione errori CSV avanzata
- 💾 **Memoria**: Elaborazione in-process

## Struttura del Progetto

```text
app-roberto/
├── app.py                  # File principale dell'applicazione Flask
├── config.py               # Configurazione dell'applicazione
├── data_processor.py       # Modulo per l'elaborazione dei dati
├── calendar_processor.py   # Parser CSV robusto per file calendario
├── requirements.txt        # Dipendenze del progetto
├── avvio_completo.bat     # Script avvio automatico completo
├── test_mcp_comparison.py  # Test confronto MCP vs locale
├── debug_mcp_server.py     # Debug server MCP
├── mcp_server/            # Server MCP (Model Context Protocol)
│   ├── main.py            # Server FastAPI principale
│   ├── activity_processor.py  # Processore attività con statistiche AI
│   ├── column_mapper.py   # Mappatura colonne automatica
│   └── requirements.txt   # Dipendenze server MCP
├── static/                # File statici (CSS, JS, immagini)
│   ├── css/
│   ├── js/
│   └── images/
├── templates/             # Template HTML
│   ├── base.html
│   ├── index.html
│   ├── preview.html
│   └── dashboard.html
├── uploads/               # Cartella per i file caricati
└── clean_env/            # Ambiente virtuale pulito
```

## Configurazioni Sistema

L'applicazione ora include un sistema di configurazione persistente che permette di:

- **Configurare costi dipendenti**: Impostare tariffe orarie per ogni dipendente con opzioni IVA
- **Gestire flotta veicoli**: Configurare veicoli aziendali con consumi e costi
- **Impostazioni IVA**: Configurare aliquote IVA e valute
- **Persistenza dati**: Tutte le configurazioni vengono salvate automaticamente

## Fasi di Sviluppo

### 🚀 Fase 1: REFACTORING COMPLETATO ✅

#### **Sistema di Importazione Avanzato**
- Creazione struttura base del progetto Flask ✅
- Implementazione interfaccia di upload con drag-and-drop ✅
- **Parser CSV Robusto**: 6 strategie per gestire errori tokenizzazione ✅
- **Real File Analyzer**: Riconoscimento automatico tipi file (67-100% confidenza) ✅
- Gestione file CSV ed Excel con fallback intelligente ✅
- Validazione avanzata dei file caricati ✅
- Anteprima dei dati importati ✅

#### **Server MCP (Model Context Protocol)**
- Server FastAPI distribuito per elaborazione dati ✅
- Mappatura automatica colonne iCalendar (SUMMARY, DTSTART, DTEND) ✅
- API RESTful con endpoints documentati ✅
- Integrazione LLM completa (OpenRouter) ✅
- Cache intelligente e persistenza dati ✅

#### **Integrazione AI e Automazione**
- Query naturali sui dati con LLM ✅
- Identificazione automatica problemi nei dati ✅
- Suggerimenti correzioni intelligenti ✅
- Data cleaning automatico ✅
- Statistiche avanzate (partecipanti unici, eventi sovrapposti) ✅

#### **Test e Validazione**
- Test confronto MCP vs elaborazione locale ✅
- Debug tools per server MCP ✅
- Gestione errori "Expected 1 fields in line 4, saw 2" ✅
- Performance testing completato ✅

### Fase 2: Elaborazione e Standardizzazione Dati

- Sviluppo parser per identificazione automatica delle intestazioni
- Implementazione gestione formato data italiano (GG/MM/AAAA)
- Gestione separatori decimali italiani (virgola)
- Sistema di mappatura campi per standardizzazione dati

### Fase 3: Analisi Dati e Visualizzazione

- Creazione dashboard con KPI principali
- Implementazione grafici temporali interattivi
- Sviluppo report specifici (presenze, produttività, correlazioni)
- Filtri dinamici per date, dipendenti e tipologie di attività

### Fase 4: Esportazione e Funzionalità Avanzate

- Funzionalità di esportazione in Excel (.xlsx)
- Esportazione in PDF con layout ottimizzato
- Esportazione in CSV per ulteriori elaborazioni
- Ottimizzazione performance e usabilità

## Licenza

Tutti i diritti riservati © 2025 Bait Service Srl
