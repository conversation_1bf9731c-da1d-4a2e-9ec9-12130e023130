@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

echo ========================================
echo    APP-ROBERTO - AVVIO PULITO
echo ========================================
echo.

:: Verifica se siamo nella directory corretta
if not exist "app.py" (
    echo ERRORE: File app.py non trovato nella directory corrente.
    echo Assicurati di essere nella directory del progetto APP-ROBERTO.
    pause
    exit /b 1
)

:: Verifica se l'ambiente virtuale clean_env esiste
if not exist "clean_env\Scripts\activate.bat" (
    echo ERRORE: Ambiente virtuale clean_env non trovato.
    echo Eseguire prima create_clean_env.bat per creare l'ambiente.
    pause
    exit /b 1
)

echo [1/3] Attivazione ambiente virtuale clean_env...
call clean_env\Scripts\activate.bat
if errorlevel 1 (
    echo ERRORE: Impossibile attivare l'ambiente virtuale.
    pause
    exit /b 1
)

echo [2/3] Verifica dipendenze critiche...
python -c "import flask, pandas, plotly, fastapi" 2>nul
if errorlevel 1 (
    echo ERRORE: Dipendenze mancanti. Reinstallazione in corso...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ERRORE: Installazione dipendenze fallita.
        pause
        exit /b 1
    )
)

echo [3/3] Pulizia file temporanei (SICURA - preserva file utente)...
REM Pulizia solo file temporanei di sistema - NON tocca file utente
if exist "uploads\temp" rmdir /s /q "uploads\temp" 2>nul
if exist "__pycache__" rmdir /s /q "__pycache__" 2>nul
if exist ".pytest_cache" rmdir /s /q ".pytest_cache" 2>nul
REM NOTA: File in uploads/ sono preservati per persistenza dati

echo.
echo ========================================
echo  Applicazione in avvio su http://127.0.0.1:5000
echo  Premere Ctrl+C per arrestare l'applicazione
echo ========================================
echo.

:: Avvia l'applicazione
python app.py

echo.
echo ========================================
echo  Applicazione arrestata
echo ========================================
pause
