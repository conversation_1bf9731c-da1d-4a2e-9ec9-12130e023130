#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test per il modulo mcp_client.py.
"""

import os
import pytest
import requests
from unittest.mock import patch, MagicMock

from mcp_client import MCPClient

class TestMCPClient:
    """Test per la classe MCPClient."""
    
    def setup_method(self):
        """Setup per i test."""
        # Crea un client MCP con un URL fittizio
        self.base_url = "http://test-mcp-server:8000"
        self.client = MCPClient(base_url=self.base_url, max_retries=0, timeout=1)
    
    @patch('requests.Session.get')
    def test_init_success(self, mock_get):
        """Test per il costruttore con connessione riuscita."""
        # Configura il mock per simulare una risposta di successo
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        # Crea un nuovo client
        client = MCPClient(base_url=self.base_url)
        
        # Verifica che il client sia stato inizializzato correttamente
        assert client.base_url == self.base_url
        assert client.is_available == True
        assert isinstance(client.session, requests.Session)
    
    @patch('requests.Session.get')
    def test_init_failure(self, mock_get):
        """Test per il costruttore con connessione fallita."""
        # Configura il mock per simulare una risposta di errore
        mock_get.side_effect = requests.exceptions.ConnectionError("Connection refused")
        
        # Crea un nuovo client
        client = MCPClient(base_url=self.base_url)
        
        # Verifica che il client sia stato inizializzato correttamente ma con is_available = False
        assert client.base_url == self.base_url
        assert client.is_available == False
        assert isinstance(client.session, requests.Session)
    
    @patch('requests.Session.post')
    def test_upload_file_success(self, mock_post):
        """Test per il metodo upload_file con successo."""
        # Configura il mock per simulare una risposta di successo
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"file_id": "test_file_id"}
        mock_post.return_value = mock_response
        
        # Imposta il client come disponibile
        self.client.is_available = True
        
        # Crea un file temporaneo per il test
        test_file_path = "tests/test_file.csv"
        os.makedirs(os.path.dirname(test_file_path), exist_ok=True)
        with open(test_file_path, "w") as f:
            f.write("test,data\n1,2\n")
        
        try:
            # Carica il file
            result = self.client.upload_file("test_file_id", test_file_path, "attivita")
            
            # Verifica che il risultato sia corretto
            assert result == {"file_id": "test_file_id"}
            
            # Verifica che la richiesta sia stata effettuata correttamente
            mock_post.assert_called_once()
            args, kwargs = mock_post.call_args
            assert args[0] == f"{self.base_url}/upload-file/"
            assert "files" in kwargs
            assert "data" in kwargs
            assert kwargs["data"]["file_id"] == "test_file_id"
            assert kwargs["data"]["file_type"] == "attivita"
        finally:
            # Pulisci il file temporaneo
            if os.path.exists(test_file_path):
                os.remove(test_file_path)
    
    @patch('requests.Session.post')
    def test_upload_file_server_unavailable(self, mock_post):
        """Test per il metodo upload_file con server non disponibile."""
        # Imposta il client come non disponibile
        self.client.is_available = False
        
        # Carica il file
        result = self.client.upload_file("test_file_id", "test_file.csv", "attivita")
        
        # Verifica che il risultato sia un errore
        assert "error" in result
        assert result["error"] == "Server MCP non disponibile"
        assert result["use_local"] == True
        
        # Verifica che non sia stata effettuata alcuna richiesta
        mock_post.assert_not_called()
    
    @patch('requests.Session.post')
    def test_upload_file_not_found(self, mock_post):
        """Test per il metodo upload_file con file non trovato."""
        # Imposta il client come disponibile
        self.client.is_available = True
        
        # Carica un file che non esiste
        result = self.client.upload_file("test_file_id", "file_non_esistente.csv", "attivita")
        
        # Verifica che il risultato sia un errore
        assert "error" in result
        assert "File non trovato" in result["error"]
        
        # Verifica che non sia stata effettuata alcuna richiesta
        mock_post.assert_not_called()
    
    @patch('requests.Session.post')
    def test_upload_file_server_error(self, mock_post):
        """Test per il metodo upload_file con errore del server."""
        # Configura il mock per simulare una risposta di errore
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        mock_post.return_value = mock_response
        
        # Imposta il client come disponibile
        self.client.is_available = True
        
        # Crea un file temporaneo per il test
        test_file_path = "tests/test_file.csv"
        os.makedirs(os.path.dirname(test_file_path), exist_ok=True)
        with open(test_file_path, "w") as f:
            f.write("test,data\n1,2\n")
        
        try:
            # Carica il file
            result = self.client.upload_file("test_file_id", test_file_path, "attivita")
            
            # Verifica che il risultato sia un errore
            assert "error" in result
            assert "500" in result["error"]
            assert result["use_local"] == True
        finally:
            # Pulisci il file temporaneo
            if os.path.exists(test_file_path):
                os.remove(test_file_path)
    
    @patch('requests.Session.post')
    def test_process_file_success(self, mock_post):
        """Test per il metodo process_file con successo."""
        # Configura il mock per simulare risposte di successo
        def mock_post_side_effect(url, **kwargs):
            mock_response = MagicMock()
            mock_response.status_code = 200
            
            if "/upload-file/" in url:
                mock_response.json.return_value = {"file_id": "test_file_id"}
            elif "/process-file/" in url:
                mock_response.json.return_value = {"result": "success"}
            
            return mock_response
        
        mock_post.side_effect = mock_post_side_effect
        
        # Imposta il client come disponibile
        self.client.is_available = True
        
        # Crea un file temporaneo per il test
        test_file_path = "tests/test_file.csv"
        os.makedirs(os.path.dirname(test_file_path), exist_ok=True)
        with open(test_file_path, "w") as f:
            f.write("test,data\n1,2\n")
        
        try:
            # Elabora il file
            result = self.client.process_file("test_file_id", test_file_path, "attivita")
            
            # Verifica che il risultato sia corretto
            assert result == {"result": "success"}
            
            # Verifica che siano state effettuate due richieste
            assert mock_post.call_count == 2
        finally:
            # Pulisci il file temporaneo
            if os.path.exists(test_file_path):
                os.remove(test_file_path)
    
    @patch('requests.Session.get')
    def test_get_file_summary_success(self, mock_get):
        """Test per il metodo get_file_summary con successo."""
        # Configura il mock per simulare una risposta di successo
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"summary": "test summary"}
        mock_get.return_value = mock_response
        
        # Imposta il client come disponibile
        self.client.is_available = True
        
        # Ottieni il riepilogo del file
        result = self.client.get_file_summary("test_file_id")
        
        # Verifica che il risultato sia corretto
        assert result == {"summary": "test summary"}
        
        # Verifica che la richiesta sia stata effettuata correttamente
        mock_get.assert_called_once_with(
            f"{self.base_url}/file-summary/test_file_id",
            timeout=self.client.timeout
        )
    
    @patch('requests.Session.get')
    def test_get_file_summary_server_unavailable(self, mock_get):
        """Test per il metodo get_file_summary con server non disponibile."""
        # Imposta il client come non disponibile
        self.client.is_available = False
        
        # Ottieni il riepilogo del file
        result = self.client.get_file_summary("test_file_id")
        
        # Verifica che il risultato sia un errore
        assert "error" in result
        assert result["error"] == "Server MCP non disponibile"
        assert result["use_local"] == True
        
        # Verifica che non sia stata effettuata alcuna richiesta
        mock_get.assert_not_called()
    
    @patch('requests.Session.post')
    def test_process_llm_query_success(self, mock_post):
        """Test per il metodo process_llm_query con successo."""
        # Configura il mock per simulare una risposta di successo
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"response": "test response"}
        mock_post.return_value = mock_response
        
        # Imposta il client come disponibile
        self.client.is_available = True
        
        # Elabora la query LLM
        result = self.client.process_llm_query("test_file_id", "test query")
        
        # Verifica che il risultato sia corretto
        assert result == {"response": "test response"}
        
        # Verifica che la richiesta sia stata effettuata correttamente
        mock_post.assert_called_once()
        args, kwargs = mock_post.call_args
        assert args[0] == f"{self.base_url}/llm-query/"
        assert kwargs["json"]["file_id"] == "test_file_id"
        assert kwargs["json"]["query"] == "test query"
        assert kwargs["json"]["model_id"] == "anthropic/claude-3-haiku"
