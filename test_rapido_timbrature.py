#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test rapido per verificare che la correzione timbrature funzioni.
"""

import pandas as pd
from enhanced_file_detector import EnhancedFileDetector

def test_rapido():
    """Test rapido della correzione timbrature."""
    
    print("⚡ TEST RAPIDO CORREZIONE TIMBRATURE")
    print("=" * 40)
    
    # Dati timbrature
    data = {
        'Data': ['2025-05-01', '2025-05-02'],
        'Dipendente': ['<PERSON>', '<PERSON>'],
        'Entrata': ['08:00', '08:15'],
        'Uscita': ['17:00', '17:30'],
        'Ore Lavorate': [8.0, 8.25]
    }
    df = pd.DataFrame(data)
    
    detector = EnhancedFileDetector()
    
    # Test caso problematico
    filename = "apprilevazionepresenze-timbrature-totali-base-2025-05-01-2025-05-31_2.xlsx"
    
    print(f"📁 File: {filename}")
    print(f"📊 Colonne: {df.columns.tolist()}")
    print()
    
    detected_type, confidence, scores = detector.detect_file_type(df, filename)
    
    print(f"🎯 Risultato:")
    print(f"   Tipo: {detected_type}")
    print(f"   Confidenza: {confidence:.3f}")
    print()
    
    if detected_type == "timbrature":
        print("✅ SUCCESSO! File timbrature riconosciuto correttamente")
        print("🎉 PROBLEMA RISOLTO!")
        print()
        print("💡 Il file 'apprilevazionepresenze-timbrature-*' ora viene")
        print("   classificato come 'timbrature' invece di 'attivita'")
        return True
    else:
        print(f"❌ ERRORE: File classificato come '{detected_type}' invece di 'timbrature'")
        return False

if __name__ == "__main__":
    success = test_rapido()
    exit(0 if success else 1)
