/**
 * Script per la gestione dei widget della dashboard
 * Versione: 1.0.0
 */

// Stampa la versione nella console per verificare il caricamento
console.log('widgets.js versione 1.0.0 caricato');

// Classe base per i widget
class Widget {
    constructor(id, options = {}) {
        this.id = id;
        this.element = document.getElementById(id);
        this.options = Object.assign({
            title: 'Widget',
            size: 'sm',
            type: 'default',
            color: 'primary',
            refreshInterval: 0,
            actions: []
        }, options);
        
        this.isLoading = false;
        this.data = null;
        this.interval = null;
        
        // Inizializza il widget
        this.init();
    }
    
    // Inizializza il widget
    init() {
        if (!this.element) {
            console.error(`Widget element with ID "${this.id}" not found`);
            return;
        }
        
        // Aggiungi le classi base
        this.element.classList.add('widget', `widget-${this.options.size}`, `widget-${this.options.type}`, `widget-${this.options.color}`);
        
        // Crea la struttura base del widget
        this.createStructure();
        
        // Aggiungi gli event listeners
        this.addEventListeners();
        
        // Carica i dati iniziali
        this.refresh();
        
        // Imposta l'intervallo di aggiornamento se specificato
        if (this.options.refreshInterval > 0) {
            this.interval = setInterval(() => this.refresh(), this.options.refreshInterval);
        }
    }
    
    // Crea la struttura base del widget
    createStructure() {
        // Crea l'header
        const header = document.createElement('div');
        header.className = 'widget-header';
        
        // Aggiungi il titolo
        const title = document.createElement('h3');
        title.className = 'widget-title';
        title.textContent = this.options.title;
        header.appendChild(title);
        
        // Aggiungi le azioni
        if (this.options.actions.length > 0) {
            const actions = document.createElement('div');
            actions.className = 'widget-actions';
            
            this.options.actions.forEach(action => {
                const button = document.createElement('button');
                button.className = 'widget-action';
                button.setAttribute('data-action', action.name);
                button.setAttribute('title', action.title || action.name);
                
                const icon = document.createElement('i');
                icon.className = action.icon || 'fas fa-cog';
                button.appendChild(icon);
                
                actions.appendChild(button);
            });
            
            header.appendChild(actions);
        }
        
        // Crea il body
        const body = document.createElement('div');
        body.className = 'widget-body';
        
        // Crea il footer se necessario
        let footer = null;
        if (this.options.footer) {
            footer = document.createElement('div');
            footer.className = 'widget-footer';
            footer.textContent = this.options.footer;
        }
        
        // Aggiungi gli elementi al widget
        this.element.innerHTML = '';
        this.element.appendChild(header);
        this.element.appendChild(body);
        if (footer) {
            this.element.appendChild(footer);
        }
        
        // Salva i riferimenti agli elementi
        this.header = header;
        this.body = body;
        this.footer = footer;
    }
    
    // Aggiungi gli event listeners
    addEventListeners() {
        // Aggiungi gli event listeners per le azioni
        const actions = this.element.querySelectorAll('.widget-action');
        actions.forEach(action => {
            action.addEventListener('click', (event) => {
                const actionName = action.getAttribute('data-action');
                this.handleAction(actionName, event);
            });
        });
    }
    
    // Gestisci le azioni
    handleAction(actionName, event) {
        // Trova l'azione corrispondente
        const action = this.options.actions.find(a => a.name === actionName);
        
        if (action && typeof action.handler === 'function') {
            // Esegui l'handler dell'azione
            action.handler.call(this, event);
        } else {
            console.warn(`No handler found for action "${actionName}"`);
        }
    }
    
    // Aggiorna il widget
    refresh() {
        this.setLoading(true);
        
        // Implementazione di base, da sovrascrivere nelle sottoclassi
        this.loadData()
            .then(data => {
                this.data = data;
                this.render();
                this.setLoading(false);
            })
            .catch(error => {
                console.error(`Error loading data for widget "${this.id}":`, error);
                this.showError(error);
                this.setLoading(false);
            });
    }
    
    // Carica i dati
    async loadData() {
        // Implementazione di base, da sovrascrivere nelle sottoclassi
        return null;
    }
    
    // Renderizza il widget
    render() {
        // Implementazione di base, da sovrascrivere nelle sottoclassi
        this.body.textContent = 'Widget content';
    }
    
    // Imposta lo stato di caricamento
    setLoading(isLoading) {
        this.isLoading = isLoading;
        
        if (isLoading) {
            this.element.classList.add('widget-loading');
        } else {
            this.element.classList.remove('widget-loading');
        }
    }
    
    // Mostra un errore
    showError(error) {
        this.body.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                ${error.message || 'Si è verificato un errore'}
            </div>
        `;
    }
    
    // Distruggi il widget
    destroy() {
        // Rimuovi l'intervallo di aggiornamento
        if (this.interval) {
            clearInterval(this.interval);
        }
        
        // Rimuovi gli event listeners
        const actions = this.element.querySelectorAll('.widget-action');
        actions.forEach(action => {
            const actionName = action.getAttribute('data-action');
            const actionObj = this.options.actions.find(a => a.name === actionName);
            
            if (actionObj && typeof actionObj.handler === 'function') {
                action.removeEventListener('click', actionObj.handler);
            }
        });
        
        // Rimuovi il contenuto
        this.element.innerHTML = '';
        this.element.className = '';
    }
}

// Widget per i KPI
class KpiWidget extends Widget {
    constructor(id, options = {}) {
        // Imposta le opzioni predefinite per i KPI
        const kpiOptions = Object.assign({
            type: 'kpi',
            value: 0,
            label: 'KPI',
            icon: 'fas fa-chart-line',
            format: value => value,
            target: null,
            suffix: '',
            prefix: ''
        }, options);
        
        super(id, kpiOptions);
    }
    
    // Renderizza il widget KPI
    render() {
        const value = this.options.format(this.data || this.options.value);
        const formattedValue = `${this.options.prefix}${value}${this.options.suffix}`;
        
        // Crea l'icona
        const iconHtml = this.options.icon ? `
            <div class="widget-kpi-icon">
                <i class="${this.options.icon}"></i>
            </div>
        ` : '';
        
        // Crea la barra di progresso se è specificato un target
        let progressHtml = '';
        if (this.options.target !== null) {
            const percentage = Math.min(100, Math.max(0, (value / this.options.target) * 100));
            const formattedPercentage = Math.round(percentage);
            
            progressHtml = `
                <div class="widget-progress">
                    <div class="widget-progress-label">
                        <span>Progresso</span>
                        <span>${formattedPercentage}%</span>
                    </div>
                    <div class="widget-progress-bar">
                        <div class="widget-progress-value bg-${this.options.color}" style="width: ${percentage}%"></div>
                    </div>
                    <div class="text-muted small mt-1">
                        Obiettivo: ${this.options.target}${this.options.suffix}
                    </div>
                </div>
            `;
        }
        
        // Aggiorna il contenuto
        this.body.innerHTML = `
            ${iconHtml}
            <div class="widget-kpi-value">${formattedValue}</div>
            <div class="widget-kpi-label">${this.options.label}</div>
            ${progressHtml}
        `;
    }
}

// Widget per i grafici
class ChartWidget extends Widget {
    constructor(id, options = {}) {
        // Imposta le opzioni predefinite per i grafici
        const chartOptions = Object.assign({
            type: 'chart',
            chartType: 'bar',
            chartOptions: {},
            dataUrl: null
        }, options);
        
        super(id, chartOptions);
        
        // Riferimento al grafico
        this.chart = null;
    }
    
    // Carica i dati per il grafico
    async loadData() {
        if (!this.options.dataUrl) {
            return null;
        }
        
        try {
            const response = await fetch(this.options.dataUrl);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error(`Error loading chart data for widget "${this.id}":`, error);
            throw error;
        }
    }
    
    // Renderizza il grafico
    render() {
        // Verifica che Plotly sia caricato
        if (typeof Plotly === 'undefined') {
            this.showError(new Error('Plotly non è caricato'));
            return;
        }
        
        // Verifica che ci siano dati
        if (!this.data) {
            this.showError(new Error('Nessun dato disponibile'));
            return;
        }
        
        // Crea il grafico
        try {
            Plotly.newPlot(
                this.body,
                this.data.data || [],
                this.data.layout || {},
                this.options.chartOptions
            );
        } catch (error) {
            console.error(`Error rendering chart for widget "${this.id}":`, error);
            this.showError(error);
        }
    }
    
    // Distruggi il widget
    destroy() {
        // Distruggi il grafico
        if (this.chart) {
            Plotly.purge(this.body);
        }
        
        // Chiama il metodo destroy della classe padre
        super.destroy();
    }
}

// Esporta le classi
window.Widget = Widget;
window.KpiWidget = KpiWidget;
window.ChartWidget = ChartWidget;
