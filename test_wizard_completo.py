#!/usr/bin/env python3
"""
Test completo del wizard con simulazione sessione
"""

import requests
import json
import time

def test_wizard_completo():
    """Test completo del flusso wizard"""
    
    base_url = "http://localhost:5000"
    
    print("🧪 TEST COMPLETO WIZARD")
    print("=" * 50)
    
    # Test 1: Verifica server attivo
    print("\n1. 🔍 Verifica server...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ Server attivo")
        else:
            print(f"⚠️ Server risponde ma con status: {response.status_code}")
    except:
        print("❌ Server non raggiungibile")
        return
    
    # Test 2: Verifica wizard page
    print("\n2. 🧙‍♂️ Verifica wizard page...")
    try:
        response = requests.get(f"{base_url}/setup-wizard", timeout=5)
        if response.status_code == 200:
            print("✅ Wizard page accessibile")
        else:
            print(f"❌ Wizard page errore: {response.status_code}")
    except Exception as e:
        print(f"❌ Errore wizard page: {str(e)}")
    
    # Test 3: Simula upload file
    print("\n3. 📁 Simula upload file...")
    try:
        # Crea file di test
        test_data = "Dipendente,Data,Ore\nMario Rossi,2025-05-01,8\nLuigi Verdi,2025-05-01,7.5"
        
        files = {
            'file': ('test.csv', test_data, 'text/csv')
        }
        data = {
            'wizard_mode': 'true'
        }
        
        response = requests.post(
            f"{base_url}/upload_file",
            files=files,
            data=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Upload completato")
            print(f"   File type: {result.get('file_type', 'N/A')}")
            print(f"   Preview: {len(result.get('preview', []))} righe")
        else:
            print(f"❌ Upload fallito: {response.status_code}")
            print(f"   Risposta: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Errore upload: {str(e)}")
    
    # Test 4: Test endpoint analisi
    print("\n4. 🔬 Test endpoint analisi...")
    try:
        test_data = {
            "analysis_type": "wizard_setup",
            "files": ["test.csv"]
        }
        
        response = requests.post(
            f"{base_url}/api/intelligent-system/analyze",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Analisi completata")
            
            if 'data' in result:
                data = result['data']
                print(f"   Dipendenti: {len(data.get('employees', []))}")
                print(f"   Veicoli: {len(data.get('vehicles', []))}")
                print(f"   Analisi: {len(data.get('suggested_analysis', []))}")
                print(f"   Automazioni: {len(data.get('suggested_automations', []))}")
                
                if data.get('employees'):
                    print(f"   Lista dipendenti: {data['employees']}")
            else:
                print(f"   Formato risposta: {list(result.keys())}")
                
        else:
            print(f"❌ Analisi fallita: {response.status_code}")
            print(f"   Risposta: {response.text}")
            
    except Exception as e:
        print(f"❌ Errore analisi: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🎯 TEST COMPLETATO")

if __name__ == "__main__":
    test_wizard_completo()
