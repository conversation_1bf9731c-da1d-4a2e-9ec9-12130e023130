#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Dashboard di stato del sistema per app-roberto.
Monitora lo stato di tutte le integrazioni e componenti.
"""

import os
import sys
import logging
from typing import Dict, Any, List
from datetime import datetime
import json

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SystemStatusDashboard:
    """
    Dashboard per monitorare lo stato del sistema e delle integrazioni.
    """

    def __init__(self):
        """
        Inizializza la dashboard di stato.
        """
        self.status_data = {}
        self.last_update = None

    def check_all_systems(self) -> Dict[str, Any]:
        """
        Verifica lo stato di tutti i sistemi.

        Returns:
            Dict: Stato completo del sistema
        """
        logger.info("🔍 Verifica stato di tutti i sistemi...")

        self.status_data = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "unknown",
            "components": {},
            "integrations": {},
            "performance": {},
            "recommendations": []
        }

        # Verifica componenti base
        self._check_base_components()

        # Verifica integrazioni
        self._check_integrations()

        # Verifica performance
        self._check_performance()

        # Calcola stato generale
        self._calculate_overall_status()

        # Genera raccomandazioni
        self._generate_recommendations()

        self.last_update = datetime.now()

        return self.status_data

    def _check_base_components(self):
        """
        Verifica i componenti base del sistema.
        """
        components = {}

        # Python e versione
        try:
            python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
            components["python"] = {
                "status": "ok",
                "version": python_version,
                "details": "Python runtime attivo"
            }
        except Exception as e:
            components["python"] = {
                "status": "error",
                "error": str(e)
            }

        # Flask
        try:
            import flask
            components["flask"] = {
                "status": "ok",
                "version": flask.__version__,
                "details": "Flask framework disponibile"
            }
        except ImportError:
            components["flask"] = {
                "status": "error",
                "error": "Flask non installato"
            }

        # FastAPI
        try:
            import fastapi
            components["fastapi"] = {
                "status": "ok",
                "version": fastapi.__version__,
                "details": "FastAPI framework disponibile"
            }
        except ImportError:
            components["fastapi"] = {
                "status": "error",
                "error": "FastAPI non installato"
            }

        # Pandas
        try:
            import pandas as pd
            components["pandas"] = {
                "status": "ok",
                "version": pd.__version__,
                "details": "Pandas per elaborazione dati disponibile"
            }
        except ImportError:
            components["pandas"] = {
                "status": "error",
                "error": "Pandas non installato"
            }

        # Plotly
        try:
            import plotly
            components["plotly"] = {
                "status": "ok",
                "version": plotly.__version__,
                "details": "Plotly per grafici disponibile"
            }
        except ImportError:
            components["plotly"] = {
                "status": "error",
                "error": "Plotly non installato"
            }

        # File principali
        main_files = {
            "app.py": "Applicazione principale Flask",
            "mcp_server/main.py": "Server MCP",
            "real_file_analyzer.py": "Analizzatore file reali",
            "config_manager.py": "Gestore configurazioni"
        }

        for file_path, description in main_files.items():
            if os.path.exists(file_path):
                components[f"file_{file_path.replace('/', '_').replace('.py', '')}"] = {
                    "status": "ok",
                    "details": f"{description} presente"
                }
            else:
                components[f"file_{file_path.replace('/', '_').replace('.py', '')}"] = {
                    "status": "error",
                    "error": f"{description} mancante"
                }

        self.status_data["components"] = components

    def _check_integrations(self):
        """
        Verifica lo stato delle integrazioni esterne.
        """
        integrations = {}

        # Supabase
        try:
            from supabase_integration import supabase_manager
            if supabase_manager.is_connected:
                connection_test = supabase_manager.test_connection()
                integrations["supabase"] = {
                    "status": "ok" if connection_test else "warning",
                    "connected": supabase_manager.is_connected,
                    "test_passed": connection_test,
                    "details": "Supabase database connesso" if connection_test else "Connesso ma test fallito"
                }
            else:
                integrations["supabase"] = {
                    "status": "warning",
                    "connected": False,
                    "details": "Supabase non connesso (verifica variabili d'ambiente)"
                }
        except ImportError:
            integrations["supabase"] = {
                "status": "error",
                "error": "Modulo Supabase non disponibile"
            }
        except Exception as e:
            integrations["supabase"] = {
                "status": "error",
                "error": str(e)
            }

        # MCP Server
        try:
            from mcp_client import MCPClient
            mcp_client = MCPClient()
            if mcp_client.is_available:  # is_available è un attributo, non un metodo
                integrations["mcp"] = {
                    "status": "ok",
                    "available": True,
                    "details": "Server MCP disponibile"
                }
            else:
                integrations["mcp"] = {
                    "status": "warning",
                    "available": False,
                    "details": "Server MCP non raggiungibile"
                }
        except ImportError:
            integrations["mcp"] = {
                "status": "error",
                "error": "MCP Client non disponibile"
            }
        except Exception as e:
            integrations["mcp"] = {
                "status": "error",
                "error": str(e)
            }

        # OpenRouter
        try:
            from openrouter_client import OpenRouterClient
            openrouter = OpenRouterClient()
            if openrouter.is_available:
                integrations["openrouter"] = {
                    "status": "ok",
                    "available": True,
                    "details": "OpenRouter API disponibile"
                }
            else:
                integrations["openrouter"] = {
                    "status": "warning",
                    "available": False,
                    "details": "OpenRouter API non disponibile (verifica chiave API)"
                }
        except ImportError:
            integrations["openrouter"] = {
                "status": "error",
                "error": "OpenRouter Client non disponibile"
            }
        except Exception as e:
            integrations["openrouter"] = {
                "status": "error",
                "error": str(e)
            }

        # Enhanced Config Manager
        try:
            from enhanced_config_manager import enhanced_config_manager
            system_info = enhanced_config_manager.get_system_info()
            integrations["enhanced_config"] = {
                "status": "ok",
                "details": "Enhanced Config Manager attivo",
                "system_info": system_info
            }
        except ImportError:
            integrations["enhanced_config"] = {
                "status": "error",
                "error": "Enhanced Config Manager non disponibile"
            }
        except Exception as e:
            integrations["enhanced_config"] = {
                "status": "error",
                "error": str(e)
            }

        self.status_data["integrations"] = integrations

    def _check_performance(self):
        """
        Verifica le metriche di performance del sistema.
        """
        performance = {}

        # Spazio disco
        try:
            import shutil
            total, used, free = shutil.disk_usage(".")
            performance["disk_space"] = {
                "total_gb": round(total / (1024**3), 2),
                "used_gb": round(used / (1024**3), 2),
                "free_gb": round(free / (1024**3), 2),
                "usage_percent": round((used / total) * 100, 1)
            }
        except Exception as e:
            performance["disk_space"] = {"error": str(e)}

        # Memoria (se psutil è disponibile)
        try:
            import psutil
            memory = psutil.virtual_memory()
            performance["memory"] = {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "usage_percent": memory.percent
            }
        except ImportError:
            performance["memory"] = {"info": "psutil non disponibile per monitoraggio memoria"}
        except Exception as e:
            performance["memory"] = {"error": str(e)}

        # File uploads directory
        uploads_dir = "uploads"
        if os.path.exists(uploads_dir):
            try:
                files = os.listdir(uploads_dir)
                total_size = sum(os.path.getsize(os.path.join(uploads_dir, f))
                               for f in files if os.path.isfile(os.path.join(uploads_dir, f)))
                performance["uploads"] = {
                    "files_count": len(files),
                    "total_size_mb": round(total_size / (1024**2), 2)
                }
            except Exception as e:
                performance["uploads"] = {"error": str(e)}
        else:
            performance["uploads"] = {"info": "Directory uploads non esistente"}

        self.status_data["performance"] = performance

    def _calculate_overall_status(self):
        """
        Calcola lo stato generale del sistema.
        """
        error_count = 0
        warning_count = 0
        ok_count = 0

        # Conta stati dei componenti
        for component in self.status_data["components"].values():
            status = component.get("status", "unknown")
            if status == "error":
                error_count += 1
            elif status == "warning":
                warning_count += 1
            elif status == "ok":
                ok_count += 1

        # Conta stati delle integrazioni
        for integration in self.status_data["integrations"].values():
            status = integration.get("status", "unknown")
            if status == "error":
                error_count += 1
            elif status == "warning":
                warning_count += 1
            elif status == "ok":
                ok_count += 1

        # Determina stato generale
        if error_count > 0:
            overall_status = "error"
        elif warning_count > 0:
            overall_status = "warning"
        else:
            overall_status = "ok"

        self.status_data["overall_status"] = overall_status
        self.status_data["status_summary"] = {
            "ok": ok_count,
            "warning": warning_count,
            "error": error_count,
            "total": ok_count + warning_count + error_count
        }

    def _generate_recommendations(self):
        """
        Genera raccomandazioni basate sullo stato del sistema.
        """
        recommendations = []

        # Raccomandazioni per componenti
        for name, component in self.status_data["components"].items():
            if component.get("status") == "error":
                if "flask" in name:
                    recommendations.append("Installare Flask: pip install flask")
                elif "fastapi" in name:
                    recommendations.append("Installare FastAPI: pip install fastapi")
                elif "pandas" in name:
                    recommendations.append("Installare Pandas: pip install pandas")
                elif "plotly" in name:
                    recommendations.append("Installare Plotly: pip install plotly")
                elif "file_" in name:
                    recommendations.append(f"Verificare la presenza del file: {name.replace('file_', '').replace('_', '/')}.py")

        # Raccomandazioni per integrazioni
        for name, integration in self.status_data["integrations"].items():
            if integration.get("status") == "error":
                if name == "supabase":
                    recommendations.append("Installare Supabase: pip install supabase")
                    recommendations.append("Configurare variabili d'ambiente SUPABASE_URL e SUPABASE_KEY")
                elif name == "openrouter":
                    recommendations.append("Configurare variabile d'ambiente OPENROUTER_API_KEY")
            elif integration.get("status") == "warning":
                if name == "supabase":
                    recommendations.append("Verificare connessione Supabase e variabili d'ambiente")
                elif name == "mcp":
                    recommendations.append("Avviare il server MCP: python mcp_server/main.py")
                elif name == "openrouter":
                    recommendations.append("Verificare chiave API OpenRouter")

        # Raccomandazioni per performance
        performance = self.status_data.get("performance", {})
        disk_space = performance.get("disk_space", {})
        if disk_space.get("usage_percent", 0) > 90:
            recommendations.append("Spazio disco quasi esaurito, liberare spazio")

        memory = performance.get("memory", {})
        if memory.get("usage_percent", 0) > 90:
            recommendations.append("Memoria RAM quasi esaurita, chiudere applicazioni non necessarie")

        self.status_data["recommendations"] = recommendations

    def print_status_report(self):
        """
        Stampa un report di stato formattato.
        """
        if not self.status_data:
            self.check_all_systems()

        print("🚀 DASHBOARD STATO SISTEMA APP-ROBERTO")
        print("=" * 60)
        print(f"📅 Ultimo aggiornamento: {self.status_data['timestamp']}")
        print(f"🎯 Stato generale: {self.status_data['overall_status'].upper()}")

        summary = self.status_data.get("status_summary", {})
        print(f"📊 Riepilogo: {summary.get('ok', 0)} OK, {summary.get('warning', 0)} Warning, {summary.get('error', 0)} Error")
        print()

        # Componenti
        print("🔧 COMPONENTI BASE:")
        for name, component in self.status_data["components"].items():
            status_icon = {"ok": "✅", "warning": "⚠️", "error": "❌"}.get(component.get("status"), "❓")
            print(f"  {status_icon} {name}: {component.get('details', component.get('error', 'Unknown'))}")
        print()

        # Integrazioni
        print("🔗 INTEGRAZIONI:")
        for name, integration in self.status_data["integrations"].items():
            status_icon = {"ok": "✅", "warning": "⚠️", "error": "❌"}.get(integration.get("status"), "❓")
            print(f"  {status_icon} {name}: {integration.get('details', integration.get('error', 'Unknown'))}")
        print()

        # Performance
        print("📈 PERFORMANCE:")
        performance = self.status_data.get("performance", {})
        for metric, data in performance.items():
            if isinstance(data, dict) and "error" not in data:
                if metric == "disk_space":
                    print(f"  💾 Spazio disco: {data.get('free_gb', 0)} GB liberi ({data.get('usage_percent', 0)}% usato)")
                elif metric == "memory":
                    print(f"  🧠 Memoria: {data.get('available_gb', 0)} GB disponibili ({data.get('usage_percent', 0)}% usato)")
                elif metric == "uploads":
                    print(f"  📁 Upload: {data.get('files_count', 0)} file ({data.get('total_size_mb', 0)} MB)")
        print()

        # Raccomandazioni
        recommendations = self.status_data.get("recommendations", [])
        if recommendations:
            print("💡 RACCOMANDAZIONI:")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")
        else:
            print("💡 Nessuna raccomandazione - sistema in buono stato!")

        print("=" * 60)

    def save_status_report(self, file_path: str = "system_status_report.json"):
        """
        Salva il report di stato in un file JSON.

        Args:
            file_path: Percorso del file di output
        """
        if not self.status_data:
            self.check_all_systems()

        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.status_data, f, indent=2, ensure_ascii=False)
            logger.info(f"📄 Report di stato salvato in: {file_path}")
        except Exception as e:
            logger.error(f"❌ Errore salvataggio report: {str(e)}")

# Istanza globale della dashboard
system_dashboard = SystemStatusDashboard()

def main():
    """
    Esegue la dashboard di stato del sistema.
    """
    dashboard = SystemStatusDashboard()
    dashboard.check_all_systems()
    dashboard.print_status_report()
    dashboard.save_status_report()

if __name__ == "__main__":
    main()
