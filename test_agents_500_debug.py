#!/usr/bin/env python3
"""
Test per debug errore 500 negli endpoint agenti
"""

import requests
import json

def test_agents_500_debug():
    """Test debug errore 500"""
    
    print("🔍 DEBUG ERRORE 500 ENDPOINT AGENTI")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test 1: Verifica connettività
    print("1️⃣ Test connettività...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   Homepage: {response.status_code} ({'✅' if response.status_code == 200 else '❌'})")
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
        return
    
    # Test 2: Test endpoint /api/agents/status con dettagli errore
    print("\n2️⃣ Test /api/agents/status con debug...")
    try:
        response = requests.get(f"{base_url}/api/agents/status", timeout=10)
        
        print(f"   Status: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        
        if response.status_code == 500:
            print("   ❌ ERRORE 500 - Internal Server Error")
            try:
                data = response.json()
                print(f"   📄 Risposta JSON: {json.dumps(data, indent=2)}")
                if 'error' in data:
                    print(f"   🔍 Errore specifico: {data['error']}")
            except:
                print(f"   📄 Risposta non JSON: {response.text[:500]}...")
        elif response.status_code == 200:
            print("   ✅ Endpoint funziona!")
            try:
                data = response.json()
                print(f"   📄 Risposta: {json.dumps(data, indent=2)}")
            except:
                print("   📄 Risposta non JSON")
        else:
            print(f"   ⚠️ Status inaspettato: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    # Test 3: Test endpoint /api/automation/rules con dettagli errore
    print("\n3️⃣ Test /api/automation/rules con debug...")
    try:
        response = requests.get(f"{base_url}/api/automation/rules", timeout=10)
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 500:
            print("   ❌ ERRORE 500 - Internal Server Error")
            try:
                data = response.json()
                print(f"   📄 Risposta JSON: {json.dumps(data, indent=2)}")
                if 'error' in data:
                    print(f"   🔍 Errore specifico: {data['error']}")
            except:
                print(f"   📄 Risposta non JSON: {response.text[:500]}...")
        elif response.status_code == 200:
            print("   ✅ Endpoint funziona!")
            try:
                data = response.json()
                print(f"   📄 Risposta: {json.dumps(data, indent=2)}")
            except:
                print("   📄 Risposta non JSON")
        else:
            print(f"   ⚠️ Status inaspettato: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    # Test 4: Test endpoint funzionante per confronto
    print("\n4️⃣ Test endpoint funzionante /agents/list...")
    try:
        response = requests.get(f"{base_url}/agents/list", timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Agenti trovati: {len(data.get('agents', []))}")
        else:
            print(f"   ❌ Errore: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    # Test 5: Test altri endpoint API per pattern
    print("\n5️⃣ Test altri endpoint API...")
    
    api_endpoints = [
        "/api/health",
        "/api/endpoints", 
        "/api/processed_data",
        "/api/models"
    ]
    
    for endpoint in api_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            status = "✅" if response.status_code == 200 else "❌"
            print(f"   {status} {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {endpoint}: Errore - {str(e)}")
    
    print("\n" + "=" * 60)
    print("📊 DIAGNOSI")
    print("=" * 60)
    print("Se gli endpoint restituiscono 500 con messaggio di errore:")
    print("- Le route sono registrate correttamente")
    print("- C'è un errore nella logica interna delle funzioni")
    print("- Possibile problema con importazioni dinamiche o chiamate ai moduli")
    print("- Verificare i log del server per dettagli specifici")
    
    return True

if __name__ == "__main__":
    test_agents_500_debug()
