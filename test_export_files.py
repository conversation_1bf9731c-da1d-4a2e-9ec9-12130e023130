#!/usr/bin/env python3
"""
Test specifico per file export
"""

import sys
sys.path.append('.')

from real_file_analyzer import real_file_analyzer

def test_export_files():
    print("🔍 TEST FILE EXPORT CORRETTI")
    print("=" * 50)
    
    # Test specifico sui file export
    test_files = [
        'uploads/export_6_1747922881_f92c9e3f.xlsx',
        'uploads/export_7_1747923195_29625521.xlsx',
        'uploads/tmp-174791856851810_1747922944_3bf3260f.csv'
    ]
    
    for file_path in test_files:
        filename = file_path.split('/')[-1]
        print(f'\n🔍 {filename}')
        analysis = real_file_analyzer.analyze_file(file_path)
        print(f'   Tipo: {analysis["detected_type"]} (confidenza: {analysis["confidence_score"]:.2f})')
        print(f'   Top 3 punteggi:')
        for file_type, score in sorted(analysis['type_scores'].items(), key=lambda x: x[1], reverse=True)[:3]:
            print(f'     {file_type}: {score:.3f}')
        
        if analysis.get('column_mapping'):
            print(f'   Mapping colonne:')
            for std_name, actual_name in list(analysis['column_mapping'].items())[:3]:
                print(f'     {std_name} → {actual_name}')

if __name__ == "__main__":
    test_export_files()
