/**
 * Script per la gestione della dashboard
 * Ottimizzato per migliorare le prestazioni di caricamento dei grafici
 * Versione: 2.3.0 - Integrazione Supabase + Gestione errori migliorata
 */

// Stampa la versione nella console per verificare il caricamento
console.log('Dashboard.js versione 2.3.0 caricato - Supabase Ready');

// Configurazione globale per Plotly
const plotlyConfig = {
    responsive: true,
    displayModeBar: 'hover',  // Mostra la barra degli strumenti solo al passaggio del mouse
    locale: 'it',
    scrollZoom: true,  // Abilita lo zoom con la rotellina del mouse
    showTips: true,    // Mostra suggerimenti
    doubleClick: 'reset+autosize'  // Doppio clic per resettare e ridimensionare
};

// Funzione per ottenere i colori del tema corrente
function getThemeColors() {
    const isDark = document.documentElement.getAttribute('data-theme') === 'dark';

    return {
        background: isDark ? '#1e1e1e' : '#ffffff',
        paper: isDark ? '#1e1e1e' : '#ffffff',
        text: isDark ? '#b3b3b3' : '#495057',
        grid: isDark ? '#404040' : '#e9ecef',
        primary: isDark ? '#4dabf7' : '#0d6efd',
        success: isDark ? '#51cf66' : '#198754',
        warning: isDark ? '#ffd43b' : '#fd7e14',
        danger: isDark ? '#ff6b6b' : '#dc3545',
        info: isDark ? '#22d3ee' : '#0dcaf0'
    };
}

// Funzione per ottenere layout comune con tema
function getCommonLayout() {
    const colors = getThemeColors();

    return {
        margin: { t: 30, r: 10, l: 50, b: 50 },
        font: {
            family: 'Segoe UI, sans-serif',
            color: colors.text
        },
        paper_bgcolor: colors.paper,
        plot_bgcolor: colors.background,
        hovermode: 'closest',
        hoverlabel: {
            bgcolor: colors.background,
            font: { family: 'Segoe UI, sans-serif', size: 12, color: colors.text },
            bordercolor: colors.grid
        },
        legend: {
            orientation: 'h',
            yanchor: 'bottom',
            y: -0.2,
            xanchor: 'center',
            x: 0.5,
            font: { color: colors.text }
        },
        modebar: {
            bgcolor: colors.background,
            color: colors.text,
            activecolor: colors.primary
        },
        transition: {
            duration: 500,
            easing: 'cubic-in-out'
        },
        xaxis: {
            gridcolor: colors.grid,
            zerolinecolor: colors.grid,
            tickfont: { color: colors.text },
            titlefont: { color: colors.text }
        },
        yaxis: {
            gridcolor: colors.grid,
            zerolinecolor: colors.grid,
            tickfont: { color: colors.text },
            titlefont: { color: colors.text }
        }
    };
}

// Cache per i dati e i grafici
const cache = {
    data: null,
    charts: {}
};

// Stato di caricamento dei grafici
const loadingState = {
    timeChart: false,
    technicianChart: false,
    durationChart: false,
    clientChart: false,
    heatmapChart: false
};

/**
 * Inizializza la dashboard
 */
function initDashboard() {
    console.log('🚀 Inizializzazione dashboard moderna...');

    // Setup listener per cambio tema
    setupThemeListener();

    // Verifica status Supabase
    checkSupabaseStatus();

    // Mostra messaggio di caricamento
    showLoadingMessage();

    // Carica i dati una sola volta
    loadData()
        .then(() => {
            // Carica solo il primo grafico inizialmente
            return loadTimeChart();
        })
        .then(() => {
            // Carica gli altri grafici con un ritardo per dare priorità al primo
            setTimeout(() => {
                // Carica i grafici rimanenti in parallelo per velocizzare il caricamento
                Promise.all([
                    loadTechnicianChart(),
                    loadDurationChart(),
                    loadClientChart(),
                    loadHeatmapChart()
                ])
                .then(() => {
                    console.log('Tutti i grafici caricati con successo');
                    hideLoadingMessage();
                })
                .catch(error => {
                    console.error('Errore durante il caricamento dei grafici secondari:', error);
                    hideLoadingMessage();
                });
            }, 500); // Ritardo di 500ms per dare priorità al primo grafico

            return Promise.resolve();
        })
        .catch(error => {
            console.error('Errore durante il caricamento della dashboard:', error);
            hideLoadingMessage();
        });
}

/**
 * Mostra un messaggio di caricamento globale
 */
function showLoadingMessage() {
    // Crea un elemento di caricamento se non esiste già
    if (!document.getElementById('global-loading')) {
        const loadingEl = document.createElement('div');
        loadingEl.id = 'global-loading';
        loadingEl.style.position = 'fixed';
        loadingEl.style.top = '50%';
        loadingEl.style.left = '50%';
        loadingEl.style.transform = 'translate(-50%, -50%)';
        loadingEl.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
        loadingEl.style.padding = '20px';
        loadingEl.style.borderRadius = '10px';
        loadingEl.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.2)';
        loadingEl.style.zIndex = '9999';
        loadingEl.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">Caricamento...</span>
                </div>
                <p class="mt-3 mb-0 fw-bold">Caricamento dashboard in corso...</p>
                <p class="text-muted small">Ottimizzazione dei dati per una visualizzazione più veloce</p>
            </div>
        `;
        document.body.appendChild(loadingEl);
    }
}

/**
 * Nasconde il messaggio di caricamento globale
 */
function hideLoadingMessage() {
    const loadingEl = document.getElementById('global-loading');
    if (loadingEl) {
        // Aggiungi una transizione di dissolvenza
        loadingEl.style.transition = 'opacity 0.5s';
        loadingEl.style.opacity = '0';

        // Rimuovi l'elemento dopo la transizione
        setTimeout(() => {
            loadingEl.remove();
        }, 500);
    }
}

/**
 * Carica i dati dal server
 */
async function loadData() {
    if (cache.data) {
        console.log('Usando dati dalla cache');
        return cache.data;
    }

    console.log('Caricamento dati dal server...');
    try {
        console.time('Tempo caricamento dati');
        // Aggiungi un parametro casuale per evitare problemi di cache
        const nocache = Date.now() + Math.random().toString(36).substring(2, 15);
        const response = await fetch(`/api/data?nocache=${nocache}`);
        console.log('Risposta ricevuta dal server:', response.status);

        const responseData = await response.json();
        console.log('Dimensione risposta ricevuta:', JSON.stringify(responseData).length, 'caratteri');

        // Gestisci il nuovo formato standardizzato
        let data;
        if (responseData.success && responseData.data) {
            data = responseData.data;
            console.log('Dati estratti dal formato standardizzato');
            if (responseData.message) {
                console.log('Messaggio server:', responseData.message);
            }
        } else if (Array.isArray(responseData)) {
            // Compatibilità con il formato precedente
            data = responseData;
            console.log('Formato precedente rilevato');
        } else {
            throw new Error(responseData.error || 'Formato dati non riconosciuto');
        }

        console.log('Numero di record:', Array.isArray(data) ? data.length : 'N/A');
        console.timeEnd('Tempo caricamento dati');

        cache.data = data;
        return data;
    } catch (error) {
        console.error('Errore nel caricamento dei dati:', error);
        throw error;
    }
}

/**
 * Carica il grafico delle attività nel tempo
 */
async function loadTimeChart() {
    if (loadingState.timeChart) return;
    loadingState.timeChart = true;

    const chartContainer = document.getElementById('time-chart');
    if (!chartContainer) return;

    try {
        console.log('Caricamento grafico attività nel tempo...');
        console.time('Tempo caricamento grafico temporale');

        // Aggiungi un parametro casuale per evitare problemi di cache
        const nocache = Date.now() + Math.random().toString(36).substring(2, 15);
        const response = await fetch(`/api/charts/time_series?nocache=${nocache}`);
        console.log('Risposta grafico temporale:', response.status);

        const data = await response.json();
        console.log('Dimensione risposta grafico temporale:', JSON.stringify(data).length, 'caratteri');

        if (data.error) {
            console.error('Errore dal server per grafico temporale:', data.error);
            showError(chartContainer, data.error);
            return;
        }

        console.log('Parsing JSON grafico temporale...');
        console.time('Tempo parsing JSON grafico temporale');
        const chartData = JSON.parse(data.chart);
        console.timeEnd('Tempo parsing JSON grafico temporale');

        console.log('Rendering grafico temporale...');
        console.time('Tempo rendering grafico temporale');
        renderChart(chartContainer, chartData, 'time-chart');
        console.timeEnd('Tempo rendering grafico temporale');
        console.timeEnd('Tempo caricamento grafico temporale');
    } catch (error) {
        console.error('Errore nel caricamento del grafico temporale:', error);
        showError(chartContainer, 'Errore nel caricamento del grafico');
    } finally {
        loadingState.timeChart = false;
    }
}

/**
 * Carica il grafico di distribuzione dei tecnici
 */
async function loadTechnicianChart() {
    if (loadingState.technicianChart) return;
    loadingState.technicianChart = true;

    const chartContainer = document.getElementById('technician-chart');
    if (!chartContainer) return;

    try {
        console.log('Caricamento grafico distribuzione tecnici...');
        // Aggiungi un parametro casuale per evitare problemi di cache
        const nocache = Date.now() + Math.random().toString(36).substring(2, 15);
        const response = await fetch(`/api/charts/distribution?nocache=${nocache}`);
        const data = await response.json();

        if (data.error) {
            showError(chartContainer, data.error);
            return;
        }

        const chartData = JSON.parse(data.chart);
        renderChart(chartContainer, chartData, 'technician-chart');
    } catch (error) {
        console.error('Errore nel caricamento del grafico di distribuzione:', error);
        showError(chartContainer, 'Errore nel caricamento del grafico');
    } finally {
        loadingState.technicianChart = false;
    }
}

/**
 * Carica il grafico della durata delle sessioni
 * @param {string} chartType - Tipo di grafico (bar, line, area, scatter)
 */
async function loadDurationChart(chartType) {
    if (loadingState.durationChart) return;
    loadingState.durationChart = true;

    const chartContainer = document.getElementById('duration-chart');
    if (!chartContainer) return;

    try {
        console.log(`Caricamento grafico durata sessioni (tipo: ${chartType || 'default'})...`);
        // Usa un timeout per dare priorità agli altri grafici
        await new Promise(resolve => setTimeout(resolve, 100));

        // Aggiungi un parametro casuale per evitare problemi di cache
        const nocache = Date.now() + Math.random().toString(36).substring(2, 15);
        let url = `api/charts/distribution?type=duration&nocache=${nocache}`;

        // Aggiungi il tipo di grafico se specificato
        if (chartType) {
            url += `&chart_type=${chartType}`;
        }

        const response = await fetch(url);
        const data = await response.json();

        if (data.error) {
            showError(chartContainer, data.error);
            return;
        }

        const chartData = JSON.parse(data.chart);

        // Modifica il tipo di grafico se necessario
        if (chartType && chartType !== 'bar') {
            // Salva il tipo di grafico corrente
            cache.currentChartType = chartType;

            // Modifica il tipo di grafico
            if (chartData.data && chartData.data.length > 0) {
                chartData.data.forEach(trace => {
                    if (chartType === 'line') {
                        trace.type = 'scatter';
                        trace.mode = 'lines+markers';
                        trace.line = { shape: 'spline', width: 3 };
                        trace.marker = { size: 6 };
                    } else if (chartType === 'area') {
                        trace.type = 'scatter';
                        trace.mode = 'lines';
                        trace.fill = 'tozeroy';
                        trace.line = { shape: 'spline' };
                    } else if (chartType === 'scatter') {
                        trace.type = 'scatter';
                        trace.mode = 'markers';
                        trace.marker = {
                            size: 10,
                            opacity: 0.7,
                            line: {
                                width: 1,
                                color: 'white'
                            }
                        };
                    }
                });
            }

            // Aggiorna il layout per il tipo di grafico
            if (chartData.layout) {
                if (chartType === 'line' || chartType === 'area') {
                    chartData.layout.hovermode = 'closest';
                } else if (chartType === 'scatter') {
                    chartData.layout.hovermode = 'closest';
                    chartData.layout.dragmode = 'zoom';
                }
            }
        }

        renderChart(chartContainer, chartData, 'duration-chart');
    } catch (error) {
        console.error('Errore nel caricamento del grafico di durata:', error);
        showError(chartContainer, 'Errore nel caricamento del grafico');
    } finally {
        loadingState.durationChart = false;
    }
}

/**
 * Carica il grafico delle attività per cliente
 */
async function loadClientChart() {
    if (loadingState.clientChart) return;
    loadingState.clientChart = true;

    const chartContainer = document.getElementById('client-chart');
    if (!chartContainer) return;

    try {
        console.log('Caricamento grafico attività per cliente...');
        // Usa un timeout per dare priorità agli altri grafici
        await new Promise(resolve => setTimeout(resolve, 200));

        // Aggiungi un parametro casuale per evitare problemi di cache
        const nocache = Date.now() + Math.random().toString(36).substring(2, 15);
        const response = await fetch(`/api/charts/pie?nocache=${nocache}`);
        const data = await response.json();

        if (data.error) {
            showError(chartContainer, data.error);
            return;
        }

        const chartData = JSON.parse(data.chart);
        renderChart(chartContainer, chartData, 'client-chart');
    } catch (error) {
        console.error('Errore nel caricamento del grafico per cliente:', error);
        showError(chartContainer, 'Errore nel caricamento del grafico');
    } finally {
        loadingState.clientChart = false;
    }
}

/**
 * Renderizza un grafico nel container specificato
 */
function renderChart(container, chartData, chartId) {
    console.time(`Rendering ${chartId}`);

    // Rimuovi il loader
    container.innerHTML = '';

    try {
        // Verifica che chartData sia valido
        if (!chartData || !chartData.data || !Array.isArray(chartData.data)) {
            console.error(`Dati del grafico ${chartId} non validi:`, chartData);
            showError(container, 'Dati del grafico non validi');
            console.timeEnd(`Rendering ${chartId}`);
            return;
        }

        // Applica le opzioni di layout comuni con tema
        const commonLayout = getCommonLayout();
        if (chartData.layout) {
            chartData.layout = { ...commonLayout, ...chartData.layout };
        } else {
            chartData.layout = { ...commonLayout };
        }

        // Aggiungi opzioni per i grafici interattivi
        const config = {
            ...plotlyConfig,
            staticPlot: false,  // Abilita l'interattività
            responsive: true,
            displayModeBar: true,  // Mostra la barra degli strumenti
            modeBarButtonsToAdd: [
                'zoom2d',
                'pan2d',
                'select2d',
                'lasso2d',
                'zoomIn2d',
                'zoomOut2d',
                'autoScale2d',
                'resetScale2d'
            ],
            modeBarButtonsToRemove: ['sendDataToCloud'],
            toImageButtonOptions: {
                format: 'png',
                filename: 'grafico_dashboard',
                height: 500,
                width: 700,
                scale: 2
            }
        };

        // Verifica che Plotly sia caricato
        if (typeof Plotly === 'undefined') {
            console.error('Plotly non è caricato');
            showError(container, 'Libreria di grafici non caricata');
            console.timeEnd(`Rendering ${chartId}`);
            return;
        }

        // Renderizza il grafico con un timeout per non bloccare l'UI
        setTimeout(() => {
            try {
                console.log(`Tentativo di rendering grafico ${chartId} con dati:`, chartData.data.length, 'serie');
                Plotly.newPlot(container, chartData.data, chartData.layout, config);
                console.log(`Grafico ${chartId} renderizzato con successo`);
            } catch (error) {
                console.error(`Errore durante il rendering del grafico ${chartId}:`, error);
                showError(container, 'Errore nel rendering del grafico');
            }
        }, 100);  // Aumentato il timeout per dare più tempo al browser

        // Salva nella cache
        cache.charts[chartId] = chartData;
    } catch (error) {
        console.error(`Errore durante la preparazione del grafico ${chartId}:`, error);
        showError(container, 'Errore nella preparazione del grafico');
    }

    console.timeEnd(`Rendering ${chartId}`);
}

/**
 * Carica il grafico heatmap
 */
async function loadHeatmapChart() {
    if (loadingState.heatmapChart) return;
    loadingState.heatmapChart = true;

    const chartContainer = document.getElementById('heatmap-chart');
    if (!chartContainer) return;

    try {
        console.log('Caricamento grafico heatmap...');
        // Usa un timeout per dare priorità agli altri grafici
        await new Promise(resolve => setTimeout(resolve, 300));

        // Aggiungi un parametro casuale per evitare problemi di cache
        const nocache = Date.now() + Math.random().toString(36).substring(2, 15);
        const response = await fetch(`/api/charts/heatmap?nocache=${nocache}`);
        const data = await response.json();

        if (data.error) {
            showError(chartContainer, data.error);
            return;
        }

        const chartData = JSON.parse(data.chart);
        renderChart(chartContainer, chartData, 'heatmap-chart');
    } catch (error) {
        console.error('Errore nel caricamento del grafico heatmap:', error);
        showError(chartContainer, 'Errore nel caricamento del grafico');
    } finally {
        loadingState.heatmapChart = false;
    }
}

/**
 * Mostra un messaggio di errore nel container del grafico
 */
function showError(container, message) {
    container.innerHTML = `
        <div class="d-flex justify-content-center align-items-center h-100">
            <div class="text-center text-danger">
                <i class="fas fa-exclamation-circle fa-2x mb-2"></i>
                <p>${message}</p>
            </div>
        </div>
    `;
}

/**
 * Mostra un toast di notifica
 * @param {string} message - Il messaggio da mostrare
 * @param {string} type - Il tipo di toast (success, info, warning, error)
 * @param {boolean} autoHide - Se il toast deve scomparire automaticamente
 * @returns {Object} - L'oggetto toast
 */
function showToast(message, type = 'info', autoHide = true) {
    // Crea il container dei toast se non esiste
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // Crea un ID univoco per il toast
    const toastId = 'toast-' + Date.now();

    // Crea il toast
    const toastEl = document.createElement('div');
    toastEl.className = `toast align-items-center text-white bg-${type} border-0`;
    toastEl.id = toastId;
    toastEl.setAttribute('role', 'alert');
    toastEl.setAttribute('aria-live', 'assertive');
    toastEl.setAttribute('aria-atomic', 'true');

    if (autoHide) {
        toastEl.setAttribute('data-bs-delay', '3000');
        toastEl.setAttribute('data-bs-autohide', 'true');
    } else {
        toastEl.setAttribute('data-bs-autohide', 'false');
    }

    // Crea il contenuto del toast
    toastEl.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Chiudi"></button>
        </div>
    `;

    // Aggiungi il toast al container
    toastContainer.appendChild(toastEl);

    // Inizializza il toast con Bootstrap
    const toast = new bootstrap.Toast(toastEl);
    toast.show();

    // Rimuovi il toast dal DOM quando viene nascosto
    toastEl.addEventListener('hidden.bs.toast', () => {
        if (toastContainer.contains(toastEl)) {
            toastContainer.removeChild(toastEl);
        }
        if (toastContainer.children.length === 0 && document.body.contains(toastContainer)) {
            document.body.removeChild(toastContainer);
        }
    });

    // Restituisci l'oggetto toast per poterlo controllare
    return toast;
}

// Inizializza la dashboard quando il documento è pronto
document.addEventListener('DOMContentLoaded', function() {
    initDashboard();

    // Gestione dei filtri
    const applyFiltersBtn = document.getElementById('apply-filters');
    if (applyFiltersBtn) {
        applyFiltersBtn.addEventListener('click', function() {
            // Resetta la cache dei grafici
            cache.charts = {};

            // Ricarica i grafici
            loadTimeChart();
            loadTechnicianChart();
            loadDurationChart();
            loadClientChart();
            loadHeatmapChart();
        });
    }

    // Gestione del reset dei filtri
    const resetFiltersBtn = document.getElementById('reset-filters');
    if (resetFiltersBtn) {
        resetFiltersBtn.addEventListener('click', function() {
            // Resetta i filtri
            document.getElementById('date-range').value = '';
            document.getElementById('technician-filter').value = '';
            document.getElementById('activity-type').value = '';

            // Resetta la cache dei grafici
            cache.charts = {};

            // Ricarica i grafici
            loadTimeChart();
            loadTechnicianChart();
            loadDurationChart();
            loadClientChart();
            loadHeatmapChart();
        });
    }

    // Gestione del cambio tipo grafico
    const toggleChartBtn = document.getElementById('toggle-chart-type');
    if (toggleChartBtn) {
        toggleChartBtn.addEventListener('click', function() {
            // Ottieni il container del grafico
            const chartContainer = document.getElementById('duration-chart');
            if (!chartContainer) return;

            // Cambia il tipo di grafico
            if (!cache.currentChartType) {
                cache.currentChartType = 'bar';
            }

            // Alterna tra i tipi di grafico
            const chartTypes = ['bar', 'line', 'area', 'scatter'];
            const currentIndex = chartTypes.indexOf(cache.currentChartType);
            const nextIndex = (currentIndex + 1) % chartTypes.length;
            cache.currentChartType = chartTypes[nextIndex];

            // Aggiorna il testo del pulsante
            const chartTypeNames = {
                'bar': 'Barre',
                'line': 'Linee',
                'area': 'Area',
                'scatter': 'Dispersione'
            };
            toggleChartBtn.innerHTML = `<i class="fas fa-chart-${cache.currentChartType} me-1"></i>${chartTypeNames[cache.currentChartType]}`;

            // Ricarica il grafico
            loadDurationChart(cache.currentChartType);

            // Mostra un toast di notifica
            showToast(`Grafico cambiato in: ${chartTypeNames[cache.currentChartType]}`, 'info');
        });
    }

    // Imposta il valore ARIA corretto per la barra di progresso delle ore
    const totalHoursElement = document.getElementById('total-hours-value');
    let totalHours = 0;
    if (totalHoursElement) {
        const totalHoursText = totalHoursElement.textContent.trim();
        totalHours = totalHoursText === '--' ? 0 : parseFloat(totalHoursText) || 0;
    }
    const hoursPercentage = Math.min(totalHours / 100 * 100, 100);
    const hoursProgressBar = document.querySelector('.progress-bar-hours');

    if (hoursProgressBar) {
        hoursProgressBar.setAttribute('aria-valuenow', Math.round(hoursPercentage));
    }
});

/**
 * Verifica status Supabase per dashboard
 */
function checkSupabaseStatus() {
    console.log('🔍 Verificando status Supabase per dashboard...');

    fetch('/api/supabase/status')
        .then(response => response.json())
        .then(status => {
            console.log('📊 Status Supabase Dashboard:', status);

            // Mostra badge status nella dashboard
            showSupabaseStatus(status);

        })
        .catch(error => {
            console.warn('⚠️ Impossibile verificare status Supabase:', error);
            showSupabaseStatus({ available: false, error: error.message });
        });
}

/**
 * Mostra status Supabase nella dashboard
 */
function showSupabaseStatus(status) {
    // Cerca un container per il badge (se esiste)
    const headerElement = document.querySelector('.card-header h4');
    if (headerElement && !document.getElementById('supabase-badge')) {
        const badge = document.createElement('span');
        badge.id = 'supabase-badge';
        badge.className = `badge ms-2 ${status.available ? 'bg-success' : 'bg-secondary'}`;
        badge.textContent = status.available ? 'Supabase' : 'Legacy';
        badge.title = status.available ?
            `Connesso - ${status.tables?.normalized_activities || 0} attività` :
            'Modalità compatibilità';

        headerElement.appendChild(badge);
    }
}

/**
 * Mostra errore generale dashboard
 */
function showDashboardError(message) {
    const alertContainer = document.querySelector('.card-body');
    if (alertContainer && !document.getElementById('dashboard-error')) {
        const alert = document.createElement('div');
        alert.id = 'dashboard-error';
        alert.className = 'alert alert-warning alert-dismissible fade show';
        alert.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        alertContainer.insertBefore(alert, alertContainer.firstChild);

        // Auto-remove dopo 10 secondi
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 10000);
    }
}

/**
 * Setup listener per cambio tema
 */
function setupThemeListener() {
    document.addEventListener('themeChanged', function(event) {
        console.log('🎨 Tema cambiato, aggiornamento grafici dashboard...');

        // Ricarica tutti i grafici con i nuovi colori
        setTimeout(() => {
            const chartContainers = [
                'time-chart',
                'technician-chart',
                'duration-chart',
                'client-chart',
                'heatmap-chart'
            ];

            chartContainers.forEach(chartId => {
                const container = document.getElementById(chartId);
                if (container && cache.charts[chartId]) {
                    // Aggiorna il layout con i nuovi colori del tema
                    const chartData = cache.charts[chartId];
                    const newLayout = getCommonLayout();
                    const updatedLayout = { ...newLayout, ...chartData.layout };

                    // Applica il nuovo layout
                    if (typeof Plotly !== 'undefined') {
                        Plotly.relayout(container, updatedLayout);
                    }
                }
            });
        }, 100); // Piccolo delay per permettere al CSS di aggiornarsi
    });
}
