@echo off
echo 🔧 BACK<PERSON> COMPLETO VS CODE
echo ========================

:: Crea cartella backup
set BACKUP_DIR=%USERPROFILE%\Desktop\VSCode_Backup_%DATE:~-4,4%%DATE:~-10,2%%DATE:~-7,2%
mkdir "%BACKUP_DIR%"

echo 📁 Creando backup in: %BACKUP_DIR%

:: Backup impostazioni utente
echo 📄 Copiando impostazioni utente...
xcopy "%APPDATA%\Code\User\*" "%BACKUP_DIR%\User\" /E /I /Y

:: Backup estensioni (lista)
echo 📦 Esportando lista estensioni...
code --list-extensions > "%BACKUP_DIR%\extensions.txt"

:: Backup workspace (se presente)
if exist "%USERPROFILE%\.vscode" (
    echo 📁 Copiando workspace settings...
    xcopy "%USERPROFILE%\.vscode\*" "%BACKUP_DIR%\workspace\" /E /I /Y
)

:: <PERSON><PERSON> script di ripristino
echo 🔄 Creando script di ripristino...
(
echo @echo off
echo echo 🔄 RIPRISTINO VS CODE
echo echo ==================
echo.
echo echo 📄 Ripristinando impostazioni utente...
echo xcopy "%%~dp0User\*" "%%APPDATA%%\Code\User\" /E /I /Y
echo.
echo echo 📦 Installando estensioni...
echo for /f %%%%i in ^(%%~dp0extensions.txt^) do code --install-extension %%%%i
echo.
echo if exist "%%~dp0workspace\" ^(
echo     echo 📁 Ripristinando workspace settings...
echo     xcopy "%%~dp0workspace\*" "%%USERPROFILE%%\.vscode\" /E /I /Y
echo ^)
echo.
echo echo ✅ Ripristino completato!
echo pause
) > "%BACKUP_DIR%\ripristina_vscode.bat"

echo.
echo ✅ BACKUP COMPLETATO!
echo 📁 Cartella: %BACKUP_DIR%
echo 🔄 Per ripristinare: esegui ripristina_vscode.bat
echo.
pause
