#!/usr/bin/env python3
"""
Test per identificare conflitti di route
"""

import requests
import json

def test_route_conflict():
    """Test conflitti route"""
    
    print("🔍 TEST CONFLITTI ROUTE")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test 1: Verifica connettività
    print("1️⃣ Test connettività...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   Homepage: {response.status_code} ({'✅' if response.status_code == 200 else '❌'})")
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
        return
    
    # Test 2: Test route blueprint agenti (funzionanti)
    print("\n2️⃣ Test route blueprint agenti...")
    
    blueprint_routes = [
        "/agents/list",
        "/agents/dashboard", 
        "/agents",  # redirect
    ]
    
    for route in blueprint_routes:
        try:
            response = requests.get(f"{base_url}{route}", timeout=5, allow_redirects=True)
            status = "✅" if response.status_code == 200 else "❌"
            print(f"   {status} {route}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {route}: Errore - {str(e)}")
    
    # Test 3: Test route API agenti (problematiche)
    print("\n3️⃣ Test route API agenti...")
    
    api_routes = [
        "/api/agents/status",
        "/api/agents/execute",
        "/api/automation/rules",
        "/api/automation/trigger",
    ]
    
    for route in api_routes:
        try:
            if route in ["/api/agents/execute", "/api/automation/trigger"]:
                response = requests.post(
                    f"{base_url}{route}",
                    json={"test": True},
                    headers={"Content-Type": "application/json"},
                    timeout=5
                )
            else:
                response = requests.get(f"{base_url}{route}", timeout=5)
                
            status = "✅" if response.status_code in [200, 400, 503] else "❌"
            print(f"   {status} {route}: {response.status_code}")
            
            if response.status_code == 404:
                print(f"      ❌ Route NON registrata")
            elif response.status_code == 503:
                print(f"      ⚠️ Route registrata ma servizio non disponibile")
            elif response.status_code == 500:
                print(f"      ⚠️ Route registrata ma errore interno")
                
        except Exception as e:
            print(f"   ❌ {route}: Errore - {str(e)}")
    
    # Test 4: Test route simili per pattern
    print("\n4️⃣ Test route simili...")
    
    similar_routes = [
        "/api/agents",  # senza /status
        "/api/agent/status",  # singolare
        "/agents/api/status",  # ordine invertito
    ]
    
    for route in similar_routes:
        try:
            response = requests.get(f"{base_url}{route}", timeout=5)
            print(f"   Test {route}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {route}: Errore - {str(e)}")
    
    # Test 5: Test con curl per debug headers
    print("\n5️⃣ Test debug headers...")
    
    import subprocess
    
    try:
        # Test con curl per vedere headers completi
        result = subprocess.run([
            'curl', '-v', f'{base_url}/api/agents/status'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("   📄 Output curl:")
            # Mostra solo le prime righe per debug
            lines = result.stderr.split('\n')[:10]
            for line in lines:
                if line.strip():
                    print(f"      {line}")
        else:
            print("   ❌ Curl non disponibile o errore")
            
    except Exception as e:
        print(f"   ⚠️ Test curl fallito: {str(e)}")
    
    # Test 6: Verifica lista endpoint completa
    print("\n6️⃣ Verifica lista endpoint...")
    
    try:
        response = requests.get(f"{base_url}/api/endpoints", timeout=10)
        if response.status_code == 200:
            data = response.json()
            endpoints = data.get('data', {}).get('endpoints', {})
            
            print(f"   📋 Totale endpoint registrati: {len(endpoints)}")
            
            # Cerca endpoint agenti
            agent_endpoints = [k for k in endpoints.keys() if 'agent' in k.lower()]
            print(f"   🤖 Endpoint agenti registrati: {len(agent_endpoints)}")
            for ep in agent_endpoints:
                print(f"      - {ep}")
                
            # Verifica se i nostri endpoint sono registrati
            target_endpoints = [
                'GET:/api/agents/status',
                'POST:/api/agents/execute',
                'GET:/api/automation/rules'
            ]
            
            print(f"\n   🎯 Verifica endpoint target:")
            for target in target_endpoints:
                found = target in endpoints
                status = "✅" if found else "❌"
                print(f"      {status} {target}")
                
        else:
            print(f"   ❌ Errore lista endpoint: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    print("\n" + "=" * 60)
    print("📊 DIAGNOSI")
    print("=" * 60)
    print("Se le route blueprint funzionano ma quelle API no:")
    print("- Possibile conflitto tra blueprint e route dirette")
    print("- Problema di ordine di registrazione")
    print("- Route API potrebbero essere sovrascritte o ignorate")
    print()
    print("Se gli endpoint appaiono nella lista ma restituiscono 404:")
    print("- Problema di routing Flask interno")
    print("- Possibile errore nella definizione delle route")
    
    return True

if __name__ == "__main__":
    test_route_conflict()
