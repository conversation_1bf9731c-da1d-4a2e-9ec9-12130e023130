#!/usr/bin/env python3
"""
Test per l'Enhanced File Detector con file grezzi
"""

import pandas as pd
import sys
import os

# Aggiungi il percorso corrente per importare i moduli
sys.path.append('.')

from enhanced_file_detector import EnhancedFileDetector
from file_detector import FileTypeDetector

def test_enhanced_detector():
    """Testa l'Enhanced File Detector con file grezzi"""
    
    print("🔧 Testando Enhanced File Detector...")
    
    # Inizializza i detector
    enhanced_detector = EnhancedFileDetector()
    original_detector = FileTypeDetector()
    
    # Test con file grezzo TeamViewer
    print("\n📁 Test 1: File TeamViewer grezzo (test_raw_file.csv)")
    try:
        df = pd.read_csv('test_raw_file.csv')
        print(f"Colonne del file: {df.columns.tolist()}")
        
        # Test Enhanced Detector
        print("\n🔍 Enhanced File Detector:")
        detected_type, confidence, scores = enhanced_detector.detect_file_type(df)
        print(f"Tipo rilevato: {detected_type}")
        print(f"Confidenza: {confidence:.3f}")
        print("Punteggi per tipo:")
        for file_type, score in scores.items():
            print(f"  - {file_type}: {score:.3f}")
        
        # Test Original Detector
        print("\n🔍 Original File Detector:")
        orig_type, orig_confidence, orig_scores = original_detector.detect_file_type(df)
        print(f"Tipo rilevato: {orig_type}")
        print(f"Confidenza: {orig_confidence:.3f}")
        print("Punteggi per tipo:")
        for file_type, score in orig_scores.items():
            print(f"  - {file_type}: {score:.3f}")
        
        # Test mappatura colonne
        if detected_type != "unknown":
            print(f"\n🗂️ Mappatura colonne suggerita per {detected_type}:")
            mapping = enhanced_detector.get_column_mapping_suggestions(df, detected_type)
            for original, standard in mapping.items():
                print(f"  '{original}' -> '{standard}'")
        
    except Exception as e:
        print(f"❌ Errore nel test: {e}")
    
    # Test con altri tipi di file simulati
    print("\n📁 Test 2: File Calendario simulato")
    calendar_data = {
        'Date': ['2025-01-15', '2025-01-16', '2025-01-17'],
        'Start Time': ['09:00', '14:00', '10:30'],
        'End Time': ['10:00', '15:30', '12:00'],
        'Subject': ['Meeting 1', 'Conference Call', 'Training'],
        'Location': ['Room A', 'Online', 'Room B']
    }
    
    df_calendar = pd.DataFrame(calendar_data)
    print(f"Colonne del file: {df_calendar.columns.tolist()}")
    
    detected_type, confidence, scores = enhanced_detector.detect_file_type(df_calendar)
    print(f"Tipo rilevato: {detected_type}")
    print(f"Confidenza: {confidence:.3f}")
    
    # Test con file Timbrature simulato
    print("\n📁 Test 3: File Timbrature simulato")
    attendance_data = {
        'Date': ['2025-01-15', '2025-01-16', '2025-01-17'],
        'Employee': ['John Doe', 'Jane Smith', 'Mike Wilson'],
        'Clock In': ['08:00', '08:30', '09:00'],
        'Clock Out': ['17:00', '17:30', '18:00'],
        'Hours Worked': ['8.0', '8.5', '8.0']
    }
    
    df_attendance = pd.DataFrame(attendance_data)
    print(f"Colonne del file: {df_attendance.columns.tolist()}")
    
    detected_type, confidence, scores = enhanced_detector.detect_file_type(df_attendance)
    print(f"Tipo rilevato: {detected_type}")
    print(f"Confidenza: {confidence:.3f}")
    
    print("\n✅ Test completati!")

if __name__ == "__main__":
    test_enhanced_detector()
