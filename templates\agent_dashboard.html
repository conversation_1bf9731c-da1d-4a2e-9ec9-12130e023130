{% extends "base.html" %}

{% block title %}Dashboard Agenti AI{% endblock %}

{% block extra_css %}
<style>
    .agent-card {
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }
    .agent-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .progress {
        height: 10px;
        margin-top: 10px;
    }
    .agent-actions {
        margin-top: 15px;
    }
    .status-badge {
        font-size: 0.8rem;
        padding: 5px 10px;
    }
    .agent-result {
        max-height: 300px;
        overflow-y: auto;
    }
    #agent-details-modal .modal-dialog {
        max-width: 800px;
    }
    .agent-progress-bar {
        transition: width 0.3s ease;
    }
    /* La classe progress-width-dynamic non ha bisogno di stili CSS poiché la larghezza viene impostata dinamicamente tramite JavaScript */
</style>
{% endblock %}

{% block content %}

    <!-- Contenuto principale -->
    <div class="container mt-4">
        <h1 class="mb-4">
            <i class="bi bi-robot"></i> Dashboard Agenti AI
        </h1>

        <!-- Agenti disponibili -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-list-check"></i> Agenti Disponibili
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for agent in agents %}
                    <div class="col-md-4">
                        <div class="card agent-card">
                            <div class="card-body">
                                <h5 class="card-title">{{ agent.name }}</h5>
                                <p class="card-text">{{ agent.description }}</p>
                                <div class="agent-actions">
                                    <button type="button" class="btn btn-primary btn-sm" onclick="showRunAgentModal('{{ agent.name }}')">
                                        <i class="bi bi-play-fill"></i> Esegui
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Agenti in esecuzione -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="bi bi-hourglass-split"></i> Agenti in Esecuzione
                </h5>
            </div>
            <div class="card-body">
                {% if running_agents %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nome</th>
                                <th>Stato</th>
                                <th>Progresso</th>
                                <th>Avviato</th>
                                <th>Azioni</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for agent in running_agents %}
                            <tr>
                                <td>{{ agent.id[:8] }}...</td>
                                <td>{{ agent.name }}</td>
                                <td>
                                    <span class="badge bg-warning text-dark status-badge">
                                        {{ agent.status }}
                                    </span>
                                </td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning agent-progress-bar progress-width-dynamic"
                                             data-progress="{{ agent.progress }}"
                                             aria-valuemin="0" aria-valuemax="100"
                                             aria-label="Progresso dell'agente {{ agent.name }}"
                                             role="progressbar">
                                            {{ agent.progress }}%
                                        </div>
                                    </div>
                                </td>
                                <td>{{ agent.start_time }}</td>
                                <td>
                                    <button type="button" class="btn btn-info btn-sm" onclick="getAgentStatus('{{ agent.id }}')" title="Visualizza dettagli">
                                        <i class="bi bi-info-circle"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">Nessun agente in esecuzione al momento.</p>
                {% endif %}
            </div>
        </div>

        <!-- Agenti completati -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="bi bi-check-circle"></i> Agenti Completati
                </h5>
            </div>
            <div class="card-body">
                {% if completed_agents %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nome</th>
                                <th>Stato</th>
                                <th>Avviato</th>
                                <th>Completato</th>
                                <th>Azioni</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for agent in completed_agents %}
                            <tr>
                                <td>{{ agent.id[:8] }}...</td>
                                <td>{{ agent.name }}</td>
                                <td>
                                    {% if agent.status == 'completed' %}
                                    <span class="badge bg-success status-badge">
                                        completato
                                    </span>
                                    {% elif agent.status == 'error' %}
                                    <span class="badge bg-danger status-badge">
                                        errore
                                    </span>
                                    {% else %}
                                    <span class="badge bg-secondary status-badge">
                                        {{ agent.status }}
                                    </span>
                                    {% endif %}
                                </td>
                                <td>{{ agent.start_time }}</td>
                                <td>{{ agent.end_time }}</td>
                                <td>
                                    <button type="button" class="btn btn-info btn-sm" onclick="getAgentStatus('{{ agent.id }}')" title="Visualizza dettagli">
                                        <i class="bi bi-info-circle"></i>
                                    </button>
                                    {% if agent.status == 'completed' %}
                                    <button type="button" class="btn btn-success btn-sm" onclick="getAgentResult('{{ agent.id }}')" title="Visualizza risultato">
                                        <i class="bi bi-file-earmark-text"></i>
                                    </button>
                                    {% if agent.cleaned_file_id %}
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" title="Esporta">
                                            <i class="bi bi-download"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="/export/mcp/{{ agent.cleaned_file_id }}/excel">Excel</a></li>
                                            <li><a class="dropdown-item" href="/export/mcp/{{ agent.cleaned_file_id }}/csv">CSV</a></li>
                                            <li><a class="dropdown-item" href="/export/mcp/{{ agent.cleaned_file_id }}/json">JSON</a></li>
                                        </ul>
                                    </div>
                                    {% endif %}
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">Nessun agente completato.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Modal per eseguire un agente -->
    <div class="modal fade" id="run-agent-modal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Esegui Agente</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" title="Chiudi"></button>
                </div>
                <div class="modal-body">
                    <form id="run-agent-form">
                        <input type="hidden" id="agent-name" name="agent-name">

                        <div class="mb-3">
                            <label for="file-id" class="form-label">ID File</label>
                            <input type="text" class="form-control" id="file-id" name="file-id"
                                   value="{{ session.get('mcp_file_id', '') }}">
                            <div class="form-text">
                                Se lasciato vuoto, verrà utilizzato l'ID del file corrente in sessione.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="aggression-level" class="form-label">Livello di Aggressività</label>
                            <select class="form-select" id="aggression-level" name="aggression-level">
                                <option value="low">Basso (solo suggerimenti)</option>
                                <option value="medium" selected>Medio (correzioni non critiche)</option>
                                <option value="high">Alto (tutte le correzioni)</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Tipi di Problemi da Cercare</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="missing-values" name="issue-types" value="missing_values" checked>
                                <label class="form-check-label" for="missing-values">
                                    Valori Mancanti
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="format-inconsistencies" name="issue-types" value="format_inconsistencies" checked>
                                <label class="form-check-label" for="format-inconsistencies">
                                    Formati Inconsistenti
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="duration-discrepancies" name="issue-types" value="duration_discrepancies" checked>
                                <label class="form-check-label" for="duration-discrepancies">
                                    Discrepanze di Durata
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annulla</button>
                    <button type="button" class="btn btn-primary" onclick="runAgent()">Esegui</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal per i dettagli dell'agente -->
    <div class="modal fade" id="agent-details-modal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Dettagli Agente</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" title="Chiudi"></button>
                </div>
                <div class="modal-body">
                    <div id="agent-details-content">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Caricamento...</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Chiudi</button>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block extra_js %}
<script>
    // Variabili globali
    let runAgentModal;
    let agentDetailsModal;

    // Inizializzazione
    document.addEventListener('DOMContentLoaded', function() {
        runAgentModal = new bootstrap.Modal(document.getElementById('run-agent-modal'));
        agentDetailsModal = new bootstrap.Modal(document.getElementById('agent-details-modal'));
    });

    // Mostra il modal per eseguire un agente
    function showRunAgentModal(agentName) {
        document.getElementById('agent-name').value = agentName;
        runAgentModal.show();
    }

    // Esegui un agente
    function runAgent() {
        const agentName = document.getElementById('agent-name').value;
        const fileId = document.getElementById('file-id').value;
        const aggressionLevel = document.getElementById('aggression-level').value;

        // Raccogli i tipi di problemi selezionati
        const issueTypes = [];
        document.querySelectorAll('input[name="issue-types"]:checked').forEach(checkbox => {
            issueTypes.push(checkbox.value);
        });

        // Prepara i dati da inviare
        const data = {
            file_id: fileId,
            aggression_level: aggressionLevel,
            issue_types: issueTypes
        };

        // Invia la richiesta
        fetch(`/agents/run/${agentName}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert(`Errore: ${data.message}`);
            } else {
                alert(`Agente avviato con successo! ID: ${data.agent_id}`);
                runAgentModal.hide();
                // Ricarica la pagina dopo 2 secondi
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            }
        })
        .catch(error => {
            console.error('Errore:', error);
            alert('Si è verificato un errore durante l\'avvio dell\'agente.');
        });
    }

    // Ottieni lo stato di un agente
    function getAgentStatus(agentId) {
        fetch(`/agents/status/${agentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert(`Errore: ${data.message}`);
            } else {
                showAgentDetails(data);
            }
        })
        .catch(error => {
            console.error('Errore:', error);
            alert('Si è verificato un errore durante il recupero dello stato dell\'agente.');
        });
    }

    // Ottieni il risultato di un agente
    function getAgentResult(agentId) {
        fetch(`/agents/result/${agentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert(`Errore: ${data.message}`);
            } else {
                showAgentResult(data);
            }
        })
        .catch(error => {
            console.error('Errore:', error);
            alert('Si è verificato un errore durante il recupero del risultato dell\'agente.');
        });
    }

    // Mostra i dettagli di un agente
    function showAgentDetails(data) {
        const state = data.state;

        let html = `
            <h5>Stato: <span class="badge ${getStatusBadgeClass(state.status)}">${state.status}</span></h5>
            <div class="progress mb-3">
                <div class="progress-bar ${getProgressBarClass(state.status)}"
                     role="progressbar"
                     style="width: ${state.progress}%">
                    ${state.progress}%
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <p><strong>ID:</strong> ${data.agent_id}</p>
                    <p><strong>File ID:</strong> ${state.file_id || 'N/A'}</p>
                    <p><strong>Avviato:</strong> ${state.start_time || 'N/A'}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Completato:</strong> ${state.end_time || 'N/A'}</p>
                    <p><strong>Livello di Aggressività:</strong> ${state.aggression_level || 'N/A'}</p>
                    <p><strong>Tipi di Problemi:</strong> ${state.issue_types ? state.issue_types.join(', ') : 'N/A'}</p>
                </div>
            </div>
        `;

        if (state.error) {
            html += `
                <div class="alert alert-danger">
                    <h6>Errore:</h6>
                    <p>${state.error}</p>
                </div>
            `;
        }

        if (state.issues_found && state.issues_found.length > 0) {
            html += `
                <h6>Problemi Trovati (${state.issues_found.length}):</h6>
                <div class="agent-result">
                    <ul class="list-group">
            `;

            state.issues_found.forEach(issue => {
                html += `
                    <li class="list-group-item">
                        <span class="badge ${getIssueBadgeClass(issue.severity)}">${issue.severity}</span>
                        ${issue.description}
                    </li>
                `;
            });

            html += `
                    </ul>
                </div>
            `;
        }

        document.getElementById('agent-details-content').innerHTML = html;
        agentDetailsModal.show();
    }

    // Mostra il risultato di un agente
    function showAgentResult(data) {
        const result = data.result;

        let html = `
            <h5>Risultato dell'Agente</h5>

            <div class="alert alert-success">
                <p><strong>Problemi trovati:</strong> ${result.issues_count}</p>
                <p><strong>Correzioni suggerite:</strong> ${result.corrections_suggested_count}</p>
                <p><strong>Correzioni applicate:</strong> ${result.corrections_applied_count}</p>
            </div>
        `;

        if (result.cleaned_file_id) {
            html += `
                <div class="alert alert-info">
                    <p><strong>ID File Pulito:</strong> ${result.cleaned_file_id}</p>
                </div>
            `;
        }

        if (result.report) {
            html += `
                <h6>Report Dettagliato:</h6>
                <div class="agent-result">
                    <pre class="bg-light p-3">${JSON.stringify(result.report, null, 2)}</pre>
                </div>
            `;
        }

        document.getElementById('agent-details-content').innerHTML = html;
        agentDetailsModal.show();
    }

    // Ottieni la classe per il badge dello stato
    function getStatusBadgeClass(status) {
        switch (status) {
            case 'running':
                return 'bg-warning text-dark';
            case 'completed':
                return 'bg-success';
            case 'error':
                return 'bg-danger';
            default:
                return 'bg-secondary';
        }
    }

    // Ottieni la classe per la barra di progresso
    function getProgressBarClass(status) {
        switch (status) {
            case 'running':
                return 'progress-bar-striped progress-bar-animated';
            case 'completed':
                return 'bg-success';
            case 'error':
                return 'bg-danger';
            default:
                return '';
        }
    }

    // Ottieni la classe per il badge della gravità del problema
    function getIssueBadgeClass(severity) {
        switch (severity) {
            case 'high':
                return 'bg-danger';
            case 'medium':
                return 'bg-warning text-dark';
            case 'low':
                return 'bg-info';
            default:
                return 'bg-secondary';
        }
    }

    // Imposta la larghezza delle barre di progresso dinamicamente
    document.addEventListener('DOMContentLoaded', function() {
        const progressBars = document.querySelectorAll('.progress-width-dynamic');
        progressBars.forEach(bar => {
            const progress = parseInt(bar.getAttribute('data-progress') || '0');
            bar.style.width = progress + '%';
        });
    });
</script>
{% endblock %}






































