# Piano di Sviluppo Web App per Analisi Dati Aziendali

## Panoramica
Questo documento descrive il piano di sviluppo per la web app di analisi dati aziendali, che elaborerà dati finanziari, presenze e attività tecniche.

## Fasi di Sviluppo

### Fase 1: Configurazione Iniziale e Sistema di Importazione Dati
- [x] Creazione struttura base del progetto Flask
- [x] Implementazione interfaccia di upload con drag-and-drop
- [x] Gestione file CSV (separatore ;) ed Excel (.xlsx)
- [x] Validazione dei file caricati
- [x] Anteprima dei dati importati

### Fase 2: Elaborazione e Standardizzazione Dati
- [ ] Sviluppo parser per identificazione automatica delle intestazioni
- [ ] Implementazione gestione formato data italiano (GG/MM/AAAA)
- [ ] Gestione separatori decimali italiani (virgola)
- [ ] Sistema di mappatura campi per standardizzazione dati

### Fase 3: Analisi Dati e Visualizzazione
- [ ] Creazione dashboard con KPI principali
- [ ] Implementazione grafici temporali interattivi
- [ ] Sviluppo report specifici (presenze, produttività, correlazioni)
- [ ] Filtri dinamici per date, dipendenti e tipologie di attività

### Fase 4: Esportazione e Funzionalità Avanzate
- [ ] Funzionalità di esportazione in Excel (.xlsx)
- [ ] Esportazione in PDF con layout ottimizzato
- [ ] Esportazione in CSV per ulteriori elaborazioni
- [ ] Ottimizzazione performance e usabilità

## Sistema di Configurazione
- Configurazione costi dipendenti con persistenza
- Gestione flotta veicoli aziendali
- Impostazioni IVA e valute
- Interfaccia web integrata nel dashboard principale

## Dipendenze Principali
- Flask (framework web)
- Pandas (elaborazione dati)
- Plotly/Chart.js (visualizzazione dati)
- Bootstrap/Tailwind CSS (interfaccia utente)
- ReportLab/WeasyPrint (generazione PDF)
- OpenPyXL (gestione file Excel)
