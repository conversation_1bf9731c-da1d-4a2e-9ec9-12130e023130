#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test specifico per il riconoscimento del file progetti.
"""

import pandas as pd
import sys
import os
from pathlib import Path

# Aggiungi il percorso del progetto
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_file_detector import EnhancedFileDetector
from universal_file_reader import UniversalFileReader

def test_progetti_recognition():
    """
    Testa specificamente il riconoscimento del file progetti.
    """
    print("🎯 TEST RICONOSCIMENTO FILE PROGETTI")
    print("=" * 50)
    
    file_path = "test_file_grezzi/progetti_230525.xlsx"
    
    if not Path(file_path).exists():
        print(f"❌ File non trovato: {file_path}")
        return
    
    # Inizializza componenti
    file_detector = EnhancedFileDetector()
    universal_reader = UniversalFileReader()
    
    try:
        # 1. Lettura file
        print(f"📖 Leggendo file: {file_path}")
        df, file_info = universal_reader.read_file(file_path)
        
        if not file_info["success"]:
            print(f"❌ Errore lettura: {file_info.get('error', 'Sconosciuto')}")
            return
        
        print(f"✅ File letto: {len(df)} righe, {len(df.columns)} colonne")
        print(f"📋 Colonne originali: {df.columns.tolist()}")
        print()
        
        # 2. Test riconoscimento
        print("🔍 Eseguendo riconoscimento tipo file...")
        detected_type, confidence, type_scores = file_detector.detect_file_type(df)
        
        print(f"🎯 RISULTATO:")
        print(f"   Tipo rilevato: {detected_type}")
        print(f"   Confidenza: {confidence:.3f}")
        print()
        
        # 3. Mostra tutti i punteggi
        print("📊 PUNTEGGI PER TUTTI I TIPI:")
        sorted_scores = sorted(type_scores.items(), key=lambda x: x[1], reverse=True)
        for file_type, score in sorted_scores:
            status = "✅" if file_type == "progetti" else "  "
            print(f"   {status} {file_type}: {score:.3f}")
        print()
        
        # 4. Verifica se progetti è il primo
        if detected_type == "progetti":
            print("🎉 SUCCESSO! File progetti riconosciuto correttamente!")
        else:
            print(f"⚠️ ATTENZIONE: File riconosciuto come '{detected_type}' invece di 'progetti'")
            
            # Mostra il punteggio progetti
            progetti_score = type_scores.get("progetti", 0.0)
            print(f"   Punteggio progetti: {progetti_score:.3f}")
            print(f"   Differenza: {confidence - progetti_score:.3f}")
        
        # 5. Test mappatura colonne
        print("\n🗺️ MAPPATURA COLONNE SUGGERITA:")
        mapping = file_detector.get_column_mapping_suggestions(df, "progetti")
        if mapping:
            for col, pattern in mapping.items():
                print(f"   {col} -> {pattern}")
        else:
            print("   Nessuna mappatura suggerita")
        
        # 6. Analisi dettagliata pattern progetti
        print("\n🔍 ANALISI PATTERN PROGETTI:")
        progetti_signature = file_detector.FILE_SIGNATURES.get("progetti", {})
        required_patterns = progetti_signature.get("required_patterns", [])
        optional_patterns = progetti_signature.get("optional_patterns", [])
        
        df_columns = [file_detector._normalize_column_name(col) for col in df.columns]
        print(f"Colonne normalizzate: {df_columns}")
        
        print("\nPattern richiesti:")
        for i, pattern_list in enumerate(required_patterns):
            found = file_detector._match_pattern_in_columns(pattern_list, df_columns)
            status = "✅" if found else "❌"
            print(f"   {status} {pattern_list}")
        
        print("\nPattern opzionali:")
        for i, pattern_list in enumerate(optional_patterns):
            found = file_detector._match_pattern_in_columns(pattern_list, df_columns)
            status = "✅" if found else "  "
            print(f"   {status} {pattern_list}")
        
    except Exception as e:
        print(f"❌ Errore durante il test: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_progetti_recognition()
