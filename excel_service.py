#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import time
import logging
import pandas as pd
import traceback
from typing import Optional, Tuple, List, Dict, Any, Union

# Importa le librerie per COM
try:
    import win32com.client
    import pythoncom
    from win32com.client import constants as const
    COM_AVAILABLE = True
except ImportError:
    COM_AVAILABLE = False
    logging.warning("Librerie COM non disponibili. Alcune funzionalità Excel potrebbero non funzionare.")

# Configura il logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('excel_service')

class ExcelService:
    """
    Servizio per interagire con Excel tramite il protocollo COM.
    Fornisce funzionalità per leggere, scrivere e manipolare file Excel.
    """
    
    def __init__(self, visible: bool = False):
        """
        Inizializza il servizio Excel.
        
        Args:
            visible (bool): Se True, rende visibile l'applicazione Excel.
        """
        self.excel_app = None
        self.workbooks = {}  # Dizionario per tenere traccia dei workbook aperti
        self.visible = visible
        self._initialize()
    
    def _initialize(self) -> None:
        """
        Inizializza la connessione COM con Excel.
        """
        if not COM_AVAILABLE:
            logger.error("Impossibile inizializzare Excel: librerie COM non disponibili")
            return
        
        try:
            # Inizializza il thread COM
            pythoncom.CoInitialize()
            
            # Crea l'oggetto Excel.Application
            self.excel_app = win32com.client.Dispatch("Excel.Application")
            self.excel_app.Visible = self.visible
            self.excel_app.DisplayAlerts = False  # Disabilita gli alert
            
            logger.info(f"Excel inizializzato (versione: {self.excel_app.Version})")
        except Exception as e:
            logger.error(f"Errore nell'inizializzazione di Excel: {str(e)}")
            self.excel_app = None
            raise
    
    def is_available(self) -> bool:
        """
        Verifica se Excel è disponibile.
        
        Returns:
            bool: True se Excel è disponibile, False altrimenti.
        """
        return COM_AVAILABLE and self.excel_app is not None
    
    def open_workbook(self, file_path: str) -> Optional[object]:
        """
        Apre un file Excel esistente.
        
        Args:
            file_path (str): Percorso completo del file Excel da aprire.
            
        Returns:
            object: L'oggetto Workbook se l'apertura ha successo, None altrimenti.
        """
        if not self.is_available():
            logger.error("Excel non disponibile")
            return None
        
        try:
            # Verifica se il file esiste
            if not os.path.exists(file_path):
                logger.error(f"Il file {file_path} non esiste")
                return None
            
            # Normalizza il percorso
            abs_path = os.path.abspath(file_path)
            
            # Verifica se il workbook è già aperto
            if abs_path in self.workbooks:
                logger.info(f"Il file {abs_path} è già aperto")
                return self.workbooks[abs_path]
            
            # Apri il workbook
            workbook = self.excel_app.Workbooks.Open(abs_path)
            self.workbooks[abs_path] = workbook
            
            logger.info(f"File Excel aperto: {abs_path}")
            return workbook
        except Exception as e:
            logger.error(f"Errore nell'apertura del file Excel {file_path}: {str(e)}")
            logger.error(traceback.format_exc())
            return None
    
    def create_workbook(self) -> Optional[object]:
        """
        Crea un nuovo file Excel.
        
        Returns:
            object: L'oggetto Workbook se la creazione ha successo, None altrimenti.
        """
        if not self.is_available():
            logger.error("Excel non disponibile")
            return None
        
        try:
            # Crea un nuovo workbook
            workbook = self.excel_app.Workbooks.Add()
            
            logger.info("Nuovo file Excel creato")
            return workbook
        except Exception as e:
            logger.error(f"Errore nella creazione del file Excel: {str(e)}")
            logger.error(traceback.format_exc())
            return None
    
    def save_workbook(self, workbook: object, file_path: str) -> bool:
        """
        Salva un workbook Excel.
        
        Args:
            workbook (object): L'oggetto Workbook da salvare.
            file_path (str): Percorso dove salvare il file.
            
        Returns:
            bool: True se il salvataggio ha successo, False altrimenti.
        """
        if not self.is_available():
            logger.error("Excel non disponibile")
            return False
        
        try:
            # Normalizza il percorso
            abs_path = os.path.abspath(file_path)
            
            # Salva il workbook
            workbook.SaveAs(abs_path)
            self.workbooks[abs_path] = workbook
            
            logger.info(f"File Excel salvato: {abs_path}")
            return True
        except Exception as e:
            logger.error(f"Errore nel salvataggio del file Excel {file_path}: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def close_workbook(self, workbook: object, save_changes: bool = True) -> bool:
        """
        Chiude un workbook Excel.
        
        Args:
            workbook (object): L'oggetto Workbook da chiudere.
            save_changes (bool): Se True, salva le modifiche prima di chiudere.
            
        Returns:
            bool: True se la chiusura ha successo, False altrimenti.
        """
        if not self.is_available():
            logger.error("Excel non disponibile")
            return False
        
        try:
            # Trova il percorso del workbook
            workbook_path = None
            for path, wb in self.workbooks.items():
                if wb == workbook:
                    workbook_path = path
                    break
            
            # Chiudi il workbook
            workbook.Close(SaveChanges=save_changes)
            
            # Rimuovi dal dizionario
            if workbook_path:
                del self.workbooks[workbook_path]
            
            logger.info(f"File Excel chiuso: {workbook_path}")
            return True
        except Exception as e:
            logger.error(f"Errore nella chiusura del file Excel: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def read_worksheet(self, workbook: object, sheet_name: Optional[str] = None, sheet_index: int = 0) -> Optional[pd.DataFrame]:
        """
        Legge un foglio di lavoro da un workbook Excel e lo converte in DataFrame.
        
        Args:
            workbook (object): L'oggetto Workbook da cui leggere.
            sheet_name (str, optional): Nome del foglio di lavoro da leggere.
            sheet_index (int): Indice del foglio di lavoro (usato se sheet_name è None).
            
        Returns:
            pd.DataFrame: DataFrame contenente i dati del foglio di lavoro, None in caso di errore.
        """
        if not self.is_available():
            logger.error("Excel non disponibile")
            return None
        
        try:
            # Seleziona il foglio di lavoro
            if sheet_name:
                try:
                    worksheet = workbook.Worksheets(sheet_name)
                except:
                    logger.error(f"Foglio di lavoro '{sheet_name}' non trovato")
                    return None
            else:
                worksheet = workbook.Worksheets(sheet_index + 1)  # Excel è 1-based
            
            # Ottieni l'area utilizzata
            used_range = worksheet.UsedRange
            
            # Leggi i valori
            values = used_range.Value
            
            # Converti in DataFrame
            if values:
                # Determina le dimensioni dell'array
                if isinstance(values, tuple):
                    # Se è una singola riga o colonna
                    if len(values) == 1:
                        data = [list(values)]
                    else:
                        data = [list(row) for row in values]
                else:
                    # Caso singola cella
                    data = [[values]]
                
                # Crea il DataFrame
                df = pd.DataFrame(data)
                
                # Usa la prima riga come intestazione
                if not df.empty:
                    df.columns = df.iloc[0]
                    df = df.iloc[1:].reset_index(drop=True)
                
                logger.info(f"Foglio di lavoro '{worksheet.Name}' letto con successo")
                return df
            else:
                logger.warning(f"Foglio di lavoro '{worksheet.Name}' vuoto")
                return pd.DataFrame()
        except Exception as e:
            logger.error(f"Errore nella lettura del foglio di lavoro: {str(e)}")
            logger.error(traceback.format_exc())
            return None
    
    def write_dataframe(self, workbook: object, df: pd.DataFrame, sheet_name: Optional[str] = None, 
                       sheet_index: int = 0, include_header: bool = True, start_cell: str = "A1") -> bool:
        """
        Scrive un DataFrame in un foglio di lavoro Excel.
        
        Args:
            workbook (object): L'oggetto Workbook in cui scrivere.
            df (pd.DataFrame): DataFrame da scrivere.
            sheet_name (str, optional): Nome del foglio di lavoro.
            sheet_index (int): Indice del foglio di lavoro (usato se sheet_name è None).
            include_header (bool): Se True, include l'intestazione del DataFrame.
            start_cell (str): Cella di inizio per la scrittura (es. "A1").
            
        Returns:
            bool: True se la scrittura ha successo, False altrimenti.
        """
        if not self.is_available():
            logger.error("Excel non disponibile")
            return False
        
        try:
            # Seleziona o crea il foglio di lavoro
            if sheet_name:
                try:
                    worksheet = workbook.Worksheets(sheet_name)
                except:
                    # Crea un nuovo foglio con il nome specificato
                    worksheet = workbook.Worksheets.Add()
                    worksheet.Name = sheet_name
            else:
                try:
                    worksheet = workbook.Worksheets(sheet_index + 1)  # Excel è 1-based
                except:
                    # Aggiungi fogli fino a raggiungere l'indice desiderato
                    while workbook.Worksheets.Count < sheet_index + 1:
                        workbook.Worksheets.Add()
                    worksheet = workbook.Worksheets(sheet_index + 1)
            
            # Converti il DataFrame in una lista di liste
            if include_header:
                data = [df.columns.tolist()] + df.values.tolist()
            else:
                data = df.values.tolist()
            
            # Determina l'intervallo di celle
            start_row, start_col = self._cell_to_indices(start_cell)
            end_row = start_row + len(data) - 1
            end_col = start_col + len(data[0]) - 1 if data else start_col
            
            # Converti gli indici in lettere di colonna
            start_col_letter = self._index_to_column_letter(start_col)
            end_col_letter = self._index_to_column_letter(end_col)
            
            # Definisci l'intervallo
            range_str = f"{start_col_letter}{start_row}:{end_col_letter}{end_row}"
            cell_range = worksheet.Range(range_str)
            
            # Scrivi i dati
            cell_range.Value = data
            
            # Formatta l'intestazione se inclusa
            if include_header:
                header_range = worksheet.Range(f"{start_col_letter}{start_row}:{end_col_letter}{start_row}")
                header_range.Font.Bold = True
                
                # Adatta automaticamente la larghezza delle colonne
                worksheet.Columns.AutoFit()
            
            logger.info(f"DataFrame scritto nel foglio '{worksheet.Name}' con successo")
            return True
        except Exception as e:
            logger.error(f"Errore nella scrittura del DataFrame: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def _cell_to_indices(self, cell_ref: str) -> Tuple[int, int]:
        """
        Converte un riferimento di cella (es. "A1") in indici di riga e colonna.
        
        Args:
            cell_ref (str): Riferimento della cella (es. "A1").
            
        Returns:
            Tuple[int, int]: Coppia (riga, colonna) di indici.
        """
        # Separa lettere e numeri
        col_str = ''.join(c for c in cell_ref if c.isalpha()).upper()
        row_str = ''.join(c for c in cell_ref if c.isdigit())
        
        # Converti la riga in indice
        row = int(row_str)
        
        # Converti la colonna in indice
        col = 0
        for c in col_str:
            col = col * 26 + (ord(c) - ord('A') + 1)
        
        return row, col
    
    def _index_to_column_letter(self, index: int) -> str:
        """
        Converte un indice di colonna in lettera (es. 1 -> "A", 27 -> "AA").
        
        Args:
            index (int): Indice della colonna.
            
        Returns:
            str: Lettera della colonna.
        """
        result = ""
        while index > 0:
            index, remainder = divmod(index - 1, 26)
            result = chr(65 + remainder) + result
        return result
    
    def close(self) -> None:
        """
        Chiude tutti i workbook aperti e l'applicazione Excel.
        """
        if not self.is_available():
            return
        
        try:
            # Chiudi tutti i workbook aperti
            for path, workbook in list(self.workbooks.items()):
                try:
                    workbook.Close(SaveChanges=False)
                except:
                    pass
            
            self.workbooks.clear()
            
            # Chiudi Excel
            self.excel_app.Quit()
            self.excel_app = None
            
            # Rilascia le risorse COM
            pythoncom.CoUninitialize()
            
            logger.info("Excel chiuso correttamente")
        except Exception as e:
            logger.error(f"Errore nella chiusura di Excel: {str(e)}")
            logger.error(traceback.format_exc())
    
    def __del__(self):
        """
        Distruttore della classe. Assicura che Excel venga chiuso correttamente.
        """
        self.close()
