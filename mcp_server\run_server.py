#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per avviare il server MCP.
Questo script è progettato per essere eseguito direttamente e avvia il server FastAPI.
"""

import uvicorn
import logging

if __name__ == "__main__":
    print("Avvio del server MCP...")

    # Configura il logger per ridurre i messaggi di avviso non necessari
    logging.getLogger("uvicorn.error").setLevel(logging.ERROR)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)

    # Utilizziamo 127.0.0.1 per localhost e porta 8000 (standard MCP)
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="error",
        access_log=False
    )
