#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
🧪 TEST APP OPERATIVA - APP ROBERTO
Sistema di testing per app già avviata - focus su funzionalità produttive.

OBIETTIVO: Verificare che tutte le funzionalità necessarie per il lavoro produttivo siano operative.
"""

import asyncio
import aiohttp
import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Tuple, Any

class AppOperativaTestSuite:
    """Suite di test per app già operativa."""

    def __init__(self, base_url: str = "http://127.0.0.1:5001"):
        self.base_url = base_url
        self.test_results = {}
        self.critical_errors = []
        self.warnings = []

        # TEST CRITICI PER LAVORO PRODUTTIVO
        self.critical_tests = [
            # FUNZIONALITÀ DATABASE QUOTIDIANO
            ("GET", "/api/health", "Health Check Sistema"),
            ("GET", "/api/database/status", "Status Database"),
            ("GET", "/api/config/employees", "Configurazione Dipendenti"),

            # FUNZIONALITÀ UPLOAD E ELABORAZIONE
            ("GET", "/api/data", "API Dati Grezzi"),
            ("GET", "/api/processed_data", "API Dati Elaborati"),
            ("GET", "/api/dashboard_data", "API Dashboard Data"),

            # FUNZIONALITÀ ANALISI
            ("GET", "/api/chart_data?x_column=data&type=bar", "Generazione Grafici"),
            ("POST", "/api/intelligent-system/analyze", "Sistema Analisi Intelligente"),

            # FUNZIONALITÀ AGENTI AI
            ("GET", "/api/agents/status", "Status Agenti AI"),
            ("GET", "/api/automation/rules", "Regole Automazione"),

            # INTERFACCE UTENTE CRITICHE
            ("GET", "/", "Homepage"),
            ("GET", "/dashboard", "Dashboard Standard"),
            ("GET", "/interactive-charts", "Grafici Interattivi"),
            ("GET", "/setup-wizard", "Setup Wizard"),
        ]

        # WORKFLOW PRODUTTIVI DA TESTARE
        self.production_workflows = [
            "database_daily_update_workflow",
            "file_upload_processing_workflow",
            "data_analysis_workflow",
            "chart_generation_workflow",
            "ai_agents_workflow"
        ]

    def log_result(self, test_name: str, status: str, details: str = "", error: str = ""):
        """Registra risultato test."""
        self.test_results[test_name] = {
            'status': status,
            'details': details,
            'error': error,
            'timestamp': datetime.now().isoformat()
        }

        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} {test_name}: {status}")
        if details:
            print(f"   📝 {details}")
        if error:
            print(f"   🚨 {error}")
            if status == "FAIL":
                self.critical_errors.append(f"{test_name}: {error}")
            else:
                self.warnings.append(f"{test_name}: {error}")

    async def test_app_connectivity(self):
        """Verifica connettività base dell'app."""
        print("🔗 VERIFICA CONNETTIVITÀ APP")
        print("=" * 40)

        try:
            response = requests.get(f"{self.base_url}/api/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                status = data.get('data', {}).get('status', 'unknown')
                self.log_result("app_connectivity", "PASS",
                              f"App connessa - Status: {status}")
                return True
            elif response.status_code == 503:
                # Status 503 può indicare sistema degraded ma funzionante
                try:
                    data = response.json()
                    if data.get('success'):
                        status = data.get('data', {}).get('status', 'degraded')
                        self.log_result("app_connectivity", "WARN",
                                      f"App connessa ma degraded - Status: {status}")
                        return True  # Accetta degraded come operativo
                except:
                    pass
                self.log_result("app_connectivity", "FAIL",
                              f"Health check degraded: {response.status_code}")
                return False
            else:
                self.log_result("app_connectivity", "FAIL",
                              f"Health check fallito: {response.status_code}")
                return False
        except Exception as e:
            self.log_result("app_connectivity", "FAIL",
                          f"Impossibile connettersi: {str(e)}")
            return False

    async def test_critical_endpoints(self):
        """Testa endpoint critici per produzione."""
        print("\n🎯 TEST ENDPOINT CRITICI")
        print("=" * 40)

        async with aiohttp.ClientSession() as session:
            for method, endpoint, description in self.critical_tests:
                await self.test_single_endpoint(session, method, endpoint, description)

    async def test_single_endpoint(self, session, method: str, endpoint: str, description: str):
        """Testa un singolo endpoint."""
        test_name = f"endpoint_{description.lower().replace(' ', '_')}"

        try:
            url = f"{self.base_url}{endpoint}" if endpoint.startswith('/') else f"{self.base_url}/{endpoint}"

            if method == "GET":
                async with session.get(url, timeout=15) as response:
                    status_code = response.status
                    content = await response.text()

            elif method == "POST":
                test_data = {"test": True, "source": "production_test"}
                async with session.post(url, json=test_data, timeout=15) as response:
                    status_code = response.status
                    content = await response.text()

            # Valuta risultato
            if status_code == 200:
                self.log_result(test_name, "PASS", f"{description} - OK")
            elif status_code in [400, 404] and self.is_acceptable_error(endpoint, status_code):
                self.log_result(test_name, "WARN", f"{description} - {status_code} (accettabile)")
            else:
                self.log_result(test_name, "FAIL", f"{description} - Status: {status_code}",
                              content[:200] if content else "No content")

        except Exception as e:
            self.log_result(test_name, "FAIL", f"{description} - Errore", str(e))

    def is_acceptable_error(self, endpoint: str, status_code: int) -> bool:
        """Determina se un errore è accettabile."""
        acceptable = {
            "/api/data": [404],  # Normale se non ci sono dati
            "/api/processed_data": [404],  # Normale se non ci sono dati elaborati
            "/api/chart_data": [400],  # Normale se mancano parametri
        }
        return status_code in acceptable.get(endpoint, [])

    async def test_production_workflows(self):
        """Testa workflow produttivi."""
        print("\n🏭 TEST WORKFLOW PRODUTTIVI")
        print("=" * 40)

        for workflow in self.production_workflows:
            await self.test_workflow(workflow)

    async def test_workflow(self, workflow_name: str):
        """Testa un workflow specifico."""
        if workflow_name == "database_daily_update_workflow":
            await self.test_database_daily_workflow()
        elif workflow_name == "file_upload_processing_workflow":
            await self.test_file_upload_workflow()
        elif workflow_name == "data_analysis_workflow":
            await self.test_data_analysis_workflow()
        elif workflow_name == "chart_generation_workflow":
            await self.test_chart_generation_workflow()
        elif workflow_name == "ai_agents_workflow":
            await self.test_ai_agents_workflow()

    async def test_database_daily_workflow(self):
        """Testa workflow aggiornamento database quotidiano."""
        test_name = "database_daily_workflow"

        try:
            # Verifica status database
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/database/status") as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('success'):
                            self.log_result(test_name, "PASS", "Database pronto per aggiornamenti quotidiani")
                        else:
                            self.log_result(test_name, "WARN", "Database risponde ma con warning")
                    else:
                        self.log_result(test_name, "FAIL", f"Database status: {response.status}")
        except Exception as e:
            self.log_result(test_name, "FAIL", "Errore test database", str(e))

    async def test_file_upload_workflow(self):
        """Testa workflow upload e elaborazione file."""
        test_name = "file_upload_workflow"

        try:
            # Simula test upload (senza file reale)
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.base_url}/upload") as response:
                    # 400/422 è normale senza file
                    if response.status in [400, 422]:
                        self.log_result(test_name, "PASS", "Endpoint upload funzionante (gestisce errori correttamente)")
                    elif response.status == 200:
                        self.log_result(test_name, "PASS", "Endpoint upload completamente funzionante")
                    else:
                        self.log_result(test_name, "FAIL", f"Upload endpoint: {response.status}")
        except Exception as e:
            self.log_result(test_name, "FAIL", "Errore test upload", str(e))

    async def test_data_analysis_workflow(self):
        """Testa workflow analisi dati."""
        test_name = "data_analysis_workflow"

        try:
            # Test sistema analisi intelligente
            async with aiohttp.ClientSession() as session:
                test_data = {"data": {"test": "analysis"}, "type": "production_test"}
                async with session.post(f"{self.base_url}/api/intelligent-system/analyze",
                                      json=test_data) as response:
                    if response.status in [200, 400]:  # 400 può essere normale per dati test
                        self.log_result(test_name, "PASS", "Sistema analisi intelligente operativo")
                    else:
                        self.log_result(test_name, "FAIL", f"Analisi intelligente: {response.status}")
        except Exception as e:
            self.log_result(test_name, "FAIL", "Errore test analisi", str(e))

    async def test_chart_generation_workflow(self):
        """Testa workflow generazione grafici."""
        test_name = "chart_generation_workflow"

        try:
            # Test generazione grafici con parametri
            async with aiohttp.ClientSession() as session:
                url = f"{self.base_url}/api/chart_data?x_column=data&type=bar"
                async with session.get(url) as response:
                    if response.status in [200, 400]:  # 400 normale se non ci sono dati
                        self.log_result(test_name, "PASS", "Sistema generazione grafici operativo")
                    else:
                        self.log_result(test_name, "FAIL", f"Generazione grafici: {response.status}")
        except Exception as e:
            self.log_result(test_name, "FAIL", "Errore test grafici", str(e))

    async def test_ai_agents_workflow(self):
        """Testa workflow agenti AI."""
        test_name = "ai_agents_workflow"

        try:
            # Test status agenti
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.base_url}/api/agents/status") as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('success'):
                            self.log_result(test_name, "PASS", "Agenti AI completamente operativi")
                        else:
                            self.log_result(test_name, "WARN", "Agenti AI parzialmente operativi")
                    else:
                        self.log_result(test_name, "FAIL", f"Agenti AI: {response.status}")
        except Exception as e:
            self.log_result(test_name, "FAIL", "Errore test agenti AI", str(e))

    def generate_production_readiness_report(self):
        """Genera report di prontezza per produzione."""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results.values() if r['status'] == 'PASS')
        failed_tests = sum(1 for r in self.test_results.values() if r['status'] == 'FAIL')
        warning_tests = sum(1 for r in self.test_results.values() if r['status'] == 'WARN')

        # Calcola prontezza produzione
        production_readiness = passed_tests / total_tests * 100

        report = f"""# 🏭 REPORT PRONTEZZA PRODUZIONE - APP ROBERTO

## 📊 RISULTATI TESTING PRODUTTIVO

- **Test totali**: {total_tests}
- **✅ PASSED**: {passed_tests} ({passed_tests/total_tests*100:.1f}%)
- **❌ FAILED**: {failed_tests} ({failed_tests/total_tests*100:.1f}%)
- **⚠️ WARNING**: {warning_tests} ({warning_tests/total_tests*100:.1f}%)

## 🎯 PRONTEZZA PRODUZIONE: {production_readiness:.1f}%

"""

        if production_readiness >= 90:
            report += "✅ **SISTEMA PRONTO PER PRODUZIONE**\n"
            report += "🚀 **Tutte le funzionalità critiche operative**\n\n"
        elif production_readiness >= 75:
            report += "⚠️ **SISTEMA QUASI PRONTO - CORREZIONI MINORI RICHIESTE**\n"
            report += "🔧 **Risolvere errori critici prima di procedere**\n\n"
        else:
            report += "❌ **SISTEMA NON PRONTO - CORREZIONI CRITICHE RICHIESTE**\n"
            report += "🚨 **Risolvere tutti gli errori prima del lavoro produttivo**\n\n"

        # Errori critici
        report += "## 🚨 ERRORI CRITICI\n\n"
        if self.critical_errors:
            for error in self.critical_errors:
                report += f"- ❌ {error}\n"
        else:
            report += "- ✅ Nessun errore critico\n"

        # Warning
        report += "\n## ⚠️ WARNING\n\n"
        if self.warnings:
            for warning in self.warnings:
                report += f"- ⚠️ {warning}\n"
        else:
            report += "- ✅ Nessun warning\n"

        # Raccomandazioni
        report += f"""
## 🎯 RACCOMANDAZIONI PER PRODUZIONE

### 🚀 PROSSIMI PASSI
1. **Database quotidiano**: {'✅ Pronto' if failed_tests == 0 else '🔧 Richiede correzioni'}
2. **Upload automatico**: {'✅ Operativo' if 'file_upload_workflow' in [k for k, v in self.test_results.items() if v['status'] == 'PASS'] else '🔧 Da verificare'}
3. **Analisi automatiche**: {'✅ Funzionanti' if 'data_analysis_workflow' in [k for k, v in self.test_results.items() if v['status'] == 'PASS'] else '🔧 Da configurare'}
4. **Grafici automatici**: {'✅ Operativi' if 'chart_generation_workflow' in [k for k, v in self.test_results.items() if v['status'] == 'PASS'] else '🔧 Da testare'}

### 📋 WORKFLOW PRODUTTIVO SUGGERITO
1. **Mattina**: Upload file giornalieri via Setup Wizard
2. **Elaborazione**: Sistema analizza automaticamente i dati
3. **Dashboard**: Visualizza risultati e grafici aggiornati
4. **Agenti AI**: Eseguono analisi avanzate e raccomandazioni

---
**Report generato**: {datetime.now().isoformat()}
**Modalità**: Testing app operativa
"""

        # Salva report
        with open("PRODUCTION_READINESS_REPORT.md", "w", encoding="utf-8") as f:
            f.write(report)

        print(f"\n📄 Report salvato: PRODUCTION_READINESS_REPORT.md")
        return production_readiness

    async def run_production_testing(self):
        """Esegue testing completo per prontezza produzione."""
        print("🏭 TESTING PRONTEZZA PRODUZIONE APP-ROBERTO")
        print("🎯 OBIETTIVO: Verificare operatività per lavoro quotidiano")
        print("=" * 60)

        # 1. Verifica connettività
        if not await self.test_app_connectivity():
            print("❌ ERRORE: App non raggiungibile")
            return False

        # 2. Test endpoint critici
        await self.test_critical_endpoints()

        # 3. Test workflow produttivi
        await self.test_production_workflows()

        # 4. Genera report
        readiness = self.generate_production_readiness_report()

        print("\n" + "="*60)
        print("🎉 TESTING PRODUZIONE COMPLETATO")
        print("="*60)
        print(f"🎯 Prontezza produzione: {readiness:.1f}%")
        print(f"🚨 Errori critici: {len(self.critical_errors)}")
        print(f"⚠️ Warning: {len(self.warnings)}")
        print("📄 Report: PRODUCTION_READINESS_REPORT.md")
        print("="*60)

        return readiness >= 75


async def main():
    """Funzione principale."""
    print("🧪 AVVIO TEST APP OPERATIVA")
    print("📋 Assicurati che App-Roberto sia già avviata su porta 5001")
    print()

    suite = AppOperativaTestSuite()
    success = await suite.run_production_testing()

    if success:
        print("\n🎉 SISTEMA PRONTO PER LAVORO PRODUTTIVO!")
    else:
        print("\n🔧 SISTEMA RICHIEDE CORREZIONI PRIMA DELLA PRODUZIONE")

    return success


if __name__ == "__main__":
    asyncio.run(main())
