@echo off
echo 🎯 BACKUP VS CODE - APP ROBERTO
echo ===============================

:: Crea cartella backup specifica
set BACKUP_DIR=%USERPROFILE%\Desktop\VSCode_AppRoberto_Backup_%DATE:~-4,4%%DATE:~-10,2%%DATE:~-7,2%
mkdir "%BACKUP_DIR%"

echo 📁 Backup specifico App-Roberto in: %BACKUP_DIR%

:: Backup impostazioni VS Code
echo 📄 Copiando settings.json...
copy "%APPDATA%\Code\User\settings.json" "%BACKUP_DIR%\"

echo 🔧 Copiando keybindings.json...
copy "%APPDATA%\Code\User\keybindings.json" "%BACKUP_DIR%\"

:: Backup configurazione workspace App-Roberto
if exist ".vscode" (
    echo 📁 Copiando configurazione workspace App-Roberto...
    xcopy ".vscode\*" "%BACKUP_DIR%\.vscode\" /E /I /Y
)

:: Backup estensioni Python/Flask specifiche
echo 📦 Esportando estensioni Python/Flask...
(
echo # Estensioni essenziali per App-Roberto
echo ms-python.python
echo ms-python.flake8
echo ms-python.pylint
echo ms-python.black-formatter
echo ms-vscode.vscode-json
echo bradlc.vscode-tailwindcss
echo formulahendry.auto-rename-tag
echo esbenp.prettier-vscode
echo ms-vscode.powershell
echo ms-vscode-remote.remote-containers
echo ms-azuretools.vscode-docker
echo redhat.vscode-yaml
echo ms-vscode.vscode-markdown
) > "%BACKUP_DIR%\extensions_app_roberto.txt"

:: Crea script ripristino specifico
echo 🔄 Creando script ripristino App-Roberto...
(
echo @echo off
echo echo 🎯 RIPRISTINO VS CODE - APP ROBERTO
echo echo ==================================
echo.
echo echo 📄 Ripristinando settings.json...
echo copy "%%~dp0settings.json" "%%APPDATA%%\Code\User\"
echo.
echo echo 🔧 Ripristinando keybindings.json...
echo copy "%%~dp0keybindings.json" "%%APPDATA%%\Code\User\"
echo.
echo if exist "%%~dp0\.vscode\" ^(
echo     echo 📁 Ripristinando configurazione workspace...
echo     xcopy "%%~dp0\.vscode\*" ".vscode\" /E /I /Y
echo ^)
echo.
echo echo 📦 Installando estensioni App-Roberto...
echo for /f %%%%i in ^(%%~dp0extensions_app_roberto.txt^) do ^(
echo     if not "%%%%i"=="" if not "%%%%i:~0,1%%"=="#" code --install-extension %%%%i
echo ^)
echo.
echo echo ✅ Ripristino App-Roberto completato!
echo echo 🎯 Riavvia VS Code per applicare le modifiche
echo pause
) > "%BACKUP_DIR%\ripristina_app_roberto.bat"

echo.
echo ✅ BACKUP APP-ROBERTO COMPLETATO!
echo 📁 Cartella: %BACKUP_DIR%
echo 🔄 Per ripristinare: esegui ripristina_app_roberto.bat nel progetto
echo.
pause
