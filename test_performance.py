#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FASE 6: Performance Testing App Roberto
Test di performance specifici per l'applicazione Flask.
Versione: 1.0.0 - FASE 6 Testing e Stabilizzazione
"""

import pytest
import time
import threading
import concurrent.futures
import statistics
import psutil
import gc
from datetime import datetime
from typing import List, Dict, Any
import sys
import os
from io import BytesIO

# Import dell'applicazione
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import app
    from health_monitor import health_monitor
    APP_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ App non disponibile per performance testing: {e}")
    APP_AVAILABLE = False

class PerformanceBenchmark:
    """Classe per benchmark di performance."""
    
    def __init__(self):
        self.results = {}
        self.start_times = {}
    
    def start_benchmark(self, name: str):
        """Avvia un benchmark."""
        self.start_times[name] = time.time()
    
    def end_benchmark(self, name: str) -> float:
        """Termina un benchmark e restituisce il tempo."""
        if name not in self.start_times:
            raise ValueError(f"Benchmark {name} non avviato")
        
        duration = time.time() - self.start_times[name]
        self.results[name] = duration
        return duration
    
    def get_results(self) -> Dict[str, float]:
        """Ottiene tutti i risultati."""
        return self.results.copy()

class TestPerformance:
    """Test di performance per App Roberto."""
    
    @pytest.fixture
    def client(self):
        """Client di test Flask."""
        if not APP_AVAILABLE:
            pytest.skip("App non disponibile")
        
        app.config['TESTING'] = True
        app.config['WTF_CSRF_ENABLED'] = False
        
        with app.test_client() as client:
            with app.app_context():
                yield client
    
    @pytest.fixture
    def benchmark(self):
        """Fixture per benchmark."""
        return PerformanceBenchmark()
    
    @pytest.mark.performance
    def test_home_page_response_time(self, client, benchmark):
        """Test tempo di risposta pagina home."""
        times = []
        
        # Warm-up
        client.get('/')
        
        # Misura 10 richieste
        for i in range(10):
            benchmark.start_benchmark(f"home_request_{i}")
            response = client.get('/')
            duration = benchmark.end_benchmark(f"home_request_{i}")
            
            assert response.status_code == 200
            times.append(duration)
        
        # Analisi risultati
        avg_time = statistics.mean(times)
        max_time = max(times)
        min_time = min(times)
        
        print(f"\n📊 Home Page Performance:")
        print(f"   Average: {avg_time:.3f}s")
        print(f"   Min: {min_time:.3f}s")
        print(f"   Max: {max_time:.3f}s")
        
        # Verifica soglie
        assert avg_time < 1.0, f"Tempo medio troppo alto: {avg_time:.3f}s"
        assert max_time < 2.0, f"Tempo massimo troppo alto: {max_time:.3f}s"
    
    @pytest.mark.performance
    def test_api_endpoints_performance(self, client, benchmark):
        """Test performance endpoint API."""
        endpoints = [
            '/api/health',
            '/api/endpoints'
        ]
        
        results = {}
        
        for endpoint in endpoints:
            times = []
            
            # Warm-up
            client.get(endpoint)
            
            # Misura 5 richieste per endpoint
            for i in range(5):
                benchmark.start_benchmark(f"{endpoint}_request_{i}")
                response = client.get(endpoint)
                duration = benchmark.end_benchmark(f"{endpoint}_request_{i}")
                
                assert response.status_code == 200
                times.append(duration)
            
            avg_time = statistics.mean(times)
            results[endpoint] = avg_time
            
            print(f"\n📊 {endpoint} Performance: {avg_time:.3f}s")
            
            # API dovrebbero essere veloci
            assert avg_time < 0.5, f"API {endpoint} troppo lenta: {avg_time:.3f}s"
        
        return results
    
    @pytest.mark.performance
    @pytest.mark.slow
    def test_concurrent_requests(self, client, benchmark):
        """Test richieste concorrenti."""
        num_threads = 10
        requests_per_thread = 5
        
        def make_requests(thread_id):
            """Funzione per thread di richieste."""
            times = []
            with app.test_client() as thread_client:
                for i in range(requests_per_thread):
                    start_time = time.time()
                    response = thread_client.get('/')
                    end_time = time.time()
                    
                    assert response.status_code == 200
                    times.append(end_time - start_time)
            
            return times
        
        # Esegui richieste concorrenti
        benchmark.start_benchmark("concurrent_requests")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [
                executor.submit(make_requests, i) 
                for i in range(num_threads)
            ]
            
            all_times = []
            for future in concurrent.futures.as_completed(futures):
                thread_times = future.result()
                all_times.extend(thread_times)
        
        total_duration = benchmark.end_benchmark("concurrent_requests")
        
        # Analisi risultati
        avg_time = statistics.mean(all_times)
        total_requests = num_threads * requests_per_thread
        requests_per_second = total_requests / total_duration
        
        print(f"\n📊 Concurrent Requests Performance:")
        print(f"   Total requests: {total_requests}")
        print(f"   Total time: {total_duration:.3f}s")
        print(f"   Requests/second: {requests_per_second:.1f}")
        print(f"   Average response time: {avg_time:.3f}s")
        
        # Verifica performance
        assert requests_per_second > 10, f"Throughput troppo basso: {requests_per_second:.1f} req/s"
        assert avg_time < 2.0, f"Tempo medio troppo alto: {avg_time:.3f}s"
    
    @pytest.mark.performance
    def test_memory_usage_stability(self, client):
        """Test stabilità utilizzo memoria."""
        process = psutil.Process()
        
        # Memoria iniziale
        gc.collect()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Esegui molte richieste
        for i in range(100):
            response = client.get('/')
            assert response.status_code == 200
            
            # Garbage collection periodico
            if i % 20 == 0:
                gc.collect()
        
        # Memoria finale
        gc.collect()
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"\n📊 Memory Stability:")
        print(f"   Initial: {initial_memory:.1f} MB")
        print(f"   Final: {final_memory:.1f} MB")
        print(f"   Increase: {memory_increase:.1f} MB")
        
        # Verifica che non ci siano memory leak significativi
        assert memory_increase < 50, f"Possibile memory leak: {memory_increase:.1f} MB"
    
    @pytest.mark.performance
    def test_static_files_performance(self, client, benchmark):
        """Test performance file statici."""
        static_files = [
            '/static/css/style.css',
            '/static/css/dark-theme.css',
            '/static/js/main.js',
            '/static/js/theme-manager.js',
            '/static/js/ui-enhancements.js'
        ]
        
        for file_path in static_files:
            times = []
            
            # Misura 3 richieste per file
            for i in range(3):
                benchmark.start_benchmark(f"static_{file_path}_{i}")
                response = client.get(file_path)
                duration = benchmark.end_benchmark(f"static_{file_path}_{i}")
                
                assert response.status_code == 200
                times.append(duration)
            
            avg_time = statistics.mean(times)
            print(f"📊 {file_path}: {avg_time:.3f}s")
            
            # File statici dovrebbero essere molto veloci
            assert avg_time < 0.1, f"File statico troppo lento: {file_path} - {avg_time:.3f}s"

class TestHealthMonitorPerformance:
    """Test performance Health Monitor."""
    
    @pytest.mark.performance
    def test_health_check_performance(self):
        """Test performance controllo salute."""
        if not APP_AVAILABLE:
            pytest.skip("App non disponibile")
        
        times = []
        
        # Misura 10 controlli salute
        for i in range(10):
            start_time = time.time()
            health = health_monitor.check_system_health()
            end_time = time.time()
            
            assert health.overall_status in ['healthy', 'warning', 'critical']
            times.append(end_time - start_time)
        
        avg_time = statistics.mean(times)
        max_time = max(times)
        
        print(f"\n📊 Health Check Performance:")
        print(f"   Average: {avg_time:.3f}s")
        print(f"   Max: {max_time:.3f}s")
        
        # Health check dovrebbe essere veloce
        assert avg_time < 0.5, f"Health check troppo lento: {avg_time:.3f}s"
        assert max_time < 1.0, f"Health check max troppo lento: {max_time:.3f}s"
    
    @pytest.mark.performance
    @pytest.mark.slow
    def test_monitoring_overhead(self):
        """Test overhead del monitoraggio."""
        if not APP_AVAILABLE:
            pytest.skip("App non disponibile")
        
        # Misura performance senza monitoraggio
        start_time = time.time()
        for _ in range(50):
            time.sleep(0.01)  # Simula lavoro
        baseline_time = time.time() - start_time
        
        # Avvia monitoraggio
        health_monitor.start_monitoring()
        
        try:
            # Misura performance con monitoraggio
            start_time = time.time()
            for _ in range(50):
                time.sleep(0.01)  # Simula lavoro
            monitoring_time = time.time() - start_time
            
        finally:
            health_monitor.stop_monitoring()
        
        overhead = ((monitoring_time - baseline_time) / baseline_time) * 100
        
        print(f"\n📊 Monitoring Overhead:")
        print(f"   Baseline: {baseline_time:.3f}s")
        print(f"   With monitoring: {monitoring_time:.3f}s")
        print(f"   Overhead: {overhead:.1f}%")
        
        # Overhead dovrebbe essere minimo
        assert overhead < 20, f"Overhead monitoraggio troppo alto: {overhead:.1f}%"

# Configurazione pytest
def pytest_configure(config):
    """Configurazione pytest per performance testing."""
    config.addinivalue_line("markers", "performance: marks tests as performance tests")
    config.addinivalue_line("markers", "slow: marks tests as slow")

if __name__ == "__main__":
    # Esegui solo test di performance
    pytest.main([__file__, "-v", "--tb=short", "-m", "performance"])
