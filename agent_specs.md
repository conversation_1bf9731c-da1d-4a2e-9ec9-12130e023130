# 🤖 Specifiche Agenti AI - Sistema di Riconoscimento Intelligente (FASE 6)

**Data:** 24 Maggio 2025
**Versione:** 2.0 - Aggiornato per Fase 6
**Stato:** Implementazione Avanzata

## 📋 Panoramica Sistema Agentico Avanzato

Il sistema agentico implementa **4 agenti AI specializzati** di nuova generazione che utilizzano il framework già implementato nella Fase 4, con integrazione completa con Enhanced LLM Assistant, Cross-Analysis Engine, e Advanced Database Manager. Gli agenti sono progettati per automatizzare task complessi e fornire insights avanzati sui dati aziendali.

## 🏗️ Architettura Agenti Avanzata

### Framework Tecnologico (Implementato)
- **Base**: Sistema Agenti Intelligenti (Fase 4) + Agent Orchestrator
- **LLM**: Enhanced LLM Assistant con OpenRouter (100+ modelli)
- **Tools**: Cross-Analysis Engine, Advanced Database Manager, Supabase
- **Execution**: Asincrono con task paralleli e prioritizzazione
- **Monitoring**: Health check integrato e performance tracking
- **Integration**: Sistema di Integrazione Intelligente completo

### Principi di Design (Implementati)
1. **Autonomia Intelligente**: Agenti capaci di decision making complesso ✅
2. **Collaborazione**: Agent Orchestrator per coordinamento ✅
3. **Adattabilità**: Auto-tuning basato su performance e feedback ✅
4. **Trasparenza**: Audit trail completo e spiegabilità delle decisioni ✅
5. **Sicurezza**: Controlli di accesso granulari e validazione input ✅

### Agenti Implementati (Fase 4)
- ✅ **DataQualityAgent**: Controllo qualità continuo
- ✅ **EntityResolutionAgent**: Risoluzione duplicati intelligente
- ✅ **AnomalyDetectionAgent**: Rilevamento pattern anomali
- ✅ **ConfigurationAgent**: Ottimizzazione configurazioni sistema

## Agente 1: Agente Pulizia Dati

### Descrizione
L'Agente Pulizia Dati è responsabile dell'identificazione e della correzione di problemi comuni nei dati caricati, come valori mancanti, formati inconsistenti e discrepanze nelle durate.

### Scopo
Migliorare la qualità dei dati analizzati identificando automaticamente problemi e suggerendo o applicando correzioni.

### Input
- **ID del file**: Identificatore univoco del file elaborato
- **Livello di aggressività**: Determina quanto l'agente sarà proattivo nell'applicare correzioni (basso, medio, alto)
- **Tipi di problemi da affrontare**: Lista di problemi specifici da cercare (es. valori mancanti, durate negative)

### Output
- **Report di pulizia**: Documento dettagliato che elenca tutti i problemi trovati e le correzioni applicate o suggerite
- **Dati puliti**: Versione pulita del dataset originale
- **Metriche di qualità**: Indicatori numerici che mostrano il miglioramento della qualità dei dati

### Strumenti (Endpoint MCP - MODEL CONTEXT PROTOCOL)
1. **Identificazione Problemi**
   - Endpoint: `/identify-issues/{file_id}`
   - Descrizione: Identifica valori mancanti, anomalie e discrepanze nei dati
   - Parametri: `file_id`, `issue_types` (opzionale)
   - Risposta: Lista di problemi trovati con dettagli su tipo, posizione e gravità

2. **Suggerimento Correzioni**
   - Endpoint: `/suggest-corrections/{file_id}`
   - Descrizione: Suggerisce correzioni per i problemi identificati
   - Parametri: `file_id`, `issues` (lista di problemi da correggere)
   - Risposta: Lista di correzioni suggerite con dettagli su come verrebbero applicate

3. **Applicazione Correzioni**
   - Endpoint: `/apply-corrections/{file_id}`
   - Descrizione: Applica le correzioni suggerite ai dati
   - Parametri: `file_id`, `corrections` (lista di correzioni da applicare)
   - Risposta: Conferma delle correzioni applicate e link ai dati aggiornati

4. **Standardizzazione Formati**
   - Endpoint: `/standardize-formats/{file_id}`
   - Descrizione: Standardizza i formati di date, durate e altri campi
   - Parametri: `file_id`, `format_specs` (specifiche dei formati da applicare)
   - Risposta: Dettagli sulle standardizzazioni applicate

### Flusso di Lavoro
1. L'utente richiede la pulizia dei dati tramite l'interfaccia frontend
2. Il backend principale invoca l'agente tramite l'endpoint `/agents/run/data-cleaner`
3. L'agente esegue i seguenti passaggi:
   - Identifica i problemi nei dati utilizzando l'endpoint `/identify-issues/{file_id}`
   - Suggerisce correzioni utilizzando l'endpoint `/suggest-corrections/{file_id}`
   - Se il livello di aggressività è medio o alto, applica automaticamente alcune o tutte le correzioni
   - Genera un report dettagliato delle operazioni eseguite
4. L'agente restituisce il report e i dati puliti al backend principale
5. Il backend principale invia i risultati al frontend per la visualizzazione

### Implementazione
L'agente sarà implementato come una classe Python nel backend principale:

```python
class DataCleanerAgent:
    def __init__(self, mcp_client):
        self.mcp_client = mcp_client
        self.state = {}

    async def run(self, file_id, aggression_level="medium", issue_types=None):
        # Inizializza lo stato
        self.state = {
            "file_id": file_id,
            "aggression_level": aggression_level,
            "issue_types": issue_types or ["missing_values", "anomalies", "discrepancies"],
            "status": "running",
            "progress": 0,
            "issues_found": [],
            "corrections_suggested": [],
            "corrections_applied": []
        }

        # Esegui il flusso di lavoro
        await self._identify_issues()
        await self._suggest_corrections()

        if aggression_level in ["medium", "high"]:
            await self._apply_corrections()

        # Genera il report
        report = self._generate_report()

        # Aggiorna lo stato
        self.state["status"] = "completed"
        self.state["progress"] = 100

        return report, self.state

    # Metodi privati per le singole fasi
    async def _identify_issues(self):
        # Implementazione...
        pass

    async def _suggest_corrections(self):
        # Implementazione...
        pass

    async def _apply_corrections(self):
        # Implementazione...
        pass

    def _generate_report(self):
        # Implementazione...
        pass
```

## Agente 2: Agente Analisi Avanzata

### Descrizione
L'Agente Analisi Avanzata esegue analisi statistiche avanzate sui dati, identifica pattern e genera insights significativi.

### Scopo
Fornire analisi approfondite che vanno oltre le semplici statistiche descrittive, aiutando gli utenti a scoprire relazioni nascoste nei dati.

### Input
- **ID del file**: Identificatore univoco del file elaborato
- **Tipi di analisi**: Lista di analisi da eseguire (es. correlazioni, trend temporali, clustering)
- **Parametri di analisi**: Configurazioni specifiche per ciascun tipo di analisi

### Output
- **Report di analisi**: Documento dettagliato con i risultati delle analisi eseguite
- **Visualizzazioni**: Grafici e visualizzazioni che illustrano i risultati
- **Insights**: Elenco di osservazioni significative estratte dai dati

### Strumenti (Endpoint MCP - MODEL CONTEXT PROTOCOL)
1. **Analisi Statistica**
   - Endpoint: `/statistical-analysis/{file_id}`
   - Descrizione: Esegue analisi statistiche avanzate sui dati
   - Parametri: `file_id`, `analysis_types`
   - Risposta: Risultati delle analisi statistiche

2. **Identificazione Pattern**
   - Endpoint: `/identify-patterns/{file_id}`
   - Descrizione: Identifica pattern ricorrenti nei dati
   - Parametri: `file_id`, `pattern_types`
   - Risposta: Pattern identificati con dettagli e significatività

3. **Generazione Insights**
   - Endpoint: `/generate-insights/{file_id}`
   - Descrizione: Genera insights significativi dai dati
   - Parametri: `file_id`, `insight_types`
   - Risposta: Lista di insights con spiegazioni e rilevanza

### Implementazione Futura
L'Agente Analisi Avanzata sarà implementato in una fase successiva, dopo che l'Agente Pulizia Dati sarà completamente funzionante e testato.

## Integrazione con il Sistema Esistente

Gli agenti saranno integrati nel sistema esistente attraverso:

1. **Endpoint API nel Backend Principale**:
   - `/agents/list`: Elenca gli agenti disponibili
   - `/agents/run/{agent_name}`: Avvia l'esecuzione di un agente specifico
   - `/agents/status/{agent_id}`: Ottiene lo stato di un'esecuzione di agente
   - `/agents/result/{agent_id}`: Ottiene i risultati di un'esecuzione di agente

2. **Componenti UI nel Frontend**:
   - Pannello di controllo degli agenti nella dashboard
   - Visualizzazione dei report generati dagli agenti
   - Interfaccia per configurare e avviare gli agenti

3. **Nuovi Endpoint nel Server MCP (MODEL CONTEXT PROTOCOL)**:
   - Implementazione degli endpoint necessari per gli strumenti degli agenti

## Roadmap di Implementazione

1. **Fase 1**: Implementazione dell'Agente Pulizia Dati
   - Creazione degli endpoint MCP (MODEL CONTEXT PROTOCOL) necessari
   - Implementazione della classe dell'agente nel backend principale
   - Sviluppo dell'interfaccia utente nel frontend

2. **Fase 2**: Implementazione dell'Agente Analisi Avanzata
   - Creazione degli endpoint MCP (MODEL CONTEXT PROTOCOL) aggiuntivi
   - Implementazione della classe dell'agente
   - Estensione dell'interfaccia utente

3. **Fase 3**: Implementazione di agenti aggiuntivi (es. Agente Previsione, Agente Reportistica)
