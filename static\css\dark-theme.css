/**
 * Dark Theme CSS - App Roberto
 * Sistema di Design Scuro Completo
 * Versione: 1.0.0
 */

/* ===== VARIABILI CSS PER TEMA SCURO ===== */
:root {
  /* Colori Base Tema <PERSON> (default) */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --text-muted: #adb5bd;

  /* Colori Accento */
  --accent-primary: #0d6efd;
  --accent-secondary: #6610f2;
  --accent-success: #198754;
  --accent-warning: #fd7e14;
  --accent-danger: #dc3545;
  --accent-info: #0dcaf0;

  /* Bordi e Ombre */
  --border-color: #dee2e6;
  --border-light: #e9ecef;
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);

  /* Grafici */
  --chart-bg: #ffffff;
  --chart-grid: #e9ecef;
  --chart-text: #495057;

  /* Navbar */
  --navbar-bg: #0d6efd;
  --navbar-text: #ffffff;

  /* Cards */
  --card-bg: #ffffff;
  --card-border: #dee2e6;

  /* Forms */
  --input-bg: #ffffff;
  --input-border: #ced4da;
  --input-focus: #86b7fe;

  /* Tables */
  --table-bg: #ffffff;
  --table-stripe: #f8f9fa;
  --table-hover: #f5f5f5;

  /* Buttons */
  --btn-primary-bg: #0d6efd;
  --btn-primary-border: #0d6efd;
  --btn-secondary-bg: #6c757d;
  --btn-secondary-border: #6c757d;
}

/* ===== TEMA SCURO - MATERIAL DESIGN 3 ENHANCED ===== */
[data-theme="dark"] {
  /* Colori Base Tema Scuro - Material Design 3 */
  --bg-primary: #121212;
  --bg-secondary: #1e1e1e;
  --bg-tertiary: #2d2d2d;
  --bg-surface: #1f1f1f;
  --bg-surface-variant: #2a2a2a;
  --bg-elevated: #252525;
  --text-primary: #e3e3e3;
  --text-secondary: #b3b3b3;
  --text-muted: #888888;
  --text-disabled: #666666;

  /* Colore tema browser per fallback cross-browser */
  --browser-theme-color: #121212;

  /* Colori Accento Ottimizzati per Scuro - Material Design 3 */
  --accent-primary: #4dabf7;
  --accent-primary-variant: #3b9ae1;
  --accent-secondary: #9775fa;
  --accent-secondary-variant: #8b66e8;
  --accent-success: #51cf66;
  --accent-success-variant: #47b85a;
  --accent-warning: #ffd43b;
  --accent-warning-variant: #e6c035;
  --accent-danger: #ff6b6b;
  --accent-danger-variant: #e65f5f;
  --accent-info: #22d3ee;
  --accent-info-variant: #1fbdd6;

  /* Bordi e Ombre per Tema Scuro - Material Design 3 */
  --border-color: #404040;
  --border-light: #333333;
  --border-focus: #4dabf7;
  --border-error: #ff6b6b;
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.6);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.7);

  /* Grafici Tema Scuro */
  --chart-bg: #1e1e1e;
  --chart-grid: #404040;
  --chart-text: #b3b3b3;
  --chart-tooltip-bg: #2d2d2d;
  --chart-tooltip-border: #404040;

  /* Navbar Tema Scuro */
  --navbar-bg: #1a1a1a;
  --navbar-text: #e3e3e3;
  --navbar-hover: #2d2d2d;
  --navbar-active: #4dabf7;

  /* Cards Tema Scuro */
  --card-bg: #1e1e1e;
  --card-border: #404040;
  --card-hover: #252525;
  --card-shadow: var(--shadow-md);

  /* Forms Tema Scuro */
  --input-bg: #2d2d2d;
  --input-border: #404040;
  --input-focus: #4dabf7;
  --input-focus-shadow: 0 0 0 0.2rem rgba(77, 171, 247, 0.25);
  --input-error: #ff6b6b;
  --input-success: #51cf66;

  /* Tables Tema Scuro */
  --table-bg: #1e1e1e;
  --table-stripe: #2d2d2d;
  --table-hover: #333333;
  --table-border: #404040;
  --table-header-bg: #252525;

  /* Buttons Tema Scuro */
  --btn-primary-bg: #4dabf7;
  --btn-primary-border: #4dabf7;
  --btn-primary-hover: #3b9ae1;
  --btn-secondary-bg: #6c757d;
  --btn-secondary-border: #6c757d;
  --btn-secondary-hover: #5a6268;
  --btn-success-bg: #51cf66;
  --btn-success-hover: #47b85a;
  --btn-warning-bg: #ffd43b;
  --btn-warning-hover: #e6c035;
  --btn-danger-bg: #ff6b6b;
  --btn-danger-hover: #e65f5f;

  /* Modals e Overlays */
  --modal-bg: #1e1e1e;
  --modal-border: #404040;
  --modal-backdrop: rgba(0, 0, 0, 0.8);
  --overlay-bg: rgba(18, 18, 18, 0.95);

  /* Scrollbars */
  --scrollbar-track: #2d2d2d;
  --scrollbar-thumb: #555555;
  --scrollbar-thumb-hover: #666666;
}

/* ===== APPLICAZIONE TEMA ===== */

/* Body e Background */
body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Navbar */
.navbar {
  background-color: var(--navbar-bg) !important;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.3s ease;
}

.navbar-brand,
.navbar-nav .nav-link {
  color: var(--navbar-text) !important;
  transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
  color: var(--accent-info) !important;
}

/* Cards - Material Design 3 Enhanced */
.card {
  background-color: var(--card-bg);
  border-color: var(--card-border);
  color: var(--text-primary);
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
}

.card:hover {
  background-color: var(--card-hover);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  background-color: var(--bg-surface);
  border-bottom-color: var(--border-color);
  color: var(--text-primary);
  font-weight: 600;
}

.card-body {
  color: var(--text-primary);
}

.card-footer {
  background-color: var(--bg-surface);
  border-top-color: var(--border-color);
  color: var(--text-secondary);
}

.card-title {
  color: var(--text-primary);
}

.card-subtitle {
  color: var(--text-secondary);
}

.card-text {
  color: var(--text-primary);
}

/* Forms */
.form-control,
.form-select {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--text-primary);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.form-control:focus,
.form-select:focus {
  background-color: var(--input-bg);
  border-color: var(--input-focus);
  color: var(--text-primary);
  box-shadow: 0 0 0 0.25rem rgba(77, 171, 247, 0.25);
}

.form-control::placeholder {
  color: var(--text-muted);
}

/* Labels */
.form-label {
  color: var(--text-primary);
}

/* Tables */
.table {
  --bs-table-bg: var(--table-bg);
  --bs-table-striped-bg: var(--table-stripe);
  --bs-table-hover-bg: var(--table-hover);
  --bs-table-border-color: var(--border-color);
  color: var(--text-primary);
}

.table th {
  border-bottom-color: var(--border-color);
  color: var(--text-primary);
}

/* Buttons - Material Design 3 Enhanced */
.btn {
  transition: all 0.3s ease;
  border-radius: 0.5rem;
  font-weight: 500;
}

.btn-primary {
  background-color: var(--btn-primary-bg);
  border-color: var(--btn-primary-border);
  color: var(--text-primary);
}

.btn-primary:hover {
  background-color: var(--btn-primary-hover);
  border-color: var(--btn-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--btn-secondary-bg);
  border-color: var(--btn-secondary-border);
  color: var(--text-primary);
}

.btn-secondary:hover {
  background-color: var(--btn-secondary-hover);
  border-color: var(--btn-secondary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-success {
  background-color: var(--btn-success-bg);
  border-color: var(--btn-success-bg);
  color: var(--text-primary);
}

.btn-success:hover {
  background-color: var(--btn-success-hover);
  border-color: var(--btn-success-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-warning {
  background-color: var(--btn-warning-bg);
  border-color: var(--btn-warning-bg);
  color: var(--bg-primary);
}

.btn-warning:hover {
  background-color: var(--btn-warning-hover);
  border-color: var(--btn-warning-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-danger {
  background-color: var(--btn-danger-bg);
  border-color: var(--btn-danger-bg);
  color: var(--text-primary);
}

.btn-danger:hover {
  background-color: var(--btn-danger-hover);
  border-color: var(--btn-danger-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Alerts */
.alert-primary {
  background-color: rgba(77, 171, 247, 0.1);
  border-color: var(--accent-primary);
  color: var(--accent-primary);
}

.alert-success {
  background-color: rgba(81, 207, 102, 0.1);
  border-color: var(--accent-success);
  color: var(--accent-success);
}

.alert-warning {
  background-color: rgba(255, 212, 59, 0.1);
  border-color: var(--accent-warning);
  color: var(--accent-warning);
}

.alert-danger {
  background-color: rgba(255, 107, 107, 0.1);
  border-color: var(--accent-danger);
  color: var(--accent-danger);
}

.alert-info {
  background-color: rgba(34, 211, 238, 0.1);
  border-color: var(--accent-info);
  color: var(--accent-info);
}

/* Modals */
.modal-content {
  background-color: var(--card-bg);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.modal-header {
  border-bottom-color: var(--border-color);
}

.modal-footer {
  border-top-color: var(--border-color);
}

/* Dropdowns */
.dropdown-menu {
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

.dropdown-item {
  color: var(--text-primary);
}

.dropdown-item:hover,
.dropdown-item:focus {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

/* Breadcrumbs */
.breadcrumb {
  background-color: var(--bg-secondary);
}

.breadcrumb-item a {
  color: var(--accent-primary);
}

/* Pagination */
.page-link {
  background-color: var(--card-bg);
  border-color: var(--border-color);
  color: var(--accent-primary);
}

.page-link:hover {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--accent-primary);
}

.page-item.active .page-link {
  background-color: var(--accent-primary);
  border-color: var(--accent-primary);
}

/* Footer */
footer {
  background-color: var(--bg-secondary) !important;
  border-top: 1px solid var(--border-color);
  color: var(--text-secondary);
}

/* Scrollbars (Webkit) */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Loading Spinners */
.spinner-border {
  color: var(--accent-primary);
}

/* Text Colors */
.text-muted {
  color: var(--text-muted) !important;
}

.text-primary {
  color: var(--accent-primary) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

.text-success {
  color: var(--accent-success) !important;
}

.text-warning {
  color: var(--accent-warning) !important;
}

.text-danger {
  color: var(--accent-danger) !important;
}

.text-info {
  color: var(--accent-info) !important;
}

/* Background Colors */
.bg-primary {
  background-color: var(--accent-primary) !important;
}

.bg-secondary {
  background-color: var(--bg-secondary) !important;
}

.bg-light {
  background-color: var(--bg-secondary) !important;
}

.bg-dark {
  background-color: var(--bg-tertiary) !important;
}

/* Borders */
.border {
  border-color: var(--border-color) !important;
}

.border-top {
  border-top-color: var(--border-color) !important;
}

.border-bottom {
  border-bottom-color: var(--border-color) !important;
}

.border-start {
  border-left-color: var(--border-color) !important;
}

.border-end {
  border-right-color: var(--border-color) !important;
}

/* ===== COMPONENTI SPECIFICI ===== */

/* Toast Notifications */
.toast {
  background-color: var(--card-bg);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.toast-header {
  background-color: var(--bg-secondary);
  border-bottom-color: var(--border-color);
  color: var(--text-primary);
}

/* Progress Bars */
.progress {
  background-color: var(--bg-secondary);
}

.progress-bar {
  background-color: var(--accent-primary);
}

/* Badges */
.badge {
  color: var(--text-primary);
}

.badge.bg-primary {
  background-color: var(--accent-primary) !important;
}

.badge.bg-secondary {
  background-color: var(--text-secondary) !important;
}

.badge.bg-success {
  background-color: var(--accent-success) !important;
}

.badge.bg-warning {
  background-color: var(--accent-warning) !important;
  color: var(--bg-primary) !important;
}

.badge.bg-danger {
  background-color: var(--accent-danger) !important;
}

.badge.bg-info {
  background-color: var(--accent-info) !important;
  color: var(--bg-primary) !important;
}

/* List Groups */
.list-group-item {
  background-color: var(--card-bg);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.list-group-item:hover {
  background-color: var(--bg-secondary);
}

.list-group-item.active {
  background-color: var(--accent-primary);
  border-color: var(--accent-primary);
}

/* Accordion */
.accordion-item {
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

.accordion-header .accordion-button {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.accordion-header .accordion-button:not(.collapsed) {
  background-color: var(--accent-primary);
  color: var(--text-primary);
}

.accordion-body {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

/* Offcanvas */
.offcanvas {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

.offcanvas-header {
  border-bottom-color: var(--border-color);
}

/* Carousel */
.carousel-indicators [data-bs-target] {
  background-color: var(--text-muted);
}

.carousel-indicators .active {
  background-color: var(--accent-primary);
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  filter: invert(1);
}

[data-theme="dark"] .carousel-control-prev-icon,
[data-theme="dark"] .carousel-control-next-icon {
  filter: invert(0);
}

/* Tooltips */
.tooltip .tooltip-inner {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

/* Popovers */
.popover {
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

.popover-header {
  background-color: var(--bg-secondary);
  border-bottom-color: var(--border-color);
  color: var(--text-primary);
}

.popover-body {
  color: var(--text-primary);
}

/* Code Blocks */
code {
  background-color: var(--bg-secondary);
  color: var(--accent-danger);
}

pre {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

/* Blockquotes */
blockquote {
  border-left-color: var(--border-color);
  color: var(--text-secondary);
}

/* HR */
hr {
  border-color: var(--border-color);
}

/* Theme Toggle Button Specific Styles */
#theme-toggle {
  transition: all 0.3s ease;
  border-radius: 0.375rem;
}

#theme-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

#theme-toggle:active {
  transform: scale(0.95);
}

/* Loading States */
.loading-overlay {
  background-color: rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .loading-overlay {
  background-color: rgba(0, 0, 0, 0.7);
}

/* Custom Scrollbar for Dark Theme */
[data-theme="dark"] ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Selection Colors */
::selection {
  background-color: var(--accent-primary);
  color: var(--text-primary);
}

::-moz-selection {
  background-color: var(--accent-primary);
  color: var(--text-primary);
}

/* Focus Styles */
.btn:focus,
.form-control:focus,
.form-select:focus {
  box-shadow: 0 0 0 0.25rem rgba(77, 171, 247, 0.25);
}

[data-theme="dark"] .btn:focus,
[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
  box-shadow: 0 0 0 0.25rem rgba(77, 171, 247, 0.4);
}

/* Smooth Transitions */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* ===== MATERIAL DESIGN 3 ENHANCEMENTS ===== */

/* Floating Action Button */
.fab {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-color: var(--accent-primary);
  color: var(--text-primary);
  border: none;
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  transition: all 0.3s ease;
  z-index: 1000;
}

.fab:hover {
  background-color: var(--accent-primary-variant);
  transform: scale(1.1);
  box-shadow: var(--shadow-xl);
}

/* Chip Components */
.chip {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  background-color: var(--bg-surface-variant);
  color: var(--text-primary);
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.chip:hover {
  background-color: var(--bg-elevated);
  transform: translateY(-1px);
}

.chip .chip-icon {
  margin-right: 0.5rem;
  font-size: 1rem;
}

.chip .chip-close {
  margin-left: 0.5rem;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  font-size: 1rem;
}

.chip .chip-close:hover {
  color: var(--text-primary);
}

/* Enhanced Navigation */
.nav-pills .nav-link {
  background-color: transparent;
  color: var(--text-secondary);
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.nav-pills .nav-link:hover {
  background-color: var(--bg-surface-variant);
  color: var(--text-primary);
}

.nav-pills .nav-link.active {
  background-color: var(--accent-primary);
  color: var(--text-primary);
}

/* Enhanced Tabs */
.nav-tabs {
  border-bottom: 1px solid var(--border-color);
}

.nav-tabs .nav-link {
  background-color: transparent;
  color: var(--text-secondary);
  border: none;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
  color: var(--text-primary);
  border-bottom-color: var(--accent-primary);
}

.nav-tabs .nav-link.active {
  color: var(--accent-primary);
  border-bottom-color: var(--accent-primary);
  background-color: transparent;
}

/* Enhanced Forms */
.form-floating > label {
  color: var(--text-secondary);
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
  color: var(--accent-primary);
}

.form-check-input:checked {
  background-color: var(--accent-primary);
  border-color: var(--accent-primary);
}

.form-switch .form-check-input:checked {
  background-color: var(--accent-primary);
  border-color: var(--accent-primary);
}

/* Enhanced Dropdowns */
.dropdown-menu {
  border-radius: 0.5rem;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
}

.dropdown-item {
  border-radius: 0.375rem;
  margin: 0.125rem 0.5rem;
  transition: all 0.3s ease;
}

.dropdown-item:hover {
  background-color: var(--bg-surface-variant);
  transform: translateX(4px);
}

/* Enhanced Modals */
.modal-content {
  border-radius: 1rem;
  box-shadow: var(--shadow-xl);
  border: none;
}

.modal-header {
  border-bottom: 1px solid var(--border-color);
  border-radius: 1rem 1rem 0 0;
}

.modal-footer {
  border-top: 1px solid var(--border-color);
  border-radius: 0 0 1rem 1rem;
}

/* Enhanced Alerts */
.alert {
  border-radius: 0.75rem;
  border: none;
  box-shadow: var(--shadow-sm);
}

/* Loading States */
.skeleton {
  background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-surface-variant) 50%, var(--bg-secondary) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Ripple Effect */
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
  width: 300px;
  height: 300px;
}

/* Ripple Effect Animation */
.ripple-effect {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  pointer-events: none;
  animation: ripple-animation 0.6s ease-out;
}

@keyframes ripple-animation {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* Enhanced Scrollbars */
[data-theme="dark"] ::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 6px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 6px;
  border: 2px solid var(--scrollbar-track);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

/* Glass Morphism Effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .glass {
  background: rgba(0, 0, 0, 0.3);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .fab {
    bottom: 1rem;
    right: 1rem;
    width: 48px;
    height: 48px;
    font-size: 1.25rem;
  }

  .card:hover {
    transform: none;
  }

  .btn:hover {
    transform: none;
  }
}

/* Print Styles */
@media print {
  [data-theme="dark"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --text-primary: #212529;
    --text-secondary: #6c757d;
  }
}

/* Prevent transition on page load */
.preload * {
  transition: none !important;
}

/* ===== WIZARD DARK THEME FIXES ===== */

/* Wizard specifico */
[data-theme="dark"] .wizard-step {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .wizard-step .card {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .wizard-step .card-header {
    background-color: var(--accent-primary);
    border-color: var(--border-color);
    color: white;
}

[data-theme="dark"] .wizard-step .card-body {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

/* Testo wizard */
[data-theme="dark"] .wizard-step p,
[data-theme="dark"] .wizard-step .text-muted {
    color: var(--text-muted) !important;
}

[data-theme="dark"] .wizard-step h1,
[data-theme="dark"] .wizard-step h2,
[data-theme="dark"] .wizard-step h3,
[data-theme="dark"] .wizard-step h4,
[data-theme="dark"] .wizard-step h5,
[data-theme="dark"] .wizard-step h6 {
    color: var(--text-primary) !important;
}

/* Sezioni specifiche wizard */
[data-theme="dark"] .detected-employees,
[data-theme="dark"] .detected-vehicles,
[data-theme="dark"] .suggested-analysis,
[data-theme="dark"] .suggested-automations {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .detected-employees .card-body,
[data-theme="dark"] .detected-vehicles .card-body {
    color: var(--text-muted) !important;
}

/* Tabelle anteprima dati */
[data-theme="dark"] .table {
    color: var(--text-primary);
}

[data-theme="dark"] .table th {
    background-color: var(--accent-primary);
    color: white;
    border-color: var(--border-color);
}

[data-theme="dark"] .table td {
    border-color: var(--border-color);
    color: var(--text-primary);
}
