@echo off
echo ===================================
echo Avvio completo dell'applicazione
echo ===================================
echo.

set CLEAN_ENV=clean_env

if not exist %CLEAN_ENV% (
    echo ERRORE: L'ambiente virtuale %CLEAN_ENV% non esiste.
    echo Eseguire prima create_clean_env.bat
    pause
    exit /b 1
)

echo Attivazione ambiente virtuale...
call %CLEAN_ENV%\Scripts\activate
if %errorlevel% neq 0 (
    echo ERRORE: Impossibile attivare l'ambiente virtuale.
    pause
    exit /b 1
)
echo Ambiente attivato: %VIRTUAL_ENV%
echo.

echo Verifica delle dipendenze principali...
python -c "import flask; print('Flask versione:', flask.__version__)"
if %errorlevel% neq 0 (
    echo ERRORE: Flask non è installato correttamente.
    pause
    exit /b 1
)

python -c "import pandas; print('Pandas versione:', pandas.__version__)"
if %errorlevel% neq 0 (
    echo ERRORE: Pandas non è installato correttamente.
    pause
    exit /b 1
)

python -c "import fastapi; print('FastAPI versione:', fastapi.__version__)"
if %errorlevel% neq 0 (
    echo ERRORE: FastAPI non è installato correttamente.
    pause
    exit /b 1
)

echo Verifica dei file principali...
if not exist app.py (
    echo ERRORE: File app.py non trovato.
    pause
    exit /b 1
)

if not exist mcp_server\main.py (
    echo ERRORE: File mcp_server\main.py non trovato.
    pause
    exit /b 1
)

echo Impostazione variabili d'ambiente...
set OPENROUTER_API_KEY=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
set APP_URL=http://localhost:5001
set MCP_URL=http://localhost:8000
set SUPABASE_URL=https://zqjllwxqjxjhdkbcawfr.supabase.co
set SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxamxsd3hxanhqaGRrYmNhd2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc4MjE4NzQsImV4cCI6MjA2MzM5Nzg3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8
set PYTHONPATH=%CD%
set PYTHONWARNINGS=ignore::UserWarning:pandas.io.excel
set FLASK_DEBUG=0

echo Variabili d'ambiente impostate:
echo OPENROUTER_API_KEY: %OPENROUTER_API_KEY%
echo APP_URL: %APP_URL%
echo MCP_URL: %MCP_URL%
echo SUPABASE_URL: %SUPABASE_URL%
echo SUPABASE_KEY: %SUPABASE_KEY%
echo PYTHONPATH: %PYTHONPATH%
echo.

echo Creazione cartella uploads se non esiste...
if not exist uploads mkdir uploads
echo.

echo Avvio del server MCP...
start "MCP Server" cmd /k "title Server MCP && color 0A && echo Avvio del server MCP... && call %CLEAN_ENV%\Scripts\activate && cd mcp_server && python main.py"
echo Server MCP avviato in una nuova finestra.
echo.

echo Attesa avvio server (5 secondi)...
ping 127.0.0.1 -n 6 > nul
echo.

echo Avvio dell'applicazione principale...
start "App Roberto" cmd /k "title Applicazione Principale && color 0B && echo Avvio dell'applicazione principale... && call %CLEAN_ENV%\Scripts\activate && python app.py"
echo Applicazione principale avviata in una nuova finestra.
echo.

echo ===================================
echo Applicazione avviata con successo!
echo - Flask: http://localhost:5001
echo - MCP: http://localhost:8000
echo ===================================
echo.
echo NOTA: Se le finestre dell'applicazione non mostrano output,
echo controlla se ci sono errori nei terminali.
echo.
echo Per debug avanzato, usa:
echo - avvio_debug.bat (per il server MCP)
echo - avvio_app_principale.bat (per l'app principale)
echo.
pause
