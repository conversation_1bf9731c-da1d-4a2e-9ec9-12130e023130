#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test del MultipleParsingStrategy per app-roberto
"""

import pandas as pd
import sys
import os

# Aggiungi il percorso corrente per gli import
sys.path.append('.')

try:
    from multiple_parsing_strategy import MultipleParsingStrategy
except ImportError as e:
    print(f"❌ Errore nell'importare MultipleParsingStrategy: {e}")
    sys.exit(1)

def create_test_data():
    """Crea dati di test per diversi tipi di file."""
    
    # Dati di test per attività (più realistici)
    activity_data = pd.DataFrame({
        'ID Ticket': ['TK-12345', 'TK-12346', 'TK-12347'],
        'Descrizione Attività': ['Installazione software', 'Riparazione PC', 'Configurazione rete'],
        'Tecnico Assegnato': ['<PERSON>', '<PERSON>', '<PERSON>'],
        'Cliente': ['Azienda ABC', 'Studio XYZ', 'Azienda ABC'],
        'Data Inizio': ['15/01/2024', '16/01/2024', '17/01/2024'],
        'Data Fine': ['15/01/2024', '16/01/2024', '17/01/2024'],
        'Durata Ore': ['2:30', '1:45', '3:15'],
        'Stato Ticket': ['Completato', 'In corso', 'Completato']
    })
    
    # Dati di test per calendario
    calendar_data = pd.DataFrame({
        'Titolo Evento': ['Riunione settimanale', 'Presentazione progetto', 'Formazione team'],
        'Data Evento': ['25/01/2024', '26/01/2024', '27/01/2024'],
        'Ora Inizio': ['09:00', '14:30', '10:00'],
        'Ora Fine': ['10:00', '16:00', '12:00'],
        'Partecipanti': ['Mario, Luigi, Anna', 'Tutto il team', 'Sviluppatori'],
        'Luogo Evento': ['Sala Riunioni A', 'Sala Conferenze', 'Ufficio']
    })
    
    return {
        'attivita': activity_data,
        'calendario': calendar_data
    }

def test_multiple_parsing_strategy():
    """Test completo del MultipleParsingStrategy."""
    print('🧪 Test MultipleParsingStrategy')
    print('=' * 50)
    
    # Inizializza la strategia
    try:
        strategy = MultipleParsingStrategy()
        print(f"✅ MultipleParsingStrategy inizializzato")
    except Exception as e:
        print(f"❌ Errore nell'inizializzazione: {e}")
        return False
    
    # Verifica analizzatori disponibili
    analyzer_status = strategy.get_analyzer_status()
    print(f"\n📊 Stato Analizzatori:")
    for analyzer, available in analyzer_status.items():
        status = "✅ Disponibile" if available else "❌ Non disponibile"
        print(f"   {analyzer}: {status}")
    
    # Verifica strategie disponibili
    strategies = strategy.get_available_strategies()
    print(f"\n🎯 Strategie Disponibili:")
    for strategy_name, description in strategies.items():
        print(f"   {strategy_name}: {description}")
    
    # Crea dati di test
    test_datasets = create_test_data()
    
    # Test con diverse strategie
    strategies_to_test = ["conservative", "balanced", "aggressive"]
    
    results = {}
    
    for strategy_name in strategies_to_test:
        print(f"\n🔍 Test Strategia: {strategy_name}")
        print("-" * 30)
        
        strategy_results = {}
        
        for expected_type, df in test_datasets.items():
            print(f"\n📁 Test file tipo: {expected_type}")
            print(f"   Righe: {len(df)}, Colonne: {len(df.columns)}")
            
            try:
                # Esegui analisi multi-strategia
                result = strategy.analyze_with_multiple_strategies(
                    df, f"test_{expected_type}.xlsx", strategy_name
                )
                
                # Estrai risultati principali
                final_result = result.get("final_result", {})
                detected_type = final_result.get("detected_type", "unknown")
                confidence = final_result.get("confidence_score", 0.0)
                method = final_result.get("method_used", "none")
                
                print(f"   🎯 Tipo rilevato: {detected_type}")
                print(f"   📈 Confidenza: {confidence:.3f}")
                print(f"   🔧 Metodo: {method}")
                
                # Verifica correttezza
                is_correct = detected_type == expected_type
                status = '✅ CORRETTO' if is_correct else '❌ ERRATO'
                print(f"   {status}")
                
                # Mostra consenso
                consensus = result.get("consensus_analysis", {})
                agreement_level = consensus.get("agreement_level", 0.0)
                successful_analyzers = consensus.get("successful_analyzers", 0)
                print(f"   🤝 Accordo: {agreement_level:.3f} ({successful_analyzers} analizzatori)")
                
                # Mostra performance
                performance = result.get("performance_metrics", {})
                success_rate = performance.get("success_rate", 0.0)
                total_time = performance.get("total_processing_time", 0.0)
                print(f"   ⚡ Successo: {success_rate:.1f}%, Tempo: {total_time:.3f}s")
                
                # Mostra raccomandazioni
                recommendations = result.get("recommendations", [])
                if recommendations:
                    print(f"   💡 Raccomandazioni: {len(recommendations)}")
                    for rec in recommendations[:2]:  # Mostra solo le prime 2
                        print(f"      - {rec}")
                
                strategy_results[expected_type] = {
                    'detected': detected_type,
                    'confidence': confidence,
                    'correct': is_correct,
                    'agreement': agreement_level,
                    'success_rate': success_rate
                }
                
            except Exception as e:
                print(f"   ❌ Errore nell'analisi: {str(e)}")
                strategy_results[expected_type] = {
                    'detected': 'error',
                    'confidence': 0.0,
                    'correct': False,
                    'error': str(e)
                }
        
        results[strategy_name] = strategy_results
    
    # Statistiche finali
    print(f"\n📈 STATISTICHE FINALI")
    print('=' * 40)
    
    for strategy_name, strategy_results in results.items():
        print(f"\n🎯 Strategia: {strategy_name}")
        
        total_tests = len(strategy_results)
        correct_detections = sum(1 for r in strategy_results.values() if r.get('correct', False))
        accuracy = (correct_detections / total_tests) * 100 if total_tests > 0 else 0.0
        
        print(f"   Accuratezza: {accuracy:.1f}% ({correct_detections}/{total_tests})")
        
        # Confidenza media
        confidences = [r.get('confidence', 0.0) for r in strategy_results.values() if 'confidence' in r]
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0
        print(f"   Confidenza media: {avg_confidence:.3f}")
        
        # Dettagli per tipo
        for file_type, result in strategy_results.items():
            status = '✅' if result.get('correct', False) else '❌'
            detected = result.get('detected', 'unknown')
            confidence = result.get('confidence', 0.0)
            print(f"   {status} {file_type}: {detected} ({confidence:.3f})")
    
    return results

def test_benchmark():
    """Test del sistema di benchmark."""
    print(f"\n🏁 Test Benchmark")
    print('=' * 30)
    
    try:
        strategy = MultipleParsingStrategy()
        test_datasets = create_test_data()
        
        # Esegui benchmark
        benchmark_results = strategy.benchmark_analyzers(test_datasets)
        
        print(f"📊 Risultati Benchmark:")
        print(f"   Test totali: {benchmark_results['test_summary']['total_tests']}")
        print(f"   Analizzatori testati: {benchmark_results['test_summary']['analyzers_tested']}")
        print(f"   Accuratezza complessiva: {benchmark_results['overall_accuracy']:.1f}%")
        
        # Performance per analizzatore
        for analyzer, perf in benchmark_results['analyzer_performance'].items():
            print(f"   {analyzer}: {perf['accuracy']:.1f}% ({perf['correct']}/{perf['total']})")
        
    except Exception as e:
        print(f"❌ Errore nel benchmark: {e}")

if __name__ == "__main__":
    try:
        results = test_multiple_parsing_strategy()
        test_benchmark()
        print(f"\n🎉 Test completato!")
    except Exception as e:
        print(f"❌ Errore generale nel test: {e}")
        import traceback
        traceback.print_exc()
