{"total_duration": 22.730883359909058, "successful_suites": 0, "total_suites": 4, "total_passed": 0, "total_failed": 0, "total_skipped": 0, "total_errors": 0, "success_rate": 0.0, "grade": "🔴 INSUFFICIENTE", "results": {"test_suite_complete.py": {"file": "test_suite_complete.py", "description": "Task 7.1 - Test Suite Completa (Integrazione Sistema)", "return_code": 1, "duration": 0.10278725624084473, "stats": {"passed": 0, "failed": 0, "skipped": 0, "errors": 0}, "output": "", "errors": "C:\\Python313\\python.exe: No module named pytest\n", "success": false}, "performance_testing.py": {"file": "performance_testing.py", "description": "Task 7.2 - Performance Testing (<PERSON><PERSON>, <PERSON><PERSON>, Sistema)", "return_code": 1, "duration": 0.08745694160461426, "stats": {"passed": 0, "failed": 0, "skipped": 0, "errors": 0}, "output": "", "errors": "C:\\Python313\\python.exe: No module named pytest\n", "success": false}, "security_testing.py": {"file": "security_testing.py", "description": "Task 7.3 - Security Testing (Validazione, Protezione, Controlli)", "return_code": 1, "duration": 0.06760907173156738, "stats": {"passed": 0, "failed": 0, "skipped": 0, "errors": 0}, "output": "", "errors": "C:\\Python313\\python.exe: No module named pytest\n", "success": false}, "test_fase6_agents.py": {"file": "test_fase6_agents.py", "description": "Test Agenti AI (Verifica Fase 6) - Funzionale", "return_code": -2, "duration": 0, "stats": {}, "output": "", "errors": "'NoneType' object has no attribute 'split'", "success": false}}}