#!/usr/bin/env python3
"""
Test per Config Manager
"""

import sys
sys.path.append('.')

from config_manager import config_manager

def test_config_manager():
    print("🔧 Test Config Manager...")
    
    # Test 1: Impostazioni di default
    print("\n📋 Test 1: Configurazione di default")
    tax_settings = config_manager.get_tax_settings()
    print(f"Impostazioni IVA: {tax_settings}")
    
    # Test 2: Aggiungi dipendente
    print("\n👤 Test 2: Aggiungi dipendente")
    success = config_manager.set_employee_cost(
        employee_name="<PERSON>",
        hourly_rate=25.0,
        vat_included=True,
        notes="Tecnico senior"
    )
    print(f"Dipendente aggiunto: {success}")
    
    # Test 3: Ottieni costo dipendente
    print("\n💰 Test 3: Ottieni costo dipendente")
    employee_cost = config_manager.get_employee_cost("<PERSON> Rossi")
    print(f"Costo Mario <PERSON>: {employee_cost}")
    
    # Test 4: Calcola costo totale
    print("\n🧮 Test 4: Calcola costo totale")
    calculation = config_manager.calculate_employee_cost("<PERSON> Rossi", 8.0)
    print(f"Calcolo 8 ore: {calculation}")
    
    # Test 5: Lista tutti i dipendenti
    print("\n📝 Test 5: Lista dipendenti")
    all_employees = config_manager.get_all_employee_costs()
    print(f"Tutti i dipendenti: {all_employees}")
    
    print("\n✅ Test Config Manager completati!")

if __name__ == "__main__":
    test_config_manager()
