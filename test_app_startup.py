#!/usr/bin/env python3
"""
Test per verificare l'avvio dell'applicazione e identificare errori
"""

import sys
import traceback

def test_app_startup():
    """Test avvio applicazione"""
    
    print("🔍 TEST AVVIO APPLICAZIONE")
    print("=" * 60)
    
    try:
        print("1️⃣ Importazione moduli base...")
        
        # Test importazioni critiche
        try:
            from flask import Flask
            print("   ✅ Flask importato")
        except Exception as e:
            print(f"   ❌ Errore Flask: {e}")
            return False
        
        try:
            import os
            print("   ✅ OS importato")
        except Exception as e:
            print(f"   ❌ Errore OS: {e}")
            return False
        
        print("\n2️⃣ Test importazione app.py...")
        
        # Cambia directory di lavoro
        import os
        os.chdir(r'C:\Users\<USER>\Documents\app-roberto')
        print(f"   📁 Directory: {os.getcwd()}")
        
        # Test importazione app
        try:
            print("   🔄 Importando app...")
            import app
            print("   ✅ app.py importato con successo")
        except Exception as e:
            print(f"   ❌ Errore importazione app.py: {e}")
            print("   📄 Traceback completo:")
            traceback.print_exc()
            return False
        
        print("\n3️⃣ Test accesso oggetto Flask...")
        
        try:
            flask_app = app.app
            print(f"   ✅ Oggetto Flask: {type(flask_app)}")
        except Exception as e:
            print(f"   ❌ Errore accesso Flask app: {e}")
            return False
        
        print("\n4️⃣ Test registrazione route...")
        
        try:
            # Ottieni tutte le route registrate
            routes = []
            for rule in flask_app.url_map.iter_rules():
                routes.append(f"{','.join(rule.methods)} {rule.rule}")
            
            print(f"   📋 Totale route registrate: {len(routes)}")
            
            # Cerca route agenti
            agent_routes = [r for r in routes if 'agent' in r.lower()]
            print(f"   🤖 Route agenti: {len(agent_routes)}")
            for route in agent_routes:
                print(f"      - {route}")
            
            # Cerca route specifiche
            target_routes = [
                '/api/agents/status',
                '/api/automation/rules',
                '/api/agents/execute'
            ]
            
            print(f"\n   🎯 Route target:")
            for target in target_routes:
                found = any(target in route for route in routes)
                status = "✅" if found else "❌"
                print(f"      {status} {target}")
                
        except Exception as e:
            print(f"   ❌ Errore analisi route: {e}")
            traceback.print_exc()
            return False
        
        print("\n5️⃣ Test variabili globali...")
        
        try:
            ai_available = getattr(app, 'AI_AGENTS_AVAILABLE', None)
            print(f"   🤖 AI_AGENTS_AVAILABLE: {ai_available}")
            
            # Test importazioni agenti
            try:
                from ai_agents_framework import agent_orchestrator
                print("   ✅ agent_orchestrator importato")
            except Exception as e:
                print(f"   ❌ Errore agent_orchestrator: {e}")
            
            try:
                from intelligent_automation import intelligent_automation
                print("   ✅ intelligent_automation importato")
            except Exception as e:
                print(f"   ❌ Errore intelligent_automation: {e}")
                
        except Exception as e:
            print(f"   ❌ Errore variabili globali: {e}")
        
        print("\n6️⃣ Test endpoint specifici...")
        
        # Test con client di test Flask
        try:
            with flask_app.test_client() as client:
                
                # Test endpoint base
                response = client.get('/')
                print(f"   🏠 Homepage: {response.status_code}")
                
                # Test endpoint agenti
                response = client.get('/api/agents/status')
                print(f"   🤖 /api/agents/status: {response.status_code}")
                
                response = client.get('/api/automation/rules')
                print(f"   ⚙️ /api/automation/rules: {response.status_code}")
                
                response = client.get('/api/endpoints')
                print(f"   📋 /api/endpoints: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Errore test client: {e}")
            traceback.print_exc()
        
        print("\n" + "=" * 60)
        print("📊 RISULTATI")
        print("=" * 60)
        print("✅ Test completato - Verifica output sopra per identificare problemi")
        
        return True
        
    except Exception as e:
        print(f"❌ ERRORE CRITICO: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_app_startup()
    exit(0 if success else 1)
