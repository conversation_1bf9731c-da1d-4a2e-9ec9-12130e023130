#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test semplice per verificare la registrazione delle route Flask
"""

import requests
import time

def test_specific_routes():
    """Test per verificare route specifiche"""

    print("🔍 TEST ROUTE FLASK SPECIFICHE")
    print("=" * 50)

    base_url = "http://127.0.0.1:5001"  # Server semplificato

    # Route che dovrebbero funzionare
    working_routes = [
        "/api/health",
        "/api/endpoints",
        "/",
    ]

    # Route che stiamo testando
    test_routes = [
        "/api/config/employees",
        "/api/config/vehicles",
        "/api/dashboard_data",
        "/api/chart_data",
    ]

    print("✅ ROUTE CHE DOVREBBERO FUNZIONARE:")
    for route in working_routes:
        try:
            response = requests.get(f"{base_url}{route}", timeout=5)
            print(f"   {route} - {response.status_code}")
        except Exception as e:
            print(f"   {route} - ERROR: {str(e)}")

    print("\n❓ ROUTE DA TESTARE:")
    for route in test_routes:
        try:
            response = requests.get(f"{base_url}{route}", timeout=5)
            if response.status_code == 404:
                print(f"   ❌ {route} - 404 NOT FOUND")
            else:
                print(f"   ✅ {route} - {response.status_code}")
        except Exception as e:
            print(f"   💥 {route} - ERROR: {str(e)}")

    # Test specifico per /api/config/employees con parametri
    print(f"\n🎯 TEST SPECIFICO /api/config/employees:")
    try:
        response = requests.get(f"{base_url}/api/config/employees", timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code != 404:
            print(f"   Content-Type: {response.headers.get('content-type', 'N/A')}")
            print(f"   Response length: {len(response.text)} chars")
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   JSON keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                except:
                    print(f"   Response text: {response.text[:200]}...")
        else:
            print(f"   404 - Route non registrata in Flask")
    except Exception as e:
        print(f"   ERROR: {str(e)}")

if __name__ == "__main__":
    print("🚀 Test route Flask...")
    test_specific_routes()
    print("\n🏁 Test completato")
