#!/usr/bin/env python3
"""
Debug completo delle route Flask registrate
"""

import requests
import json

def debug_flask_routes():
    """Debug completo delle route Flask"""
    
    print("🔍 DEBUG COMPLETO ROUTE FLASK")
    print("=" * 60)
    
    # Test 1: Verifica se l'applicazione risponde
    print("1️⃣ Test connettività base...")
    try:
        response = requests.get("http://127.0.0.1:5000/", timeout=5)
        print(f"   Homepage: {response.status_code} ({'✅' if response.status_code == 200 else '❌'})")
    except Exception as e:
        print(f"   ❌ Errore connettività: {str(e)}")
        return False
    
    # Test 2: Verifica endpoint che funzionano
    print("\n2️⃣ Test endpoint funzionanti...")
    working_endpoints = [
        ("GET", "/api/health"),
        ("GET", "/api/endpoints"),
        ("GET", "/setup-wizard"),
        ("GET", "/dashboard")
    ]
    
    for method, path in working_endpoints:
        try:
            if method == "GET":
                response = requests.get(f"http://127.0.0.1:5000{path}", timeout=5)
            else:
                response = requests.post(f"http://127.0.0.1:5000{path}", json={}, timeout=5)
            
            status = "✅" if response.status_code == 200 else "❌"
            print(f"   {method} {path}: {response.status_code} ({status})")
            
        except Exception as e:
            print(f"   {method} {path}: ❌ ERRORE - {str(e)}")
    
    # Test 3: Test specifico endpoint wizard
    print("\n3️⃣ Test specifico endpoint wizard...")
    
    # Test OPTIONS
    try:
        response = requests.options("http://127.0.0.1:5000/api/wizard/complete", timeout=5)
        print(f"   OPTIONS /api/wizard/complete: {response.status_code}")
        print(f"   Headers CORS: {dict(response.headers)}")
    except Exception as e:
        print(f"   OPTIONS: ❌ ERRORE - {str(e)}")
    
    # Test POST con dati minimi
    try:
        test_data = {
            "files": [],
            "employees": [],
            "vehicles": [],
            "configuration": {}
        }
        
        response = requests.post(
            "http://127.0.0.1:5000/api/wizard/complete",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"   POST /api/wizard/complete: {response.status_code}")
        print(f"   Content-Type: {response.headers.get('content-type', 'N/A')}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   ✅ RISPOSTA JSON VALIDA")
                print(f"   Success: {result.get('success', 'N/A')}")
                print(f"   Message: {result.get('message', 'N/A')}")
                if 'data' in result:
                    print(f"   Redirect: {result['data'].get('redirect', 'N/A')}")
                return True
            except json.JSONDecodeError:
                print(f"   ❌ RISPOSTA NON JSON: {response.text[:200]}...")
        else:
            print(f"   ❌ ERRORE: {response.text[:200]}...")
            
    except Exception as e:
        print(f"   POST: ❌ ERRORE - {str(e)}")
    
    # Test 4: Verifica route Flask interne
    print("\n4️⃣ Test route Flask interne...")
    try:
        # Testa un endpoint interno per vedere se Flask funziona
        response = requests.get("http://127.0.0.1:5000/api/persistence/status", timeout=5)
        print(f"   /api/persistence/status: {response.status_code} ({'✅' if response.status_code == 200 else '❌'})")
        
        response = requests.post("http://127.0.0.1:5000/api/persistence/cleanup", json={}, timeout=5)
        print(f"   /api/persistence/cleanup: {response.status_code} ({'✅' if response.status_code == 200 else '❌'})")
        
    except Exception as e:
        print(f"   ❌ Errore test route interne: {str(e)}")
    
    # Test 5: Verifica con URL diversi
    print("\n5️⃣ Test varianti URL...")
    
    test_urls = [
        "http://127.0.0.1:5000/api/wizard/complete",
        "http://localhost:5000/api/wizard/complete",
        "http://127.0.0.1:5000/api/wizard/complete/",
        "http://127.0.0.1:5000/api/wizard/complete?test=1"
    ]
    
    for url in test_urls:
        try:
            response = requests.post(url, json={"test": True}, timeout=5)
            status = "✅" if response.status_code != 404 else "❌"
            print(f"   {url}: {response.status_code} ({status})")
        except Exception as e:
            print(f"   {url}: ❌ ERRORE - {str(e)}")
    
    print("\n" + "=" * 60)
    print("🎯 CONCLUSIONI:")
    print("   Se tutti i test falliscono con 404, il problema è nella registrazione della route Flask")
    print("   Se alcuni test funzionano, il problema è nella configurazione dell'endpoint specifico")
    
    return False

if __name__ == "__main__":
    debug_flask_routes()
