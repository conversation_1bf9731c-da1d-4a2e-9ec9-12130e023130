#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FASE 6: Test Finale App Roberto
Test finale semplificato per verificare stabilità del sistema.
Versione: 1.0.0 - FASE 6 Testing e Stabilizzazione
"""

import sys
import time
import os
from datetime import datetime

def test_health_monitor():
    """Test del health monitor."""
    print("\n=== TEST HEALTH MONITOR ===")
    
    try:
        from health_monitor import health_monitor
        print("✅ Import health_monitor: OK")
        
        health = health_monitor.check_system_health()
        print(f"✅ System health check: {health.overall_status}")
        
        report = health_monitor.get_health_report()
        print(f"✅ Health report: {len(report)} keys")
        
        return True, f"Status: {health.overall_status}"
        
    except Exception as e:
        print(f"❌ Health Monitor Error: {str(e)}")
        return False, str(e)

def test_app_basic():
    """Test di base dell'applicazione."""
    print("\n=== TEST APP BASIC ===")
    
    try:
        from app import app
        print("✅ Import app: OK")
        
        app.config['TESTING'] = True
        print("✅ App config: OK")
        
        with app.test_client() as client:
            response = client.get('/')
            if response.status_code == 200:
                print("✅ Home page: OK")
            else:
                print(f"⚠️ Home page: {response.status_code}")
            
            response = client.get('/api/health')
            if response.status_code == 200:
                print("✅ API health: OK")
            else:
                print(f"⚠️ API health: {response.status_code}")
        
        return True, "Routes accessible, API responding"
        
    except Exception as e:
        print(f"❌ App Basic Error: {str(e)}")
        return False, str(e)

def test_ui_files():
    """Test file UI."""
    print("\n=== TEST UI FILES ===")
    
    try:
        ui_files = [
            'static/css/style.css',
            'static/css/dark-theme.css',
            'static/js/theme-manager.js',
            'static/js/ui-enhancements.js'
        ]
        
        missing_files = []
        for file_path in ui_files:
            if os.path.exists(file_path):
                print(f"✅ {file_path}: OK")
            else:
                print(f"❌ {file_path}: Missing")
                missing_files.append(file_path)
        
        if missing_files:
            return False, f"Missing files: {', '.join(missing_files)}"
        else:
            return True, f"All {len(ui_files)} UI files present"
            
    except Exception as e:
        print(f"❌ UI Files Error: {str(e)}")
        return False, str(e)

def test_api_standardization():
    """Test standardizzazione API."""
    print("\n=== TEST API STANDARDIZATION ===")
    
    try:
        from app import app
        
        with app.test_client() as client:
            response = client.get('/api/health')
            if response.status_code == 200:
                data = response.get_json()
                if data and 'success' in data and 'data' in data:
                    print("✅ API Health format: OK")
                    health_ok = True
                else:
                    print("⚠️ API Health format: Invalid")
                    health_ok = False
            else:
                print(f"❌ API Health: {response.status_code}")
                health_ok = False
            
            response = client.get('/api/endpoints')
            if response.status_code == 200:
                data = response.get_json()
                if data and 'success' in data and 'data' in data:
                    print("✅ API Endpoints format: OK")
                    endpoints_ok = True
                else:
                    print("⚠️ API Endpoints format: Invalid")
                    endpoints_ok = False
            else:
                print(f"❌ API Endpoints: {response.status_code}")
                endpoints_ok = False
        
        if health_ok and endpoints_ok:
            return True, "API endpoints responding with standard format"
        else:
            return False, "API format issues detected"
            
    except Exception as e:
        print(f"❌ API Standardization Error: {str(e)}")
        return False, str(e)

def test_performance_basic():
    """Test performance di base."""
    print("\n=== TEST PERFORMANCE BASIC ===")
    
    try:
        from app import app
        import psutil
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        with app.test_client() as client:
            times = []
            for i in range(10):
                start_time = time.time()
                response = client.get('/')
                end_time = time.time()
                
                if response.status_code == 200:
                    times.append(end_time - start_time)
                else:
                    print(f"⚠️ Request {i+1} failed: {response.status_code}")
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        if times:
            avg_time = sum(times) / len(times)
            max_time = max(times)
            
            print(f"✅ Average response time: {avg_time:.3f}s")
            print(f"✅ Max response time: {max_time:.3f}s")
            print(f"✅ Memory increase: {memory_increase:.1f}MB")
            
            performance_ok = avg_time < 1.0 and max_time < 2.0 and memory_increase < 50
            
            return performance_ok, f"Avg: {avg_time:.3f}s, Max: {max_time:.3f}s, Mem: +{memory_increase:.1f}MB"
        else:
            return False, "No successful requests"
            
    except Exception as e:
        print(f"❌ Performance Basic Error: {str(e)}")
        return False, str(e)

def main():
    """Funzione principale."""
    print("🚀 TEST FINALE - APP ROBERTO")
    print("=" * 50)
    start_time = datetime.now()
    print(f"Start: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Lista test
    tests = [
        ("Health Monitor", test_health_monitor),
        ("App Basic", test_app_basic),
        ("UI Files", test_ui_files),
        ("API Standardization", test_api_standardization),
        ("Performance Basic", test_performance_basic)
    ]
    
    results = []
    
    # Esegui test
    for test_name, test_func in tests:
        try:
            success, details = test_func()
            results.append({
                'name': test_name,
                'success': success,
                'details': details
            })
        except Exception as e:
            results.append({
                'name': test_name,
                'success': False,
                'details': str(e)
            })
    
    # Report finale
    print("\n" + "=" * 50)
    print("📋 FINAL REPORT")
    print("=" * 50)
    
    total_tests = len(results)
    passed = sum(1 for r in results if r['success'])
    failed = total_tests - passed
    
    duration = (datetime.now() - start_time).total_seconds()
    
    print(f"Duration: {duration:.2f}s")
    print(f"Tests: {total_tests}")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print()
    
    # Dettagli
    for result in results:
        status_icon = "✅" if result['success'] else "❌"
        status_text = "PASS" if result['success'] else "FAIL"
        
        print(f"{status_icon} {result['name']}: {status_text}")
        print(f"   {result['details']}")
    
    # Valutazione
    success_rate = (passed / total_tests * 100) if total_tests > 0 else 0
    
    if success_rate >= 90:
        grade = "🟢 EXCELLENT"
    elif success_rate >= 70:
        grade = "🟡 GOOD"
    elif success_rate >= 50:
        grade = "🟠 FAIR"
    else:
        grade = "🔴 POOR"
    
    print(f"\n🎯 Grade: {grade} ({success_rate:.1f}%)")
    
    # Raccomandazioni
    print("\n💡 Recommendations:")
    if failed > 0:
        print("   - Fix failed tests immediately")
    if success_rate >= 90:
        print("   - System is stable and well-tested")
        print("   - Ready for production use")
    else:
        print("   - Improve system stability")
        print("   - Review failed components")
    
    # Exit code
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED!")
        sys.exit(0)
    else:
        print(f"\n⚠️ {failed} TESTS FAILED!")
        sys.exit(1)

if __name__ == "__main__":
    main()
