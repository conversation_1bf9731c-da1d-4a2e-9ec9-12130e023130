#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Content-Based File Analyzer - Riconoscimento intelligente basato sul contenuto.
Utilizza analisi semantica, pattern recognition e machine learning per identificare
i tipi di file indipendentemente dal nome o dalla struttura.
"""

import pandas as pd
import numpy as np
import logging
import re
from typing import Dict, List, Tuple, Any, Optional, Set
from datetime import datetime
from collections import Counter
from difflib import SequenceMatcher

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ContentBasedFileAnalyzer:
    """
    Analizzatore avanzato che identifica i tipi di file basandosi sul contenuto
    piuttosto che sui nomi delle colonne o dei file.

    Caratteristiche:
    - Analisi semantica del contenuto
    - Pattern recognition sui dati
    - Machine learning per classificazione
    - Estrazione automatica di entità
    """

    def __init__(self):
        # Pattern semantici per identificare i tipi di contenuto
        self.CONTENT_PATTERNS = {
            "attivita": {
                "semantic_indicators": [
                    # Pattern per identificare attività/ticket
                    {"pattern": r"\b(ticket|task|attivit[àa]|lavoro|job)\b", "weight": 1.5},
                    {"pattern": r"\b(iniziat[oa]|start|begin|avvio)\b", "weight": 1.2},
                    {"pattern": r"\b(conclus[oa]|end|fine|terminat[oa])\b", "weight": 1.2},
                    {"pattern": r"\b(durata|duration|tempo|time)\b", "weight": 1.0},
                    {"pattern": r"\b(contratto|contract|cliente|client)\b", "weight": 1.0},
                    {"pattern": r"\b(stato|status|progress)\b", "weight": 0.8}
                ],
                "data_patterns": [
                    # Pattern per identificare dati tipici delle attività
                    {"type": "ticket_id", "pattern": r"^[A-Z]{2,4}-?\d{3,6}$", "weight": 2.0},
                    {"type": "duration", "pattern": r"^\d{1,3}:\d{2}(:\d{2})?$", "weight": 1.5},
                    {"type": "datetime", "pattern": r"\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}", "weight": 1.0},
                    {"type": "status", "pattern": r"^(aperto|chiuso|in corso|completato|open|closed|in progress|completed)$", "weight": 1.0}
                ],
                "min_confidence": 0.6
            },

            "timbrature": {
                "semantic_indicators": [
                    {"pattern": r"\b(timbrat[ura]|punch|clock|orario|schedule)\b", "weight": 1.5},
                    {"pattern": r"\b(entrata|ingresso|entry|in)\b", "weight": 1.2},
                    {"pattern": r"\b(uscita|exit|out)\b", "weight": 1.2},
                    {"pattern": r"\b(pausa|break|lunch|pranzo)\b", "weight": 1.0},
                    {"pattern": r"\b(dipendente|employee|worker|operatore)\b", "weight": 1.0}
                ],
                "data_patterns": [
                    {"type": "time", "pattern": r"^\d{1,2}:\d{2}(:\d{2})?$", "weight": 1.5},
                    {"type": "employee_code", "pattern": r"^[A-Z]{2,4}\d{2,4}$", "weight": 1.2},
                    {"type": "punch_type", "pattern": r"^(IN|OUT|BREAK|LUNCH)$", "weight": 1.0}
                ],
                "min_confidence": 0.6
            },

            "teamviewer": {
                "semantic_indicators": [
                    {"pattern": r"\b(teamviewer|remote|connection|connessione)\b", "weight": 1.5},
                    {"pattern": r"\b(computer|pc|client|host|endpoint)\b", "weight": 1.2},
                    {"pattern": r"\b(utente|user|tecnico|operator)\b", "weight": 1.2},
                    {"pattern": r"\b(sessione|session|collegamento)\b", "weight": 1.0}
                ],
                "data_patterns": [
                    {"type": "computer_id", "pattern": r"^\d{9,12}$", "weight": 2.0},
                    {"type": "ip_address", "pattern": r"^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$", "weight": 1.5},
                    {"type": "session_duration", "pattern": r"^\d{1,3}:\d{2}:\d{2}$", "weight": 1.0}
                ],
                "min_confidence": 0.6
            },

            "calendario": {
                "semantic_indicators": [
                    {"pattern": r"\b(evento|event|appuntamento|meeting|riunione)\b", "weight": 1.5},
                    {"pattern": r"\b(calendario|calendar|agenda|schedule)\b", "weight": 1.5},
                    {"pattern": r"\b(partecipant[ei]|attendee|invitat[oi])\b", "weight": 1.2},
                    {"pattern": r"\b(luogo|location|sala|room)\b", "weight": 1.0}
                ],
                "data_patterns": [
                    {"type": "event_title", "pattern": r"^.{5,100}$", "weight": 1.0},
                    {"type": "location", "pattern": r"\b(sala|room|ufficio|office)\b", "weight": 1.0}
                ],
                "min_confidence": 0.5
            },

            "registro_auto": {
                "semantic_indicators": [
                    {"pattern": r"\b(auto|car|vehicle|veicolo|macchina)\b", "weight": 1.5},
                    {"pattern": r"\b(targa|plate|license|matricola)\b", "weight": 1.5},
                    {"pattern": r"\b(chilometr[oi]|km|mileage|odometer)\b", "weight": 1.2},
                    {"pattern": r"\b(carburante|fuel|benzina|gasolio)\b", "weight": 1.2}
                ],
                "data_patterns": [
                    {"type": "license_plate", "pattern": r"^[A-Z]{2}\d{3}[A-Z]{2}$", "weight": 2.0},
                    {"type": "kilometers", "pattern": r"^\d{1,6}(\.\d{1,2})?\s?(km)?$", "weight": 1.5}
                ],
                "min_confidence": 0.6
            }
        }

        # Configurazione per l'analisi
        self.MIN_SAMPLE_SIZE = 5  # Minimo numero di righe per analisi affidabile
        self.MAX_SAMPLE_SIZE = 100  # Massimo numero di righe da analizzare
        self.FUZZY_THRESHOLD = 0.7  # Soglia per fuzzy matching

    def analyze_content(self, df: pd.DataFrame, filename: str = "") -> Dict[str, Any]:
        """
        Analizza il contenuto di un DataFrame per determinare il tipo di file.

        Args:
            df: DataFrame da analizzare
            filename: Nome del file (opzionale, per contesto aggiuntivo)

        Returns:
            Dizionario con risultati dell'analisi
        """
        logger.info(f"🔍 Analisi content-based per file: {filename}")

        if df is None or df.empty:
            return self._create_empty_result("DataFrame vuoto o None")

        # Prepara il campione di dati per l'analisi
        sample_df = self._prepare_sample(df)

        # Analisi multi-livello
        results = {
            "filename": filename,
            "total_rows": len(df),
            "total_columns": len(df.columns),
            "sample_size": len(sample_df),
            "analysis_timestamp": datetime.now().isoformat(),
            "detected_type": "unknown",
            "confidence_score": 0.0,
            "type_scores": {},
            "content_analysis": {},
            "entity_extraction": {},
            "recommendations": []
        }

        try:
            # 1. Analisi semantica delle colonne
            column_analysis = self._analyze_column_semantics(df.columns.tolist())
            results["content_analysis"]["columns"] = column_analysis

            # 2. Analisi del contenuto dei dati
            data_analysis = self._analyze_data_content(sample_df)
            results["content_analysis"]["data"] = data_analysis

            # 3. Calcolo punteggi per ogni tipo di file
            type_scores = self._calculate_content_scores(column_analysis, data_analysis)
            results["type_scores"] = type_scores

            # 4. Determinazione del tipo migliore
            if type_scores:
                best_type = max(type_scores, key=type_scores.get)
                best_score = type_scores[best_type]

                # Verifica soglia minima
                min_confidence = self.CONTENT_PATTERNS[best_type]["min_confidence"]
                if best_score >= min_confidence:
                    results["detected_type"] = best_type
                    results["confidence_score"] = best_score

                    # 5. Estrazione entità specifiche per il tipo rilevato
                    entities = self._extract_entities(sample_df, best_type)
                    results["entity_extraction"] = entities

                    # 6. Generazione raccomandazioni
                    recommendations = self._generate_recommendations(df, best_type, best_score)
                    results["recommendations"] = recommendations

                    logger.info(f"✅ Tipo rilevato: {best_type} (confidenza: {best_score:.3f})")
                else:
                    logger.info(f"⚠️ Punteggio insufficiente per {best_type}: {best_score:.3f} < {min_confidence}")

        except Exception as e:
            logger.error(f"❌ Errore durante l'analisi content-based: {str(e)}")
            results["error"] = str(e)

        return results

    def _create_empty_result(self, error_message: str) -> Dict[str, Any]:
        """Crea un risultato vuoto con messaggio di errore."""
        return {
            "filename": "",
            "total_rows": 0,
            "total_columns": 0,
            "sample_size": 0,
            "analysis_timestamp": datetime.now().isoformat(),
            "detected_type": "unknown",
            "confidence_score": 0.0,
            "type_scores": {},
            "content_analysis": {},
            "entity_extraction": {},
            "recommendations": [],
            "error": error_message
        }

    def _prepare_sample(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prepara un campione rappresentativo del DataFrame per l'analisi.

        Args:
            df: DataFrame originale

        Returns:
            DataFrame campione
        """
        if len(df) <= self.MIN_SAMPLE_SIZE:
            return df

        # Prendi un campione stratificato
        sample_size = min(self.MAX_SAMPLE_SIZE, max(self.MIN_SAMPLE_SIZE, len(df) // 10))

        # Campionamento: prime righe + campione casuale + ultime righe
        head_size = min(5, sample_size // 3)
        tail_size = min(5, sample_size // 3)
        middle_size = sample_size - head_size - tail_size

        sample_parts = []

        # Prime righe
        if head_size > 0:
            sample_parts.append(df.head(head_size))

        # Campione casuale dal centro
        if middle_size > 0 and len(df) > head_size + tail_size:
            middle_df = df.iloc[head_size:-tail_size] if tail_size > 0 else df.iloc[head_size:]
            if len(middle_df) > 0:
                sample_parts.append(middle_df.sample(min(middle_size, len(middle_df))))

        # Ultime righe
        if tail_size > 0:
            sample_parts.append(df.tail(tail_size))

        return pd.concat(sample_parts, ignore_index=True) if sample_parts else df.head(self.MIN_SAMPLE_SIZE)

    def _analyze_column_semantics(self, columns: List[str]) -> Dict[str, Any]:
        """
        Analizza la semantica dei nomi delle colonne.

        Args:
            columns: Lista dei nomi delle colonne

        Returns:
            Dizionario con analisi semantica
        """
        analysis = {
            "total_columns": len(columns),
            "semantic_matches": {},
            "pattern_scores": {}
        }

        # Normalizza i nomi delle colonne
        normalized_columns = [self._normalize_text(col) for col in columns]
        combined_text = " ".join(normalized_columns)

        # Analizza ogni tipo di file
        for file_type, patterns in self.CONTENT_PATTERNS.items():
            semantic_score = 0.0
            matches = []

            for indicator in patterns["semantic_indicators"]:
                pattern = indicator["pattern"]
                weight = indicator["weight"]

                # Cerca pattern nel testo combinato delle colonne
                matches_found = re.findall(pattern, combined_text, re.IGNORECASE)
                if matches_found:
                    match_score = len(matches_found) * weight
                    semantic_score += match_score
                    matches.extend(matches_found)

            # Normalizza il punteggio
            max_possible_score = sum(ind["weight"] for ind in patterns["semantic_indicators"])
            normalized_score = min(1.0, semantic_score / max_possible_score) if max_possible_score > 0 else 0.0

            analysis["semantic_matches"][file_type] = matches
            analysis["pattern_scores"][file_type] = normalized_score

        return analysis

    def _analyze_data_content(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Analizza il contenuto effettivo dei dati.

        Args:
            df: DataFrame campione da analizzare

        Returns:
            Dizionario con analisi del contenuto
        """
        analysis = {
            "data_type_distribution": {},
            "pattern_matches": {},
            "value_analysis": {}
        }

        # Analizza ogni colonna
        for col in df.columns:
            col_analysis = self._analyze_column_data(df[col])
            analysis["value_analysis"][col] = col_analysis

        # Cerca pattern specifici nei dati
        for file_type, patterns in self.CONTENT_PATTERNS.items():
            pattern_score = 0.0
            matches = {}

            for data_pattern in patterns["data_patterns"]:
                pattern_type = data_pattern["type"]
                pattern_regex = data_pattern["pattern"]
                weight = data_pattern["weight"]

                # Cerca il pattern in tutte le colonne
                total_matches = 0
                total_values = 0

                for col in df.columns:
                    col_matches = 0
                    col_values = 0

                    for value in df[col].dropna().astype(str):
                        col_values += 1
                        if re.match(pattern_regex, str(value).strip(), re.IGNORECASE):
                            col_matches += 1

                    total_matches += col_matches
                    total_values += col_values

                # Calcola il punteggio per questo pattern
                if total_values > 0:
                    match_ratio = total_matches / total_values
                    pattern_score += match_ratio * weight
                    matches[pattern_type] = {
                        "matches": total_matches,
                        "total": total_values,
                        "ratio": match_ratio
                    }

            analysis["pattern_matches"][file_type] = {
                "score": pattern_score,
                "matches": matches
            }

        return analysis

    def _analyze_column_data(self, series: pd.Series) -> Dict[str, Any]:
        """
        Analizza i dati di una singola colonna.

        Args:
            series: Serie pandas da analizzare

        Returns:
            Dizionario con analisi della colonna
        """
        analysis = {
            "total_values": len(series),
            "non_null_values": series.count(),
            "null_percentage": (len(series) - series.count()) / len(series) * 100,
            "unique_values": series.nunique(),
            "data_types": {},
            "sample_values": []
        }

        # Campione di valori non nulli
        non_null_series = series.dropna()
        if len(non_null_series) > 0:
            sample_size = min(10, len(non_null_series))
            analysis["sample_values"] = non_null_series.head(sample_size).tolist()

            # Analisi dei tipi di dati
            type_counts = Counter()
            for value in non_null_series:
                value_str = str(value).strip()
                detected_type = self._detect_value_type(value_str)
                type_counts[detected_type] += 1

            # Converti in percentuali
            total = sum(type_counts.values())
            for data_type, count in type_counts.items():
                analysis["data_types"][data_type] = {
                    "count": count,
                    "percentage": (count / total) * 100
                }

        return analysis

    def _detect_value_type(self, value: str) -> str:
        """
        Rileva il tipo di un valore basandosi sul contenuto.

        Args:
            value: Valore da analizzare

        Returns:
            Tipo rilevato
        """
        if not value or value.lower() in ['nan', 'null', 'none', '']:
            return "empty"

        # Pattern per diversi tipi di dati
        patterns = {
            "ticket_id": r"^[A-Z]{2,4}-?\d{3,6}$",
            "email": r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$",
            "phone": r"^[\+]?[1-9][\d\s\-\(\)]{7,15}$",
            "date": r"\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}",
            "time": r"\d{1,2}:\d{2}(:\d{2})?",
            "duration": r"^\d{1,3}:\d{2}(:\d{2})?$",
            "number": r"^\d+(\.\d+)?$",
            "currency": r"^[\€\$\£]?\d+(\.\d{2})?$",
            "percentage": r"^\d+(\.\d+)?%$",
            "ip_address": r"^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$",
            "url": r"^https?://[^\s]+$"
        }

        for data_type, pattern in patterns.items():
            if re.match(pattern, value, re.IGNORECASE):
                return data_type

        # Controlli aggiuntivi
        if value.isdigit():
            return "integer"

        try:
            float(value)
            return "float"
        except ValueError:
            pass

        if len(value) > 50:
            return "long_text"
        elif len(value) > 10:
            return "text"
        else:
            return "short_text"

    def _calculate_content_scores(self, column_analysis: Dict[str, Any],
                                 data_analysis: Dict[str, Any]) -> Dict[str, float]:
        """
        Calcola i punteggi finali per ogni tipo di file.

        Args:
            column_analysis: Analisi semantica delle colonne
            data_analysis: Analisi del contenuto dei dati

        Returns:
            Dizionario con punteggi per tipo di file
        """
        scores = {}

        for file_type in self.CONTENT_PATTERNS.keys():
            # Punteggio semantico dalle colonne (peso 40%)
            semantic_score = column_analysis["pattern_scores"].get(file_type, 0.0)

            # Punteggio dai pattern dei dati (peso 60%)
            data_score = data_analysis["pattern_matches"].get(file_type, {}).get("score", 0.0)

            # Normalizza il punteggio dei dati
            max_data_score = sum(p["weight"] for p in self.CONTENT_PATTERNS[file_type]["data_patterns"])
            normalized_data_score = min(1.0, data_score / max_data_score) if max_data_score > 0 else 0.0

            # Calcola punteggio finale ponderato
            final_score = (semantic_score * 0.4) + (normalized_data_score * 0.6)
            scores[file_type] = final_score

        return scores

    def _extract_entities(self, df: pd.DataFrame, file_type: str) -> Dict[str, Any]:
        """
        Estrae entità specifiche per il tipo di file rilevato.

        Args:
            df: DataFrame da cui estrarre entità
            file_type: Tipo di file rilevato

        Returns:
            Dizionario con entità estratte
        """
        entities = {
            "extracted_count": 0,
            "entities_by_type": {},
            "confidence_scores": {}
        }

        # Definisci estrattori specifici per tipo
        extractors = {
            "attivita": self._extract_activity_entities,
            "timbrature": self._extract_timesheet_entities,
            "teamviewer": self._extract_teamviewer_entities,
            "calendario": self._extract_calendar_entities,
            "registro_auto": self._extract_vehicle_entities
        }

        if file_type in extractors:
            try:
                extracted = extractors[file_type](df)
                entities.update(extracted)
            except Exception as e:
                logger.warning(f"Errore nell'estrazione entità per {file_type}: {str(e)}")

        return entities

    def _extract_activity_entities(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Estrae entità specifiche per file di attività."""
        entities = {
            "tickets": [],
            "employees": set(),
            "clients": set(),
            "durations": [],
            "date_range": {}
        }

        for col in df.columns:
            col_lower = col.lower()

            # Estrai ID ticket
            if any(keyword in col_lower for keyword in ["ticket", "id", "numero"]):
                for value in df[col].dropna():
                    if re.match(r"^[A-Z]{2,4}-?\d{3,6}$", str(value)):
                        entities["tickets"].append(str(value))

            # Estrai nomi dipendenti
            elif any(keyword in col_lower for keyword in ["creato", "tecnico", "operatore", "user"]):
                for value in df[col].dropna():
                    if isinstance(value, str) and len(value.strip()) > 2:
                        entities["employees"].add(value.strip())

            # Estrai clienti/contratti
            elif any(keyword in col_lower for keyword in ["cliente", "contratto", "client"]):
                for value in df[col].dropna():
                    if isinstance(value, str) and len(value.strip()) > 2:
                        entities["clients"].add(value.strip())

        # Converti set in liste per serializzazione
        entities["employees"] = list(entities["employees"])
        entities["clients"] = list(entities["clients"])

        return entities

    def _extract_timesheet_entities(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Estrae entità specifiche per file di timbrature."""
        return {"punch_types": [], "employees": [], "time_patterns": []}

    def _extract_teamviewer_entities(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Estrae entità specifiche per file TeamViewer."""
        return {"computer_ids": [], "users": [], "session_durations": []}

    def _extract_calendar_entities(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Estrae entità specifiche per file calendario."""
        return {"events": [], "participants": [], "locations": []}

    def _extract_vehicle_entities(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Estrae entità specifiche per registro auto."""
        return {"license_plates": [], "drivers": [], "routes": []}

    def _generate_recommendations(self, df: pd.DataFrame, file_type: str,
                                confidence: float) -> List[str]:
        """
        Genera raccomandazioni basate sull'analisi.

        Args:
            df: DataFrame analizzato
            file_type: Tipo rilevato
            confidence: Livello di confidenza

        Returns:
            Lista di raccomandazioni
        """
        recommendations = []

        if confidence < 0.8:
            recommendations.append(f"Confidenza moderata ({confidence:.2f}). Verificare manualmente il tipo rilevato.")

        if len(df) < 10:
            recommendations.append("Dataset piccolo. Risultati potrebbero essere meno affidabili.")

        # Raccomandazioni specifiche per tipo
        type_recommendations = {
            "attivita": [
                "Verificare che tutte le attività abbiano date di inizio e fine",
                "Controllare la coerenza dei formati delle durate"
            ],
            "timbrature": [
                "Verificare la presenza di timbrature di entrata e uscita bilanciate",
                "Controllare orari anomali o fuori dall'orario lavorativo"
            ],
            "teamviewer": [
                "Verificare la validità degli ID computer",
                "Controllare la durata delle sessioni per identificare anomalie"
            ]
        }

        if file_type in type_recommendations:
            recommendations.extend(type_recommendations[file_type])

        return recommendations

    def _normalize_text(self, text: str) -> str:
        """
        Normalizza il testo per l'analisi.

        Args:
            text: Testo da normalizzare

        Returns:
            Testo normalizzato
        """
        if not isinstance(text, str):
            text = str(text)

        # Rimuovi caratteri speciali e normalizza
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        text = re.sub(r'\s+', ' ', text).strip()

        return text

# Istanza globale
content_based_analyzer = ContentBasedFileAnalyzer()
