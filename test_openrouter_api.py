#!/usr/bin/env python3
"""
Script di test per verificare l'API di OpenRouter
"""

import requests
import os
import json
from dotenv import load_dotenv

def test_openrouter_api():
    """Testa l'API di OpenRouter per recuperare tutti i modelli"""
    
    # Carica le variabili d'ambiente
    load_dotenv()
    api_key = os.getenv('OPENROUTER_API_KEY')
    
    print(f"🔑 API Key presente: {bool(api_key)}")
    
    if not api_key:
        print("❌ API Key non trovata nel file .env!")
        return
    
    # Headers per la richiesta
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'HTTP-Referer': 'http://localhost:5000',
        'X-Title': 'App Roberto'
    }
    
    try:
        print("🌐 Effettuando richiesta a OpenRouter...")
        response = requests.get(
            'https://openrouter.ai/api/v1/models', 
            headers=headers, 
            timeout=30
        )
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📋 Content-Type: {response.headers.get('Content-Type', 'N/A')}")
        
        if response.status_code == 200:
            data = response.json()
            models = data.get('data', [])
            
            print(f"🎯 Numero totale di modelli recuperati: {len(models)}")
            
            if models:
                print("\n📝 Primi 10 modelli:")
                for i, model in enumerate(models[:10]):
                    model_id = model.get('id', 'N/A')
                    model_name = model.get('name', 'N/A')
                    pricing = model.get('pricing', {})
                    prompt_price = float(pricing.get('prompt', '1'))
                    completion_price = float(pricing.get('completion', '1'))
                    
                    is_free = prompt_price == 0 and completion_price == 0
                    free_indicator = "🆓" if is_free else "💰"
                    
                    print(f"  {i+1:2d}. {free_indicator} {model_id} - {model_name}")
                
                # Conta i modelli per categoria
                free_models = [m for m in models if 
                             float(m.get('pricing', {}).get('prompt', '1')) == 0 and
                             float(m.get('pricing', {}).get('completion', '1')) == 0]
                
                # Modelli con quote gratuite (prezzi bassi)
                low_cost_models = [m for m in models if 
                                 float(m.get('pricing', {}).get('prompt', '1')) <= 0.0001]
                
                print(f"\n📈 Statistiche:")
                print(f"  🆓 Modelli completamente gratuiti: {len(free_models)}")
                print(f"  💸 Modelli a basso costo (≤ $0.0001): {len(low_cost_models)}")
                print(f"  📊 Modelli totali: {len(models)}")
                
                # Mostra alcuni modelli gratuiti
                if free_models:
                    print(f"\n🆓 Primi 10 modelli gratuiti:")
                    for i, model in enumerate(free_models[:10]):
                        print(f"  {i+1:2d}. {model.get('id', 'N/A')} - {model.get('name', 'N/A')}")
                
                # Salva tutti i modelli in un file per debug
                with open('openrouter_models_debug.json', 'w', encoding='utf-8') as f:
                    json.dump(models, f, indent=2, ensure_ascii=False)
                print(f"\n💾 Tutti i modelli salvati in 'openrouter_models_debug.json'")
                
            else:
                print("❌ Nessun modello trovato nella risposta!")
                
        else:
            print(f"❌ Errore nella richiesta: {response.status_code}")
            print(f"📄 Risposta: {response.text[:500]}...")
            
    except requests.exceptions.Timeout:
        print("⏰ Timeout nella richiesta a OpenRouter")
    except requests.exceptions.ConnectionError:
        print("🌐 Errore di connessione a OpenRouter")
    except Exception as e:
        print(f"❌ Errore generico: {e}")

if __name__ == "__main__":
    test_openrouter_api()
