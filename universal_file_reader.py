#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Universal File Reader per rilevamento automatico separatori e encoding
"""

import pandas as pd
import csv
import chardet
from typing import Tuple, Optional

class UniversalFileReader:
    """
    Lettore universale che rileva automaticamente:
    - Separatore CSV (virgola, punto e virgola, tab)
    - Encoding del file (UTF-8, ISO-8859-1, Windows-1252)
    - Formato file (CSV, Excel)
    """

    def __init__(self):
        # Separatori comuni da testare
        self.common_separators = [',', ';', '\t', '|']
        
        # Encoding comuni da testare
        self.common_encodings = ['utf-8', 'utf-8-sig', 'iso-8859-1', 'windows-1252', 'cp1252']

    def read_file(self, file_path: str) -> Tuple[pd.DataFrame, dict]:
        """
        Legge un file rilevando automaticamente formato, separatore ed encoding.
        
        Args:
            file_path: Percorso del file da leggere
            
        Returns:
            Tuple contenente:
            - DataFrame pandas con i dati
            - Dizionario con informazioni di lettura (separatore, encoding, etc.)
        """
        file_info = {
            'file_path': file_path,
            'file_type': None,
            'separator': None,
            'encoding': None,
            'rows': 0,
            'columns': 0,
            'success': False,
            'error': None
        }
        
        try:
            # Determina il tipo di file dall'estensione
            if file_path.lower().endswith(('.xlsx', '.xls')):
                return self._read_excel(file_path, file_info)
            elif file_path.lower().endswith('.csv'):
                return self._read_csv(file_path, file_info)
            else:
                # Prova a leggere come CSV per file senza estensione o estensioni sconosciute
                return self._read_csv(file_path, file_info)
                
        except Exception as e:
            file_info['error'] = str(e)
            file_info['success'] = False
            return pd.DataFrame(), file_info

    def _read_excel(self, file_path: str, file_info: dict) -> Tuple[pd.DataFrame, dict]:
        """Legge file Excel"""
        try:
            df = pd.read_excel(file_path)
            file_info.update({
                'file_type': 'excel',
                'rows': len(df),
                'columns': len(df.columns),
                'success': True
            })
            return df, file_info
        except Exception as e:
            file_info['error'] = f"Errore lettura Excel: {str(e)}"
            return pd.DataFrame(), file_info

    def _read_csv(self, file_path: str, file_info: dict) -> Tuple[pd.DataFrame, dict]:
        """Legge file CSV con rilevamento automatico separatore ed encoding"""
        
        # 1. Rileva encoding
        encoding = self._detect_encoding(file_path)
        file_info['encoding'] = encoding
        
        # 2. Rileva separatore
        separator = self._detect_separator(file_path, encoding)
        file_info['separator'] = separator
        
        # 3. Leggi il file con i parametri rilevati
        try:
            df = pd.read_csv(file_path, sep=separator, encoding=encoding)
            
            # Verifica che la lettura sia andata a buon fine
            if len(df.columns) == 1 and separator != ',':
                # Prova con virgola come fallback
                print(f"⚠️ Rilevato solo 1 colonna con separatore '{separator}', provo con virgola...")
                df = pd.read_csv(file_path, sep=',', encoding=encoding)
                separator = ','
                file_info['separator'] = separator
            
            file_info.update({
                'file_type': 'csv',
                'rows': len(df),
                'columns': len(df.columns),
                'success': True
            })
            
            print(f"✅ File letto con successo:")
            print(f"   - Encoding: {encoding}")
            print(f"   - Separatore: '{separator}'")
            print(f"   - Righe: {len(df)}")
            print(f"   - Colonne: {len(df.columns)}")
            
            return df, file_info
            
        except Exception as e:
            file_info['error'] = f"Errore lettura CSV: {str(e)}"
            return pd.DataFrame(), file_info

    def _detect_encoding(self, file_path: str) -> str:
        """Rileva l'encoding del file"""
        try:
            # Leggi un campione del file per rilevare l'encoding
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # Leggi primi 10KB
            
            # Usa chardet per rilevare l'encoding
            result = chardet.detect(raw_data)
            detected_encoding = result['encoding']
            confidence = result['confidence']
            
            print(f"🔍 Encoding rilevato: {detected_encoding} (confidenza: {confidence:.2f})")
            
            # Se la confidenza è bassa, usa UTF-8 come fallback
            if confidence < 0.7:
                print(f"⚠️ Confidenza bassa, uso UTF-8 come fallback")
                return 'utf-8'
            
            # Normalizza nomi encoding comuni
            if detected_encoding:
                detected_encoding = detected_encoding.lower()
                if 'utf-8' in detected_encoding:
                    return 'utf-8'
                elif 'iso-8859' in detected_encoding:
                    return 'iso-8859-1'
                elif 'windows-1252' in detected_encoding or 'cp1252' in detected_encoding:
                    return 'windows-1252'
            
            return detected_encoding or 'utf-8'
            
        except Exception as e:
            print(f"⚠️ Errore rilevamento encoding: {e}, uso UTF-8")
            return 'utf-8'

    def _detect_separator(self, file_path: str, encoding: str) -> str:
        """Rileva il separatore CSV"""
        try:
            # Leggi le prime righe del file
            with open(file_path, 'r', encoding=encoding) as f:
                sample = f.read(1024)  # Leggi primo KB
            
            # Usa il Sniffer di Python per rilevare il separatore
            sniffer = csv.Sniffer()
            try:
                dialect = sniffer.sniff(sample, delimiters=',;\t|')
                detected_sep = dialect.delimiter
                print(f"🔍 Separatore rilevato dal Sniffer: '{detected_sep}'")
                return detected_sep
            except:
                pass
            
            # Fallback: conta le occorrenze di ogni separatore
            separator_counts = {}
            for sep in self.common_separators:
                separator_counts[sep] = sample.count(sep)
            
            # Trova il separatore più comune
            best_separator = max(separator_counts, key=separator_counts.get)
            best_count = separator_counts[best_separator]
            
            print(f"🔍 Conteggio separatori: {separator_counts}")
            print(f"🔍 Separatore più comune: '{best_separator}' ({best_count} occorrenze)")
            
            # Se nessun separatore è molto comune, usa virgola come default
            if best_count < 2:
                print(f"⚠️ Nessun separatore chiaro, uso virgola come default")
                return ','
            
            return best_separator
            
        except Exception as e:
            print(f"⚠️ Errore rilevamento separatore: {e}, uso virgola")
            return ','

    def get_file_sample(self, file_path: str, n_rows: int = 5) -> Tuple[pd.DataFrame, dict]:
        """
        Legge solo le prime n righe del file per anteprima veloce.
        
        Args:
            file_path: Percorso del file
            n_rows: Numero di righe da leggere
            
        Returns:
            Tuple con DataFrame campione e info file
        """
        df, file_info = self.read_file(file_path)
        
        if file_info['success'] and not df.empty:
            sample_df = df.head(n_rows)
            file_info['is_sample'] = True
            file_info['sample_rows'] = len(sample_df)
            return sample_df, file_info
        
        return df, file_info

# Istanza globale per uso nell'applicazione
universal_reader = UniversalFileReader()
