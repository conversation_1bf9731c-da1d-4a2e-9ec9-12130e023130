#!/usr/bin/env python3
"""
Test del Real File Analyzer sui file reali
"""

import sys
sys.path.append('.')

from real_file_analyzer import real_file_analyzer
import json

def test_real_analyzer():
    print("🔍 TEST REAL FILE ANALYZER")
    print("=" * 60)
    
    # Test analisi directory uploads
    print("\n📁 ANALISI DIRECTORY UPLOADS")
    directory_results = real_file_analyzer.analyze_directory("uploads")
    
    print(f"\n📊 RIEPILOGO ANALISI:")
    print(f"   - File totali: {directory_results['total_files']}")
    print(f"   - File analizzati: {directory_results['analyzed_files']}")
    print(f"   - Ana<PERSON>i riuscite: {directory_results['successful_analyses']}")
    
    print(f"\n🎯 TIPI FILE RILEVATI:")
    for file_type, count in directory_results['file_types_found'].items():
        print(f"   - {file_type}: {count} file")
    
    print(f"\n📋 DETTAGLIO FILE:")
    for file_analysis in directory_results['files']:
        if file_analysis['filename'] == 'Controlli quotidiani.xlsx':
            continue  # Salta il file di controllo
            
        print(f"\n📄 {file_analysis['filename']}")
        print(f"   Tipo rilevato: {file_analysis['detected_type']}")
        print(f"   Confidenza: {file_analysis['confidence_score']:.2f}")
        print(f"   Righe: {file_analysis.get('rows', 'N/A')}")
        print(f"   Colonne: {file_analysis.get('columns', 'N/A')}")
        
        if file_analysis.get('column_mapping'):
            print(f"   Mapping colonne:")
            for std_name, actual_name in file_analysis['column_mapping'].items():
                print(f"     {std_name} → {actual_name}")
        
        if file_analysis.get('data_quality'):
            quality = file_analysis['data_quality']
            print(f"   Qualità dati: {quality.get('overall_score', 0):.2f}")
            if quality.get('missing_required_fields'):
                print(f"   Campi mancanti: {len(quality['missing_required_fields'])}")
        
        if file_analysis.get('recommendations'):
            print(f"   Raccomandazioni: {len(file_analysis['recommendations'])}")
            for rec in file_analysis['recommendations'][:2]:  # Prime 2
                print(f"     - {rec}")
    
    # Test file specifici
    print(f"\n" + "=" * 60)
    print("🎯 TEST FILE SPECIFICI")
    
    test_files = [
        "uploads/export_6_1747922881_f92c9e3f.xlsx",  # Attività
        "uploads/connectionreport_5_1747922973_18d3c766.csv",  # TeamViewer
        "uploads/apprilevazionepresenze-timbrature-totali-base-2025-05-01-2025-05-31_1_1747923212_c6878d2b.xlsx",  # Timbrature
        "uploads/auto_1747923310_1ed039f6.CSV"  # Registro Auto
    ]
    
    for file_path in test_files:
        print(f"\n🔍 ANALISI DETTAGLIATA: {file_path.split('/')[-1]}")
        analysis = real_file_analyzer.analyze_file(file_path)
        
        if analysis['success']:
            print(f"   ✅ Tipo: {analysis['detected_type']} (confidenza: {analysis['confidence_score']:.2f})")
            print(f"   📊 Dati: {analysis['rows']} righe, {analysis['columns']} colonne")
            
            # Mostra punteggi per tipo
            print(f"   🎯 Punteggi per tipo:")
            for file_type, score in sorted(analysis['type_scores'].items(), key=lambda x: x[1], reverse=True)[:3]:
                print(f"     {file_type}: {score:.3f}")
            
            # Mostra mapping colonne più importante
            if analysis.get('column_mapping'):
                print(f"   🗂️ Mapping colonne principali:")
                for std_name, actual_name in list(analysis['column_mapping'].items())[:5]:
                    print(f"     {std_name} → {actual_name}")
            
            # Qualità dati
            if analysis.get('data_quality'):
                quality = analysis['data_quality']
                print(f"   📈 Qualità: {quality.get('overall_score', 0):.2f}")
                if quality.get('empty_rows', 0) > 0:
                    print(f"     Righe vuote: {quality['empty_rows']}")
                if quality.get('duplicate_rows', 0) > 0:
                    print(f"     Righe duplicate: {quality['duplicate_rows']}")
        else:
            print(f"   ❌ Errore: {analysis.get('error', 'Sconosciuto')}")
    
    print(f"\n" + "=" * 60)
    print("✅ TEST COMPLETATO!")

if __name__ == "__main__":
    test_real_analyzer()
