@echo off
echo ===================================
echo Arresto dell'applicazione app-roberto
echo ===================================
echo.

echo Arresto dei processi Python in esecuzione...

REM Arresta tutti i processi Python associati all'applicazione
taskkill /f /im python.exe /fi "WINDOWTITLE eq MCP Server*" 2>nul
if %errorlevel% equ 0 (
    echo Server MCP arrestato.
) else (
    echo Il server MCP non era in esecuzione.
)

REM Arresta anche eventuali processi uvicorn
taskkill /f /im uvicorn.exe 2>nul
if %errorlevel% equ 0 (
    echo Processo uvicorn arrestato.
)

echo.
echo ===================================
echo Applicazione arrestata
echo ===================================
pause
