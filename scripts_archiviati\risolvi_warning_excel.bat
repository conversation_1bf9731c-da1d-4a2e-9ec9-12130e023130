@echo off
echo ===================================
echo Risoluzione definitiva warning Excel COM
echo ===================================
echo.

set CLEAN_ENV=clean_env

if not exist %CLEAN_ENV% (
    echo L'ambiente virtuale %CLEAN_ENV% non esiste.
    echo Crearlo prima con avvio_app.bat
    goto :EOF
)

echo Attivazione ambiente virtuale...
call %CLEAN_ENV%\Scripts\activate
echo.

echo Installazione pywin32 per supporto Excel COM...
pip install pywin32
echo.

echo Installazione comtypes (libreria alternativa COM)...
pip install comtypes
echo.

echo Installazione xlwings (libreria avanzata per Excel)...
pip install xlwings
echo.

echo Installazione openpyxl con supporto completo...
pip install openpyxl
echo.

echo Installazione xlrd per supporto vecchi formati Excel...
pip install xlrd
echo.

echo Installazione xlsxwriter per scrittura avanzata Excel...
pip install xlsxwriter
echo.

echo Creazione script di patch per pandas...
echo import pandas as pd > %CLEAN_ENV%\patch_pandas_excel.py
echo import warnings >> %CLEAN_ENV%\patch_pandas_excel.py
echo import os >> %CLEAN_ENV%\patch_pandas_excel.py
echo. >> %CLEAN_ENV%\patch_pandas_excel.py
echo # Filtra il warning specifico sulle librerie COM >> %CLEAN_ENV%\patch_pandas_excel.py
echo warnings.filterwarnings("ignore", message="Librerie COM non disponibili") >> %CLEAN_ENV%\patch_pandas_excel.py
echo warnings.filterwarnings("ignore", message=".*Excel.*") >> %CLEAN_ENV%\patch_pandas_excel.py
echo. >> %CLEAN_ENV%\patch_pandas_excel.py
echo print("Patch per warning Excel applicata con successo!") >> %CLEAN_ENV%\patch_pandas_excel.py
echo.

echo Esecuzione patch...
python %CLEAN_ENV%\patch_pandas_excel.py
echo.

echo Creazione file di avvio senza warning...
echo @echo off > avvia_senza_warning.bat
echo echo Avvio applicazione senza warning Excel... >> avvia_senza_warning.bat
echo call %CLEAN_ENV%\Scripts\activate >> avvia_senza_warning.bat
echo. >> avvia_senza_warning.bat
echo REM Imposta le variabili d'ambiente >> avvia_senza_warning.bat
echo set OPENROUTER_API_KEY=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 >> avvia_senza_warning.bat
echo set APP_URL=http://localhost:5000 >> avvia_senza_warning.bat
echo set MCP_URL=http://localhost:8000 >> avvia_senza_warning.bat
echo. >> avvia_senza_warning.bat
echo REM Filtra i warning di Excel >> avvia_senza_warning.bat
echo set PYTHONWARNINGS=ignore::UserWarning:pandas.io.excel >> avvia_senza_warning.bat
echo. >> avvia_senza_warning.bat
echo echo Avvio del server MCP... >> avvia_senza_warning.bat
echo start "MCP Server" cmd /k "call %CLEAN_ENV%\Scripts\activate ^&^& cd mcp_server ^&^& python -W ignore run_server.py" >> avvia_senza_warning.bat
echo. >> avvia_senza_warning.bat
echo echo Attesa avvio server (5 secondi)... >> avvia_senza_warning.bat
echo ping 127.0.0.1 -n 6 ^> nul >> avvia_senza_warning.bat
echo. >> avvia_senza_warning.bat
echo echo Avvio applicazione principale... >> avvia_senza_warning.bat
echo start "App Roberto" cmd /k "call %CLEAN_ENV%\Scripts\activate ^&^& python -W ignore app.py" >> avvia_senza_warning.bat
echo. >> avvia_senza_warning.bat
echo echo ===================================== >> avvia_senza_warning.bat
echo echo Applicazione avviata senza warning Excel! >> avvia_senza_warning.bat
echo echo - Flask: http://localhost:5000 >> avvia_senza_warning.bat
echo echo - MCP: http://localhost:8000 >> avvia_senza_warning.bat
echo echo ===================================== >> avvia_senza_warning.bat
echo. >> avvia_senza_warning.bat
echo pause >> avvia_senza_warning.bat
echo.

echo ===================================
echo Soluzione warning Excel completata!
echo.
echo Per avviare l'applicazione senza warning Excel:
echo   1. Esegui "avvia_senza_warning.bat"
echo.
echo Questo script ha:
echo   1. Installato tutte le librerie necessarie per Excel
echo   2. Creato una patch per filtrare i warning
echo   3. Creato un nuovo script di avvio che sopprime i warning
echo ===================================
echo.
pause
