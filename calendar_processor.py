#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import re
from data_processor import DataProcessor

class CalendarProcessor:
    """
    Classe specifica per l'elaborazione dei dati di calendario.
    Estende le funzionalità di DataProcessor per gestire le specificità dei file di calendario.
    """

    def __init__(self):
        self.data_processor = DataProcessor()

        # Mappatura specifica per i campi di calendario
        self.calendar_fields = {
            'SUMMARY': 'titolo',
            'DESCRIPTION': 'descrizione',
            'DTSTART': 'data_inizio',
            'DTEND': 'data_fine',
            'LOCATION': 'luogo',
            'ATTENDEE': 'partecipanti',
            'ORGANIZER': 'organizzatore',
            'PRIORITY': 'priorita',
            'STATUS': 'stato',
            'CATEGORIES': 'categorie'
        }

        # Mappatura degli stati degli eventi
        self.event_status = {
            'CONFIRMED': 'confermato',
            'TENTATIVE': 'provvisorio',
            'CANCELLED': 'annullato'
        }

        # Mappatura delle priorità
        self.priority_levels = {
            '1': 'alta',
            '2': 'media',
            '3': 'bassa',
            '0': 'non specificata'
        }

    def process_calendar_file(self, file_path):
        """
        Elabora un file di calendario e restituisce un DataFrame standardizzato
        """
        # Leggi il file con gestione robusta degli errori
        if file_path.endswith('.csv'):
            df = self._read_csv_robust(file_path)
        elif file_path.endswith(('.xlsx', '.xls')):
            df = pd.read_excel(file_path)
        else:
            raise ValueError("Formato file non supportato")

        # Standardizza i nomi delle colonne
        df = self._standardize_calendar_columns(df)

        # Elabora le date
        df = self._process_calendar_dates(df)

        # Elabora i partecipanti
        df = self._process_calendar_attendees(df)

        # Calcola metriche aggiuntive
        df = self._calculate_additional_metrics(df)

        return df

    def _read_csv_robust(self, file_path):
        """
        Legge un file CSV calendario con gestione robusta degli errori di parsing.
        """
        # 1. Rileva encoding
        try:
            import chardet
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)
            encoding_result = chardet.detect(raw_data)
            encoding = encoding_result.get('encoding', 'utf-8')
        except ImportError:
            print("⚠️ chardet non disponibile, uso utf-8")
            encoding = 'utf-8'

        print(f"🔍 Encoding rilevato: {encoding}")

        # 2. Prova diversi metodi di lettura
        read_attempts = [
            # Tentativo 1: Separatore ; con gestione virgolette
            {'sep': ';', 'encoding': 'utf-8-sig', 'quoting': 1, 'engine': 'python'},
            # Tentativo 2: Separatore ; standard
            {'sep': ';', 'encoding': 'utf-8-sig'},
            # Tentativo 3: Separatore , con gestione virgolette
            {'sep': ',', 'encoding': 'utf-8-sig', 'quoting': 1, 'engine': 'python'},
            # Tentativo 4: Separatore , standard
            {'sep': ',', 'encoding': 'utf-8-sig'},
            # Tentativo 5: Con encoding rilevato
            {'sep': ';', 'encoding': encoding, 'quoting': 1, 'engine': 'python'},
            # Tentativo 6: Lettura manuale e correzione
            {'manual': True}
        ]

        for i, params in enumerate(read_attempts):
            try:
                print(f"🔄 Tentativo {i+1}: {params}")

                if params.get('manual'):
                    # Lettura manuale e correzione
                    df = self._read_csv_manual_fix(file_path)
                else:
                    df = pd.read_csv(file_path, **params)

                # Verifica che la lettura sia andata bene
                if len(df.columns) > 1 and len(df) > 0:
                    print(f"✅ Lettura riuscita con tentativo {i+1}")
                    print(f"   - Righe: {len(df)}")
                    print(f"   - Colonne: {len(df.columns)}")
                    print(f"   - Colonne: {df.columns.tolist()}")
                    return df
                else:
                    print(f"⚠️ Tentativo {i+1} ha prodotto risultati insufficienti")

            except Exception as e:
                print(f"❌ Tentativo {i+1} fallito: {str(e)}")
                continue

        # Se tutti i tentativi falliscono, solleva un'eccezione
        raise ValueError("Impossibile leggere il file CSV calendario con nessuno dei metodi disponibili")

    def _read_csv_manual_fix(self, file_path):
        """
        Legge il file CSV manualmente correggendo i problemi di formattazione.
        """
        print("🔧 Lettura manuale con correzione...")

        # Leggi il file come testo
        with open(file_path, 'r', encoding='utf-8-sig') as f:
            lines = f.readlines()

        if not lines:
            raise ValueError("File vuoto")

        # Analizza la prima riga per determinare il separatore
        header_line = lines[0].strip()
        if ';' in header_line:
            separator = ';'
        elif ',' in header_line:
            separator = ','
        else:
            raise ValueError("Separatore non riconosciuto")

        print(f"🔍 Separatore rilevato: '{separator}'")

        # Estrai le colonne dall'header
        headers = [col.strip().strip('"') for col in header_line.split(separator)]
        print(f"🔍 Colonne rilevate: {headers}")

        # Processa le righe dati
        data_rows = []
        for line_num, line in enumerate(lines[1:], 2):
            try:
                line = line.strip()
                if not line:
                    continue

                # Gestisci le virgolette e i caratteri speciali
                # Sostituisci caratteri problematici
                line = line.replace('\t', ' ')  # Tab -> spazio

                # Split intelligente che rispetta le virgolette
                values = self._smart_split(line, separator)

                # Assicurati che abbiamo il numero giusto di colonne
                while len(values) < len(headers):
                    values.append('')

                # Tronca se abbiamo troppe colonne
                values = values[:len(headers)]

                data_rows.append(values)

            except Exception as e:
                print(f"⚠️ Errore riga {line_num}: {str(e)}")
                # Aggiungi una riga vuota per mantenere la struttura
                data_rows.append([''] * len(headers))

        # Crea il DataFrame
        df = pd.DataFrame(data_rows, columns=headers)
        print(f"✅ DataFrame creato manualmente: {len(df)} righe, {len(df.columns)} colonne")

        return df

    def _smart_split(self, line, separator):
        """
        Split intelligente che rispetta le virgolette.
        """
        values = []
        current_value = ""
        in_quotes = False
        i = 0

        while i < len(line):
            char = line[i]

            if char == '"':
                in_quotes = not in_quotes
            elif char == separator and not in_quotes:
                values.append(current_value.strip().strip('"'))
                current_value = ""
                i += 1
                continue
            else:
                current_value += char

            i += 1

        # Aggiungi l'ultimo valore
        values.append(current_value.strip().strip('"'))

        return values

    def _standardize_calendar_columns(self, df):
        """
        Standardizza i nomi delle colonne specifiche di calendario
        """
        renamed_columns = {}

        for col in df.columns:
            if col in self.calendar_fields:
                renamed_columns[col] = self.calendar_fields[col]

        if renamed_columns:
            return df.rename(columns=renamed_columns)

        return df

    def _process_calendar_dates(self, df):
        """
        Elabora le colonne di date specifiche di calendario
        """
        date_columns = ['data_inizio', 'data_fine']

        for col in date_columns:
            if col in df.columns:
                df[col] = df[col].apply(self.data_processor.parse_italian_date)

        # Calcola la durata degli eventi
        if 'data_inizio' in df.columns and 'data_fine' in df.columns:
            df['durata'] = (df['data_fine'] - df['data_inizio']).dt.total_seconds() / 60

            # Formatta la durata in ore e minuti
            df['durata_formattata'] = df['durata'].apply(
                lambda x: f"{int(x // 60)}h {int(x % 60)}m" if x >= 60 else f"{int(x)}m"
            )

            # Aggiungi colonne per data e ora separate
            # Salva la data come stringa in formato ISO
            df['data'] = df['data_inizio'].dt.date.apply(lambda x: x.isoformat() if pd.notna(x) else None)

            # Salva gli oggetti time in colonne temporanee per i calcoli
            ora_inizio_series = df['data_inizio'].dt.time
            ora_fine_series = df['data_fine'].dt.time

            df['ora_inizio_obj'] = ora_inizio_series
            df['ora_fine_obj'] = ora_fine_series

            # Converti gli oggetti time in stringhe per la serializzazione JSON
            df['ora_inizio'] = ora_inizio_series.apply(lambda x: x.strftime('%H:%M') if pd.notna(x) else None)
            df['ora_fine'] = ora_fine_series.apply(lambda x: x.strftime('%H:%M') if pd.notna(x) else None)

            # Aggiungi colonne utili per l'analisi
            df['giorno_settimana'] = df['data_inizio'].dt.day_name()
            df['mese'] = df['data_inizio'].dt.month
            df['anno'] = df['data_inizio'].dt.year
            df['settimana_anno'] = df['data_inizio'].dt.isocalendar().week

        return df

    def _process_calendar_attendees(self, df):
        """
        Elabora la lista dei partecipanti
        """
        if 'partecipanti' in df.columns:
            # Estrai i nomi/email dai partecipanti
            df['num_partecipanti'] = df['partecipanti'].apply(
                lambda x: len(str(x).split(',')) if pd.notna(x) else 0
            )

            # Estrai il primo partecipante come responsabile
            df['responsabile'] = df['partecipanti'].apply(
                lambda x: str(x).split(',')[0].strip() if pd.notna(x) and len(str(x)) > 0 else None
            )

        return df

    def _calculate_additional_metrics(self, df):
        """
        Calcola metriche aggiuntive utili per l'analisi
        """
        # Categorizza gli eventi per durata
        if 'durata' in df.columns:
            df['categoria_durata'] = pd.cut(
                df['durata'],
                bins=[0, 30, 60, 120, 240, float('inf')],
                labels=['Breve (<30m)', 'Media (30-60m)', 'Lunga (1-2h)',
                        'Molto lunga (2-4h)', 'Estesa (>4h)']
            )

        # Categorizza gli eventi per ora del giorno
        if 'data_inizio' in df.columns:
            df['ora_del_giorno'] = df['data_inizio'].dt.hour

            df['fascia_oraria'] = pd.cut(
                df['ora_del_giorno'],
                bins=[0, 9, 12, 14, 18, 24],
                labels=['Mattina presto (0-9)', 'Mattina (9-12)', 'Pranzo (12-14)',
                        'Pomeriggio (14-18)', 'Sera (18-24)']
            )

        # Identifica eventi sovrapposti
        if 'data_inizio' in df.columns and 'data_fine' in df.columns:
            df = df.sort_values('data_inizio')
            df['evento_sovrapposto'] = False

            for i in range(1, len(df)):
                if df.iloc[i]['data_inizio'] < df.iloc[i-1]['data_fine']:
                    df.iloc[i, df.columns.get_loc('evento_sovrapposto')] = True

        # Rimuovi le colonne temporanee se esistono
        temp_columns = ['ora_inizio_obj', 'ora_fine_obj']
        for col in temp_columns:
            if col in df.columns:
                df.drop(col, axis=1, inplace=True)

        return df

    def generate_summary_stats(self, df):
        """
        Genera statistiche di riepilogo per i dati di calendario
        """
        stats = {}

        # Conteggio totale eventi
        stats['total_events'] = len(df)

        # Durata media degli eventi
        if 'durata' in df.columns:
            stats['avg_duration'] = df['durata'].mean()
            stats['total_duration'] = df['durata'].sum()

        # Eventi per categoria di durata
        if 'categoria_durata' in df.columns:
            stats['events_by_duration'] = df.groupby('categoria_durata').size().to_dict()

        # Eventi per fascia oraria
        if 'fascia_oraria' in df.columns:
            stats['events_by_time_slot'] = df.groupby('fascia_oraria').size().to_dict()

        # Eventi per giorno della settimana
        if 'giorno_settimana' in df.columns:
            stats['events_by_weekday'] = df.groupby('giorno_settimana').size().to_dict()

        # Eventi per luogo
        if 'luogo' in df.columns:
            stats['events_by_location'] = df.groupby('luogo').size().to_dict()

        # Eventi sovrapposti
        if 'evento_sovrapposto' in df.columns:
            stats['overlapping_events'] = df['evento_sovrapposto'].sum()

        return stats
