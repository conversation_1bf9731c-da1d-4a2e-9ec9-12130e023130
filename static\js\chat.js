/**
 * Script per la gestione della chat con l'assistente AI
 * Versione: 1.0.0
 */

// Stampa la versione nella console per verificare il caricamento
console.log('chat.js versione 1.0.0 caricato');

// Elementi DOM
const chatMessages = document.getElementById('chat-messages');
const messageInput = document.getElementById('message-input');
const sendButton = document.getElementById('send-button');
const modelSelector = document.getElementById('model-selector');

// Stato della chat
let isWaitingForResponse = false;
let chatHistory = [];

/**
 * Inizializza la chat
 */
function initChat() {
    console.log('Inizializzazione chat...');

    // Aggiungi event listeners
    if (sendButton) {
        sendButton.addEventListener('click', sendMessage);
    }

    if (messageInput) {
        messageInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        });
    }

    // Aggiungi event listener per il cambio di modello
    if (modelSelector) {
        modelSelector.addEventListener('change', updateModelInfo);

        // Aggiungi i controlli per il filtro dei modelli
        addModelFilters();
    }

    // Carica i modelli disponibili
    loadModels();

    // Scorri la chat verso il basso
    scrollChatToBottom();
}

/**
 * Aggiunge i controlli per il filtro dei modelli
 */
function addModelFilters() {
    // Crea il container per i filtri
    const filtersContainer = document.createElement('div');
    filtersContainer.className = 'model-filters mt-2 mb-2';
    filtersContainer.innerHTML = `
        <div class="form-check form-check-inline">
            <input class="form-check-input" type="checkbox" id="filter-free-quota" checked>
            <label class="form-check-label" for="filter-free-quota">Solo modelli con quote gratuite</label>
        </div>
        <div class="form-check form-check-inline">
            <input class="form-check-input" type="checkbox" id="filter-paid" checked>
            <label class="form-check-label" for="filter-paid">Includi modelli a pagamento</label>
        </div>
        <button id="apply-filters" class="btn btn-sm btn-outline-primary ms-2">Applica filtri</button>
    `;

    // Inserisci il container prima del selettore dei modelli
    modelSelector.parentNode.insertBefore(filtersContainer, modelSelector);

    // Aggiungi event listener per il pulsante di applicazione dei filtri
    const applyFiltersButton = document.getElementById('apply-filters');
    if (applyFiltersButton) {
        applyFiltersButton.addEventListener('click', function() {
            const freeQuotaFilter = document.getElementById('filter-free-quota');
            const paidFilter = document.getElementById('filter-paid');

            // Carica i modelli con i filtri applicati
            loadModels({
                includeFree: true, // Sempre includi i modelli gratuiti
                includePaid: paidFilter && paidFilter.checked,
                onlyFreeQuota: freeQuotaFilter && freeQuotaFilter.checked
            });
        });
    }
}

/**
 * Carica i modelli disponibili
 * @param {Object} filters - Filtri da applicare ai modelli
 * @param {boolean} filters.includeFree - Se includere i modelli gratuiti
 * @param {boolean} filters.includePaid - Se includere i modelli a pagamento
 * @param {boolean} filters.onlyFreeQuota - Se mostrare solo i modelli con quote gratuite
 */
function loadModels(filters = { includeFree: true, includePaid: true, onlyFreeQuota: false }) {
    // Verifica se il selettore dei modelli esiste
    if (!modelSelector) {
        console.error('Selettore dei modelli non trovato');
        return;
    }

    // Costruisci la query string per i filtri
    const queryParams = new URLSearchParams({
        include_free: filters.includeFree,
        include_paid: filters.includePaid,
        only_free_quota: filters.onlyFreeQuota
    });

    // Effettua la richiesta per ottenere i modelli disponibili
    console.log('Richiesta modelli a:', `/api/llm/models?${queryParams.toString()}`);

    fetch(`/api/llm/models?${queryParams.toString()}`)
        .then(response => {
            console.log('Risposta ricevuta:', response.status, response.statusText);
            return response.json();
        })
        .then(data => {
            console.log('Dati modelli ricevuti:', data);
            if (data.error) {
                console.error('Errore nel caricamento dei modelli:', data.error);
                return;
            }

            // Ottieni i modelli
            const models = data.models || [];

            // Verifica se ci sono modelli
            if (models.length === 0) {
                console.warn('Nessun modello disponibile');
                // Mostra un messaggio all'utente
                modelSelector.innerHTML = '<option value="" disabled selected>Nessun modello disponibile</option>';
                return;
            }

            // Salva il valore selezionato corrente
            const currentValue = modelSelector.value;

            // Svuota il selettore
            modelSelector.innerHTML = '';

            // Raggruppa i modelli per provider
            const modelsByProvider = {};
            models.forEach(model => {
                const id = model.id;
                const provider = id.split('/')[0];

                if (!modelsByProvider[provider]) {
                    modelsByProvider[provider] = [];
                }

                modelsByProvider[provider].push(model);
            });

            // Aggiungi i modelli al selettore, raggruppati per provider
            Object.keys(modelsByProvider).sort().forEach(provider => {
                const optgroup = document.createElement('optgroup');
                optgroup.label = provider.charAt(0).toUpperCase() + provider.slice(1); // Capitalizza il nome del provider

                modelsByProvider[provider].forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;

                    // Aggiungi il nome del modello
                    let modelName = model.name;

                    // Aggiungi indicatori per modelli gratuiti o con quote gratuite
                    if (model.is_free) {
                        modelName += ' 🆓';
                        option.classList.add('free-model');
                    } else if (model.has_free_quota) {
                        modelName += ' 🎁';
                        option.classList.add('free-quota-model');
                    }

                    option.textContent = modelName;

                    // Aggiungi informazioni sul prezzo e sul contesto come attributi data
                    option.dataset.promptPrice = model.pricing.prompt;
                    option.dataset.completionPrice = model.pricing.completion;
                    option.dataset.contextLength = model.context_length;
                    option.dataset.description = model.description || '';

                    optgroup.appendChild(option);
                });

                modelSelector.appendChild(optgroup);
            });

            // Ripristina il valore selezionato se esiste ancora, altrimenti seleziona il primo modello con quote gratuite
            if (currentValue && modelSelector.querySelector(`option[value="${currentValue}"]`)) {
                modelSelector.value = currentValue;
            } else {
                // Cerca un modello con quote gratuite come default
                const defaultModel = models.find(m => m.id === 'anthropic/claude-3-haiku') ||
                                    models.find(m => m.has_free_quota) ||
                                    models[0];

                if (defaultModel) {
                    modelSelector.value = defaultModel.id;
                }
            }

            // Aggiorna le informazioni sul modello selezionato
            updateModelInfo();
        })
        .catch(error => {
            console.error('Errore nella richiesta dei modelli:', error);
            modelSelector.innerHTML = '<option value="" disabled selected>Errore nel caricamento dei modelli</option>';
        });
}

/**
 * Aggiorna le informazioni sul modello selezionato
 */
function updateModelInfo() {
    // Verifica se il selettore dei modelli esiste
    if (!modelSelector) {
        return;
    }

    // Ottieni l'opzione selezionata
    const selectedOption = modelSelector.options[modelSelector.selectedIndex];
    if (!selectedOption) {
        return;
    }

    // Ottieni le informazioni sul modello
    const modelInfo = {
        promptPrice: selectedOption.dataset.promptPrice,
        completionPrice: selectedOption.dataset.completionPrice,
        contextLength: selectedOption.dataset.contextLength,
        description: selectedOption.dataset.description
    };

    // Trova o crea il container per le informazioni sul modello
    let modelInfoContainer = document.getElementById('model-info');
    if (!modelInfoContainer) {
        modelInfoContainer = document.createElement('div');
        modelInfoContainer.id = 'model-info';
        modelInfoContainer.className = 'mt-2 small text-muted';
        modelSelector.parentNode.appendChild(modelInfoContainer);
    }

    // Formatta i prezzi
    const formatPrice = (price) => {
        const priceNum = parseFloat(price);
        if (priceNum === 0) return 'Gratuito';
        return `$${priceNum.toFixed(6)} / token`;
    };

    // Formatta il contesto
    const formatContext = (context) => {
        const contextNum = parseInt(context);
        if (contextNum >= 1000000) {
            return `${(contextNum / 1000000).toFixed(1)}M token`;
        } else if (contextNum >= 1000) {
            return `${(contextNum / 1000).toFixed(0)}K token`;
        }
        return `${contextNum} token`;
    };

    // Aggiorna il contenuto
    modelInfoContainer.innerHTML = `
        <div><strong>Descrizione:</strong> ${modelInfo.description || 'Non disponibile'}</div>
        <div><strong>Prezzo input:</strong> ${formatPrice(modelInfo.promptPrice)}</div>
        <div><strong>Prezzo output:</strong> ${formatPrice(modelInfo.completionPrice)}</div>
        <div><strong>Contesto:</strong> ${formatContext(modelInfo.contextLength)}</div>
        <div class="mt-1">
            <span class="badge bg-info">🆓 = Gratuito</span>
            <span class="badge bg-success ms-1">🎁 = Quote gratuite</span>
        </div>
    `;
}

/**
 * Invia un messaggio con gestione errori migliorata
 */
function sendMessage() {
    // Verifica se è in attesa di una risposta
    if (isWaitingForResponse) {
        console.warn('In attesa di una risposta...');
        addMessageToChat('system', 'Attendi la risposta precedente prima di inviare un nuovo messaggio.');
        return;
    }

    // Ottieni il messaggio
    const message = messageInput.value.trim();

    // Verifica se il messaggio è vuoto
    if (!message) {
        console.warn('Messaggio vuoto');
        // Feedback visivo per input vuoto
        messageInput.classList.add('is-invalid');
        setTimeout(() => {
            messageInput.classList.remove('is-invalid');
        }, 1000);
        return;
    }

    // Verifica lunghezza messaggio
    if (message.length > 2000) {
        addMessageToChat('system', 'Il messaggio è troppo lungo. Massimo 2000 caratteri.');
        return;
    }

    // Ottieni il modello selezionato
    const modelId = modelSelector.value;

    // Aggiungi il messaggio alla chat
    addMessageToChat('user', message);

    // Pulisci l'input
    messageInput.value = '';

    // Aggiungi il messaggio alla cronologia
    chatHistory.push({
        role: 'user',
        content: message
    });

    // Mostra l'indicatore di digitazione
    addTypingIndicator();

    // Imposta lo stato di attesa
    isWaitingForResponse = true;

    // Aggiorna UI durante l'invio
    updateSendButtonState(true);

    // Disabilita l'input e il pulsante di invio
    messageInput.disabled = true;
    sendButton.disabled = true;

    // Invia la richiesta al server
    fetch('/api/chat/send', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            message: message,
            model_id: modelId
        })
    })
    .then(response => response.json())
    .then(data => {
        // Rimuovi l'indicatore di digitazione
        removeTypingIndicator();

        // Verifica se c'è un errore
        if (data.error) {
            console.error('Errore nella richiesta:', data.error);
            addMessageToChat('system', `Errore: ${data.message || data.error}`);
            return;
        }

        // Aggiungi la risposta alla chat
        addMessageToChat('ai', data.response);

        // Aggiungi la risposta alla cronologia
        chatHistory.push({
            role: 'assistant',
            content: data.response
        });

        // Aggiungi un badge con la fonte dei dati
        if (data.source) {
            const sourceText = data.source === 'mcp' ? 'Elaborato con MCP' : 'Risposta diretta';
            addSourceBadge(sourceText);
        }
    })
    .catch(error => {
        console.error('Errore nella richiesta:', error);
        addMessageToChat('system', `Errore nella comunicazione con il server: ${error.message}`);
        removeTypingIndicator();
    })
    .finally(() => {
        // Gestisci la fine della risposta
        handleResponseEnd();
    });
}

/**
 * Aggiunge un messaggio alla chat con animazione
 *
 * @param {string} role - Ruolo del messaggio ('user', 'ai', 'system')
 * @param {string} content - Contenuto del messaggio
 */
function addMessageToChat(role, content) {
    // Crea l'elemento del messaggio
    const messageElement = document.createElement('div');
    messageElement.className = `message ${role}-message`;

    // Aggiungi animazione di entrata
    messageElement.style.opacity = '0';
    messageElement.style.transform = 'translateY(20px)';

    // Crea il contenuto del messaggio
    const contentElement = document.createElement('div');
    contentElement.className = 'message-content';

    // Formatta il contenuto con Markdown semplice
    const formattedContent = formatMessage(content);
    contentElement.innerHTML = formattedContent;

    // Aggiungi timestamp
    const timeElement = document.createElement('div');
    timeElement.className = 'message-time';
    timeElement.textContent = new Date().toLocaleTimeString('it-IT', {
        hour: '2-digit',
        minute: '2-digit'
    });

    // Aggiungi il contenuto al messaggio
    messageElement.appendChild(contentElement);

    // Aggiungi timestamp solo per messaggi user e ai
    if (role !== 'system') {
        messageElement.appendChild(timeElement);
    }

    // Aggiungi il messaggio alla chat
    chatMessages.appendChild(messageElement);

    // Anima l'entrata del messaggio
    setTimeout(() => {
        messageElement.style.transition = 'all 0.3s ease';
        messageElement.style.opacity = '1';
        messageElement.style.transform = 'translateY(0)';
    }, 10);

    // Scorri la chat verso il basso
    setTimeout(() => {
        scrollChatToBottom();
    }, 100);
}

/**
 * Aggiunge un badge con la fonte dei dati
 *
 * @param {string} sourceText - Testo del badge
 */
function addSourceBadge(sourceText) {
    // Crea l'elemento del messaggio
    const messageElement = document.createElement('div');
    messageElement.className = 'message system-message';
    messageElement.style.fontSize = '0.8rem';
    messageElement.style.padding = '0.5rem';
    messageElement.style.opacity = '0.7';

    // Crea il contenuto del messaggio
    const contentElement = document.createElement('div');
    contentElement.className = 'message-content';
    contentElement.innerHTML = `<i class="fas fa-info-circle me-1"></i>${sourceText}`;

    // Aggiungi il contenuto al messaggio
    messageElement.appendChild(contentElement);

    // Aggiungi il messaggio alla chat
    chatMessages.appendChild(messageElement);

    // Scorri la chat verso il basso
    scrollChatToBottom();
}

/**
 * Aggiunge l'indicatore di digitazione
 */
function addTypingIndicator() {
    // Crea l'elemento del messaggio
    const messageElement = document.createElement('div');
    messageElement.className = 'message ai-message typing-message';
    messageElement.id = 'typing-indicator';

    // Crea il contenuto del messaggio
    const contentElement = document.createElement('div');
    contentElement.className = 'message-content';
    contentElement.innerHTML = '<span class="typing-indicator"></span>';

    // Aggiungi il contenuto al messaggio
    messageElement.appendChild(contentElement);

    // Aggiungi il messaggio alla chat
    chatMessages.appendChild(messageElement);

    // Scorri la chat verso il basso
    scrollChatToBottom();
}

/**
 * Rimuove l'indicatore di digitazione
 */
function removeTypingIndicator() {
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

/**
 * Formatta un messaggio con Markdown semplice
 *
 * @param {string} message - Messaggio da formattare
 * @returns {string} - Messaggio formattato
 */
function formatMessage(message) {
    // Converti i ritorni a capo in tag <br>
    let formatted = message.replace(/\n/g, '<br>');

    // Formatta il testo in grassetto
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // Formatta il testo in corsivo
    formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // Formatta il codice inline
    formatted = formatted.replace(/`(.*?)`/g, '<code>$1</code>');

    // Formatta i blocchi di codice
    formatted = formatted.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');

    // Formatta le liste non ordinate
    formatted = formatted.replace(/^\s*-\s+(.*?)$/gm, '<li>$1</li>');
    formatted = formatted.replace(/<li>.*?<\/li>/g, function(match) {
        return '<ul>' + match + '</ul>';
    });

    // Formatta le liste ordinate
    formatted = formatted.replace(/^\s*\d+\.\s+(.*?)$/gm, '<li>$1</li>');
    formatted = formatted.replace(/<li>.*?<\/li>/g, function(match) {
        return '<ol>' + match + '</ol>';
    });

    // Avvolgi il testo in un paragrafo se non è già formattato
    if (!formatted.includes('<')) {
        formatted = '<p>' + formatted + '</p>';
    }

    return formatted;
}

/**
 * Aggiorna lo stato del pulsante di invio
 */
function updateSendButtonState(isLoading) {
    if (!sendButton) return;

    if (isLoading) {
        sendButton.disabled = true;
        sendButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Invio...';
        if (messageInput) messageInput.disabled = true;
    } else {
        sendButton.disabled = false;
        sendButton.innerHTML = '<i class="fas fa-paper-plane me-1"></i>Invia';
        if (messageInput) {
            messageInput.disabled = false;
            messageInput.focus();
        }
    }
}

/**
 * Gestisce la fine della risposta
 */
function handleResponseEnd() {
    // Rimuovi l'indicatore di digitazione
    removeTypingIndicator();

    // Ripristina lo stato di attesa
    isWaitingForResponse = false;

    // Aggiorna UI
    updateSendButtonState(false);
}

/**
 * Scorre la chat verso il basso con animazione fluida
 */
function scrollChatToBottom() {
    if (chatMessages) {
        chatMessages.scrollTo({
            top: chatMessages.scrollHeight,
            behavior: 'smooth'
        });
    }
}

// Inizializza la chat quando il documento è pronto
document.addEventListener('DOMContentLoaded', initChat);
