#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test di confronto elaborazione con e senza server MCP.
Verifica le differenze nell'elaborazione dei file.
"""

import os
import sys
import time
import requests
import pandas as pd
from datetime import datetime

def test_mcp_server_status():
    """
    Testa se il server MCP è attivo e funzionante.
    """
    print("🔍 Test stato server MCP...")

    try:
        # Test connessione
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server MCP attivo e raggiungibile")
            return True
        else:
            print(f"❌ Server MCP risponde con codice: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Server MCP non raggiungibile: {str(e)}")
        return False

def test_file_processing_with_mcp(file_path):
    """
    Testa l'elaborazione di un file con il server MCP attivo.
    """
    print(f"\n🤖 Test elaborazione CON MCP: {file_path}")

    try:
        # Simula il caricamento file tramite MCP
        file_id = f"test_{int(time.time())}"

        # 1. Upload file
        with open(file_path, 'rb') as f:
            files = {'file': f}
            data = {
                'file_id': file_id,
                'file_type': 'calendario'
            }

            response = requests.post(
                "http://localhost:8000/upload-file/",
                files=files,
                data=data,
                timeout=30
            )

        if response.status_code == 200:
            upload_result = response.json()
            print(f"✅ Upload MCP riuscito: {upload_result.get('message', 'OK')}")

            # 2. Process file
            process_data = {
                'file_id': file_id,
                'file_path': file_path,
                'file_type': 'calendario'
            }

            response = requests.post(
                "http://localhost:8000/process-file/",
                json=process_data,
                timeout=30
            )

            if response.status_code == 200:
                process_result = response.json()
                print(f"✅ Elaborazione MCP riuscita")
                print(f"   - Righe elaborate: {process_result.get('rows_processed', 'N/A')}")
                print(f"   - Tipo rilevato: {process_result.get('detected_type', 'N/A')}")
                print(f"   - Tempo elaborazione: {process_result.get('processing_time', 'N/A')}s")

                # 3. Get summary
                response = requests.get(
                    f"http://localhost:8000/file-summary/{file_id}",
                    timeout=10
                )

                if response.status_code == 200:
                    summary_result = response.json()
                    print(f"✅ Riepilogo MCP ottenuto")
                    print(f"   - Eventi totali: {summary_result.get('total_events', 'N/A')}")
                    print(f"   - Durata media: {summary_result.get('average_duration', 'N/A')} min")
                    print(f"   - Partecipanti unici: {summary_result.get('unique_attendees', 'N/A')}")

                    return {
                        'success': True,
                        'method': 'MCP',
                        'upload': upload_result,
                        'process': process_result,
                        'summary': summary_result,
                        'total_time': process_result.get('processing_time', 0)
                    }
                else:
                    print(f"❌ Errore riepilogo MCP: {response.status_code}")
            else:
                print(f"❌ Errore elaborazione MCP: {response.status_code}")
        else:
            print(f"❌ Errore upload MCP: {response.status_code}")

    except Exception as e:
        print(f"❌ Errore test MCP: {str(e)}")

    return {'success': False, 'method': 'MCP', 'error': 'Test fallito'}

def test_file_processing_without_mcp(file_path):
    """
    Testa l'elaborazione di un file senza server MCP (solo locale).
    """
    print(f"\n💻 Test elaborazione SENZA MCP: {file_path}")

    try:
        start_time = time.time()

        # Elaborazione locale diretta
        from calendar_processor import CalendarProcessor

        processor = CalendarProcessor()

        # 1. Leggi file
        df = processor.process_calendar_file(file_path)
        read_time = time.time() - start_time

        print(f"✅ Lettura locale riuscita")
        print(f"   - Righe lette: {len(df)}")
        print(f"   - Colonne: {len(df.columns)}")
        print(f"   - Tempo lettura: {read_time:.2f}s")

        # 2. Genera statistiche
        stats_start = time.time()
        stats = processor.generate_summary_stats(df)
        stats_time = time.time() - stats_start

        total_time = time.time() - start_time

        print(f"✅ Statistiche locali generate")
        print(f"   - Eventi totali: {stats.get('total_events', 'N/A')}")
        print(f"   - Durata media: {stats.get('average_duration', 'N/A')} min")
        print(f"   - Tempo statistiche: {stats_time:.2f}s")
        print(f"   - Tempo totale: {total_time:.2f}s")

        return {
            'success': True,
            'method': 'LOCAL',
            'rows_processed': len(df),
            'columns': len(df.columns),
            'stats': stats,
            'read_time': read_time,
            'stats_time': stats_time,
            'total_time': total_time
        }

    except Exception as e:
        print(f"❌ Errore elaborazione locale: {str(e)}")
        import traceback
        traceback.print_exc()

    return {'success': False, 'method': 'LOCAL', 'error': 'Test fallito'}

def compare_results(mcp_result, local_result):
    """
    Confronta i risultati dell'elaborazione MCP vs locale.
    """
    print("\n" + "="*60)
    print("📊 CONFRONTO RISULTATI MCP vs LOCALE")
    print("="*60)

    if mcp_result['success'] and local_result['success']:
        print("✅ Entrambi i metodi hanno funzionato")

        # Confronto performance
        mcp_time = mcp_result.get('total_time', 0)
        local_time = local_result.get('total_time', 0)

        print(f"\n⏱️ PERFORMANCE:")
        print(f"   MCP:    {mcp_time:.2f}s")
        print(f"   Locale: {local_time:.2f}s")

        if mcp_time > 0 and local_time > 0:
            if mcp_time < local_time:
                speedup = local_time / mcp_time
                print(f"   🚀 MCP è {speedup:.1f}x più veloce")
            else:
                slowdown = mcp_time / local_time
                print(f"   🐌 MCP è {slowdown:.1f}x più lento")

        # Confronto dati
        mcp_events = mcp_result.get('summary', {}).get('total_events', 0)
        local_events = local_result.get('stats', {}).get('total_events', 0)

        print(f"\n📊 DATI ELABORATI:")
        print(f"   MCP:    {mcp_events} eventi")
        print(f"   Locale: {local_events} eventi")

        if mcp_events == local_events:
            print("   ✅ Stesso numero di eventi elaborati")
        else:
            print("   ⚠️ Differenza nel numero di eventi")

        # Confronto funzionalità
        print(f"\n🔧 FUNZIONALITÀ:")
        print(f"   MCP:    Elaborazione cloud, cache, AI insights")
        print(f"   Locale: Elaborazione diretta, più veloce")

    elif mcp_result['success']:
        print("✅ Solo MCP ha funzionato")
        print("❌ Elaborazione locale fallita")

    elif local_result['success']:
        print("❌ MCP non disponibile o fallito")
        print("✅ Solo elaborazione locale ha funzionato")

    else:
        print("❌ Entrambi i metodi sono falliti")

    return {
        'mcp_working': mcp_result['success'],
        'local_working': local_result['success'],
        'mcp_faster': mcp_result.get('total_time', float('inf')) < local_result.get('total_time', float('inf')),
        'same_results': mcp_result.get('summary', {}).get('total_events', 0) == local_result.get('stats', {}).get('total_events', 0)
    }

def main():
    """
    Esegue il test completo di confronto MCP vs locale.
    """
    print("🚀 TEST CONFRONTO ELABORAZIONE MCP vs LOCALE")
    print("=" * 60)

    # Trova il file calendario più recente
    upload_dir = 'uploads'
    calendar_files = []

    for file in os.listdir(upload_dir):
        if file.endswith('.csv') and (
            'tmp-' in file.lower() or
            'calendar' in file.lower() or
            'calendario' in file.lower() or
            file.lower().startswith('tmp')
        ):
            file_path = os.path.join(upload_dir, file)
            calendar_files.append((file_path, os.path.getctime(file_path)))

    if not calendar_files:
        print("❌ Nessun file calendario trovato in uploads/")
        return False

    # Usa il file più recente
    latest_file = max(calendar_files, key=lambda x: x[1])[0]
    print(f"📁 File di test: {latest_file}")

    # Test stato MCP
    mcp_available = test_mcp_server_status()

    # Test elaborazione locale
    local_result = test_file_processing_without_mcp(latest_file)

    # Test elaborazione MCP (solo se disponibile)
    if mcp_available:
        mcp_result = test_file_processing_with_mcp(latest_file)
    else:
        mcp_result = {'success': False, 'method': 'MCP', 'error': 'Server non disponibile'}

    # Confronto risultati
    comparison = compare_results(mcp_result, local_result)

    # Raccomandazioni
    print(f"\n💡 RACCOMANDAZIONI:")
    if comparison['mcp_working'] and comparison['local_working']:
        if comparison['mcp_faster']:
            print("   🚀 Usa MCP per performance migliori")
        else:
            print("   💻 Usa elaborazione locale per velocità")
        print("   🔄 MCP offre funzionalità aggiuntive (cache, AI)")
    elif comparison['local_working']:
        print("   💻 Usa elaborazione locale (MCP non disponibile)")
    else:
        print("   ❌ Verifica configurazione sistema")

    return comparison['mcp_working'] or comparison['local_working']

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
