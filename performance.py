#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Modulo per l'ottimizzazione delle performance.
"""

import os
import time
import logging
import functools
import threading
from typing import Dict, Any, Callable, Optional, List, Tuple
from datetime import datetime, timedelta

# Configurazione del logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Cache:
    """
    Classe per la gestione della cache in memoria.
    Implementa un meccanismo di cache con scadenza per ridurre le chiamate ripetute.
    """
    
    def __init__(self, max_size: int = 100, default_ttl: int = 300):
        """
        Inizializza la cache.
        
        Args:
            max_size: Dimensione massima della cache (default: 100)
            default_ttl: Tempo di vita predefinito degli elementi in secondi (default: 300)
        """
        self.cache = {}
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """
        Ottiene un valore dalla cache.
        
        Args:
            key: Chiave del valore da ottenere
            
        Returns:
            Valore se presente e non scaduto, None altrimenti
        """
        with self.lock:
            if key not in self.cache:
                return None
            
            value, expiry = self.cache[key]
            
            # Verifica se il valore è scaduto
            if expiry and expiry < datetime.now():
                # Rimuovi il valore scaduto
                del self.cache[key]
                return None
            
            return value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        Imposta un valore nella cache.
        
        Args:
            key: Chiave del valore da impostare
            value: Valore da memorizzare
            ttl: Tempo di vita in secondi (default: None, usa il default_ttl)
        """
        with self.lock:
            # Calcola la scadenza
            if ttl is None:
                ttl = self.default_ttl
            
            expiry = None
            if ttl > 0:
                expiry = datetime.now() + timedelta(seconds=ttl)
            
            # Imposta il valore
            self.cache[key] = (value, expiry)
            
            # Se la cache è piena, rimuovi gli elementi più vecchi
            if len(self.cache) > self.max_size:
                self._cleanup()
    
    def delete(self, key: str) -> None:
        """
        Elimina un valore dalla cache.
        
        Args:
            key: Chiave del valore da eliminare
        """
        with self.lock:
            if key in self.cache:
                del self.cache[key]
    
    def clear(self) -> None:
        """Svuota la cache."""
        with self.lock:
            self.cache.clear()
    
    def _cleanup(self) -> None:
        """
        Rimuove gli elementi scaduti e, se necessario, quelli più vecchi.
        """
        # Rimuovi gli elementi scaduti
        now = datetime.now()
        expired_keys = [k for k, (_, expiry) in self.cache.items() if expiry and expiry < now]
        for key in expired_keys:
            del self.cache[key]
        
        # Se la cache è ancora piena, rimuovi gli elementi più vecchi
        if len(self.cache) > self.max_size:
            # Ordina gli elementi per scadenza (quelli senza scadenza sono considerati i più recenti)
            sorted_items = sorted(
                self.cache.items(),
                key=lambda x: x[1][1] or datetime.max
            )
            
            # Rimuovi gli elementi più vecchi
            for key, _ in sorted_items[:len(self.cache) - self.max_size]:
                del self.cache[key]

# Istanza globale della cache
global_cache = Cache()

def cached(ttl: Optional[int] = None, key_prefix: str = ""):
    """
    Decoratore per memorizzare nella cache il risultato di una funzione.
    
    Args:
        ttl: Tempo di vita in secondi (default: None, usa il default_ttl della cache)
        key_prefix: Prefisso per la chiave della cache (default: "")
        
    Returns:
        Decoratore
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Genera una chiave univoca per la cache
            cache_key = f"{key_prefix}:{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # Prova a ottenere il risultato dalla cache
            cached_result = global_cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {cache_key}")
                return cached_result
            
            # Calcola il risultato
            result = func(*args, **kwargs)
            
            # Memorizza il risultato nella cache
            global_cache.set(cache_key, result, ttl)
            logger.debug(f"Cache miss for {cache_key}, stored result")
            
            return result
        return wrapper
    return decorator

def timed(func):
    """
    Decoratore per misurare il tempo di esecuzione di una funzione.
    
    Args:
        func: Funzione da decorare
        
    Returns:
        Funzione decorata
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        logger.info(f"Function {func.__name__} took {end_time - start_time:.4f} seconds to execute")
        
        return result
    return wrapper

class RateLimiter:
    """
    Classe per limitare la frequenza delle chiamate a una funzione.
    """
    
    def __init__(self, max_calls: int = 10, period: int = 60):
        """
        Inizializza il rate limiter.
        
        Args:
            max_calls: Numero massimo di chiamate consentite nel periodo (default: 10)
            period: Periodo in secondi (default: 60)
        """
        self.max_calls = max_calls
        self.period = period
        self.calls = {}
        self.lock = threading.RLock()
    
    def is_allowed(self, key: str) -> bool:
        """
        Verifica se una chiamata è consentita.
        
        Args:
            key: Chiave per identificare il chiamante
            
        Returns:
            True se la chiamata è consentita, False altrimenti
        """
        with self.lock:
            now = time.time()
            
            # Inizializza la lista delle chiamate se non esiste
            if key not in self.calls:
                self.calls[key] = []
            
            # Rimuovi le chiamate più vecchie del periodo
            self.calls[key] = [t for t in self.calls[key] if now - t < self.period]
            
            # Verifica se il numero di chiamate è inferiore al massimo
            if len(self.calls[key]) < self.max_calls:
                self.calls[key].append(now)
                return True
            
            return False

# Istanza globale del rate limiter
global_rate_limiter = RateLimiter()

def rate_limited(max_calls: int = 10, period: int = 60, key_func: Optional[Callable] = None):
    """
    Decoratore per limitare la frequenza delle chiamate a una funzione.
    
    Args:
        max_calls: Numero massimo di chiamate consentite nel periodo (default: 10)
        period: Periodo in secondi (default: 60)
        key_func: Funzione per generare la chiave del rate limiter (default: None, usa l'indirizzo IP)
        
    Returns:
        Decoratore
    """
    limiter = RateLimiter(max_calls, period)
    
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Genera una chiave per il rate limiter
            if key_func:
                key = key_func(*args, **kwargs)
            else:
                # Usa l'indirizzo IP come chiave predefinita (per le route Flask)
                from flask import request
                key = request.remote_addr
            
            # Verifica se la chiamata è consentita
            if not limiter.is_allowed(key):
                from flask import jsonify
                return jsonify({"error": "Troppe richieste"}), 429
            
            # Esegui la funzione
            return func(*args, **kwargs)
        return wrapper
    return decorator
