<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestione Costi Dipendenti - App Roberto</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/employee-costs.css') }}">
</head>
<body class="bg-light">
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-chart-line me-2"></i>App Roberto
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('index') }}">
                    <i class="fas fa-home me-1"></i>Home
                </a>
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                </a>
                <a class="nav-link active" href="{{ url_for('employee_costs') }}">
                    <i class="fas fa-users me-1"></i>Costi Dipendenti
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="display-6">
                    <i class="fas fa-users text-primary me-3"></i>
                    Gestione Costi Dipendenti
                </h1>
                <p class="lead text-muted">
                    Configura i costi orari dei dipendenti e le impostazioni IVA per calcoli automatici
                </p>
            </div>
        </div>

        <!-- Alert per messaggi -->
        <div id="alertContainer"></div>

        <!-- Sezione Aggiunta Dipendente -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card employee-form text-white">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>
                            Aggiungi/Modifica Dipendente
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="employeeForm">
                            <div class="mb-3">
                                <label for="employeeName" class="form-label">Nome Dipendente</label>
                                <input type="text" class="form-control" id="employeeName" required>
                                <div class="form-text text-light">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    Puoi estrarre automaticamente i nomi dai file caricati
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="hourlyRate" class="form-label">Costo Orario (€)</label>
                                <input type="number" class="form-control" id="hourlyRate" step="0.01" min="0" required>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="vatIncluded" checked>
                                    <label class="form-check-label" for="vatIncluded">
                                        IVA Inclusa nel costo orario
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="notes" class="form-label">Note (opzionale)</label>
                                <textarea class="form-control" id="notes" rows="2"></textarea>
                            </div>
                            <div class="d-grid gap-2 d-md-flex">
                                <button type="submit" class="btn btn-light flex-fill">
                                    <i class="fas fa-save me-2"></i>Salva Dipendente
                                </button>
                                <button type="button" class="btn btn-outline-light" id="extractEmployeesBtn">
                                    <i class="fas fa-download me-2"></i>Estrai da File
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sezione Impostazioni IVA -->
            <div class="col-lg-6">
                <div class="card settings-section text-white">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            Impostazioni IVA
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="taxSettingsForm">
                            <div class="mb-3">
                                <label for="vatRate" class="form-label">Aliquota IVA (%)</label>
                                <input type="number" class="form-control" id="vatRate"
                                       value="{{ tax_settings.vat_rate or 22 }}" step="0.1" min="0" max="100">
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="defaultVatIncluded"
                                           {{ 'checked' if tax_settings.default_vat_included else '' }}>
                                    <label class="form-check-label" for="defaultVatIncluded">
                                        IVA inclusa di default per nuovi dipendenti
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="currency" class="form-label">Valuta</label>
                                <select class="form-select" id="currency">
                                    <option value="EUR" {{ 'selected' if tax_settings.currency == 'EUR' else '' }}>Euro (€)</option>
                                    <option value="USD" {{ 'selected' if tax_settings.currency == 'USD' else '' }}>Dollaro ($)</option>
                                    <option value="GBP" {{ 'selected' if tax_settings.currency == 'GBP' else '' }}>Sterlina (£)</option>
                                </select>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-light">
                                    <i class="fas fa-save me-2"></i>Salva Impostazioni
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lista Dipendenti -->
        <div class="row">
            <div class="col">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            Dipendenti Configurati
                        </h5>
                        <span class="badge bg-primary" id="employeeCount">
                            {{ employee_costs|length }} dipendenti
                        </span>
                    </div>
                    <div class="card-body">
                        <div id="employeesList" class="row">
                            {% if employee_costs %}
                                {% for name, data in employee_costs.items() %}
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card cost-card h-100">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="card-title mb-0">{{ name }}</h6>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary" type="button"
                                                            title="Opzioni dipendente"
                                                            data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item edit-employee" href="#"
                                                               data-name="{{ name }}" data-rate="{{ data.hourly_rate }}"
                                                               data-vat="{{ data.vat_included }}" data-notes="{{ data.notes or '' }}">
                                                            <i class="fas fa-edit me-2"></i>Modifica
                                                        </a></li>
                                                        <li><a class="dropdown-item text-danger delete-employee" href="#"
                                                               data-name="{{ name }}">
                                                            <i class="fas fa-trash me-2"></i>Elimina
                                                        </a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <p class="card-text">
                                                <strong>€{{ "%.2f"|format(data.hourly_rate) }}/ora</strong>
                                                <span class="badge vat-badge {{ 'bg-success' if data.vat_included else 'bg-warning' }} ms-2">
                                                    {{ 'IVA Incl.' if data.vat_included else 'IVA Escl.' }}
                                                </span>
                                            </p>
                                            {% if data.notes %}
                                            <p class="card-text">
                                                <small class="text-muted">{{ data.notes }}</small>
                                            </p>
                                            {% endif %}
                                            <small class="text-muted">
                                                Aggiornato: {{ data.last_updated[:10] if data.last_updated else 'N/A' }}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                            <div class="col-12">
                                <div class="text-center py-5">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Nessun dipendente configurato</h5>
                                    <p class="text-muted">Aggiungi il primo dipendente usando il form sopra</p>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Calcolatore Costi -->
        <div class="row mt-4">
            <div class="col">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>
                            Calcolatore Costi
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <label for="calcEmployee" class="form-label">Dipendente</label>
                                <select class="form-select" id="calcEmployee">
                                    <option value="">Seleziona dipendente...</option>
                                    {% for name in employee_costs.keys() %}
                                    <option value="{{ name }}">{{ name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="calcHours" class="form-label">Ore</label>
                                <input type="number" class="form-control" id="calcHours" step="0.25" min="0">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="calcIncludeVat" checked>
                                    <label class="form-check-label" for="calcIncludeVat">
                                        Includi IVA
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="button" class="btn btn-primary d-block w-100" id="calculateBtn">
                                    <i class="fas fa-calculator me-2"></i>Calcola
                                </button>
                            </div>
                        </div>
                        <div id="calculationResult" class="mt-3 wizard-hidden">
                            <div class="alert alert-info">
                                <h6>Risultato Calcolo:</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Costo Netto:</strong><br>
                                        <span id="resultNet" class="h5 text-primary"></span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>IVA:</strong><br>
                                        <span id="resultVat" class="h5 text-warning"></span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Totale:</strong><br>
                                        <span id="resultTotal" class="h5 text-success"></span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Ore:</strong><br>
                                        <span id="resultHours" class="h5 text-info"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Funzioni di utilità
        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            alertContainer.appendChild(alert);

            // Rimuovi automaticamente dopo 5 secondi
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }

        // Form dipendente
        document.getElementById('employeeForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = {
                employee_name: document.getElementById('employeeName').value,
                hourly_rate: parseFloat(document.getElementById('hourlyRate').value),
                vat_included: document.getElementById('vatIncluded').checked,
                notes: document.getElementById('notes').value
            };

            try {
                const response = await fetch('/api/employee-costs', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (result.success) {
                    showAlert(result.message, 'success');
                    // Reset form
                    this.reset();
                    // Ricarica la pagina per aggiornare la lista
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert(result.error, 'danger');
                }
            } catch (error) {
                showAlert('Errore nella comunicazione con il server', 'danger');
            }
        });

        // Form impostazioni IVA
        document.getElementById('taxSettingsForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = {
                vat_rate: parseFloat(document.getElementById('vatRate').value),
                default_vat_included: document.getElementById('defaultVatIncluded').checked,
                currency: document.getElementById('currency').value
            };

            try {
                const response = await fetch('/api/tax-settings', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (result.success) {
                    showAlert(result.message, 'success');
                } else {
                    showAlert(result.error, 'danger');
                }
            } catch (error) {
                showAlert('Errore nella comunicazione con il server', 'danger');
            }
        });

        // Estrai dipendenti da file
        document.getElementById('extractEmployeesBtn').addEventListener('click', async function() {
            try {
                const response = await fetch('/api/extract-employees', {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    if (result.employees.length > 0) {
                        const employeeSelect = result.employees.join(', ');
                        showAlert(`Dipendenti trovati: ${employeeSelect}`, 'info');

                        // Popola il primo dipendente nel form
                        document.getElementById('employeeName').value = result.employees[0];
                    } else {
                        showAlert('Nessun dipendente trovato nel file caricato', 'warning');
                    }
                } else {
                    showAlert(result.error, 'danger');
                }
            } catch (error) {
                showAlert('Errore nell\'estrazione dipendenti', 'danger');
            }
        });

        // Modifica dipendente
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('edit-employee') || e.target.closest('.edit-employee')) {
                const link = e.target.closest('.edit-employee');
                document.getElementById('employeeName').value = link.dataset.name;
                document.getElementById('hourlyRate').value = link.dataset.rate;
                document.getElementById('vatIncluded').checked = link.dataset.vat === 'True';
                document.getElementById('notes').value = link.dataset.notes;

                // Scroll al form
                document.getElementById('employeeForm').scrollIntoView({ behavior: 'smooth' });
            }
        });

        // Elimina dipendente
        document.addEventListener('click', async function(e) {
            if (e.target.classList.contains('delete-employee') || e.target.closest('.delete-employee')) {
                const link = e.target.closest('.delete-employee');
                const employeeName = link.dataset.name;

                if (confirm(`Sei sicuro di voler eliminare ${employeeName}?`)) {
                    try {
                        const response = await fetch(`/api/employee-costs/${encodeURIComponent(employeeName)}`, {
                            method: 'DELETE'
                        });

                        const result = await response.json();

                        if (result.success) {
                            showAlert(result.message, 'success');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showAlert(result.error, 'danger');
                        }
                    } catch (error) {
                        showAlert('Errore nell\'eliminazione dipendente', 'danger');
                    }
                }
            }
        });

        // Calcolatore costi
        document.getElementById('calculateBtn').addEventListener('click', async function() {
            const employee = document.getElementById('calcEmployee').value;
            const hours = parseFloat(document.getElementById('calcHours').value);
            const includeVat = document.getElementById('calcIncludeVat').checked;

            if (!employee || !hours) {
                showAlert('Seleziona dipendente e inserisci le ore', 'warning');
                return;
            }

            try {
                const response = await fetch('/api/calculate-employee-cost', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        employee_name: employee,
                        hours: hours,
                        include_vat: includeVat
                    })
                });

                const result = await response.json();

                if (result.success) {
                    const calc = result.calculation;
                    document.getElementById('resultNet').textContent = `€${calc.costo_netto}`;
                    document.getElementById('resultVat').textContent = `€${calc.iva}`;
                    document.getElementById('resultTotal').textContent = `€${calc.costo_totale}`;
                    document.getElementById('resultHours').textContent = `${calc.ore}h`;
                    document.getElementById('calculationResult').classList.remove('wizard-hidden');
                } else {
                    showAlert(result.error, 'danger');
                }
            } catch (error) {
                showAlert('Errore nel calcolo costi', 'danger');
            }
        });
    </script>
</body>
</html>


