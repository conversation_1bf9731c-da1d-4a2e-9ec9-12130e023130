#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Diagnosi errore "Column not found: ore_lavorate"
Analizza il file caricato e identifica il problema.
"""

import pandas as pd
import sys
import os
from enhanced_file_detector import EnhancedFileDetector

def diagnosi_errore_colonne():
    """Diagnostica l'errore delle colonne mancanti."""
    
    print("🔍 DIAGNOSI ERRORE 'Column not found: ore_lavorate'")
    print("=" * 60)
    
    # Trova il file più recente in uploads
    uploads_dir = 'uploads'
    if not os.path.exists(uploads_dir):
        print(f"❌ Directory {uploads_dir} non trovata")
        return
    
    files = [f for f in os.listdir(uploads_dir) if f.endswith(('.xlsx', '.xls', '.csv'))]
    if not files:
        print(f"❌ Nessun file trovato in {uploads_dir}")
        return
    
    # Prendi il file più recente
    latest_file = max(files, key=lambda f: os.path.getctime(os.path.join(uploads_dir, f)))
    file_path = os.path.join(uploads_dir, latest_file)
    
    print(f"📁 File analizzato: {latest_file}")
    print()
    
    try:
        # Leggi il file
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        else:
            df = pd.read_excel(file_path)
        
        print(f"📊 Dimensioni file: {df.shape[0]} righe x {df.shape[1]} colonne")
        print()
        
        print("📋 COLONNE PRESENTI NEL FILE:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i}. '{col}'")
        print()
        
        # Rileva il tipo di file
        detector = EnhancedFileDetector()
        detected_type, confidence, scores = detector.detect_file_type(df, latest_file)
        
        print("🎯 RILEVAMENTO TIPO FILE:")
        print(f"   Tipo rilevato: {detected_type}")
        print(f"   Confidenza: {confidence:.3f}")
        print()
        
        print("📊 PUNTEGGI PER TUTTI I TIPI:")
        for tipo, punteggio in sorted(scores.items(), key=lambda x: x[1], reverse=True):
            print(f"   {tipo}: {punteggio:.3f}")
        print()
        
        # Analizza il problema specifico
        print("🔍 ANALISI PROBLEMA 'ore_lavorate':")
        print("-" * 40)
        
        if 'ore_lavorate' in df.columns:
            print("✅ Colonna 'ore_lavorate' PRESENTE nel file")
        else:
            print("❌ Colonna 'ore_lavorate' NON PRESENTE nel file")
            
            # Cerca colonne simili
            similar_cols = []
            for col in df.columns:
                col_lower = col.lower()
                if any(keyword in col_lower for keyword in ['ore', 'hour', 'lavoro', 'work', 'tempo', 'time']):
                    similar_cols.append(col)
            
            if similar_cols:
                print("💡 Colonne simili trovate:")
                for col in similar_cols:
                    print(f"   - '{col}'")
            else:
                print("💡 Nessuna colonna simile a 'ore_lavorate' trovata")
        
        print()
        
        # Analizza il tipo di file rilevato vs aspettato
        print("🎯 ANALISI TIPO FILE:")
        print("-" * 40)
        
        if detected_type == "permessi":
            print("✅ File correttamente rilevato come PERMESSI")
            print("❌ PROBLEMA: Il sistema sta applicando logica TIMBRATURE")
            print("💡 CAUSA: Processore sbagliato chiamato per questo tipo di file")
            
            print("\n📋 COLONNE ATTESE PER PERMESSI:")
            expected_permessi = ['Data della richiesta', 'Dipendente', 'Tipo', 'Data inizio', 'Data fine', 'Stato', 'Note']
            for col in expected_permessi:
                if col in df.columns:
                    print(f"   ✅ '{col}' - PRESENTE")
                else:
                    print(f"   ❌ '{col}' - MANCANTE")
                    
        elif detected_type == "timbrature":
            print("✅ File rilevato come TIMBRATURE")
            print("❌ PROBLEMA: File timbrature senza colonna 'ore_lavorate'")
            
            print("\n📋 COLONNE ATTESE PER TIMBRATURE:")
            expected_timbrature = ['Data', 'Dipendente', 'Entrata', 'Uscita', 'Ore Lavorate']
            for col in expected_timbrature:
                if col in df.columns:
                    print(f"   ✅ '{col}' - PRESENTE")
                else:
                    print(f"   ❌ '{col}' - MANCANTE")
        else:
            print(f"⚠️ File rilevato come: {detected_type}")
            print("❌ PROBLEMA: Tipo non gestito correttamente")
        
        print()
        
        # Mostra prime righe per contesto
        print("🔍 PRIME 3 RIGHE DEL FILE:")
        print("-" * 40)
        print(df.head(3).to_string())
        print()
        
        # Suggerimenti per la risoluzione
        print("🔧 SUGGERIMENTI PER RISOLVERE:")
        print("=" * 40)
        
        if detected_type == "permessi":
            print("1. ✅ Il file è correttamente un file di PERMESSI")
            print("2. ❌ Il sistema sta applicando il processore TIMBRATURE")
            print("3. 🔧 SOLUZIONE: Correggere il routing del processore")
            print("4. 💡 Il file NON dovrebbe avere 'ore_lavorate'")
            print("5. 🎯 Usare AttendanceProcessor invece di TimbratureProcessor")
        else:
            print("1. 🔍 Verificare il tipo di file rilevato")
            print("2. 📋 Controllare le colonne presenti")
            print("3. 🔧 Aggiungere mappatura colonne se necessario")
            print("4. 💡 Considerare rinominare colonne nel file")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore nell'analisi: {str(e)}")
        return False

if __name__ == "__main__":
    success = diagnosi_errore_colonne()
    exit(0 if success else 1)
