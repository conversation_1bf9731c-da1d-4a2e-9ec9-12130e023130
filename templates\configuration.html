<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configurazione Sistema - App Roberto</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-cogs me-2"></i>App Roberto - Configurazione
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-chart-bar me-1"></i>Dashboard
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="display-6">
                    <i class="fas fa-cogs text-primary me-3"></i>
                    Configurazione Sistema
                </h1>
                <p class="lead text-muted">Gestisci dipendenti, veicoli e configurazioni fiscali</p>
            </div>
        </div>

        <!-- Tabs Navigation -->
        <ul class="nav nav-tabs mb-4" id="configTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="employees-tab" data-bs-toggle="tab" data-bs-target="#employees" type="button" role="tab">
                    <i class="fas fa-users me-2"></i>Dipendenti
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="vehicles-tab" data-bs-toggle="tab" data-bs-target="#vehicles" type="button" role="tab">
                    <i class="fas fa-car me-2"></i>Veicoli
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="tax-tab" data-bs-toggle="tab" data-bs-target="#tax" type="button" role="tab">
                    <i class="fas fa-calculator me-2"></i>Configurazione Fiscale
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="automation-tab" data-bs-toggle="tab" data-bs-target="#automation" type="button" role="tab">
                    <i class="fas fa-robot me-2"></i>Automazione
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                    <i class="fas fa-server me-2"></i>Sistema API
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="configTabContent">
            <!-- Dipendenti Tab -->
            <div class="tab-pane fade show active" id="employees" role="tabpanel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>Gestione Dipendenti
                        </h5>
                        <button type="button" class="btn btn-primary btn-sm" onclick="showAddEmployeeModal()">
                            <i class="fas fa-plus me-1"></i>Aggiungi Dipendente
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="employees-list">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Caricamento...</span>
                                </div>
                                <p class="mt-2 text-muted">Caricamento dipendenti...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Veicoli Tab -->
            <div class="tab-pane fade" id="vehicles" role="tabpanel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-car me-2"></i>Gestione Veicoli
                        </h5>
                        <button type="button" class="btn btn-primary btn-sm" onclick="showAddVehicleModal()">
                            <i class="fas fa-plus me-1"></i>Aggiungi Veicolo
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="vehicles-list">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Caricamento...</span>
                                </div>
                                <p class="mt-2 text-muted">Caricamento veicoli...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Configurazione Fiscale Tab -->
            <div class="tab-pane fade" id="tax" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>Configurazione Fiscale
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="tax-config-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="vat-rate" class="form-label">Aliquota IVA (%)</label>
                                        <input type="number" class="form-control" id="vat-rate" step="0.01" min="0" max="100">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="tax-rate" class="form-label">Aliquota Tasse (%)</label>
                                        <input type="number" class="form-control" id="tax-rate" step="0.01" min="0" max="100">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="social-security-rate" class="form-label">Contributi Previdenziali (%)</label>
                                        <input type="number" class="form-control" id="social-security-rate" step="0.01" min="0" max="100">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="overtime-multiplier" class="form-label">Moltiplicatore Straordinari</label>
                                        <input type="number" class="form-control" id="overtime-multiplier" step="0.1" min="1" max="5">
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>Salva Configurazione
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Automazione Tab -->
            <div class="tab-pane fade" id="automation" role="tabpanel">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-robot me-2"></i>Regole di Automazione
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="automation-rules">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Caricamento...</span>
                                </div>
                                <p class="mt-2 text-muted">Caricamento regole di automazione...</p>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button type="button" class="btn btn-success" onclick="triggerAutomation()">
                                <i class="fas fa-play me-1"></i>Esegui Automazione
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sistema API Tab - FASE 4 ALLINEAMENTO FRONTEND-BACKEND -->
            <div class="tab-pane fade" id="system" role="tabpanel">
                <!-- Stato Sistema -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-heartbeat me-2"></i>Stato Sistema
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="system-health">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Caricamento...</span>
                                </div>
                                <p class="mt-2 text-muted">Controllo stato sistema...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mappa API Endpoints -->
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-sitemap me-2"></i>Mappa API Endpoints
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="endpoints-map">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Caricamento...</span>
                                </div>
                                <p class="mt-2 text-muted">Caricamento mappa endpoints...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <!-- Add Employee Modal -->
    <div class="modal fade" id="addEmployeeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Aggiungi Dipendente</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Chiudi"></button>
                </div>
                <div class="modal-body">
                    <form id="add-employee-form">
                        <div class="mb-3">
                            <label for="employee-name" class="form-label">Nome Completo</label>
                            <input type="text" class="form-control" id="employee-name" required>
                        </div>
                        <div class="mb-3">
                            <label for="employee-role" class="form-label">Ruolo</label>
                            <input type="text" class="form-control" id="employee-role" required>
                        </div>
                        <div class="mb-3">
                            <label for="employee-hourly-rate" class="form-label">Tariffa Oraria (€)</label>
                            <input type="number" class="form-control" id="employee-hourly-rate" step="0.01" min="0" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annulla</button>
                    <button type="button" class="btn btn-primary" onclick="addEmployee()">Aggiungi</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Vehicle Modal -->
    <div class="modal fade" id="addVehicleModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Aggiungi Veicolo</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Chiudi"></button>
                </div>
                <div class="modal-body">
                    <form id="add-vehicle-form">
                        <div class="mb-3">
                            <label for="vehicle-plate" class="form-label">Targa</label>
                            <input type="text" class="form-control" id="vehicle-plate" required>
                        </div>
                        <div class="mb-3">
                            <label for="vehicle-model" class="form-label">Modello</label>
                            <input type="text" class="form-control" id="vehicle-model" required>
                        </div>
                        <div class="mb-3">
                            <label for="vehicle-cost-per-km" class="form-label">Costo per Km (€)</label>
                            <input type="number" class="form-control" id="vehicle-cost-per-km" step="0.01" min="0" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annulla</button>
                    <button type="button" class="btn btn-primary" onclick="addVehicle()">Aggiungi</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/configuration.js') }}"></script>
</body>
</html>



