#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re
import logging
from datetime import datetime, timedelta
from typing import Union, Tuple, Optional

# Configurazione del logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DurationParser:
    """
    Classe per la gestione della conversione e validazione dei formati di durata.
    Supporta formati HH:MM e decimali (con virgola/punto).
    """

    def __init__(self):
        # Definizione dei pattern regex per i diversi formati di durata
        self.duration_patterns = {
            'time_format': r'^(\d+):(\d{1,2})$',  # Formato HH:MM
            'decimal_dot': r'^(\d+)\.(\d+)$',     # Formato decimale con punto
            'decimal_comma': r'^(\d+),(\d+)$',    # Formato decimale con virgola
            'integer': r'^(\d+)$',                # Formato intero (solo ore)
            'empty': r'^(0|0:00|0,0|0\.0)$'       # Valori vuoti o zero
        }

    def parse_duration(self, duration_str) -> float:
        """
        Converte una stringa di durata in ore decimali.

        Args:
            duration_str: Stringa rappresentante la durata (es. "1:30", "1.5", "1,5")

        Returns:
            Durata in ore decimali (es. 1.5 per "1:30")
        """
        # Gestisci valori None, NaN o vuoti senza generare warning
        if duration_str is None:
            return 0.0

        # Gestisci pandas NaN
        try:
            import pandas as pd
            if pd.isna(duration_str):
                return 0.0
        except ImportError:
            pass

        # Gestisci numpy NaN
        try:
            import numpy as np
            if np.isnan(duration_str):
                return 0.0
        except (ImportError, TypeError):
            pass

        # Se non è una stringa, prova a convertirlo
        if not isinstance(duration_str, str):
            # Se è un numero, assumiamo che sia già in ore
            if isinstance(duration_str, (int, float)):
                return float(duration_str)
            # Altrimenti prova a convertirlo in stringa
            try:
                duration_str = str(duration_str).strip()
            except:
                logger.warning(f"Impossibile convertire in stringa: {duration_str}")
                return 0.0

        # Gestisci stringhe vuote
        if not duration_str or duration_str.strip() == '':
            return 0.0

        # Rimuovi spazi e normalizza
        duration_str = duration_str.strip()

        # Gestisci valori vuoti o zero
        if re.match(self.duration_patterns['empty'], duration_str):
            return 0.0

        # Formato HH:MM
        match = re.match(self.duration_patterns['time_format'], duration_str)
        if match:
            hours = int(match.group(1))
            minutes = int(match.group(2))
            if minutes >= 60:
                logger.warning(f"Formato orario non valido (minuti >= 60): {duration_str}")
                minutes = minutes % 60
            return hours + (minutes / 60.0)

        # Formato decimale con punto
        match = re.match(self.duration_patterns['decimal_dot'], duration_str)
        if match:
            hours = int(match.group(1))
            decimal_part = match.group(2)
            # Normalizza la parte decimale a due cifre
            if len(decimal_part) == 1:
                decimal_part = decimal_part + '0'
            elif len(decimal_part) > 2:
                decimal_part = decimal_part[:2]
            return hours + (int(decimal_part) / 100.0)

        # Formato decimale con virgola
        match = re.match(self.duration_patterns['decimal_comma'], duration_str)
        if match:
            hours = int(match.group(1))
            decimal_part = match.group(2)
            # Normalizza la parte decimale a due cifre
            if len(decimal_part) == 1:
                decimal_part = decimal_part + '0'
            elif len(decimal_part) > 2:
                decimal_part = decimal_part[:2]
            return hours + (int(decimal_part) / 100.0)

        # Formato intero (solo ore)
        match = re.match(self.duration_patterns['integer'], duration_str)
        if match:
            return float(match.group(1))

        # Se non corrisponde a nessun pattern, prova a convertire direttamente
        try:
            # Sostituisci la virgola con il punto per la conversione
            duration_str = duration_str.replace(',', '.')
            return float(duration_str)
        except ValueError:
            logger.warning(f"Impossibile convertire la durata: {duration_str}")
            return 0.0

    def format_duration(self, hours: float, format_type: str = 'hh:mm') -> str:
        """
        Formatta una durata in ore decimali in una stringa nel formato specificato.

        Args:
            hours: Durata in ore decimali
            format_type: Tipo di formato ('hh:mm', 'decimal_dot', 'decimal_comma')

        Returns:
            Stringa formattata
        """
        if hours is None or not isinstance(hours, (int, float)):
            return "0:00" if format_type == 'hh:mm' else "0.00" if format_type == 'decimal_dot' else "0,00"

        # Gestisci valori negativi
        if hours < 0:
            logger.warning(f"Durata negativa: {hours}")
            hours = 0

        if format_type == 'hh:mm':
            h = int(hours)
            m = int((hours - h) * 60)
            return f"{h}:{m:02d}"
        elif format_type == 'decimal_dot':
            return f"{hours:.2f}"
        elif format_type == 'decimal_comma':
            return f"{hours:.2f}".replace('.', ',')
        else:
            logger.warning(f"Formato non supportato: {format_type}")
            return f"{hours:.2f}"

    def decimal_to_hhmm(self, hours: float) -> str:
        """
        Converte ore decimali in formato HH:MM.

        Args:
            hours: Durata in ore decimali

        Returns:
            Stringa nel formato HH:MM
        """
        if hours is None or not isinstance(hours, (int, float)):
            return "0:00"

        # Gestisci valori negativi
        if hours < 0:
            logger.warning(f"Durata negativa: {hours}")
            hours = abs(hours)

        # Converti in ore e minuti
        h = int(hours)
        m = int((hours - h) * 60)

        return f"{h}:{m:02d}"

    def validate_duration(self, start_time: Optional[datetime], end_time: Optional[datetime],
                         duration_parsed: float) -> Tuple[bool, float, str]:
        """
        Verifica la coerenza tra durate calcolate e dichiarate.

        Args:
            start_time: Data e ora di inizio
            end_time: Data e ora di fine
            duration_parsed: Durata dichiarata in ore decimali

        Returns:
            Tuple contenente:
            - Validità (True/False)
            - Durata calcolata in ore decimali
            - Messaggio di errore (vuoto se valido)
        """
        # Se mancano start_time o end_time, non possiamo calcolare la durata
        if start_time is None or end_time is None:
            return True, duration_parsed, ""

        # Calcola la durata in base a start_time e end_time
        if end_time < start_time:
            # Gestisci il caso in cui l'attività termina il giorno successivo
            end_time = end_time + timedelta(days=1)

        time_diff = end_time - start_time
        calculated_duration = time_diff.total_seconds() / 3600.0  # Converti in ore

        # Arrotonda a due decimali per evitare problemi di precisione
        calculated_duration = round(calculated_duration, 2)
        duration_parsed = round(duration_parsed, 2)

        # Verifica se la differenza è significativa (più di 5 minuti)
        tolerance = 5 / 60.0  # 5 minuti in ore
        if abs(calculated_duration - duration_parsed) > tolerance:
            message = (f"Discrepanza nella durata: dichiarata {self.format_duration(duration_parsed)} vs "
                      f"calcolata {self.format_duration(calculated_duration)}")
            logger.warning(message)
            return False, calculated_duration, message

        return True, duration_parsed, ""
