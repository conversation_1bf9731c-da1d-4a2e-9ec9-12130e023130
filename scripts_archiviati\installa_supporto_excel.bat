@echo off
echo ===================================
echo Installazione supporto Excel COM
echo ===================================
echo.

set CLEAN_ENV=clean_env

if not exist %CLEAN_ENV% (
    echo L'ambiente virtuale %CLEAN_ENV% non esiste.
    echo Crearlo prima con avvio_app.bat
    goto :EOF
)

echo Attivazione ambiente virtuale...
call %CLEAN_ENV%\Scripts\activate
echo.

echo Installazione pywin32 per supporto Excel COM...
pip install pywin32
echo.

echo Installazione comtypes (libreria alternativa COM)...
pip install comtypes
echo.

echo Verifica installazione...
python -c "import win32com.client; print('Supporto COM installato correttamente')" 2>nul
if %errorlevel% neq 0 (
    echo ATTENZIONE: L'installazione potrebbe non essere completa.
    echo Assicurati che Microsoft Office/Excel sia installato sul sistema.
) else (
    echo Supporto COM installato correttamente.
)
echo.

echo ===================================
echo Installazione completata!
echo.
echo Se il warning persiste, potrebbe essere necessario:
echo 1. Verificare che Microsoft Excel sia installato
echo 2. Riavviare l'applicazione
echo 3. Eseguire il comando 'python -m win32com.client.makepy'
echo ===================================
echo.
pause
