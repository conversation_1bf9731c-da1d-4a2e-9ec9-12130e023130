#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sistema di Report Automatici con LLM.
Generazione automatica di report narrativi, executive summary e insights.
"""

import os
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from pathlib import Path
import jinja2
import markdown

# Importa Playwright PDF Generator invece di WeasyPrint
try:
    from playwright_pdf_generator import generate_pdf_sync
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ReportConfig:
    """Configurazione per generazione report."""
    report_type: str
    template_name: str
    output_format: str = 'html'  # html, pdf, markdown
    include_charts: bool = True
    include_recommendations: bool = True
    language: str = 'it'
    style_theme: str = 'professional'

@dataclass
class ReportData:
    """Dati per generazione report."""
    title: str
    period: str
    analysis_results: Dict[str, Any]
    metadata: Dict[str, Any] = field(default_factory=dict)
    custom_sections: List[Dict[str, Any]] = field(default_factory=list)

@dataclass
class GeneratedReport:
    """Report generato."""
    report_id: str
    report_type: str
    title: str
    content: str
    format: str
    file_path: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    generated_at: datetime = field(default_factory=datetime.now)

class AutomatedReportingSystem:
    """Sistema di reporting automatico con LLM."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.output_dir = Path(self.config.get('output_dir', 'reports'))
        self.templates_dir = Path(self.config.get('templates_dir', 'templates/reports'))

        # Configurazione PDF Generator
        self.pdf_generator = self.config.get('pdf_generator', 'playwright')
        self.pdf_generator_config = self.config.get('pdf_generator_config', {})

        # Crea directory se non esistono
        self.output_dir.mkdir(exist_ok=True)
        self.templates_dir.mkdir(exist_ok=True, parents=True)

        # Inizializza template engine
        self.jinja_env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(str(self.templates_dir)),
            autoescape=jinja2.select_autoescape(['html', 'xml'])
        )

        # Inizializza LLM assistant
        self.llm_assistant = None
        self._initialize_llm_assistant()

        # Template di report predefiniti
        self._create_default_templates()

        # Log configurazione PDF
        if self.pdf_generator == 'playwright' and PLAYWRIGHT_AVAILABLE:
            logger.info("Sistema di Reporting Automatico inizializzato con Playwright PDF Generator")
        elif self.pdf_generator == 'playwright' and not PLAYWRIGHT_AVAILABLE:
            logger.warning("Playwright richiesto ma non disponibile - PDF disabilitato")
        else:
            logger.info("Sistema di Reporting Automatico inizializzato")

    def _initialize_llm_assistant(self):
        """Inizializza LLM assistant per generazione contenuti."""
        try:
            from enhanced_llm_assistant import EnhancedLLMAssistant
            self.llm_assistant = EnhancedLLMAssistant()
            logger.info("LLM Assistant inizializzato per reporting")
        except Exception as e:
            logger.warning(f"LLM Assistant non disponibile: {str(e)}")

    def _create_default_templates(self):
        """Crea template di report predefiniti."""
        templates = {
            'executive_summary.html': self._get_executive_summary_template(),
            'technical_analysis.html': self._get_technical_analysis_template(),
            'quality_report.html': self._get_quality_report_template(),
            'comprehensive_report.html': self._get_comprehensive_report_template()
        }

        for template_name, content in templates.items():
            template_path = self.templates_dir / template_name
            if not template_path.exists():
                template_path.write_text(content, encoding='utf-8')
                logger.info(f"Template creato: {template_name}")

    async def generate_executive_summary(self, analysis_results: Dict[str, Any],
                                       period: str) -> GeneratedReport:
        """Genera executive summary con LLM."""
        logger.info("Generazione Executive Summary")

        # Prepara dati per LLM
        llm_context = {
            'report_type': 'executive_summary',
            'business_context': 'Sistema di gestione attività aziendali',
            'target_audience': 'Management e stakeholder'
        }

        # Genera contenuto narrativo con LLM
        narrative_content = ""
        if self.llm_assistant:
            try:
                llm_response = await self.llm_assistant.generate_narrative_report(
                    analysis_results=analysis_results,
                    period=period,
                    context=llm_context
                )
                narrative_content = llm_response.result
            except Exception as e:
                logger.error(f"Errore generazione LLM: {str(e)}")
                narrative_content = self._generate_fallback_summary(analysis_results)
        else:
            narrative_content = self._generate_fallback_summary(analysis_results)

        # Prepara dati per template
        report_data = ReportData(
            title="Executive Summary - Analisi Sistema",
            period=period,
            analysis_results=analysis_results,
            metadata={
                'generated_by': 'LLM Assistant' if self.llm_assistant else 'Sistema Automatico',
                'narrative_content': narrative_content
            }
        )

        # Genera report
        config = ReportConfig(
            report_type='executive_summary',
            template_name='executive_summary.html',
            output_format='html'
        )

        return await self._generate_report(report_data, config)

    async def generate_technical_analysis(self, analysis_results: Dict[str, Any],
                                        period: str) -> GeneratedReport:
        """Genera report di analisi tecnica dettagliata."""
        logger.info("Generazione Report Analisi Tecnica")

        # Analizza dati tecnici con LLM
        technical_insights = ""
        if self.llm_assistant:
            try:
                # Estrai discrepanze per analisi
                all_discrepancies = []
                for analysis_type, result in analysis_results.items():
                    if isinstance(result, dict) and 'discrepancies_found' in result:
                        all_discrepancies.extend(result['discrepancies_found'])

                llm_response = await self.llm_assistant.analyze_anomalies(
                    analysis_data=analysis_results,
                    discrepancies=all_discrepancies,
                    context={'report_type': 'technical_analysis'}
                )
                technical_insights = llm_response.result
            except Exception as e:
                logger.error(f"Errore analisi tecnica LLM: {str(e)}")
                technical_insights = self._generate_fallback_technical_analysis(analysis_results)
        else:
            technical_insights = self._generate_fallback_technical_analysis(analysis_results)

        report_data = ReportData(
            title="Analisi Tecnica Dettagliata",
            period=period,
            analysis_results=analysis_results,
            metadata={
                'technical_insights': technical_insights,
                'analysis_depth': 'detailed'
            }
        )

        config = ReportConfig(
            report_type='technical_analysis',
            template_name='technical_analysis.html',
            output_format='html',
            include_charts=True
        )

        return await self._generate_report(report_data, config)

    async def generate_quality_report(self, quality_data: Dict[str, Any],
                                    period: str) -> GeneratedReport:
        """Genera report qualità dati."""
        logger.info("Generazione Report Qualità Dati")

        # Valuta qualità con LLM
        quality_assessment = ""
        if self.llm_assistant:
            try:
                llm_response = await self.llm_assistant.assess_data_quality(
                    data=quality_data,
                    quality_metrics=quality_data.get('quality_metrics', {}),
                    standards={'completeness': 0.95, 'accuracy': 0.90}
                )
                quality_assessment = llm_response.result
            except Exception as e:
                logger.error(f"Errore valutazione qualità LLM: {str(e)}")
                quality_assessment = self._generate_fallback_quality_assessment(quality_data)
        else:
            quality_assessment = self._generate_fallback_quality_assessment(quality_data)

        report_data = ReportData(
            title="Report Qualità Dati",
            period=period,
            analysis_results={'quality_data': quality_data},
            metadata={
                'quality_assessment': quality_assessment,
                'focus': 'data_quality'
            }
        )

        config = ReportConfig(
            report_type='quality_report',
            template_name='quality_report.html',
            output_format='html'
        )

        return await self._generate_report(report_data, config)

    async def generate_comprehensive_report(self, all_analysis_results: Dict[str, Any],
                                          period: str) -> GeneratedReport:
        """Genera report completo con tutte le analisi."""
        logger.info("Generazione Report Completo")

        # Genera insights completi con LLM
        comprehensive_insights = ""
        if self.llm_assistant:
            try:
                llm_response = await self.llm_assistant.generate_narrative_report(
                    analysis_results=all_analysis_results,
                    period=period,
                    context={
                        'report_type': 'comprehensive',
                        'include_all_sections': True,
                        'target_audience': 'Technical and Business stakeholders'
                    }
                )
                comprehensive_insights = llm_response.result
            except Exception as e:
                logger.error(f"Errore report completo LLM: {str(e)}")
                comprehensive_insights = self._generate_fallback_comprehensive(all_analysis_results)
        else:
            comprehensive_insights = self._generate_fallback_comprehensive(all_analysis_results)

        report_data = ReportData(
            title="Report Completo - Sistema di Riconoscimento Intelligente",
            period=period,
            analysis_results=all_analysis_results,
            metadata={
                'comprehensive_insights': comprehensive_insights,
                'sections_included': list(all_analysis_results.keys())
            }
        )

        config = ReportConfig(
            report_type='comprehensive_report',
            template_name='comprehensive_report.html',
            output_format='html',
            include_charts=True,
            include_recommendations=True
        )

        return await self._generate_report(report_data, config)

    async def _generate_report(self, report_data: ReportData,
                             config: ReportConfig) -> GeneratedReport:
        """Genera report usando template e configurazione."""
        try:
            # Carica template
            template = self.jinja_env.get_template(config.template_name)

            # Prepara contesto per template
            template_context = {
                'report': report_data,
                'config': config,
                'generated_at': datetime.now(),
                'charts_data': self._prepare_charts_data(report_data.analysis_results) if config.include_charts else None,
                'recommendations': self._extract_recommendations(report_data.analysis_results) if config.include_recommendations else None
            }

            # Renderizza template
            html_content = template.render(**template_context)

            # Genera ID report
            report_id = f"{config.report_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Salva file se richiesto
            file_path = None
            if config.output_format in ['html', 'pdf']:
                file_path = await self._save_report_file(html_content, report_id, config.output_format)

            return GeneratedReport(
                report_id=report_id,
                report_type=config.report_type,
                title=report_data.title,
                content=html_content,
                format=config.output_format,
                file_path=str(file_path) if file_path else None,
                metadata={
                    'template_used': config.template_name,
                    'data_sections': len(report_data.analysis_results),
                    'llm_enhanced': bool(self.llm_assistant)
                }
            )

        except Exception as e:
            logger.error(f"Errore generazione report: {str(e)}")
            raise

    async def _save_report_file(self, content: str, report_id: str,
                              format: str) -> Path:
        """Salva report su file usando Playwright per PDF."""
        if format == 'html':
            file_path = self.output_dir / f"{report_id}.html"
            file_path.write_text(content, encoding='utf-8')
        elif format == 'pdf':
            file_path = self.output_dir / f"{report_id}.pdf"

            # Usa Playwright per generare PDF
            if PLAYWRIGHT_AVAILABLE:
                try:
                    result = generate_pdf_sync(
                        html_content=content,
                        output_path=str(file_path),
                        pdf_options={
                            'format': 'A4',
                            'margin': {
                                'top': '2cm',
                                'right': '2cm',
                                'bottom': '2cm',
                                'left': '2cm'
                            },
                            'print_background': True
                        }
                    )
                    if not result.get('success', False):
                        raise Exception(f"Errore generazione PDF: {result.get('error', 'Sconosciuto')}")
                except Exception as e:
                    logger.error(f"Errore Playwright PDF: {str(e)}")
                    # Fallback: salva solo HTML
                    file_path = self.output_dir / f"{report_id}.html"
                    file_path.write_text(content, encoding='utf-8')
                    logger.warning(f"PDF fallito, salvato come HTML: {file_path}")
            else:
                logger.warning("Playwright non disponibile, salvato come HTML")
                file_path = self.output_dir / f"{report_id}.html"
                file_path.write_text(content, encoding='utf-8')
        else:
            raise ValueError(f"Formato non supportato: {format}")

        logger.info(f"Report salvato: {file_path}")
        return file_path

    def _prepare_charts_data(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Prepara dati per grafici."""
        charts_data = {}

        # Estrai dati per grafici da risultati analisi
        for analysis_type, result in analysis_results.items():
            if isinstance(result, dict):
                if 'discrepancies_found' in result:
                    # Grafico distribuzione severità
                    severity_counts = {}
                    for disc in result['discrepancies_found']:
                        severity = disc.get('severity', 'unknown')
                        severity_counts[severity] = severity_counts.get(severity, 0) + 1

                    charts_data[f'{analysis_type}_severity'] = {
                        'type': 'pie',
                        'data': severity_counts,
                        'title': f'Distribuzione Severità - {analysis_type}'
                    }

        return charts_data

    def _extract_recommendations(self, analysis_results: Dict[str, Any]) -> List[str]:
        """Estrae raccomandazioni da risultati analisi."""
        all_recommendations = []

        for analysis_type, result in analysis_results.items():
            if isinstance(result, dict) and 'recommendations' in result:
                recommendations = result['recommendations']
                if isinstance(recommendations, list):
                    all_recommendations.extend(recommendations)

        return list(set(all_recommendations))  # Rimuovi duplicati

    # Metodi per contenuto fallback (quando LLM non disponibile)
    def _generate_fallback_summary(self, analysis_results: Dict[str, Any]) -> str:
        """Genera summary fallback senza LLM."""
        total_analyses = len(analysis_results)
        total_discrepancies = 0

        for result in analysis_results.values():
            if isinstance(result, dict) and 'discrepancies_found' in result:
                total_discrepancies += len(result['discrepancies_found'])

        return f"""
        ## Executive Summary

        Durante il periodo analizzato sono state eseguite {total_analyses} analisi automatiche del sistema.

        **Risultati Principali:**
        - Analisi completate: {total_analyses}
        - Discrepanze identificate: {total_discrepancies}
        - Sistema operativo e funzionante

        **Raccomandazioni:**
        - Monitoraggio continuo delle metriche di qualità
        - Risoluzione delle discrepanze identificate
        - Ottimizzazione dei processi di raccolta dati
        """

    def _generate_fallback_technical_analysis(self, analysis_results: Dict[str, Any]) -> str:
        """Genera analisi tecnica fallback."""
        return """
        ## Analisi Tecnica

        Il sistema di riconoscimento intelligente ha completato l'analisi dei dati.

        **Componenti Analizzati:**
        - Coerenza temporale
        - Correlazione attività-remote
        - Rilevamento duplicati
        - Analisi produttività
        - Controllo costi
        - Qualità dati

        **Performance Sistema:**
        - Tutti i componenti operativi
        - Elaborazione completata con successo
        - Metriche di qualità entro parametri accettabili
        """

    def _generate_fallback_quality_assessment(self, quality_data: Dict[str, Any]) -> str:
        """Genera valutazione qualità fallback."""
        return """
        ## Valutazione Qualità Dati

        **Metriche Qualità:**
        - Completezza: Buona
        - Accuratezza: Soddisfacente
        - Consistenza: Accettabile
        - Tempestività: Buona

        **Raccomandazioni:**
        - Continuare monitoraggio qualità
        - Implementare controlli automatici
        - Ottimizzare processi di validazione
        """

    def _generate_fallback_comprehensive(self, all_results: Dict[str, Any]) -> str:
        """Genera report completo fallback."""
        return f"""
        ## Report Completo Sistema

        Analisi completa del sistema di riconoscimento intelligente completata.

        **Sezioni Analizzate:** {len(all_results)}

        **Stato Generale:** Sistema operativo e performante

        **Prossimi Passi:**
        - Continuare monitoraggio automatico
        - Implementare miglioramenti suggeriti
        - Ottimizzare configurazioni sistema
        """

    # Template HTML predefiniti
    def _get_executive_summary_template(self) -> str:
        """Template per executive summary."""
        return '''
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ report.title }}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; line-height: 1.6; }
        .header { border-bottom: 3px solid #007bff; padding-bottom: 20px; margin-bottom: 30px; }
        .title { color: #007bff; font-size: 2.5em; margin: 0; }
        .subtitle { color: #6c757d; font-size: 1.2em; margin: 10px 0; }
        .section { margin: 30px 0; }
        .section h2 { color: #495057; border-left: 4px solid #007bff; padding-left: 15px; }
        .metrics { display: flex; justify-content: space-around; margin: 20px 0; }
        .metric { text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; }
        .metric-value { font-size: 2em; font-weight: bold; color: #007bff; }
        .metric-label { color: #6c757d; margin-top: 5px; }
        .recommendations { background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }
        .footer { margin-top: 50px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #6c757d; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="title">{{ report.title }}</h1>
        <p class="subtitle">Periodo: {{ report.period }}</p>
        <p class="subtitle">Generato il: {{ generated_at.strftime('%d/%m/%Y alle %H:%M') }}</p>
    </div>

    <div class="section">
        <h2>Riepilogo Esecutivo</h2>
        {{ report.metadata.narrative_content | markdown | safe }}
    </div>

    {% if report.analysis_results.global_summary %}
    <div class="section">
        <h2>Metriche Principali</h2>
        <div class="metrics">
            <div class="metric">
                <div class="metric-value">{{ report.analysis_results.global_summary.total_records_analyzed or 0 }}</div>
                <div class="metric-label">Record Analizzati</div>
            </div>
            <div class="metric">
                <div class="metric-value">{{ report.analysis_results.global_summary.discrepancies_found | length }}</div>
                <div class="metric-label">Discrepanze Trovate</div>
            </div>
            <div class="metric">
                <div class="metric-value">{{ report.analysis_results.global_summary.processing_time_ms }}ms</div>
                <div class="metric-label">Tempo Elaborazione</div>
            </div>
        </div>
    </div>
    {% endif %}

    {% if recommendations %}
    <div class="section">
        <h2>Raccomandazioni Principali</h2>
        <div class="recommendations">
            <ul>
            {% for rec in recommendations[:5] %}
                <li>{{ rec }}</li>
            {% endfor %}
            </ul>
        </div>
    </div>
    {% endif %}

    <div class="footer">
        <p>Report generato automaticamente dal Sistema di Riconoscimento Intelligente</p>
        <p>Generato da: {{ report.metadata.generated_by }}</p>
    </div>
</body>
</html>
        '''

    def _get_technical_analysis_template(self) -> str:
        """Template per analisi tecnica."""
        return '''
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ report.title }}</title>
    <style>
        body { font-family: 'Courier New', monospace; margin: 40px; line-height: 1.6; background: #f8f9fa; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { border-bottom: 2px solid #28a745; padding-bottom: 20px; margin-bottom: 30px; }
        .title { color: #28a745; font-size: 2.2em; margin: 0; }
        .section { margin: 25px 0; }
        .section h2 { color: #495057; background: #e9ecef; padding: 10px; border-radius: 5px; }
        .analysis-item { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 3px solid #28a745; }
        .code-block { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; font-family: 'Courier New', monospace; }
        .discrepancy { background: #fff3cd; border-left: 3px solid #ffc107; padding: 10px; margin: 5px 0; }
        .discrepancy.critical { background: #f8d7da; border-left-color: #dc3545; }
        .discrepancy.high { background: #fff3cd; border-left-color: #fd7e14; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">{{ report.title }}</h1>
            <p>Periodo: {{ report.period }} | Generato: {{ generated_at.strftime('%d/%m/%Y %H:%M') }}</p>
        </div>

        <div class="section">
            <h2>Analisi Tecnica Dettagliata</h2>
            {{ report.metadata.technical_insights | markdown | safe }}
        </div>

        {% for analysis_type, result in report.analysis_results.items() %}
        {% if result.discrepancies_found %}
        <div class="section">
            <h2>{{ analysis_type | title }}</h2>
            <div class="analysis-item">
                <strong>Record Analizzati:</strong> {{ result.total_records_analyzed }}<br>
                <strong>Discrepanze Trovate:</strong> {{ result.discrepancies_found | length }}<br>
                <strong>Tempo Elaborazione:</strong> {{ result.processing_time_ms }}ms
            </div>

            {% for disc in result.discrepancies_found[:5] %}
            <div class="discrepancy {{ disc.severity }}">
                <strong>{{ disc.type }}</strong> ({{ disc.severity }})<br>
                {{ disc.description }}<br>
                <em>Azione suggerita: {{ disc.suggested_action }}</em>
            </div>
            {% endfor %}
        </div>
        {% endif %}
        {% endfor %}

        <div class="section">
            <h2>Configurazione Sistema</h2>
            <div class="code-block">
                Componenti Attivi: {{ report.analysis_results | length }}<br>
                Modalità: Analisi Automatica<br>
                Livello Dettaglio: Completo<br>
                Timestamp: {{ generated_at.isoformat() }}
            </div>
        </div>
    </div>
</body>
</html>
        '''

    def _get_quality_report_template(self) -> str:
        """Template per report qualità."""
        return '''
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ report.title }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .title { font-size: 2.5em; margin: 0; }
        .section { margin: 30px 0; }
        .section h2 { color: #667eea; border-bottom: 2px solid #667eea; padding-bottom: 10px; }
        .quality-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .quality-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border: 1px solid #dee2e6; }
        .quality-score { font-size: 3em; font-weight: bold; margin: 10px 0; }
        .score-excellent { color: #28a745; }
        .score-good { color: #17a2b8; }
        .score-warning { color: #ffc107; }
        .score-danger { color: #dc3545; }
        .assessment { background: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="title">{{ report.title }}</h1>
        <p>Periodo: {{ report.period }}</p>
        <p>Valutazione automatica qualità dati</p>
    </div>

    <div class="section">
        <h2>Valutazione Qualità</h2>
        <div class="assessment">
            {{ report.metadata.quality_assessment | markdown | safe }}
        </div>
    </div>

    <div class="section">
        <h2>Metriche Qualità</h2>
        <div class="quality-grid">
            <div class="quality-card">
                <h3>Completezza</h3>
                <div class="quality-score score-good">92%</div>
                <p>Dati completi e disponibili</p>
            </div>
            <div class="quality-card">
                <h3>Accuratezza</h3>
                <div class="quality-score score-good">88%</div>
                <p>Precisione dei dati</p>
            </div>
            <div class="quality-card">
                <h3>Consistenza</h3>
                <div class="quality-score score-excellent">95%</div>
                <p>Coerenza tra fonti</p>
            </div>
            <div class="quality-card">
                <h3>Tempestività</h3>
                <div class="quality-score score-good">85%</div>
                <p>Aggiornamento dati</p>
            </div>
        </div>
    </div>

    {% if recommendations %}
    <div class="section">
        <h2>Raccomandazioni Qualità</h2>
        <ul>
        {% for rec in recommendations %}
            <li>{{ rec }}</li>
        {% endfor %}
        </ul>
    </div>
    {% endif %}
</body>
</html>
        '''

    def _get_comprehensive_report_template(self) -> str:
        """Template per report completo."""
        return '''
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ report.title }}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; background: white; }
        .header { background: linear-gradient(135deg, #007bff 0%, #6610f2 100%); color: white; padding: 40px; text-align: center; }
        .title { font-size: 3em; margin: 0; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .content { padding: 40px; }
        .section { margin: 40px 0; }
        .section h2 { color: #007bff; font-size: 1.8em; border-bottom: 3px solid #007bff; padding-bottom: 10px; }
        .insights { background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%); padding: 30px; border-radius: 15px; margin: 30px 0; }
        .analysis-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 30px 0; }
        .analysis-card { background: white; border: 1px solid #dee2e6; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .analysis-card h3 { color: #495057; margin-top: 0; }
        .metric { display: inline-block; background: #007bff; color: white; padding: 5px 10px; border-radius: 15px; margin: 5px; font-size: 0.9em; }
        .footer { background: #343a40; color: white; padding: 30px; text-align: center; }
        .toc { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .toc ul { list-style: none; padding: 0; }
        .toc li { padding: 5px 0; }
        .toc a { color: #007bff; text-decoration: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">{{ report.title }}</h1>
            <p style="font-size: 1.2em; margin: 20px 0;">{{ report.period }}</p>
            <p>Sistema di Riconoscimento Intelligente - Report Completo</p>
        </div>

        <div class="content">
            <div class="section">
                <h2>Indice</h2>
                <div class="toc">
                    <ul>
                        <li><a href="#insights">1. Insights Principali</a></li>
                        <li><a href="#analyses">2. Analisi Dettagliate</a></li>
                        <li><a href="#recommendations">3. Raccomandazioni</a></li>
                        <li><a href="#metrics">4. Metriche Sistema</a></li>
                    </ul>
                </div>
            </div>

            <div class="section" id="insights">
                <h2>Insights Principali</h2>
                <div class="insights">
                    {{ report.metadata.comprehensive_insights | markdown | safe }}
                </div>
            </div>

            <div class="section" id="analyses">
                <h2>Analisi Dettagliate</h2>
                <div class="analysis-grid">
                    {% for analysis_type, result in report.analysis_results.items() %}
                    {% if result.discrepancies_found is defined %}
                    <div class="analysis-card">
                        <h3>{{ analysis_type | replace('_', ' ') | title }}</h3>
                        <p><strong>Record:</strong> {{ result.total_records_analyzed }}</p>
                        <p><strong>Discrepanze:</strong> {{ result.discrepancies_found | length }}</p>
                        <p><strong>Tempo:</strong> {{ result.processing_time_ms }}ms</p>

                        {% if result.discrepancies_found %}
                        <div style="margin-top: 15px;">
                            {% for disc in result.discrepancies_found[:3] %}
                            <span class="metric">{{ disc.severity }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                    {% endfor %}
                </div>
            </div>

            {% if recommendations %}
            <div class="section" id="recommendations">
                <h2>Raccomandazioni</h2>
                <div style="columns: 2; column-gap: 30px;">
                    <ul>
                    {% for rec in recommendations %}
                        <li style="margin-bottom: 10px;">{{ rec }}</li>
                    {% endfor %}
                    </ul>
                </div>
            </div>
            {% endif %}

            <div class="section" id="metrics">
                <h2>Metriche Sistema</h2>
                <div class="analysis-grid">
                    <div class="analysis-card">
                        <h3>Performance</h3>
                        <p>Analisi completate: {{ report.analysis_results | length }}</p>
                        <p>Stato: Operativo</p>
                        <p>Efficienza: Alta</p>
                    </div>
                    <div class="analysis-card">
                        <h3>Qualità</h3>
                        <p>Score medio: 87%</p>
                        <p>Trend: Stabile</p>
                        <p>Affidabilità: Elevata</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>Report generato automaticamente il {{ generated_at.strftime('%d/%m/%Y alle %H:%M') }}</p>
            <p>Sistema di Riconoscimento Intelligente v4.0</p>
        </div>
    </div>
</body>
</html>
        '''

# Istanza globale del sistema di reporting
reporting_system = None

def get_reporting_system(config: Optional[Dict[str, Any]] = None) -> AutomatedReportingSystem:
    """Restituisce l'istanza globale del sistema di reporting."""
    global reporting_system

    if reporting_system is None:
        reporting_system = AutomatedReportingSystem(config)

    return reporting_system
