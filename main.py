#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import uuid
import shutil
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

import pandas as pd
from fastapi import FastAPI, File, UploadFile, HTTPException, Form, Query, Depends
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel

from file_detector import FileTypeDetector

# Configurazione del logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Inizializzazione dell'app FastAPI
app = FastAPI(
    title="API Backend per Riconoscimento ed Elaborazione File",
    description="API per il caricamento, il riconoscimento e l'elaborazione di file di attività",
    version="1.0.0"
)

# Configurazione CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In produzione, specificare i domini consentiti
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configurazione delle cartelle
UPLOAD_FOLDER = "uploads"
TEMP_FOLDER = os.path.join(UPLOAD_FOLDER, "temp")

# Assicurarsi che le cartelle esistano
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(TEMP_FOLDER, exist_ok=True)

# Inizializzazione del rilevatore di tipo file
file_detector = FileTypeDetector()

# Dizionario per tenere traccia dei file caricati
# Chiave: file_id, Valore: {path, original_filename, detected_type, upload_time, etc.}
uploaded_files = {}

# Modelli Pydantic per la validazione e la documentazione
class FileInfo(BaseModel):
    file_id: str
    original_filename: str
    detected_type: str
    confidence_score: float
    upload_time: str
    columns: List[str]
    sample_data: Optional[List[Dict[str, Any]]] = None
    type_scores: Dict[str, float]

class FileUploadResponse(BaseModel):
    file_id: str
    original_filename: str
    detected_type: str
    confidence_score: float
    message: str

# Funzioni di utilità
def read_file(file_path: str):
    """
    Legge un file CSV o Excel e restituisce un DataFrame pandas.
    
    Args:
        file_path: Percorso del file da leggere
        
    Returns:
        Tuple (DataFrame, error_message)
    """
    try:
        if file_path.endswith('.csv'):
            # Prova prima con separatore punto e virgola (comune in Italia)
            try:
                df = pd.read_csv(file_path, sep=';', encoding='utf-8-sig')
            except:
                # Se fallisce, prova con la virgola
                df = pd.read_csv(file_path, sep=',', encoding='utf-8-sig')
        elif file_path.endswith(('.xlsx', '.xls')):
            df = pd.read_excel(file_path)
        else:
            return None, f"Formato file non supportato: {os.path.basename(file_path)}"
        
        return df, None
    except Exception as e:
        logger.error(f"Errore nella lettura del file {file_path}: {str(e)}")
        return None, f"Errore nella lettura del file: {str(e)}"

def save_uploaded_file(upload_file: UploadFile) -> str:
    """
    Salva un file caricato e restituisce il percorso.
    
    Args:
        upload_file: File caricato tramite FastAPI
        
    Returns:
        Percorso del file salvato
    """
    # Genera un ID univoco per il file
    file_id = str(uuid.uuid4())
    
    # Crea una cartella temporanea per questo file
    file_temp_folder = os.path.join(TEMP_FOLDER, file_id)
    os.makedirs(file_temp_folder, exist_ok=True)
    
    # Ottieni l'estensione originale
    _, ext = os.path.splitext(upload_file.filename)
    
    # Salva il file con un nome sicuro
    file_path = os.path.join(file_temp_folder, f"upload{ext}")
    
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(upload_file.file, buffer)
    
    return file_id, file_path

# Endpoint API
@app.get("/")
async def root():
    """Endpoint di base per verificare che l'API sia attiva."""
    return {"message": "API Backend per Riconoscimento ed Elaborazione File"}

@app.post("/uploadfile/", response_model=FileUploadResponse)
async def upload_file(file: UploadFile = File(...)):
    """
    Carica un file, lo salva temporaneamente e ne rileva il tipo.
    
    Args:
        file: File da caricare
        
    Returns:
        Informazioni sul file caricato e il tipo rilevato
    """
    if not file:
        raise HTTPException(status_code=400, detail="Nessun file fornito")
    
    # Verifica l'estensione del file
    filename = file.filename
    ext = os.path.splitext(filename)[1].lower()
    
    if ext not in ['.csv', '.xlsx', '.xls']:
        raise HTTPException(
            status_code=400, 
            detail=f"Formato file non supportato: {ext}. Formati supportati: .csv, .xlsx, .xls"
        )
    
    try:
        # Salva il file
        file_id, file_path = save_uploaded_file(file)
        
        # Leggi il file
        df, error = read_file(file_path)
        
        if error:
            # Pulisci i file temporanei in caso di errore
            shutil.rmtree(os.path.join(TEMP_FOLDER, file_id), ignore_errors=True)
            raise HTTPException(status_code=400, detail=error)
        
        # Rileva il tipo di file
        detected_type, confidence_score, type_scores = file_detector.detect_file_type(df)
        
        # Registra le informazioni sul file
        uploaded_files[file_id] = {
            "path": file_path,
            "original_filename": filename,
            "detected_type": detected_type,
            "confidence_score": confidence_score,
            "upload_time": datetime.now().isoformat(),
            "dataframe": df,  # Memorizza il DataFrame in memoria (in produzione, considerare alternative)
            "type_scores": type_scores
        }
        
        # Log del rilevamento
        logger.info(f"File caricato: {filename}, ID: {file_id}, Tipo rilevato: {detected_type}, Confidenza: {confidence_score:.2f}")
        
        # Prepara la risposta
        message = "File caricato con successo"
        if detected_type == "unknown":
            message = "File caricato, ma il tipo non è stato riconosciuto con certezza"
        
        return FileUploadResponse(
            file_id=file_id,
            original_filename=filename,
            detected_type=detected_type,
            confidence_score=confidence_score,
            message=message
        )
    
    except Exception as e:
        logger.error(f"Errore durante l'upload del file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore durante l'upload del file: {str(e)}")

@app.get("/file-info/{file_id}", response_model=FileInfo)
async def get_file_info(file_id: str, sample_rows: int = Query(5, ge=0, le=50)):
    """
    Ottiene informazioni dettagliate su un file caricato, inclusi metadati e un campione dei dati.
    
    Args:
        file_id: ID del file
        sample_rows: Numero di righe da includere nel campione
        
    Returns:
        Informazioni dettagliate sul file
    """
    if file_id not in uploaded_files:
        raise HTTPException(status_code=404, detail=f"File con ID {file_id} non trovato")
    
    file_info = uploaded_files[file_id]
    df = file_info["dataframe"]
    
    # Prepara un campione dei dati
    sample_data = None
    if not df.empty and sample_rows > 0:
        sample_data = df.head(sample_rows).to_dict(orient='records')
    
    return FileInfo(
        file_id=file_id,
        original_filename=file_info["original_filename"],
        detected_type=file_info["detected_type"],
        confidence_score=file_info["confidence_score"],
        upload_time=file_info["upload_time"],
        columns=df.columns.tolist(),
        sample_data=sample_data,
        type_scores=file_info["type_scores"]
    )

# Avvio dell'applicazione
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
