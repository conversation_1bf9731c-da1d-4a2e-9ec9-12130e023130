# 🔧 PIANO RIFINITURA APP ROBERTO
**Onboarding Dati e Persistenza - Architettura Moderna**

## 📋 ANALISI SITUAZIONE ATTUALE

### ✅ **COMPONENTI STABILI (NON MODIFICARE)**
- **Setup Wizard**: Funzionante, solo fix errori navigazione
- **Agenti Dashboard**: Perfetto - **VINCOLO: NON TOCCARE**
- **Dashboard AI**: Funziona bene, valutazione post-caricamento dati
- **Sistema Supabase**: Integrazione attiva e stabile

### ⚠️ **COMPONENTI DA VALUTARE**
- **Dati <PERSON>**: Errori evidenti - candidato rimozione
- **Grafici Interattivi**: Logica pre-Supabase, errori dipendenti
- **Chat AI**: Funzionale ma styling non conforme
- **Dashboard Standard**: Possibili problemi logica legacy

---

## 🎯 FASI DI LAVORO

### **FASE 1: ANALISI E STABILIZZAZIONE** 
*Durata: 1-2 giorni*

#### **Task 1.1: Audit Errori Console**
**Priorità: CRITICA**

**Workflow obbligatorio:**
1. **Commit/Push**: Stato attuale prima modifiche
2. **Context 7**: Ricerca soluzioni errori JavaScript/Flask
3. **Analisi**: Catalogazione errori per sezione
4. **Remember**: Aggiornamento memoria con errori identificati

**Deliverable:**
- `errori-console-audit.md` con screenshot errori
- Classificazione: Critici vs. Warning vs. Info
- Mappatura errori → sezioni applicazione

#### **Task 1.2: Valutazione Sezioni Legacy**
**Priorità: ALTA**

**Workflow obbligatorio:**
1. **Context 7**: Ricerca best practices migrazione Supabase
2. **Analisi codice**: Identificazione logiche pre-Supabase
3. **Test funzionalità**: Verifica compatibilità architettura moderna

**Deliverable:**
- Matrice decisionale: Mantenere/Aggiornare/Rimuovere
- Stima effort per ogni sezione
- Piano migrazione graduale

---

### **FASE 2: RIMOZIONE COMPONENTI LEGACY**
*Durata: 1 giorno*

#### **Task 2.1: Rimozione Dati Grezzi (se confermato)**
**Priorità: MEDIA**

**Workflow obbligatorio:**
1. **Commit/Push**: Backup prima rimozione
2. **Context 7**: Ricerca alternative moderne per visualizzazione dati
3. **Backup**: Salvataggio codice in `legacy-backup/`
4. **Test**: Verifica nessuna dipendenza rotta

**Deliverable:**
- Rimozione pulita sezione Dati Grezzi
- Aggiornamento navigation menu
- Test regressione completo

#### **Task 2.2: Cleanup Routes e Template**
**Priorità: BASSA**

**Workflow obbligatorio:**
1. **Analisi dipendenze**: Verifica import/link rotti
2. **Pulizia**: Rimozione route, template, static files
3. **Remember**: Aggiornamento architettura sistema

**Deliverable:**
- Codebase pulito da riferimenti legacy
- Documentazione modifiche effettuate

---

### **FASE 3: MODERNIZZAZIONE GRAFICI INTERATTIVI**
*Durata: 2-3 giorni*

#### **Task 3.1: Migrazione Logica Supabase**
**Priorità: ALTA**

**Workflow obbligatorio:**
1. **Context 7**: Ricerca pattern integrazione Supabase + Plotly
2. **Analisi**: Identificazione query pre-Supabase
3. **Migrazione**: Sostituzione con chiamate Supabase API
4. **Test**: Verifica riconoscimento dipendenti

**Deliverable:**
- Grafici Interattivi compatibili Supabase
- Riconoscimento corretto dipendenti
- Performance ottimizzate

#### **Task 3.2: Fix Comunicazione Backend**
**Priorità: CRITICA**

**Workflow obbligatorio:**
1. **Debug**: Analisi errori comunicazione Supabase
2. **Context 7**: Ricerca soluzioni errori API
3. **Fix**: Correzione endpoint e gestione errori
4. **Remember**: Pattern comunicazione corretti

**Deliverable:**
- Comunicazione Supabase stabile
- Gestione errori robusta
- Logging migliorato

---

### **FASE 4: RAFFINAMENTO CHAT AI**
*Durata: 1-2 giorni*

#### **Task 4.1: Allineamento Styling**
**Priorità: MEDIA**

**Workflow obbligatorio:**
1. **Context 7**: Ricerca design system moderni per chat
2. **Analisi**: Confronto con Setup Wizard (riferimento design)
3. **Implementazione**: Allineamento colori, layout, componenti
4. **Test**: Verifica responsive e tema scuro

**Deliverable:**
- Chat AI con styling coerente
- Compatibilità tema scuro/chiaro
- Design responsive

#### **Task 4.2: Ottimizzazione Filtri LLM**
**Priorità: BASSA**

**Workflow obbligatorio:**
1. **Context 7**: Ricerca best practices UI filtri AI
2. **Implementazione**: Miglioramento UX filtri
3. **Test**: Verifica funzionalità e performance

**Deliverable:**
- Filtri LLM ottimizzati
- UX migliorata
- Performance stabili

---

### **FASE 5: STABILIZZAZIONE SETUP WIZARD**
*Durata: 1 giorno*

#### **Task 5.1: Fix Errori Navigazione**
**Priorità: ALTA**

**Workflow obbligatorio:**
1. **Debug**: Riproduzione errore navigazione
2. **Context 7**: Ricerca soluzioni gestione stato wizard
3. **Fix**: Correzione logica navigazione
4. **Test**: Verifica flusso completo wizard

**Deliverable:**
- Setup Wizard stabile in ogni scenario
- Gestione stato corretta
- UX fluida

---

### **FASE 6: VERIFICA DASHBOARD STANDARD**
*Durata: 1 giorno*

#### **Task 6.1: Audit Compatibilità Supabase**
**Priorità: MEDIA**

**Workflow obbligatorio:**
1. **Analisi**: Verifica logiche pre-Supabase
2. **Context 7**: Ricerca pattern dashboard moderne
3. **Test**: Caricamento dati e verifica errori
4. **Remember**: Stato compatibilità dashboard

**Deliverable:**
- Dashboard Standard verificata
- Compatibilità Supabase confermata
- Piano miglioramenti se necessario

---

## 📊 MATRICE PRIORITÀ

| Sezione | Priorità | Azione | Effort | Rischio |
|---------|----------|--------|--------|---------|
| Agenti Dashboard | - | **NON TOCCARE** | - | - |
| Setup Wizard | ALTA | Fix navigazione | BASSO | BASSO |
| Grafici Interattivi | CRITICA | Migrazione Supabase | ALTO | MEDIO |
| Chat AI | MEDIA | Styling | MEDIO | BASSO |
| Dati Grezzi | MEDIA | Rimozione | BASSO | BASSO |
| Dashboard Standard | MEDIA | Verifica | BASSO | BASSO |
| Dashboard AI | BASSA | Monitoraggio | - | - |

---

## 🔒 VINCOLI E REGOLE

### **VINCOLI ASSOLUTI**
- ❌ **NON modificare Agenti Dashboard**
- ❌ **NON rompere integrazione Supabase**
- ❌ **NON compromettere Setup Wizard**
- ❌ **NON modificare sistema agenti AI**

### **WORKFLOW OBBLIGATORIO PER OGNI TASK**
1. **📝 Commit/Push**: Stato sicuro prima modifiche
2. **🔍 Context 7**: Ricerca soluzioni e best practices
3. **🧠 Remember**: Aggiornamento memoria se necessario
4. **🧪 Test**: Verifica funzionalità prima e dopo
5. **📋 Documentazione**: Aggiornamento modifiche

### **CRITERI DECISIONE**
- **Mantenere**: Se funziona e compatibile Supabase
- **Aggiornare**: Se funziona ma logica legacy
- **Rimuovere**: Se errori evidenti e non essenziale

---

## 🎯 OBIETTIVI FINALI

### **RISULTATI ATTESI**
- ✅ Zero errori console visibili
- ✅ Compatibilità 100% architettura Supabase
- ✅ UX coerente in tutte le sezioni
- ✅ Performance ottimizzate
- ✅ Codebase pulito da legacy

### **METRICHE SUCCESSO**
- Riduzione errori console: 100%
- Tempo caricamento pagine: <2s
- Compatibilità mobile: 100%
- Copertura test: >80%
- Soddisfazione utente: Miglioramento UX

---

## 📅 TIMELINE STIMATA

**TOTALE: 7-10 giorni lavorativi**

- **Settimana 1**: Fasi 1-3 (Analisi + Rimozione + Grafici)
- **Settimana 2**: Fasi 4-6 (Chat + Wizard + Dashboard)

**MILESTONE INTERMEDI**
- Giorno 3: Audit completo + decisioni architetturali
- Giorno 5: Grafici Interattivi modernizzati
- Giorno 8: Tutte le sezioni stabilizzate
- Giorno 10: Test finali + documentazione

---

*Documento creato: 2025-01-25*
*Versione: 1.0*
*Stato: PRONTO PER ESECUZIONE*
