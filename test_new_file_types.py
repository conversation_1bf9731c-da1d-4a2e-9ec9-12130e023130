#!/usr/bin/env python3
"""
Test per i nuovi tipi di file: registro auto e progetti
"""

import pandas as pd
import sys
import os

# Aggiungi il percorso corrente per importare i moduli
sys.path.append('.')

from enhanced_file_detector import EnhancedFileDetector

def test_new_file_types():
    """Testa il rilevamento dei nuovi tipi di file"""
    
    print("🔧 Testando rilevamento nuovi tipi di file...")
    
    # Inizializza il detector
    enhanced_detector = EnhancedFileDetector()
    
    # Test 1: Registro Auto
    print("\n🚗 Test 1: Registro Auto")
    try:
        df_auto = pd.read_csv('test_registro_auto.csv')
        print(f"Colonne del file: {df_auto.columns.tolist()}")
        
        detected_type, confidence, scores = enhanced_detector.detect_file_type(df_auto)
        print(f"Tipo rilevato: {detected_type}")
        print(f"Confidenza: {confidence:.3f}")
        print("Punteggi per tipo:")
        for file_type, score in scores.items():
            print(f"  - {file_type}: {score:.3f}")
        
        # Test mappatura colonne
        if detected_type != "unknown":
            print(f"\n🗂️ Mappatura colonne suggerita per {detected_type}:")
            mapping = enhanced_detector.get_column_mapping_suggestions(df_auto, detected_type)
            for original, standard in mapping.items():
                print(f"  '{original}' -> '{standard}'")
        
    except Exception as e:
        print(f"❌ Errore nel test registro auto: {e}")
    
    # Test 2: File Progetti simulato
    print("\n📊 Test 2: File Progetti simulato")
    progetti_data = {
        'Nome Progetto': ['Progetto Alpha', 'Progetto Beta', 'Progetto Gamma'],
        'Cliente': ['Azienda A', 'Azienda B', 'Azienda C'],
        'Data Inizio': ['2025-01-15', '2025-02-01', '2025-03-01'],
        'Stato': ['In corso', 'Completato', 'Pianificato'],
        'Responsabile': ['Mario Rossi', 'Luigi Verdi', 'Anna Bianchi'],
        'Budget': ['10000', '15000', '8000']
    }
    
    df_progetti = pd.DataFrame(progetti_data)
    print(f"Colonne del file: {df_progetti.columns.tolist()}")
    
    detected_type, confidence, scores = enhanced_detector.detect_file_type(df_progetti)
    print(f"Tipo rilevato: {detected_type}")
    print(f"Confidenza: {confidence:.3f}")
    print("Punteggi per tipo:")
    for file_type, score in scores.items():
        print(f"  - {file_type}: {score:.3f}")
    
    # Test 3: File con nomi inglesi (TeamViewer export)
    print("\n🖥️ Test 3: TeamViewer Export (nomi inglesi)")
    teamviewer_data = {
        'User': ['john.doe', 'jane.smith', 'mike.wilson'],
        'Computer': ['PC-001', 'LAPTOP-002', 'DESKTOP-003'],
        'Start Time': ['2025-01-15 09:30', '2025-01-15 11:00', '2025-01-15 14:20'],
        'End Time': ['2025-01-15 10:15', '2025-01-15 11:30', '2025-01-15 15:45'],
        'Duration': ['45 min', '30 min', '1h 25min'],
        'Session Type': ['Remote Support', 'File Transfer', 'Remote Support']
    }
    
    df_teamviewer = pd.DataFrame(teamviewer_data)
    print(f"Colonne del file: {df_teamviewer.columns.tolist()}")
    
    detected_type, confidence, scores = enhanced_detector.detect_file_type(df_teamviewer)
    print(f"Tipo rilevato: {detected_type}")
    print(f"Confidenza: {confidence:.3f}")
    print("Punteggi per tipo:")
    for file_type, score in scores.items():
        print(f"  - {file_type}: {score:.3f}")
    
    # Test mappatura colonne
    if detected_type != "unknown":
        print(f"\n🗂️ Mappatura colonne suggerita per {detected_type}:")
        mapping = enhanced_detector.get_column_mapping_suggestions(df_teamviewer, detected_type)
        for original, standard in mapping.items():
            print(f"  '{original}' -> '{standard}'")
    
    print("\n✅ Test completati!")

if __name__ == "__main__":
    test_new_file_types()
