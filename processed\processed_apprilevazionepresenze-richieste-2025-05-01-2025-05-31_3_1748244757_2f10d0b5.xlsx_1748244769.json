[{"Data della richiesta": "2025-03-25 10:14:45", "Dipendente": "Hoxha, Arlind", "Tipo": "<PERSON><PERSON>", "Data inizio": "28/04/2025", "Data fine": "05/05/2025", "Stato": "Approvata", "Note": "Causa dovrò sottopormi ad una operazione e la dovrò fare in Albania "}, {"Data della richiesta": "2025-04-01 10:16:50", "Dipendente": "Serratore, Lorenzo", "Tipo": "<PERSON><PERSON>", "Data inizio": "02/05/2025", "Data fine": "02/05/2025", "Stato": "Approvata", "Note": NaN}, {"Data della richiesta": "2025-04-01 21:02:55", "Dipendente": "<PERSON><PERSON>, <PERSON>", "Tipo": "ROL", "Data inizio": "05/05/2025", "Data fine": "05/05/2025", "Stato": "Approvata", "Note": NaN}, {"Data della richiesta": "2025-04-09 16:12:33", "Dipendente": "<PERSON><PERSON><PERSON>, Franco", "Tipo": "ROL", "Data inizio": "05/05/2025 12:15:00", "Data fine": "05/05/2025 13:30:00", "Stato": "Approvata", "Note": "Visita specialistica"}, {"Data della richiesta": "2025-05-05 10:08:38", "Dipendente": "Serratore, Lorenzo", "Tipo": "ROL", "Data inizio": "09/05/2025 16:30:00", "Data fine": "09/05/2025 18:00:00", "Stato": "Approvata", "Note": NaN}, {"Data della richiesta": "2025-05-05 16:14:49", "Dipendente": "De Palma, Gabriele", "Tipo": "<PERSON><PERSON>", "Data inizio": "15/05/2025", "Data fine": "16/05/2025", "Stato": "Approvata", "Note": NaN}, {"Data della richiesta": "2025-05-06 09:07:57", "Dipendente": "<PERSON><PERSON><PERSON>, <PERSON>", "Tipo": "Permessi ex festività", "Data inizio": "06/05/2025 16:50:00", "Data fine": "06/05/2025 18:00:00", "Stato": "Approvata", "Note": NaN}, {"Data della richiesta": "2025-05-06 10:52:45", "Dipendente": "<PERSON><PERSON><PERSON>, Franco", "Tipo": "ROL", "Data inizio": "06/05/2025 17:45:00", "Data fine": "06/05/2025 18:00:00", "Stato": "Approvata", "Note": NaN}, {"Data della richiesta": "2025-05-14 08:46:10", "Dipendente": "<PERSON><PERSON><PERSON>, <PERSON>", "Tipo": "Permessi ex festività", "Data inizio": "14/05/2025 09:00:00", "Data fine": "14/05/2025 09:30:00", "Stato": "Da approvare", "Note": NaN}, {"Data della richiesta": "2025-05-14 11:35:20", "Dipendente": "<PERSON><PERSON><PERSON>, Franco", "Tipo": "<PERSON><PERSON>", "Data inizio": "14/05/2025 14:00:00", "Data fine": "14/05/2025 18:00:00", "Stato": "Da approvare", "Note": "Richieste 4 ore di ferie "}, {"Data della richiesta": "2025-05-15 09:21:57", "Dipendente": "Hoxha, Arlind", "Tipo": "<PERSON><PERSON>", "Data inizio": "15/05/2025 10:00:00", "Data fine": "15/05/2025 11:00:00", "Stato": "Da approvare", "Note": "Denuncia smarrimento documenti "}, {"Data della richiesta": "2025-05-16 09:34:26", "Dipendente": "<PERSON><PERSON><PERSON>, <PERSON>", "Tipo": "Permessi ex festività", "Data inizio": "16/05/2025 09:00:00", "Data fine": "16/05/2025 09:45:00", "Stato": "Da approvare", "Note": NaN}, {"Data della richiesta": "2025-05-16 12:36:37", "Dipendente": "Serratore, Lorenzo", "Tipo": "ROL", "Data inizio": "16/05/2025 09:00:00", "Data fine": "16/05/2025 10:30:00", "Stato": "Da approvare", "Note": NaN}, {"Data della richiesta": "2025-05-19 19:38:55", "Dipendente": "<PERSON><PERSON><PERSON>, <PERSON>", "Tipo": "Permessi ex festività", "Data inizio": "20/05/2025 09:00:00", "Data fine": "20/05/2025 10:30:00", "Stato": "Da approvare", "Note": NaN}, {"Data della richiesta": "2025-05-19 19:40:39", "Dipendente": "<PERSON><PERSON><PERSON>, <PERSON>", "Tipo": "Permessi ex festività", "Data inizio": "27/05/2025 14:00:00", "Data fine": "27/05/2025 18:00:00", "Stato": "Da approvare", "Note": NaN}, {"Data della richiesta": "2025-05-22 10:08:46", "Dipendente": "<PERSON><PERSON><PERSON>, Franco", "Tipo": "<PERSON><PERSON>", "Data inizio": "22/05/2025", "Data fine": "22/05/2025", "Stato": "Da approvare", "Note": NaN}, {"Data della richiesta": "2025-05-22 16:32:24", "Dipendente": "<PERSON><PERSON><PERSON>, <PERSON>", "Tipo": "Permessi ex festività", "Data inizio": "22/05/2025 16:00:00", "Data fine": "22/05/2025 16:30:00", "Stato": "Da approvare", "Note": "visita medica"}]