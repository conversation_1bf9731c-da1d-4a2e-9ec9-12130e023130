#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test delle nuove funzionalità implementate nelle Fasi 6-7.
Verifica che tutte le nuove funzionalità siano operative.
"""

import sys
import os
import requests
import json
import time
from datetime import datetime

# Aggiungi il percorso del progetto
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_app_running():
    """Testa se l'app principale è in esecuzione."""
    try:
        response = requests.get('http://localhost:5000', timeout=5)
        return response.status_code == 200
    except:
        return False

def test_agents_dashboard():
    """Testa la dashboard degli agenti."""
    try:
        response = requests.get('http://localhost:5000/agents-dashboard', timeout=5)
        return response.status_code == 200
    except:
        return False

def test_monitoring_endpoints():
    """Testa gli endpoint di monitoring."""
    endpoints = [
        '/monitoring/health',
        '/monitoring/metrics',
        '/monitoring/status',
        '/monitoring/alerts'
    ]
    
    results = {}
    for endpoint in endpoints:
        try:
            response = requests.get(f'http://localhost:5000{endpoint}', timeout=5)
            results[endpoint] = {
                'status_code': response.status_code,
                'success': response.status_code == 200,
                'response_time': response.elapsed.total_seconds()
            }
        except Exception as e:
            results[endpoint] = {
                'success': False,
                'error': str(e)
            }
    
    return results

def test_agents_api():
    """Testa le API degli agenti."""
    endpoints = [
        '/api/agents/list',
        '/api/agents/health',
        '/api/agents/data-cleaning/capabilities',
        '/api/agents/export/formats'
    ]
    
    results = {}
    for endpoint in endpoints:
        try:
            response = requests.get(f'http://localhost:5000{endpoint}', timeout=5)
            results[endpoint] = {
                'status_code': response.status_code,
                'success': response.status_code == 200,
                'response_time': response.elapsed.total_seconds()
            }
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    results[endpoint]['data'] = data
                except:
                    pass
                    
        except Exception as e:
            results[endpoint] = {
                'success': False,
                'error': str(e)
            }
    
    return results

def test_advanced_agent_framework():
    """Testa il framework agenti avanzato."""
    try:
        from advanced_agent_framework import get_advanced_orchestrator
        
        orchestrator = get_advanced_orchestrator()
        
        return {
            'success': True,
            'agents_count': len(orchestrator.agents),
            'agents_list': list(orchestrator.agents.keys()),
            'queue_size': len(orchestrator.task_queue),
            'running_tasks': len(orchestrator.running_tasks),
            'completed_tasks': len(orchestrator.completed_tasks)
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def test_production_monitoring():
    """Testa il sistema di monitoring produzione."""
    try:
        from production_monitoring import production_monitoring
        
        # Test raccolta metriche
        import asyncio
        metrics = asyncio.run(production_monitoring.collect_system_metrics())
        
        # Test health check
        health = asyncio.run(production_monitoring.health_check())
        
        return {
            'success': True,
            'metrics_collected': 'timestamp' in metrics,
            'health_check': health.get('overall_status', 'unknown'),
            'system_metrics': 'system' in metrics,
            'application_metrics': 'application' in metrics
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def run_comprehensive_test():
    """Esegue test completo delle nuove funzionalità."""
    print("🧪 AVVIO TEST NUOVE FUNZIONALITÀ")
    print("=" * 50)
    
    results = {}
    
    # Test 1: App principale
    print("\n1. 🌐 Test App Principale...")
    results['app_running'] = test_app_running()
    print(f"   Risultato: {'✅ OK' if results['app_running'] else '❌ FAIL'}")
    
    # Test 2: Dashboard Agenti
    print("\n2. 🤖 Test Dashboard Agenti...")
    results['agents_dashboard'] = test_agents_dashboard()
    print(f"   Risultato: {'✅ OK' if results['agents_dashboard'] else '❌ FAIL'}")
    
    # Test 3: Endpoint Monitoring
    print("\n3. 📊 Test Endpoint Monitoring...")
    monitoring_results = test_monitoring_endpoints()
    results['monitoring_endpoints'] = monitoring_results
    
    success_count = sum(1 for r in monitoring_results.values() if r.get('success', False))
    total_count = len(monitoring_results)
    print(f"   Risultato: {success_count}/{total_count} endpoint OK")
    
    for endpoint, result in monitoring_results.items():
        status = "✅" if result.get('success', False) else "❌"
        print(f"   {status} {endpoint}")
    
    # Test 4: API Agenti
    print("\n4. 🔧 Test API Agenti...")
    agents_results = test_agents_api()
    results['agents_api'] = agents_results
    
    success_count = sum(1 for r in agents_results.values() if r.get('success', False))
    total_count = len(agents_results)
    print(f"   Risultato: {success_count}/{total_count} API OK")
    
    for endpoint, result in agents_results.items():
        status = "✅" if result.get('success', False) else "❌"
        print(f"   {status} {endpoint}")
    
    # Test 5: Advanced Agent Framework
    print("\n5. 🚀 Test Advanced Agent Framework...")
    framework_result = test_advanced_agent_framework()
    results['advanced_framework'] = framework_result
    
    if framework_result.get('success', False):
        print(f"   ✅ Framework OK - {framework_result['agents_count']} agenti")
        print(f"   📋 Agenti: {', '.join(framework_result['agents_list'])}")
    else:
        print(f"   ❌ Framework FAIL: {framework_result.get('error', 'Unknown')}")
    
    # Test 6: Production Monitoring
    print("\n6. 📈 Test Production Monitoring...")
    monitoring_result = test_production_monitoring()
    results['production_monitoring'] = monitoring_result
    
    if monitoring_result.get('success', False):
        print(f"   ✅ Monitoring OK - Status: {monitoring_result['health_check']}")
        print(f"   📊 Metriche: {'✅' if monitoring_result['metrics_collected'] else '❌'}")
    else:
        print(f"   ❌ Monitoring FAIL: {monitoring_result.get('error', 'Unknown')}")
    
    # Riepilogo finale
    print("\n" + "=" * 50)
    print("📋 RIEPILOGO TEST")
    print("=" * 50)
    
    total_tests = 6
    passed_tests = 0
    
    test_names = [
        ("App Principale", results['app_running']),
        ("Dashboard Agenti", results['agents_dashboard']),
        ("Monitoring Endpoints", all(r.get('success', False) for r in monitoring_results.values())),
        ("API Agenti", all(r.get('success', False) for r in agents_results.values())),
        ("Advanced Framework", framework_result.get('success', False)),
        ("Production Monitoring", monitoring_result.get('success', False))
    ]
    
    for name, success in test_names:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {name}")
        if success:
            passed_tests += 1
    
    print(f"\n🎯 RISULTATO FINALE: {passed_tests}/{total_tests} test superati")
    
    if passed_tests == total_tests:
        print("🎉 TUTTI I TEST SUPERATI! Le nuove funzionalità sono operative!")
    elif passed_tests >= total_tests * 0.8:
        print("✅ La maggior parte dei test superati. Sistema funzionale.")
    else:
        print("⚠️ Alcuni test falliti. Verificare la configurazione.")
    
    # Salva risultati dettagliati
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f'test_results_{timestamp}.json'
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📄 Risultati dettagliati salvati in: {results_file}")
    
    return results

if __name__ == "__main__":
    # Aspetta che l'app si avvii
    print("⏳ Attendo che l'applicazione si avvii...")
    time.sleep(5)
    
    # Esegui test
    results = run_comprehensive_test()
    
    # Exit code basato sui risultati
    if results.get('app_running', False):
        sys.exit(0)
    else:
        sys.exit(1)
