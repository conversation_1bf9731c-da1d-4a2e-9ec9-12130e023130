#!/usr/bin/env python3
"""
Test per verificare la registrazione delle route
"""

import requests
import json

def test_route_registration():
    """Test registrazione route"""
    
    print("🔍 TEST REGISTRAZIONE ROUTE")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test 1: Verifica connettività
    print("1️⃣ Test connettività...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   Homepage: {response.status_code} ({'✅' if response.status_code == 200 else '❌'})")
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
        return
    
    # Test 2: Lista completa endpoint
    print("\n2️⃣ Lista completa endpoint...")
    try:
        response = requests.get(f"{base_url}/api/endpoints", timeout=5)
        if response.status_code == 200:
            data = response.json()
            endpoints = data.get('endpoints', [])
            print(f"   📋 Totale endpoint: {len(endpoints)}")
            
            # Filtra endpoint agenti
            agent_endpoints = [ep for ep in endpoints if 'agent' in ep.lower()]
            print(f"   🤖 Endpoint agenti: {len(agent_endpoints)}")
            for ep in agent_endpoints:
                print(f"      - {ep}")
            
            # Cerca endpoint specifici
            target_endpoints = [
                '/api/agents/status',
                '/api/agents/execute', 
                '/api/automation/rules'
            ]
            
            print(f"\n   🎯 Endpoint target:")
            for target in target_endpoints:
                found = any(target in ep for ep in endpoints)
                status = "✅" if found else "❌"
                print(f"      {status} {target}")
                
        else:
            print(f"   ❌ Errore lista endpoint: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    # Test 3: Test route specifiche con metodi diversi
    print("\n3️⃣ Test route specifiche...")
    
    test_routes = [
        ("GET", "/api/agents/status", "Status agenti"),
        ("POST", "/api/agents/execute", "Execute agenti"),
        ("GET", "/api/automation/rules", "Regole automazione"),
        ("GET", "/agents/list", "Lista agenti (blueprint)"),
        ("GET", "/agents/dashboard", "Dashboard agenti (blueprint)"),
    ]
    
    for method, path, description in test_routes:
        try:
            print(f"   Test {method} {path} ({description})...")
            
            if method == "GET":
                response = requests.get(f"{base_url}{path}", timeout=5)
            else:
                response = requests.post(
                    f"{base_url}{path}",
                    json={"test": True},
                    headers={"Content-Type": "application/json"},
                    timeout=5
                )
            
            print(f"      Status: {response.status_code}")
            
            if response.status_code == 200:
                print("      ✅ Route registrata e funzionante")
            elif response.status_code == 404:
                print("      ❌ Route NON registrata")
            elif response.status_code == 405:
                print("      ⚠️ Route registrata ma metodo non consentito")
            elif response.status_code == 503:
                print("      ⚠️ Route registrata ma servizio non disponibile")
                try:
                    data = response.json()
                    if 'error' in data:
                        print(f"         Motivo: {data['error']}")
                except:
                    pass
            else:
                print(f"      ⚠️ Status inaspettato: {response.status_code}")
                
        except Exception as e:
            print(f"      ❌ Errore: {str(e)}")
    
    # Test 4: Test con curl per debug avanzato
    print("\n4️⃣ Test debug avanzato...")
    
    import subprocess
    
    try:
        # Test con curl per vedere headers completi
        result = subprocess.run([
            'curl', '-I', '-s', f'{base_url}/api/agents/status'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("   📄 Headers risposta curl:")
            for line in result.stdout.split('\n'):
                if line.strip():
                    print(f"      {line}")
        else:
            print("   ❌ Curl non disponibile o errore")
            
    except Exception as e:
        print(f"   ⚠️ Test curl fallito: {str(e)}")
    
    print("\n" + "=" * 60)
    print("📊 DIAGNOSI")
    print("=" * 60)
    print("Se le route /api/agents/* NON appaiono nella lista endpoint:")
    print("- Le route non sono registrate a causa di errori durante l'avvio")
    print("- Possibile errore di importazione o sintassi che interrompe l'esecuzione")
    print("- Le route sono definite dopo un punto di fallimento nell'app.py")
    print()
    print("Se le route appaiono nella lista ma restituiscono 404:")
    print("- Problema di routing Flask o conflitto con blueprint")
    print("- Possibile problema di registrazione tardiva")
    
    return True

if __name__ == "__main__":
    test_route_registration()
