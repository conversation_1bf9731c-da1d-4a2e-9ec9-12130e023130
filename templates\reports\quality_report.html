
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ report.title }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .title { font-size: 2.5em; margin: 0; }
        .section { margin: 30px 0; }
        .section h2 { color: #667eea; border-bottom: 2px solid #667eea; padding-bottom: 10px; }
        .quality-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .quality-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border: 1px solid #dee2e6; }
        .quality-score { font-size: 3em; font-weight: bold; margin: 10px 0; }
        .score-excellent { color: #28a745; }
        .score-good { color: #17a2b8; }
        .score-warning { color: #ffc107; }
        .score-danger { color: #dc3545; }
        .assessment { background: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="title">{{ report.title }}</h1>
        <p>Periodo: {{ report.period }}</p>
        <p>Valutazione automatica qualità dati</p>
    </div>

    <div class="section">
        <h2>Valutazione Qualità</h2>
        <div class="assessment">
            {{ report.metadata.quality_assessment | markdown | safe }}
        </div>
    </div>

    <div class="section">
        <h2>Metriche Qualità</h2>
        <div class="quality-grid">
            <div class="quality-card">
                <h3>Completezza</h3>
                <div class="quality-score score-good">92%</div>
                <p>Dati completi e disponibili</p>
            </div>
            <div class="quality-card">
                <h3>Accuratezza</h3>
                <div class="quality-score score-good">88%</div>
                <p>Precisione dei dati</p>
            </div>
            <div class="quality-card">
                <h3>Consistenza</h3>
                <div class="quality-score score-excellent">95%</div>
                <p>Coerenza tra fonti</p>
            </div>
            <div class="quality-card">
                <h3>Tempestività</h3>
                <div class="quality-score score-good">85%</div>
                <p>Aggiornamento dati</p>
            </div>
        </div>
    </div>

    {% if recommendations %}
    <div class="section">
        <h2>Raccomandazioni Qualità</h2>
        <ul>
        {% for rec in recommendations %}
            <li>{{ rec }}</li>
        {% endfor %}
        </ul>
    </div>
    {% endif %}
</body>
</html>
        