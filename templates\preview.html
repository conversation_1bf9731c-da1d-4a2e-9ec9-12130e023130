{% extends "base.html" %}

{% block title %}Anteprima Dati - <PERSON><PERSON>i <PERSON>{% endblock %}

{% block content %}
<!-- Parametro anti-cache: {{ session.get('upload_timestamp', '') }} -->
<!-- ID sessione: {{ session.get('session_id', '') }} -->
<!-- Tipo file: {{ file_type }} -->
<div class="card shadow">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h4 class="mb-0"><i class="fas fa-table me-2"></i>Anteprima Dati</h4>
        <div>
            <span class="badge bg-light text-dark me-2">{{ filename }}</span>
            <span class="badge {% if file_type == 'unknown' or file_type == 'generico' %}bg-warning{% else %}bg-success{% endif %} text-white">
                <i class="fas {% if file_type == 'attivita' %}fa-tasks{% elif 'teamviewer' in file_type %}fa-desktop{% elif file_type == 'calendario' %}fa-calendar{% elif file_type == 'timbrature' %}fa-clock{% elif file_type == 'permessi' %}fa-calendar-check{% else %}fa-file{% endif %} me-1"></i>
                Tipo: {{ file_type }}
            </span>
        </div>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            Visualizzazione dei primi 100 record. Utilizza i controlli sotto per elaborare i dati.
        </div>

        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        {% for column in columns %}
                        <th>{{ column }}</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for row in preview_data %}
                    <tr>
                        {% for column in columns %}
                        <td>{{ row[column] }}</td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer">
        <form action="{{ url_for('process_data', t=session.get('upload_timestamp', '')) }}" method="post" class="d-flex justify-content-between">
            <div>
                <a href="{{ url_for('index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Torna all'Upload
                </a>
                <a href="{{ url_for('clear_session_route') }}" class="btn btn-outline-danger ms-2">
                    <i class="fas fa-broom me-1"></i>Pulisci sessione
                </a>
            </div>
            <button type="submit" class="btn btn-success">
                <i class="fas fa-cogs me-2"></i>Elabora Dati
            </button>
        </form>
    </div>
</div>

<div class="card shadow mt-4">
    <div class="card-header bg-info text-white">
        <h4 class="mb-0"><i class="fas fa-sliders-h me-2"></i>Opzioni di Elaborazione</h4>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="mb-0">Formato Data</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="date_format" id="date_format_it" value="it" checked>
                            <label class="form-check-label" for="date_format_it">
                                Italiano (GG/MM/AAAA)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="date_format" id="date_format_en" value="en">
                            <label class="form-check-label" for="date_format_en">
                                Inglese (MM/DD/YYYY)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="date_format" id="date_format_iso" value="iso">
                            <label class="form-check-label" for="date_format_iso">
                                ISO (YYYY-MM-DD)
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="mb-0">Separatore Decimale</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="decimal_sep" id="decimal_sep_comma" value="comma" checked>
                            <label class="form-check-label" for="decimal_sep_comma">
                                Virgola (,)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="decimal_sep" id="decimal_sep_dot" value="dot">
                            <label class="form-check-label" for="decimal_sep_dot">
                                Punto (.)
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Mappatura Campi</h5>
            </div>
            <div class="card-body">
                <p>Seleziona i campi da standardizzare:</p>
                <div class="row">
                    {% for column in columns %}
                    <div class="col-md-4 mb-2">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="map_fields" id="field_{{ loop.index }}" value="{{ column }}">
                            <label class="form-check-label" for="field_{{ loop.index }}">
                                {{ column }}
                            </label>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
