/**
 * Raw Data CSS - App Roberto
 * <PERSON>ili per la visualizzazione dati grezzi
 * Versione: 1.0.0
 */

/* ===== TABELLE SORTABLE ===== */
.sortable {
    cursor: pointer;
}

.sort-icon {
    display: inline-block;
    width: 1em;
}

.sort-asc .sort-icon i:before {
    content: "\f0de"; /* fa-sort-up */
}

.sort-desc .sort-icon i:before {
    content: "\f0dd"; /* fa-sort-down */
}

/* ===== TABELLE RESPONSIVE ===== */
.table-responsive {
    max-height: 600px;
}

#data-table th {
    position: sticky;
    top: 0;
    z-index: 10;
}

/* ===== CONTROLLI PAGINAZIONE ===== */
.page-size-select {
    width: auto;
}

/* ===== BOTTONI EXPORT ===== */
.export-buttons .dropdown-item:hover {
    background-color: #f8f9fa;
}

.export-buttons .dropdown-item:active {
    background-color: #e9ecef;
    color: #212529;
}

/* ===== STILI ANOMALIE ===== */
.anomaly {
    position: relative;
}

.anomaly-missing {
    background-color: rgba(255, 0, 0, 0.1) !important;
}

.anomaly-format {
    background-color: rgba(255, 165, 0, 0.1) !important;
}

.anomaly-duplicate {
    background-color: rgba(255, 255, 0, 0.1) !important;
}

.anomaly-outlier {
    background-color: rgba(128, 0, 128, 0.1) !important;
}

/* ===== TOOLTIP ANOMALIE ===== */
.anomaly-tooltip {
    position: absolute;
    background: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    z-index: 1000;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s;
}

.anomaly:hover .anomaly-tooltip {
    opacity: 1;
}

/* ===== FILTRI ===== */
.filter-controls {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.filter-group {
    margin-bottom: 0.5rem;
}

/* ===== STATISTICHE TABELLA ===== */
.table-stats {
    background-color: #e9ecef;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

/* ===== TEMA SCURO ===== */
[data-theme="dark"] .table-responsive {
    background-color: var(--table-bg);
}

[data-theme="dark"] #data-table th {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] #data-table td {
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .export-buttons .dropdown-item:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

[data-theme="dark"] .export-buttons .dropdown-item:active {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

[data-theme="dark"] .anomaly-missing {
    background-color: rgba(255, 107, 107, 0.2) !important;
}

[data-theme="dark"] .anomaly-format {
    background-color: rgba(255, 212, 59, 0.2) !important;
}

[data-theme="dark"] .anomaly-duplicate {
    background-color: rgba(255, 255, 0, 0.2) !important;
}

[data-theme="dark"] .anomaly-outlier {
    background-color: rgba(151, 117, 250, 0.2) !important;
}

[data-theme="dark"] .filter-controls {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="dark"] .table-stats {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .table-responsive {
        max-height: 400px;
    }
    
    .filter-controls {
        padding: 0.5rem;
    }
    
    .export-buttons {
        margin-bottom: 1rem;
    }
}
