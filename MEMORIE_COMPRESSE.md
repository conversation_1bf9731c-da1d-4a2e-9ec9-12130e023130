# 🧠 MEMORIE COMPRESSE - APP ROBERTO

## Sistema di backup delle informazioni chiave per evitare perdita di contesto

---

## 📊 INFORMAZIONI PROGETTO ESSENZIALI

### **Repository e Configurazione**

- **GitHub**: <https://github.com/Fiore0312/app-roberto.git>
- **Branch**: main
- **Ultimo commit**: 53f7a9f (Fix MCP Server - Model Context Protocol)
- **Directory**: C:\Users\<USER>\Documents\app-roberto

### **Avvio Sistema**
- **Script principale**: `avvio_completo.bat`
- **Comando alternativo**: `python app.py` (solo app principale)
- **MCP Server**: `cd mcp_server && python main.py`

---

## 🎯 STATO SVILUPPO

### **COMPLETATO ✅**
- Sistema di Riconoscimento Intelligente (7 fasi complete)
- 4 agenti AI specializzati operativi
- Dashboard intelligente funzionante
- Chat AI con OpenRouter
- Server MCP (Model Context Protocol)
- Performance 400-500% superiori al previsto

### **IN REVISIONE ⚠️**
- API keys Supabase (errore connessione database)
- Persistenza file (cancellazione automatica)
- Inizializzazione sistema intelligente
- Allineamento frontend-backend

---

## 🔧 PREFERENZE UTENTE

### **Sviluppo**
- **Lingua**: Sempre italiano per comunicazioni
- **PowerShell**: Usare `;` invece di `&&` per concatenare comandi
- **Commit obbligatori**: Alla fine di ogni fase completata
- **Context 7**: Sempre consultare per best practices e librerie aggiornate
- **Test coverage**: 100% richiesto prima di procedere

### **Architettura**
- **Database**: Supabase per persistenza
- **Package managers**: Sempre usare invece di editing manuale
- **Modifiche incrementali**: Max 500 righe per blocco
- **Approccio iterativo**: Suddividere task complessi

### **Qualità**
- **Zero compromessi**: Qualità al 100%
- **Tema scuro**: Richiesto per UI
- **Configurazione guidata**: Setup intelligente desiderato
- **CPU optimization**: Ridurre utilizzo CPU alto

---

## 🚀 FUNZIONALITÀ CHIAVE OPERATIVE

### **File Recognition System**
- 7 tipi file supportati (attività, timbrature, teamviewer, calendario, registro auto, permessi, progetti)
- Confidenza 67-100%
- Processing <5s per file tipico

### **Agenti AI**
- Data Cleaning Agent (4 capacità)
- Export Management Agent (5 formati)
- Entity Resolution Agent (fuzzy matching)
- Configuration Agent (ottimizzazione automatica)

### **Dashboard e API**
- Dashboard principale, intelligente, agenti
- 20+ endpoints REST operativi
- Health monitoring (5 endpoint)
- Real-time metrics

---

## ⚙️ CONFIGURAZIONI TECNICHE

### **Environment Variables**

```env
OPENROUTER_API_KEY=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
SUPABASE_URL=https://zqjllwxqjxjhdkbcawfr.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... (da verificare)
```

### **Dipendenze Principali**
- Flask 2.2.5, Pandas 2.2.3, FastAPI 0.104.1
- Plotly, Supabase, OpenRouter integration
- Python 3.13, Environment: clean_env

### **Porte Servizi**
- Flask App: 5000
- MCP Server: 8000

---

## 📋 PIANO ATTUALE

**Seguendo**: PIANO_REVISIONE_OPERATIVITA.md
**Fase corrente**: Revisione operativa per problemi residuali
**Prossimo**: Fase 1 - Configurazione Database Persistente

### **Problemi da Risolvere**
1. API keys Supabase (CRITICO)
2. Pulizia file automatica (CRITICO)
3. Sistema intelligente non inizializzato (ALTA)
4. Frontend-backend misalignment (ALTA)

---

## 🎯 WORKFLOW STANDARD

1. **Analisi dettagliata** problema
2. **Context 7** per best practices
3. **Piano implementazione** specifico
4. **Sviluppo** con test
5. **Test completi** (100%)
6. **Commit & Push** obbligatorio
7. **Verifica** prima di procedere

---

**📝 Nota**: Questo file sostituisce le memorie troppo grandi di Augment e mantiene il contesto essenziale per continuare il lavoro efficacemente.
