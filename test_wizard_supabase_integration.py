#!/usr/bin/env python3
"""
Test completo per l'integrazione Supabase del Setup Wizard
Verifica che il pulsante "Vai alla Dashboard" funzioni correttamente
"""

import requests
import json
import sys
import time

def test_wizard_complete_endpoint():
    """Testa l'endpoint di completamento wizard con integrazione Supabase"""
    
    print("🧪 TEST INTEGRAZIONE SUPABASE SETUP WIZARD")
    print("=" * 50)
    
    url = "http://localhost:5000/api/wizard/complete"
    
    # Dati di test realistici per il wizard
    test_data = {
        "files": [
            {
                "name": "timbrature_test.xlsx",
                "size": 15000,
                "type": "timbrature"
            }
        ],
        "configuration": {
            "timezone": "Europe/Rome",
            "currency": "EUR",
            "dateFormat": "DD/MM/YYYY"
        },
        "employees": [
            "<PERSON> Rossi",
            "Luigi Verdi",
            "Anna Bianchi"
        ],
        "vehicles": [
            "Fiat Punto",
            "Peugeot 208"
        ],
        "analysis": [
            {
                "name": "Analisi Ore Lavorate",
                "description": "Monitora le ore lavorate per dipendente"
            }
        ],
        "automations": [
            {
                "name": "Report Ore Settimanali",
                "description": "Genera automaticamente report settimanali"
            }
        ],
        "timestamp": "2025-05-26T21:00:00"
    }
    
    try:
        print(f"📤 Invio richiesta a: {url}")
        print(f"📊 Dati test:")
        print(f"   - File: {len(test_data['files'])}")
        print(f"   - Dipendenti: {len(test_data['employees'])}")
        print(f"   - Veicoli: {len(test_data['vehicles'])}")
        print(f"   - Analisi: {len(test_data['analysis'])}")
        print(f"   - Automazioni: {len(test_data['automations'])}")
        print()
        
        # Invia richiesta
        response = requests.post(
            url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📥 Risposta ricevuta:")
        print(f"   Status Code: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        print()
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("✅ RISPOSTA JSON VALIDA")
                print(f"   Success: {result.get('success', 'N/A')}")
                print(f"   Message: {result.get('message', 'N/A')}")
                print()
                
                # Verifica struttura dati
                if 'data' in result:
                    data = result['data']
                    print("📊 DATI RISPOSTA:")
                    print(f"   Redirect URL: {data.get('redirect', 'N/A')}")
                    print(f"   Wizard Completed: {data.get('wizard_completed', 'N/A')}")
                    print(f"   Files Processed: {data.get('files_processed', 'N/A')}")
                    print(f"   Employees Configured: {data.get('employees_configured', 'N/A')}")
                    print(f"   Vehicles Configured: {data.get('vehicles_configured', 'N/A')}")
                    print(f"   Supabase Integration: {data.get('supabase_integration', 'N/A')}")
                    print(f"   User ID: {data.get('user_id', 'N/A')}")
                    print()
                    
                    # Verifica dettagli Supabase
                    if 'supabase_details' in data:
                        details = data['supabase_details']
                        print("🗄️ DETTAGLI SUPABASE:")
                        print(f"   Onboarding: {details.get('onboarding', 'N/A')}")
                        print(f"   Employees: {details.get('employees', 'N/A')}")
                        print(f"   Vehicles: {details.get('vehicles', 'N/A')}")
                        print(f"   Files: {details.get('files', 'N/A')}")
                        print()
                
                # Verifica che la risposta sia compatibile con il frontend
                if result.get('success') and 'data' in result and 'redirect' in result['data']:
                    print("🎉 TEST SUPERATO!")
                    print("   ✅ Success = True")
                    print("   ✅ Redirect URL presente")
                    print("   ✅ Struttura dati corretta")
                    print("   ✅ Compatibile con frontend JavaScript")
                    
                    if result['data'].get('supabase_integration'):
                        print("   ✅ Integrazione Supabase attiva")
                    else:
                        print("   ⚠️ Integrazione Supabase non attiva (fallback locale)")
                    
                    return True
                else:
                    print("❌ TEST FALLITO!")
                    print("   Struttura risposta non compatibile con frontend")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ ERRORE PARSING JSON: {str(e)}")
                print(f"   Contenuto risposta: {response.text[:500]}...")
                return False
                
        else:
            print(f"❌ ERRORE HTTP: {response.status_code}")
            print(f"   Contenuto: {response.text[:500]}...")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ TIMEOUT: Il server ha impiegato troppo tempo a rispondere")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ ERRORE CONNESSIONE: Impossibile connettersi al server")
        print("   Assicurati che l'applicazione sia in esecuzione su http://localhost:5000")
        return False
    except Exception as e:
        print(f"❌ ERRORE GENERICO: {str(e)}")
        return False

def test_wizard_frontend_simulation():
    """Simula il comportamento del frontend JavaScript"""
    
    print("\n" + "=" * 50)
    print("🎭 SIMULAZIONE FRONTEND JAVASCRIPT")
    print("=" * 50)
    
    # Simula i dati che il frontend invierebbe
    frontend_data = {
        "files": [],
        "configuration": {
            "timezone": "Europe/Rome",
            "currency": "EUR",
            "dateFormat": "DD/MM/YYYY"
        },
        "employees": [],
        "vehicles": [],
        "analysis": [],
        "automations": [],
        "timestamp": "2025-05-26T21:00:00"
    }
    
    try:
        response = requests.post(
            "http://localhost:5000/api/wizard/complete",
            json=frontend_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            
            # Simula la logica JavaScript
            if result.get('success'):
                redirect_url = (result.get('data', {}).get('redirect') or 
                               result.get('redirect') or 
                               '/dashboard')
                
                print(f"✅ Frontend riceverà:")
                print(f"   Success: {result.get('success')}")
                print(f"   Message: {result.get('message', 'N/A')}")
                print(f"   Redirect URL: {redirect_url}")
                print(f"   Supabase Integration: {result.get('data', {}).get('supabase_integration', 'N/A')}")
                print()
                print("🔄 Frontend eseguirà:")
                print(f"   localStorage.setItem('wizard_completed', 'true')")
                print(f"   window.location.href = '{redirect_url}'")
                print()
                print("🎉 SIMULAZIONE FRONTEND SUPERATA!")
                return True
            else:
                print("❌ Frontend riceverà success=false")
                return False
        else:
            print(f"❌ Frontend riceverà errore HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Errore simulazione frontend: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 AVVIO TEST COMPLETO SETUP WIZARD")
    print("Verifica che l'applicazione sia in esecuzione su http://localhost:5000")
    print()
    
    # Attendi che l'applicazione sia pronta
    time.sleep(2)
    
    # Esegui i test
    test1_passed = test_wizard_complete_endpoint()
    test2_passed = test_wizard_frontend_simulation()
    
    print("\n" + "=" * 50)
    print("📊 RISULTATI FINALI")
    print("=" * 50)
    print(f"Test Endpoint Supabase: {'✅ SUPERATO' if test1_passed else '❌ FALLITO'}")
    print(f"Test Simulazione Frontend: {'✅ SUPERATO' if test2_passed else '❌ FALLITO'}")
    print()
    
    if test1_passed and test2_passed:
        print("🎉 TUTTI I TEST SUPERATI!")
        print("Il pulsante 'Vai alla Dashboard' dovrebbe funzionare correttamente.")
        sys.exit(0)
    else:
        print("❌ ALCUNI TEST FALLITI!")
        print("Il pulsante 'Vai alla Dashboard' potrebbe non funzionare.")
        sys.exit(1)
