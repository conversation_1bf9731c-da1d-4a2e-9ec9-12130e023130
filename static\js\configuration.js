/**
 * Script per la gestione della pagina di configurazione
 * Versione: 1.0.0 - Gestione dipendenti, veicoli e configurazioni
 */

console.log('Configuration.js versione 1.0.0 caricato');

// Inizializzazione al caricamento della pagina
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inizializzazione pagina configurazione...');

    // Carica i dati iniziali
    loadEmployees();
    loadVehicles();
    loadAutomationRules();

    // Setup event listeners
    setupEventListeners();

    // Setup tab change listener per Sistema API - FASE 4
    setupSystemTabListener();
});

/**
 * Setup degli event listeners
 */
function setupEventListeners() {
    // Form configurazione fiscale
    const taxForm = document.getElementById('tax-config-form');
    if (taxForm) {
        taxForm.addEventListener('submit', handleTaxConfigSubmit);
    }

    // Form aggiunta dipendente
    const employeeForm = document.getElementById('add-employee-form');
    if (employeeForm) {
        employeeForm.addEventListener('submit', function(e) {
            e.preventDefault();
            addEmployee();
        });
    }

    // Form aggiunta veicolo
    const vehicleForm = document.getElementById('add-vehicle-form');
    if (vehicleForm) {
        vehicleForm.addEventListener('submit', function(e) {
            e.preventDefault();
            addVehicle();
        });
    }
}

/**
 * Carica la lista dei dipendenti
 */
async function loadEmployees() {
    const container = document.getElementById('employees-list');
    if (!container) return;

    try {
        console.log('Caricamento dipendenti...');
        const response = await fetch('/api/config/employees');
        const data = await response.json();

        if (data.success && data.data) {
            renderEmployeesList(data.data);
        } else {
            container.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Nessun dipendente configurato. Aggiungi il primo dipendente per iniziare.
                </div>
            `;
        }
    } catch (error) {
        console.error('Errore nel caricamento dipendenti:', error);
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Errore nel caricamento dei dipendenti: ${error.message}
            </div>
        `;
    }
}

/**
 * Renderizza la lista dei dipendenti
 */
function renderEmployeesList(employees) {
    const container = document.getElementById('employees-list');
    if (!container) return;

    if (!employees || employees.length === 0) {
        container.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                Nessun dipendente configurato.
            </div>
        `;
        return;
    }

    const html = `
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Nome</th>
                        <th>Ruolo</th>
                        <th>Tariffa Oraria</th>
                        <th>Azioni</th>
                    </tr>
                </thead>
                <tbody>
                    ${employees.map(employee => `
                        <tr>
                            <td>
                                <i class="fas fa-user me-2 text-primary"></i>
                                ${employee.name || 'N/A'}
                            </td>
                            <td>${employee.role || 'N/A'}</td>
                            <td>€${employee.hourly_rate || '0.00'}</td>
                            <td>
                                <button class="btn btn-sm btn-outline-danger" onclick="removeEmployee('${employee.name}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = html;
}

/**
 * Carica la lista dei veicoli
 */
async function loadVehicles() {
    const container = document.getElementById('vehicles-list');
    if (!container) return;

    try {
        console.log('Caricamento veicoli...');
        const response = await fetch('/api/config/vehicles');
        const data = await response.json();

        if (data.success && data.data) {
            renderVehiclesList(data.data);
        } else {
            container.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Nessun veicolo configurato. Aggiungi il primo veicolo per iniziare.
                </div>
            `;
        }
    } catch (error) {
        console.error('Errore nel caricamento veicoli:', error);
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Errore nel caricamento dei veicoli: ${error.message}
            </div>
        `;
    }
}

/**
 * Renderizza la lista dei veicoli
 */
function renderVehiclesList(vehicles) {
    const container = document.getElementById('vehicles-list');
    if (!container) return;

    if (!vehicles || vehicles.length === 0) {
        container.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                Nessun veicolo configurato.
            </div>
        `;
        return;
    }

    const html = `
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Targa</th>
                        <th>Modello</th>
                        <th>Costo per Km</th>
                        <th>Azioni</th>
                    </tr>
                </thead>
                <tbody>
                    ${vehicles.map(vehicle => `
                        <tr>
                            <td>
                                <i class="fas fa-car me-2 text-primary"></i>
                                ${vehicle.plate || 'N/A'}
                            </td>
                            <td>${vehicle.model || 'N/A'}</td>
                            <td>€${vehicle.cost_per_km || '0.00'}</td>
                            <td>
                                <button class="btn btn-sm btn-outline-danger" onclick="removeVehicle('${vehicle.plate}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = html;
}

/**
 * Carica le regole di automazione
 */
async function loadAutomationRules() {
    const container = document.getElementById('automation-rules');
    if (!container) return;

    try {
        console.log('Caricamento regole automazione...');
        const response = await fetch('/api/automation/rules');
        const data = await response.json();

        if (data.success && data.data) {
            renderAutomationRules(data.data);
        } else {
            container.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Nessuna regola di automazione configurata.
                </div>
            `;
        }
    } catch (error) {
        console.error('Errore nel caricamento regole automazione:', error);
        container.innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Regole di automazione non disponibili al momento.
            </div>
        `;
    }
}

/**
 * Renderizza le regole di automazione
 */
function renderAutomationRules(rules) {
    const container = document.getElementById('automation-rules');
    if (!container) return;

    if (!rules || rules.length === 0) {
        container.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                Nessuna regola di automazione configurata.
            </div>
        `;
        return;
    }

    const html = `
        <div class="row">
            ${rules.map(rule => `
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-cog me-2"></i>
                                ${rule.name || 'Regola Senza Nome'}
                            </h6>
                            <p class="card-text text-muted">${rule.description || 'Nessuna descrizione'}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge ${rule.enabled ? 'bg-success' : 'bg-secondary'}">
                                    ${rule.enabled ? 'Attiva' : 'Disattiva'}
                                </span>
                                <button class="btn btn-sm btn-outline-primary" onclick="toggleRule('${rule.id}')">
                                    <i class="fas fa-toggle-${rule.enabled ? 'on' : 'off'}"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
    `;

    container.innerHTML = html;
}

/**
 * Mostra il modal per aggiungere un dipendente
 */
function showAddEmployeeModal() {
    const modal = new bootstrap.Modal(document.getElementById('addEmployeeModal'));
    modal.show();
}

/**
 * Mostra il modal per aggiungere un veicolo
 */
function showAddVehicleModal() {
    const modal = new bootstrap.Modal(document.getElementById('addVehicleModal'));
    modal.show();
}

/**
 * Aggiunge un nuovo dipendente
 */
async function addEmployee() {
    const name = document.getElementById('employee-name').value.trim();
    const role = document.getElementById('employee-role').value.trim();
    const hourlyRate = parseFloat(document.getElementById('employee-hourly-rate').value);

    if (!name || !role || isNaN(hourlyRate)) {
        alert('Tutti i campi sono obbligatori');
        return;
    }

    try {
        const response = await fetch('/api/config/employees', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name: name,
                role: role,
                hourly_rate: hourlyRate
            })
        });

        const data = await response.json();

        if (data.success) {
            // Chiudi il modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addEmployeeModal'));
            modal.hide();

            // Reset form
            document.getElementById('add-employee-form').reset();

            // Ricarica la lista
            loadEmployees();

            // Mostra messaggio di successo
            showToast('Dipendente aggiunto con successo', 'success');
        } else {
            alert('Errore nell\'aggiunta del dipendente: ' + (data.error || 'Errore sconosciuto'));
        }
    } catch (error) {
        console.error('Errore nell\'aggiunta del dipendente:', error);
        alert('Errore nell\'aggiunta del dipendente: ' + error.message);
    }
}

/**
 * Rimuove un dipendente
 */
async function removeEmployee(name) {
    if (!confirm(`Sei sicuro di voler rimuovere il dipendente "${name}"?`)) {
        return;
    }

    try {
        const response = await fetch(`/api/config/employees/${encodeURIComponent(name)}`, {
            method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
            loadEmployees();
            showToast('Dipendente rimosso con successo', 'success');
        } else {
            alert('Errore nella rimozione del dipendente: ' + (data.error || 'Errore sconosciuto'));
        }
    } catch (error) {
        console.error('Errore nella rimozione del dipendente:', error);
        alert('Errore nella rimozione del dipendente: ' + error.message);
    }
}

/**
 * Aggiunge un nuovo veicolo
 */
async function addVehicle() {
    const plate = document.getElementById('vehicle-plate').value.trim();
    const model = document.getElementById('vehicle-model').value.trim();
    const costPerKm = parseFloat(document.getElementById('vehicle-cost-per-km').value);

    if (!plate || !model || isNaN(costPerKm)) {
        alert('Tutti i campi sono obbligatori');
        return;
    }

    try {
        const response = await fetch('/api/config/vehicles', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                plate: plate,
                model: model,
                cost_per_km: costPerKm
            })
        });

        const data = await response.json();

        if (data.success) {
            // Chiudi il modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addVehicleModal'));
            modal.hide();

            // Reset form
            document.getElementById('add-vehicle-form').reset();

            // Ricarica la lista
            loadVehicles();

            // Mostra messaggio di successo
            showToast('Veicolo aggiunto con successo', 'success');
        } else {
            alert('Errore nell\'aggiunta del veicolo: ' + (data.error || 'Errore sconosciuto'));
        }
    } catch (error) {
        console.error('Errore nell\'aggiunta del veicolo:', error);
        alert('Errore nell\'aggiunta del veicolo: ' + error.message);
    }
}

/**
 * Gestisce il submit della configurazione fiscale
 */
async function handleTaxConfigSubmit(event) {
    event.preventDefault();

    const vatRate = parseFloat(document.getElementById('vat-rate').value);
    const taxRate = parseFloat(document.getElementById('tax-rate').value);
    const socialSecurityRate = parseFloat(document.getElementById('social-security-rate').value);
    const overtimeMultiplier = parseFloat(document.getElementById('overtime-multiplier').value);

    try {
        const response = await fetch('/api/config/tax', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                vat_rate: vatRate,
                tax_rate: taxRate,
                social_security_rate: socialSecurityRate,
                overtime_multiplier: overtimeMultiplier
            })
        });

        const data = await response.json();

        if (data.success) {
            showToast('Configurazione fiscale salvata con successo', 'success');
        } else {
            alert('Errore nel salvataggio: ' + (data.error || 'Errore sconosciuto'));
        }
    } catch (error) {
        console.error('Errore nel salvataggio configurazione fiscale:', error);
        alert('Errore nel salvataggio: ' + error.message);
    }
}

/**
 * Esegue l'automazione
 */
async function triggerAutomation() {
    try {
        const response = await fetch('/api/automation/trigger', {
            method: 'POST'
        });

        const data = await response.json();

        if (data.success) {
            showToast('Automazione eseguita con successo', 'success');
        } else {
            alert('Errore nell\'esecuzione dell\'automazione: ' + (data.error || 'Errore sconosciuto'));
        }
    } catch (error) {
        console.error('Errore nell\'esecuzione dell\'automazione:', error);
        alert('Errore nell\'esecuzione dell\'automazione: ' + error.message);
    }
}

/**
 * Mostra un toast di notifica
 */
function showToast(message, type = 'info') {
    // Crea un toast dinamico
    const toastContainer = document.getElementById('toast-container') || createToastContainer();

    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'primary'} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement);
    toast.show();

    // Rimuovi il toast dopo che è stato nascosto
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

/**
 * Crea il container per i toast se non esiste
 */
function createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
    container.style.zIndex = '1055';
    document.body.appendChild(container);
    return container;
}

// ===== FUNZIONI SISTEMA API - FASE 4 ALLINEAMENTO FRONTEND-BACKEND =====

/**
 * Setup del listener per la tab Sistema API
 */
function setupSystemTabListener() {
    const systemTab = document.getElementById('system-tab');
    if (systemTab) {
        systemTab.addEventListener('shown.bs.tab', function() {
            console.log('🔧 Tab Sistema API attivata - caricamento dati...');
            loadSystemHealth();
            loadEndpointsMap();
        });
    }
}

/**
 * Carica lo stato di salute del sistema
 */
async function loadSystemHealth() {
    const container = document.getElementById('system-health');
    if (!container) return;

    try {
        console.log('🏥 Caricamento stato sistema...');
        const response = await fetch('/api/health');
        const result = await response.json();

        if (result.success) {
            renderSystemHealth(result.data);
        } else {
            renderSystemHealthError(result.error);
        }
    } catch (error) {
        console.error('❌ Errore caricamento stato sistema:', error);
        renderSystemHealthError(error.message);
    }
}

/**
 * Renderizza lo stato di salute del sistema
 */
function renderSystemHealth(healthData) {
    const container = document.getElementById('system-health');
    if (!container) return;

    const statusClass = healthData.status === 'healthy' ? 'success' :
                      healthData.status === 'degraded' ? 'warning' : 'danger';

    container.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-info-circle text-primary me-2"></i>Informazioni Generali</h6>
                <p><strong>Stato:</strong>
                    <span class="badge bg-${statusClass}">${healthData.status.toUpperCase()}</span>
                </p>
                <p><strong>Versione:</strong> ${healthData.version}</p>
                <p><strong>Endpoints Registrati:</strong> ${healthData.endpoints_registered}</p>
                <p><strong>Ultimo Controllo:</strong> ${new Date(healthData.timestamp).toLocaleString('it-IT')}</p>
            </div>
            <div class="col-md-6">
                <h6><i class="fas fa-server text-primary me-2"></i>Servizi</h6>
                ${Object.entries(healthData.services).map(([service, status]) => `
                    <p><strong>${formatServiceName(service)}:</strong>
                        <span class="badge bg-${status ? 'success' : 'danger'}">
                            ${status ? 'Attivo' : 'Non Disponibile'}
                        </span>
                    </p>
                `).join('')}
            </div>
        </div>
    `;
}

/**
 * Renderizza errore stato sistema
 */
function renderSystemHealthError(error) {
    const container = document.getElementById('system-health');
    if (!container) return;

    container.innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Errore nel caricamento dello stato sistema: ${error}
        </div>
    `;
}

/**
 * Carica la mappa degli endpoints
 */
async function loadEndpointsMap() {
    const container = document.getElementById('endpoints-map');
    if (!container) return;

    try {
        console.log('🗺️ Caricamento mappa endpoints...');
        const response = await fetch('/api/endpoints');
        const result = await response.json();

        if (result.success) {
            renderEndpointsMap(result.data);
        } else {
            renderEndpointsMapError(result.error);
        }
    } catch (error) {
        console.error('❌ Errore caricamento mappa endpoints:', error);
        renderEndpointsMapError(error.message);
    }
}

/**
 * Renderizza la mappa degli endpoints
 */
function renderEndpointsMap(endpointsData) {
    const container = document.getElementById('endpoints-map');
    if (!container) return;

    const endpoints = endpointsData.endpoints;

    // Raggruppa per tag
    const groupedEndpoints = {};
    Object.values(endpoints).forEach(endpoint => {
        const primaryTag = endpoint.tags[0] || 'general';
        if (!groupedEndpoints[primaryTag]) {
            groupedEndpoints[primaryTag] = [];
        }
        groupedEndpoints[primaryTag].push(endpoint);
    });

    container.innerHTML = `
        <div class="row mb-3">
            <div class="col-12">
                <h6><i class="fas fa-chart-bar text-primary me-2"></i>Statistiche</h6>
                <p><strong>Totale Endpoints:</strong> ${endpointsData.total_endpoints}</p>
                <p><strong>Generato il:</strong> ${new Date(endpointsData.generated_at).toLocaleString('it-IT')}</p>
            </div>
        </div>
        <div class="row">
            ${Object.entries(groupedEndpoints).map(([tag, endpoints]) => `
                <div class="col-md-6 mb-4">
                    <h6><i class="fas fa-tag text-primary me-2"></i>${formatTagName(tag)}</h6>
                    ${endpoints.map(endpoint => `
                        <div class="card mb-2">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <span class="badge bg-${getMethodColor(endpoint.method)} me-2">${endpoint.method}</span>
                                        <strong>${endpoint.path}</strong>
                                    </div>
                                </div>
                                <p class="mb-1 text-muted small">${endpoint.description}</p>
                                <div>
                                    ${endpoint.tags.map(tag => `<span class="badge bg-light text-dark me-1">${tag}</span>`).join('')}
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `).join('')}
        </div>
    `;
}

/**
 * Renderizza errore mappa endpoints
 */
function renderEndpointsMapError(error) {
    const container = document.getElementById('endpoints-map');
    if (!container) return;

    container.innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Errore nel caricamento della mappa endpoints: ${error}
        </div>
    `;
}

/**
 * Formatta il nome del servizio
 */
function formatServiceName(serviceName) {
    const names = {
        'api_standardization': 'Standardizzazione API',
        'onboarding_system': 'Sistema Onboarding',
        'persistence_manager': 'Gestione Persistenza',
        'database_manager': 'Gestione Database'
    };
    return names[serviceName] || serviceName;
}

/**
 * Formatta il nome del tag
 */
function formatTagName(tag) {
    const names = {
        'onboarding': 'Onboarding',
        'persistence': 'Persistenza',
        'data': 'Dati',
        'ai': 'Intelligenza Artificiale',
        'chat': 'Chat',
        'upload': 'Upload',
        'wizard': 'Setup Wizard',
        'config': 'Configurazione',
        'automation': 'Automazione',
        'database': 'Database',
        'system': 'Sistema'
    };
    return names[tag] || tag.charAt(0).toUpperCase() + tag.slice(1);
}

/**
 * Ottiene il colore per il metodo HTTP
 */
function getMethodColor(method) {
    const colors = {
        'GET': 'success',
        'POST': 'primary',
        'PUT': 'warning',
        'DELETE': 'danger',
        'PATCH': 'secondary'
    };
    return colors[method] || 'secondary';
}
