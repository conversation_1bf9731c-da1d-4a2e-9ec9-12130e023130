#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test finale per verificare che i problemi segnalati siano risolti:
1. Scikit-learn non disponibile - funzionalità ML limitate
2. 'MCPClient' object has no attribute 'max_retries'
"""

import sys
import os
from datetime import datetime

def test_scikit_learn():
    """Testa che scikit-learn sia installato e funzionante."""
    print("🧠 TEST SCIKIT-LEARN")
    print("=" * 30)

    try:
        import sklearn
        print(f"✅ Scikit-learn importato con successo")
        print(f"📦 Versione: {sklearn.__version__}")

        # Test funzionalità base
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import accuracy_score
        import numpy as np

        # Crea dati di test
        X = np.random.rand(100, 4)
        y = np.random.randint(0, 2, 100)

        # Test train/test split
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # Test modello
        clf = RandomForestClassifier(n_estimators=10, random_state=42)
        clf.fit(X_train, y_train)
        predictions = clf.predict(X_test)
        accuracy = accuracy_score(y_test, predictions)

        print(f"✅ RandomForestClassifier funzionante")
        print(f"📊 Accuracy test: {accuracy:.2f}")
        print(f"✅ Funzionalità ML completamente disponibili")

        return True

    except ImportError as e:
        print(f"❌ Errore import scikit-learn: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Errore test scikit-learn: {str(e)}")
        return False

def test_mcp_client():
    """Testa che MCPClient funzioni correttamente."""
    print("\n🔌 TEST MCP CLIENT")
    print("=" * 30)

    try:
        # Aggiungi il percorso del progetto
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))

        from mcp_client import MCPClient

        # Test creazione client con tutti i parametri
        client = MCPClient(
            base_url='http://127.0.0.1:8001',
            max_retries=3,
            timeout=10
        )

        print(f"✅ MCPClient creato con successo")
        print(f"🌐 Base URL: {client.base_url}")
        print(f"⏱️ Timeout: {client.timeout}s")
        print(f"🔄 Max Retries: {client.max_retries}")
        print(f"📡 Disponibile: {client.is_available}")

        # Test attributi
        assert hasattr(client, 'max_retries'), "Attributo max_retries mancante"
        assert hasattr(client, 'timeout'), "Attributo timeout mancante"
        assert hasattr(client, 'base_url'), "Attributo base_url mancante"
        assert hasattr(client, 'is_available'), "Attributo is_available mancante"

        print(f"✅ Tutti gli attributi presenti e accessibili")
        print(f"✅ Errore 'max_retries' risolto")

        return True

    except ImportError as e:
        print(f"❌ Errore import MCPClient: {str(e)}")
        return False
    except AttributeError as e:
        print(f"❌ Errore attributo MCPClient: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Errore test MCPClient: {str(e)}")
        return False

def test_server_mcp():
    """Testa che il server MCP sia raggiungibile."""
    print("\n🖥️ TEST SERVER MCP")
    print("=" * 30)

    try:
        import requests

        # Test connessione server MCP
        response = requests.get('http://127.0.0.1:8000/health', timeout=5)

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Server MCP raggiungibile")
            print(f"📊 Status: {data.get('status', 'unknown')}")
            print(f"⏱️ Timestamp: {data.get('timestamp', 'unknown')}")
            print(f"✅ Timeout MCP risolto")
            return True
        else:
            print(f"⚠️ Server MCP risponde ma con status: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Server MCP non raggiungibile: {str(e)}")
        print(f"💡 Assicurati che il server MCP sia avviato: cd mcp_server && python run_server.py")
        return False

def main():
    """Esegue tutti i test per verificare la risoluzione dei problemi."""
    print("🧪 TEST RISOLUZIONE PROBLEMI SEGNALATI")
    print("=" * 50)
    print(f"📅 Data: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Test 1: Scikit-learn
    sklearn_ok = test_scikit_learn()

    # Test 2: MCPClient
    mcp_client_ok = test_mcp_client()

    # Test 3: Server MCP
    server_mcp_ok = test_server_mcp()

    # Risultato finale
    print("\n" + "=" * 50)
    print("📋 RISULTATO FINALE")
    print("=" * 50)

    problems_resolved = 0
    total_problems = 2  # I due problemi segnalati

    if sklearn_ok:
        print("✅ PROBLEMA 1 RISOLTO: Scikit-learn installato e funzionante")
        print("   - Funzionalità ML completamente disponibili")
        print("   - Warning 'Scikit-learn non disponibile' eliminato")
        problems_resolved += 1
    else:
        print("❌ PROBLEMA 1 PERSISTE: Scikit-learn non funzionante")

    if mcp_client_ok:
        print("✅ PROBLEMA 2 RISOLTO: MCPClient corretto")
        print("   - Attributo 'max_retries' presente e accessibile")
        print("   - Errore 'object has no attribute max_retries' eliminato")
        problems_resolved += 1
    else:
        print("❌ PROBLEMA 2 PERSISTE: MCPClient non funzionante")

    if server_mcp_ok:
        print("✅ BONUS: Server MCP raggiungibile")
        print("   - Timeout MCP risolto")
        print("   - Connessione stabile")
    else:
        print("⚠️ NOTA: Server MCP non attivo (non era nei problemi originali)")

    print(f"\n🎯 RISOLUZIONE: {problems_resolved}/{total_problems} problemi risolti")

    if problems_resolved == total_problems:
        print("🎉 TUTTI I PROBLEMI SEGNALATI SONO STATI RISOLTI!")
        print("✅ Sistema completamente funzionante")
        print("✅ Nessun warning o errore dovrebbe più apparire")
        return True
    else:
        print("⚠️ Alcuni problemi potrebbero persistere")
        return False

if __name__ == "__main__":
    success = main()

    # Salva risultati
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    results_file = f'test_problemi_risolti_{timestamp}.txt'

    with open(results_file, 'w', encoding='utf-8') as f:
        f.write(f"Test Risoluzione Problemi - {timestamp}\n")
        f.write(f"Risultato: {'SUCCESS' if success else 'PARTIAL'}\n")
        f.write("Problemi testati:\n")
        f.write("1. Scikit-learn non disponibile\n")
        f.write("2. MCPClient max_retries attribute error\n")

    print(f"\n📄 Risultati salvati in: {results_file}")

    exit(0 if success else 1)
