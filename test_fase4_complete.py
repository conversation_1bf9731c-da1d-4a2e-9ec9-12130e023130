#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test completo della Fase 4: Sincronizzazione Dati e Aggiornamento Database
"""

import asyncio
import pandas as pd
import sys
import time
from datetime import datetime, timedelta

# Aggiungi il percorso corrente per gli import
sys.path.append('.')

def test_fase4_complete():
    """Test integrato di tutti i componenti della Fase 4."""
    print('🚀 TEST COMPLETO FASE 4: SINCRONIZZAZIONE DATI E DATABASE')
    print('=' * 65)
    
    # Test 1: DataSynchronizer
    print('\n🔄 TEST 1: DataSynchronizer')
    print('-' * 40)
    
    try:
        from data_synchronizer import DataSynchronizer, SyncOperation
        synchronizer = DataSynchronizer()
        
        print(f'✅ DataSynchronizer inizializzato')
        print(f'   Intervallo sync: {synchronizer.SYNC_INTERVAL_SECONDS}s')
        print(f'   Batch size: {synchronizer.BATCH_SIZE}')
        print(f'   Max retry: {synchronizer.MAX_RETRY_ATTEMPTS}')
        
        # Test aggiunta alla coda
        test_data = {"name": "Test Record", "value": 123}
        synchronizer.add_to_sync_queue("test_table", 1, SyncOperation.CREATE, test_data)
        
        status = synchronizer.get_sync_status()
        print(f'   Coda sync: {status["queue_size"]} elementi')
        print(f'   Cache size: {status["cache_size"]} elementi')
        
        sync_test_result = {"status": "success", "component": "DataSynchronizer"}
        
    except Exception as e:
        print(f'❌ Errore DataSynchronizer: {e}')
        sync_test_result = {"status": "error", "error": str(e)}
    
    # Test 2: IncrementalUpdater
    print('\n📊 TEST 2: IncrementalUpdater')
    print('-' * 40)
    
    try:
        from incremental_updater import IncrementalUpdater, UpdateStrategy
        updater = IncrementalUpdater()
        
        print(f'✅ IncrementalUpdater inizializzato')
        print(f'   Strategia: {updater.UPDATE_STRATEGY.value}')
        print(f'   Batch size: {updater.BATCH_SIZE}')
        
        # Test rilevamento modifiche
        old_records = [
            {"id": 1, "name": "Record 1", "value": 100, "updated_at": "2024-01-01T10:00:00"},
            {"id": 2, "name": "Record 2", "value": 200, "updated_at": "2024-01-01T10:00:00"}
        ]
        
        new_records = [
            {"id": 1, "name": "Record 1 Modified", "value": 150, "updated_at": "2024-01-01T11:00:00"},
            {"id": 2, "name": "Record 2", "value": 200, "updated_at": "2024-01-01T10:00:00"},
            {"id": 3, "name": "New Record", "value": 300, "updated_at": "2024-01-01T11:00:00"}
        ]
        
        changes = updater.detect_changes("test_table", new_records)
        
        print(f'   📈 Modifiche rilevate:')
        print(f'      Nuovi: {changes["change_summary"]["new_count"]}')
        print(f'      Modificati: {changes["change_summary"]["modified_count"]}')
        print(f'      Non modificati: {changes["change_summary"]["unchanged_count"]}')
        print(f'      Percentuale cambiamento: {changes["change_summary"]["change_percentage"]:.1f}%')
        
        # Test ottimizzazione strategia
        historical_data = [{"has_changes": True}, {"has_changes": False}, {"has_changes": True}]
        optimal_strategy = updater.optimize_update_strategy("test_table", historical_data)
        print(f'   🎯 Strategia ottimale: {optimal_strategy.value}')
        
        # Test statistiche
        stats = updater.get_update_statistics()
        print(f'   📊 Statistiche: {stats["total_updates"]} aggiornamenti totali')
        
        update_test_result = {"status": "success", "component": "IncrementalUpdater", "changes": changes}
        
    except Exception as e:
        print(f'❌ Errore IncrementalUpdater: {e}')
        update_test_result = {"status": "error", "error": str(e)}
    
    # Test 3: ConflictResolver
    print('\n🔥 TEST 3: ConflictResolver')
    print('-' * 40)
    
    try:
        from conflict_resolver import ConflictResolver, ConflictType, ResolutionStrategy
        resolver = ConflictResolver()
        
        print(f'✅ ConflictResolver inizializzato')
        print(f'   Strategia default: {resolver.DEFAULT_STRATEGY.value}')
        print(f'   Soglia auto-risoluzione: {resolver.AUTO_RESOLVE_THRESHOLD}')
        
        # Test rilevamento conflitto
        local_version = {
            "id": 1,
            "name": "Local Version",
            "value": 100,
            "updated_at": "2024-01-01T12:00:00"
        }
        
        remote_version = {
            "id": 1,
            "name": "Remote Version",
            "value": 150,
            "updated_at": "2024-01-01T12:01:00"
        }
        
        conflict = resolver.detect_conflict("test_table", 1, local_version, remote_version)
        
        if conflict:
            print(f'   🔥 Conflitto rilevato: {conflict.conflict_type.value}')
            print(f'      Campi in conflitto: {", ".join(conflict.conflicting_fields)}')
            
            # Test risoluzione conflitto
            resolution = resolver.resolve_conflict(conflict)
            
            if resolution["success"]:
                print(f'   ✅ Conflitto risolto con strategia: {resolution["strategy_used"].value}')
                print(f'      Auto-risolto: {resolution["auto_resolved"]}')
            else:
                print(f'   ⚠️ Conflitto richiede revisione manuale')
        else:
            print(f'   ℹ️ Nessun conflitto rilevato')
        
        # Test statistiche conflitti
        conflict_stats = resolver.get_conflict_statistics()
        print(f'   📊 Statistiche conflitti: {conflict_stats["total_conflicts"]} totali')
        
        conflict_test_result = {"status": "success", "component": "ConflictResolver", "conflict": conflict is not None}
        
    except Exception as e:
        print(f'❌ Errore ConflictResolver: {e}')
        conflict_test_result = {"status": "error", "error": str(e)}
    
    # Test 4: SyncMonitor
    print('\n🔍 TEST 4: SyncMonitor')
    print('-' * 40)
    
    try:
        from sync_monitor import SyncMonitor, AlertLevel, MetricType
        monitor = SyncMonitor()
        
        print(f'✅ SyncMonitor inizializzato')
        print(f'   Soglia performance: {monitor.PERFORMANCE_THRESHOLD_MS}ms')
        print(f'   Soglia errori: {monitor.ERROR_RATE_THRESHOLD:.1%}')
        
        # Test registrazione operazioni
        monitor.record_sync_operation("test_sync", 1500.0, True, {"test": True})
        monitor.record_sync_operation("test_sync", 6000.0, False, {"test": True})  # Supera soglia
        
        # Test registrazione metriche
        monitor.record_metric(MetricType.PERFORMANCE, "test_metric", 2500.0, "ms", {"component": "test"})
        
        # Test creazione allerta
        monitor.create_alert(AlertLevel.WARNING, "Test Alert", "Questo è un test", "test_component")
        
        # Test salute sistema
        health = monitor.get_system_health()
        print(f'   🏥 Stato sistema: {health["system_status"]}')
        print(f'      Operazioni totali: {health["metrics"]["total_operations"]}')
        print(f'      Tasso errori: {health["metrics"]["error_rate"]:.1f}%')
        print(f'      Allerte attive: {health["alerts"]["active"]}')
        
        # Test dashboard
        dashboard = monitor.get_dashboard_data()
        print(f'   📊 Dashboard: {len(dashboard["timeline"])} punti timeline')
        print(f'      Allerte attive: {len(dashboard["active_alerts"])}')
        
        # Test statistiche monitoraggio
        monitor_stats = monitor.get_monitoring_statistics()
        print(f'   📈 Buffer metriche: {monitor_stats["metrics_buffer_size"]} elementi')
        
        monitor_test_result = {"status": "success", "component": "SyncMonitor", "health": health}
        
    except Exception as e:
        print(f'❌ Errore SyncMonitor: {e}')
        monitor_test_result = {"status": "error", "error": str(e)}
    
    # Test Integrazione
    print('\n🔗 TEST 5: Integrazione Componenti')
    print('-' * 40)
    
    integration_score = 0
    total_tests = 4
    
    test_results = [sync_test_result, update_test_result, conflict_test_result, monitor_test_result]
    
    for result in test_results:
        if result["status"] == "success":
            integration_score += 1
            print(f'   ✅ {result["component"]}: OK')
        else:
            print(f'   ❌ {result["component"]}: ERRORE')
    
    integration_percentage = (integration_score / total_tests) * 100
    
    # Statistiche finali
    print(f'\n📈 STATISTICHE FINALI FASE 4')
    print('=' * 40)
    
    print(f'📊 Componenti testati: {total_tests}')
    print(f'✅ Componenti funzionanti: {integration_score}/{total_tests}')
    print(f'📈 Percentuale successo: {integration_percentage:.1f}%')
    
    # Valutazione complessiva
    print(f'\n🏆 VALUTAZIONE COMPLESSIVA FASE 4')
    print('-' * 40)
    
    if integration_percentage >= 90:
        status = "🟢 ECCELLENTE"
        description = "Tutti i componenti funzionano perfettamente"
    elif integration_percentage >= 75:
        status = "🟡 BUONO"
        description = "La maggior parte dei componenti funziona correttamente"
    elif integration_percentage >= 50:
        status = "🟠 SUFFICIENTE"
        description = "Alcuni componenti necessitano di correzioni"
    else:
        status = "🔴 INSUFFICIENTE"
        description = "Molti componenti necessitano di correzioni"
    
    print(f'Stato: {status}')
    print(f'Descrizione: {description}')
    
    # Dettagli componenti
    print(f'\n📋 DETTAGLI COMPONENTI:')
    for result in test_results:
        component = result["component"]
        status = "✅ OK" if result["status"] == "success" else "❌ ERRORE"
        print(f'   {component}: {status}')
        
        if result["status"] == "error":
            print(f'      Errore: {result["error"]}')
    
    return {
        "integration_score": integration_score,
        "total_tests": total_tests,
        "success_percentage": integration_percentage,
        "test_results": test_results
    }

if __name__ == "__main__":
    try:
        start_time = time.time()
        results = test_fase4_complete()
        end_time = time.time()
        
        print(f'\n⏱️ Tempo totale test: {end_time - start_time:.2f} secondi')
        print(f'🎉 Test Fase 4 completato!')
        
    except Exception as e:
        print(f"❌ Errore generale nel test: {e}")
        import traceback
        traceback.print_exc()
