# Guida alla Risoluzione dei Problemi - APP-ROBERTO

## Problemi Comuni e Soluzioni

### 1. Errori di Test

#### Test `test_login_missing_data` fallisce con errore JSON
**Problema**: Il test riceve una risposta HTML invece di JSON
**Causa**: Richiesta POST senza `Content-Type: application/json`
**Soluzione**: Assicurarsi che tutte le richieste POST ai endpoint JSON includano il content type appropriato:
```python
response = self.client.post(
    '/auth/login',
    data=json.dumps({}),
    content_type='application/json'
)
```

#### Test `test_rate_limited_decorator` fallisce con "Working outside of request context"
**Problema**: Il decoratore `rate_limited` cerca di accedere a `flask.request` fuori dal contesto Flask
**Soluzione**: Usare `app.test_request_context()` nei test:
```python
from flask import Flask
app = Flask(__name__)
with app.test_request_context('/', environ_base={'REMOTE_ADDR': '127.0.0.1'}):
    # Eseguire i test qui
```

### 2. Problemi di Ambiente

#### ModuleNotFoundError: No module named 'flask'
**Problema**: Le dipendenze non sono installate nell'ambiente virtuale corrente
**Soluzione**: Attivare l'ambiente virtuale corretto:
```powershell
.\clean_env\Scripts\activate
```

#### Porta 8000 già in uso per il server MCP
**Problema**: Un'istanza del server MCP è già in esecuzione
**Soluzione**: 
1. Terminare il processo esistente
2. Oppure usare una porta diversa modificando la configurazione

### 3. Problemi di Dipendenze

#### Plotly package issues
**Problema**: Warning sui pacchetti plotly
**Soluzione**: Reinstallare plotly:
```powershell
pip uninstall plotly
pip install plotly==5.16.1
```

#### Cachelib missing
**Problema**: Flask-Session richiede cachelib
**Soluzione**: Installare cachelib:
```powershell
pip install cachelib
```

### 4. Warning di Deprecazione

#### datetime.utcnow() deprecated
**Problema**: Warning di deprecazione per datetime.utcnow()
**Soluzione**: Aggiornare il codice per usare timezone-aware datetime:
```python
# Invece di:
datetime.utcnow()
# Usare:
datetime.now(datetime.UTC)
```

### 5. Problemi di Performance

#### Cache non funziona correttamente
**Problema**: La cache globale non memorizza i risultati
**Soluzione**: Verificare che la cache sia inizializzata correttamente e che le chiavi siano uniche

#### Rate limiting non funziona
**Problema**: Il rate limiter non blocca le richieste eccessive
**Soluzione**: Verificare che il contesto Flask sia disponibile e che le chiavi siano generate correttamente

## Comandi Utili per il Debug

### Eseguire tutti i test
```powershell
pytest -v
```

### Eseguire test specifici
```powershell
pytest tests/test_auth_routes.py::TestAuthRoutes::test_login_missing_data -v
```

### Eseguire test con copertura
```powershell
pytest --cov=. --cov-report=term-missing
```

### Verificare l'ambiente virtuale
```powershell
pip list
```

### Pulire la cache di pytest
```powershell
pytest --cache-clear
```

## Contatti per Supporto

Per problemi non risolti da questa guida, consultare:
1. Il file `debug_log.md` per errori specifici
2. I log dell'applicazione
3. La documentazione del progetto nel README.md
