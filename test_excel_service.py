#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import unittest
import pandas as pd
import tempfile
import time
from excel_service import ExcelService

class TestExcelService(unittest.TestCase):
    """
    Test per il servizio Excel.
    Questi test verificano l'integrazione con il server COM di Excel.
    """
    
    @classmethod
    def setUpClass(cls):
        """
        Inizializza il servizio Excel prima di eseguire i test.
        """
        try:
            cls.excel_service = ExcelService(visible=False)
            cls.test_dir = tempfile.mkdtemp()
            cls.test_file = os.path.join(cls.test_dir, "test_excel.xlsx")
            
            # Crea un DataFrame di test
            cls.test_df = pd.DataFrame({
                'Nome': ['<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>'],
                'Cognome': ['<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>'],
                'Età': [30, 25, 45, 35],
                'Stipendio': [1500.50, 1800.75, 2200.00, 1950.25]
            })
            
            print(f"Test inizializzati. Directory temporanea: {cls.test_dir}")
        except Exception as e:
            print(f"Errore nell'inizializzazione dei test: {str(e)}")
            raise
    
    @classmethod
    def tearDownClass(cls):
        """
        Chiude il servizio Excel e pulisce i file temporanei dopo i test.
        """
        try:
            # Chiudi Excel
            if hasattr(cls, 'excel_service'):
                cls.excel_service.close()
            
            # Rimuovi i file temporanei
            if hasattr(cls, 'test_file') and os.path.exists(cls.test_file):
                try:
                    os.remove(cls.test_file)
                except:
                    pass
            
            print("Test completati e risorse rilasciate")
        except Exception as e:
            print(f"Errore nella pulizia dopo i test: {str(e)}")
    
    def test_01_excel_availability(self):
        """
        Verifica che Excel sia disponibile.
        """
        self.assertTrue(self.excel_service.is_available(), "Excel non è disponibile")
        print("Test 1: Excel è disponibile")
    
    def test_02_create_workbook(self):
        """
        Verifica la creazione di un nuovo workbook.
        """
        workbook = self.excel_service.create_workbook()
        self.assertIsNotNone(workbook, "Impossibile creare un nuovo workbook")
        
        # Salva il workbook
        success = self.excel_service.save_workbook(workbook, self.test_file)
        self.assertTrue(success, "Impossibile salvare il workbook")
        self.assertTrue(os.path.exists(self.test_file), "Il file non è stato creato")
        
        # Chiudi il workbook
        success = self.excel_service.close_workbook(workbook)
        self.assertTrue(success, "Impossibile chiudere il workbook")
        
        print(f"Test 2: Workbook creato e salvato in {self.test_file}")
    
    def test_03_open_workbook(self):
        """
        Verifica l'apertura di un workbook esistente.
        """
        workbook = self.excel_service.open_workbook(self.test_file)
        self.assertIsNotNone(workbook, "Impossibile aprire il workbook esistente")
        
        # Chiudi il workbook
        success = self.excel_service.close_workbook(workbook)
        self.assertTrue(success, "Impossibile chiudere il workbook")
        
        print("Test 3: Workbook aperto con successo")
    
    def test_04_write_dataframe(self):
        """
        Verifica la scrittura di un DataFrame in un foglio di lavoro.
        """
        # Apri il workbook
        workbook = self.excel_service.open_workbook(self.test_file)
        self.assertIsNotNone(workbook, "Impossibile aprire il workbook")
        
        # Scrivi il DataFrame
        success = self.excel_service.write_dataframe(
            workbook, 
            self.test_df, 
            sheet_name="Test", 
            include_header=True
        )
        self.assertTrue(success, "Impossibile scrivere il DataFrame")
        
        # Salva e chiudi
        self.excel_service.save_workbook(workbook, self.test_file)
        self.excel_service.close_workbook(workbook)
        
        print("Test 4: DataFrame scritto con successo")
    
    def test_05_read_worksheet(self):
        """
        Verifica la lettura di un foglio di lavoro.
        """
        # Apri il workbook
        workbook = self.excel_service.open_workbook(self.test_file)
        self.assertIsNotNone(workbook, "Impossibile aprire il workbook")
        
        # Leggi il foglio di lavoro
        df = self.excel_service.read_worksheet(workbook, sheet_name="Test")
        self.assertIsNotNone(df, "Impossibile leggere il foglio di lavoro")
        self.assertFalse(df.empty, "Il DataFrame letto è vuoto")
        
        # Verifica che i dati siano corretti
        self.assertEqual(len(df), len(self.test_df), "Il numero di righe non corrisponde")
        
        # Chiudi il workbook
        self.excel_service.close_workbook(workbook)
        
        print("Test 5: Foglio di lavoro letto con successo")
        print(f"DataFrame letto:\n{df.head()}")
    
    def test_06_multiple_sheets(self):
        """
        Verifica la gestione di più fogli di lavoro.
        """
        # Apri il workbook
        workbook = self.excel_service.open_workbook(self.test_file)
        self.assertIsNotNone(workbook, "Impossibile aprire il workbook")
        
        # Crea un secondo DataFrame
        df2 = pd.DataFrame({
            'Prodotto': ['Laptop', 'Monitor', 'Tastiera', 'Mouse'],
            'Prezzo': [1200.00, 350.50, 45.99, 25.50],
            'Quantità': [10, 15, 30, 50]
        })
        
        # Scrivi il secondo DataFrame in un nuovo foglio
        success = self.excel_service.write_dataframe(
            workbook, 
            df2, 
            sheet_name="Prodotti", 
            include_header=True
        )
        self.assertTrue(success, "Impossibile scrivere il secondo DataFrame")
        
        # Salva e chiudi
        self.excel_service.save_workbook(workbook, self.test_file)
        self.excel_service.close_workbook(workbook)
        
        # Riapri e verifica entrambi i fogli
        workbook = self.excel_service.open_workbook(self.test_file)
        
        # Leggi il primo foglio
        df_test = self.excel_service.read_worksheet(workbook, sheet_name="Test")
        self.assertIsNotNone(df_test, "Impossibile leggere il primo foglio")
        self.assertFalse(df_test.empty, "Il primo DataFrame è vuoto")
        
        # Leggi il secondo foglio
        df_prodotti = self.excel_service.read_worksheet(workbook, sheet_name="Prodotti")
        self.assertIsNotNone(df_prodotti, "Impossibile leggere il secondo foglio")
        self.assertFalse(df_prodotti.empty, "Il secondo DataFrame è vuoto")
        
        # Chiudi il workbook
        self.excel_service.close_workbook(workbook)
        
        print("Test 6: Gestione di più fogli completata con successo")
        print(f"Foglio 'Prodotti':\n{df_prodotti.head()}")

if __name__ == '__main__':
    print("Avvio dei test per il servizio Excel...")
    unittest.main()
