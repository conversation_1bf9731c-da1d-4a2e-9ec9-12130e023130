# 🚀 Guida Setup Supabase + MCP per App Roberto

## 📋 Prerequisiti
- Account Supabase (gratuito)
- Node.js installato
- Progetto app-roberto funzionante

## 🎯 FASE 1: Creazione Progetto Supabase

### 1. Registrazione Supabase
1. Vai su [supabase.com](https://supabase.com)
2. <PERSON><PERSON><PERSON> "Start your project"
3. Registrati con GitHub/Google
4. Crea nuovo progetto:
   - Nome: `app-roberto-db`
   - Password: (genera sicura)
   - Regione: `Europe (Frankfurt)` (più vicina)

### 2. Configurazione Database
```sql
-- Crea tabelle per i nostri dati
CREATE TABLE teamviewer_sessions (
    id SERIAL PRIMARY KEY,
    utente VARCHAR(255),
    computer VARCHAR(255),
    inizio TIMESTAMP,
    fine TIMESTAMP,
    durata INTERVAL,
    tipo_sessione VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE registro_auto (
    id SERIAL PRIMARY KEY,
    dipendente VARCHAR(255),
    data DATE,
    veicolo VARCHAR(100),
    presa_datetime TIMESTAMP,
    riconsegna_datetime TIMESTAMP,
    cliente VARCHAR(255),
    ore_utilizzo DECIMAL(5,2),
    note TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE progetti (
    id SERIAL PRIMARY KEY,
    nome_progetto VARCHAR(255),
    cliente VARCHAR(255),
    data_inizio DATE,
    data_fine DATE,
    stato VARCHAR(100),
    responsabile VARCHAR(255),
    budget DECIMAL(10,2),
    descrizione TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE timbrature (
    id SERIAL PRIMARY KEY,
    dipendente VARCHAR(255),
    data DATE,
    entrata TIME,
    uscita TIME,
    ore_lavorate DECIMAL(4,2),
    note TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE calendario (
    id SERIAL PRIMARY KEY,
    data DATE,
    ora_inizio TIME,
    ora_fine TIME,
    titolo VARCHAR(255),
    descrizione TEXT,
    luogo VARCHAR(255),
    partecipanti TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

## 🎯 FASE 2: Integrazione MCP

### 1. Installazione Supabase MCP
```bash
# Nel progetto app-roberto
npm install @supabase/mcp-server-supabase @supabase/mcp-utils
```

### 2. Configurazione MCP Server
Crea file `mcp_supabase_config.json`:
```json
{
  "mcpServers": {
    "supabase": {
      "command": "npx",
      "args": [
        "-y",
        "@supabase/mcp-server-supabase@latest",
        "--access-token",
        "TUO_ACCESS_TOKEN_QUI"
      ]
    }
  }
}
```

### 3. Ottieni Access Token
1. Vai su Supabase Dashboard
2. Settings → API
3. Copia "Project API keys" → "anon public"
4. Sostituisci in `mcp_supabase_config.json`

## 🎯 FASE 3: Integrazione con App Roberto

### 1. Crea Database Service
```python
# database_service.py
import os
from supabase import create_client, Client
from typing import List, Dict, Any

class SupabaseService:
    def __init__(self):
        url = os.environ.get("SUPABASE_URL")
        key = os.environ.get("SUPABASE_ANON_KEY")
        self.supabase: Client = create_client(url, key)
    
    def insert_teamviewer_data(self, data: List[Dict[str, Any]]):
        """Inserisce dati TeamViewer nel database"""
        return self.supabase.table('teamviewer_sessions').insert(data).execute()
    
    def insert_vehicle_data(self, data: List[Dict[str, Any]]):
        """Inserisce dati registro auto nel database"""
        return self.supabase.table('registro_auto').insert(data).execute()
    
    def get_teamviewer_stats(self):
        """Ottiene statistiche TeamViewer"""
        return self.supabase.table('teamviewer_sessions').select("*").execute()
    
    def get_vehicle_usage(self, start_date: str, end_date: str):
        """Ottiene utilizzo veicoli per periodo"""
        return self.supabase.table('registro_auto')\
            .select("*")\
            .gte('data', start_date)\
            .lte('data', end_date)\
            .execute()
```

### 2. Aggiorna .env
```bash
# Aggiungi al file .env
SUPABASE_URL=https://tuo-progetto.supabase.co
SUPABASE_ANON_KEY=tua-chiave-anon
SUPABASE_SERVICE_ROLE_KEY=tua-chiave-service-role
```

### 3. Modifica app.py
```python
# Aggiungi import
from database_service import SupabaseService

# Inizializza servizio
supabase_service = SupabaseService()

# Modifica funzione di elaborazione
def process_and_save_data(file_type, processed_df):
    """Elabora e salva dati nel database"""
    
    # Converti DataFrame in lista di dizionari
    data = processed_df.to_dict('records')
    
    # Salva in base al tipo
    if file_type == 'teamviewer':
        result = supabase_service.insert_teamviewer_data(data)
    elif file_type == 'registro_auto':
        result = supabase_service.insert_vehicle_data(data)
    # ... altri tipi
    
    return result
```

## 🎯 FASE 4: Dashboard Real-time

### 1. Abilita Real-time
```sql
-- Nel SQL Editor di Supabase
ALTER TABLE teamviewer_sessions REPLICA IDENTITY FULL;
ALTER TABLE registro_auto REPLICA IDENTITY FULL;
```

### 2. Frontend Real-time
```javascript
// dashboard.js
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Ascolta cambiamenti in tempo reale
supabase
  .channel('public:teamviewer_sessions')
  .on('postgres_changes', 
    { event: 'INSERT', schema: 'public', table: 'teamviewer_sessions' },
    (payload) => {
      console.log('Nuova sessione TeamViewer:', payload.new);
      updateDashboard();
    }
  )
  .subscribe();
```

## 🎯 VANTAGGI IMMEDIATI

### ✅ Cosa Ottieni Subito:
1. **Database PostgreSQL** completo e gestito
2. **API REST automatica** per tutti i dati
3. **Dashboard web** per visualizzare dati
4. **Backup automatici** ogni giorno
5. **Accesso multi-utente** sicuro
6. **Real-time updates** automatici
7. **Integrazione MCP** nativa
8. **Scalabilità automatica** fino a 500MB gratis

### 💰 Costi:
- **Gratis** fino a 500MB database
- **$25/mese** per uso professionale illimitato
- **Molto più economico** di server dedicato

## 🚀 Prossimi Passi

1. **Crea account Supabase** (5 min)
2. **Configura database** (10 min)
3. **Integra con app** (30 min)
4. **Testa funzionalità** (15 min)

**Totale setup: ~1 ora per database enterprise-grade!**
