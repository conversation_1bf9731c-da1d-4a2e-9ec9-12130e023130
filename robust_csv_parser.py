#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import logging
import re
import chardet
from typing import Tuple, Optional, List, Dict, Any

# Configurazione del logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RobustCSVParser:
    """
    Parser CSV robusto per gestire file con dati misti, corrotti o con strutture complesse.
    Gestisce automaticamente:
    - Rilevamento encoding
    - Rilevamento separatori
    - Pulizia dati misti (tabelle pivot, righe vuote, ecc.)
    - Gestione case-insensitive delle estensioni
    """
    
    def __init__(self):
        self.supported_encodings = ['utf-8-sig', 'utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
        self.supported_separators = [';', ',', '\t', '|']
        
    def parse_csv_file(self, file_path: str) -> <PERSON><PERSON>[Optional[pd.DataFrame], Optional[str]]:
        """
        Analizza un file CSV con gestione robusta degli errori.
        
        Args:
            file_path: Percorso del file CSV da analizzare
            
        Returns:
            Tuple (DataFrame, error_message)
        """
        try:
            # Verifica estensione (case-insensitive)
            if not self._is_csv_file(file_path):
                return None, f"File non è un CSV: {file_path}"
            
            logger.info(f"🔍 Analisi file CSV: {file_path}")
            
            # 1. Rileva encoding
            encoding = self._detect_encoding(file_path)
            logger.info(f"📝 Encoding rilevato: {encoding}")
            
            # 2. Rileva separatore
            separator = self._detect_separator(file_path, encoding)
            logger.info(f"🔗 Separatore rilevato: '{separator}'")
            
            # 3. Leggi e pulisci il file
            df = self._read_and_clean_csv(file_path, encoding, separator)
            
            if df is None or df.empty:
                return None, "File CSV vuoto o non leggibile"
            
            logger.info(f"✅ File CSV letto con successo: {len(df)} righe, {len(df.columns)} colonne")
            logger.info(f"📊 Colonne: {list(df.columns)}")
            
            return df, None
            
        except Exception as e:
            error_msg = f"Errore nella lettura del file CSV: {str(e)}"
            logger.error(error_msg)
            return None, error_msg
    
    def _is_csv_file(self, file_path: str) -> bool:
        """Verifica se il file è un CSV (case-insensitive)."""
        return file_path.lower().endswith(('.csv', '.txt'))
    
    def _detect_encoding(self, file_path: str) -> str:
        """Rileva l'encoding del file."""
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # Leggi primi 10KB
            
            result = chardet.detect(raw_data)
            detected_encoding = result.get('encoding', 'utf-8')
            confidence = result.get('confidence', 0)
            
            logger.debug(f"Encoding rilevato: {detected_encoding} (confidenza: {confidence:.2f})")
            
            # Se la confidenza è bassa, prova con encoding comuni
            if confidence < 0.7:
                for encoding in self.supported_encodings:
                    try:
                        with open(file_path, 'r', encoding=encoding) as f:
                            f.read(1000)  # Prova a leggere
                        return encoding
                    except:
                        continue
            
            return detected_encoding if detected_encoding else 'utf-8'
            
        except ImportError:
            logger.warning("chardet non disponibile, uso utf-8-sig")
            return 'utf-8-sig'
        except Exception:
            return 'utf-8-sig'
    
    def _detect_separator(self, file_path: str, encoding: str) -> str:
        """Rileva il separatore del file CSV."""
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                # Leggi le prime righe per analizzare i separatori
                sample_lines = []
                for i, line in enumerate(f):
                    if i >= 5:  # Analizza solo le prime 5 righe
                        break
                    if line.strip():  # Ignora righe vuote
                        sample_lines.append(line.strip())
            
            if not sample_lines:
                return ';'  # Default per file italiani
            
            # Conta le occorrenze di ogni separatore
            separator_counts = {}
            for sep in self.supported_separators:
                counts = [line.count(sep) for line in sample_lines if line.count(sep) > 0]
                if counts:
                    # Usa la media delle occorrenze
                    separator_counts[sep] = sum(counts) / len(counts)
            
            if not separator_counts:
                return ';'
            
            # Restituisci il separatore più comune
            best_separator = max(separator_counts.items(), key=lambda x: x[1])[0]
            logger.debug(f"Conteggi separatori: {separator_counts}")
            
            return best_separator
            
        except Exception as e:
            logger.warning(f"Errore nel rilevamento separatore: {e}, uso ';'")
            return ';'
    
    def _read_and_clean_csv(self, file_path: str, encoding: str, separator: str) -> Optional[pd.DataFrame]:
        """Legge e pulisce il file CSV."""
        try:
            # Prima prova la lettura standard
            try:
                df = pd.read_csv(file_path, sep=separator, encoding=encoding)
                
                # Verifica se la lettura è andata bene
                if len(df.columns) > 1 and len(df) > 0:
                    # Pulisci il DataFrame
                    df_cleaned = self._clean_dataframe(df)
                    if df_cleaned is not None and not df_cleaned.empty:
                        return df_cleaned
            except Exception as e:
                logger.warning(f"Lettura standard fallita: {e}, provo lettura manuale")
            
            # Se la lettura standard fallisce, prova la lettura manuale
            return self._manual_csv_read(file_path, encoding, separator)
            
        except Exception as e:
            logger.error(f"Errore nella lettura del CSV: {e}")
            return None
    
    def _clean_dataframe(self, df: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Pulisce il DataFrame da dati misti e righe problematiche."""
        try:
            logger.info("🧹 Pulizia DataFrame...")
            
            # 1. Rimuovi righe completamente vuote
            df = df.dropna(how='all')
            
            # 2. Identifica e rimuovi righe che sembrano essere tabelle pivot o metadati
            df_cleaned = self._remove_pivot_data(df)
            
            # 3. Rimuovi colonne completamente vuote
            df_cleaned = df_cleaned.dropna(axis=1, how='all')
            
            # 4. Pulisci i nomi delle colonne
            df_cleaned.columns = [self._clean_column_name(col) for col in df_cleaned.columns]
            
            # 5. Rimuovi righe con troppi valori mancanti (più del 70%)
            threshold = len(df_cleaned.columns) * 0.3  # Almeno 30% dei valori devono essere presenti
            df_cleaned = df_cleaned.dropna(thresh=threshold)
            
            logger.info(f"🧹 Pulizia completata: {len(df_cleaned)} righe rimanenti")
            
            return df_cleaned if not df_cleaned.empty else None
            
        except Exception as e:
            logger.error(f"Errore nella pulizia del DataFrame: {e}")
            return df
    
    def _remove_pivot_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Rimuove righe che contengono dati di tabelle pivot o metadati."""
        try:
            # Parole chiave che indicano dati di tabelle pivot o metadati
            pivot_keywords = [
                'somma di', 'etichette di', 'totale complessivo', 'etichette di riga',
                'etichette di colonna', 'manca data', 'vuoto', '(vuoto)'
            ]
            
            # Trova righe che contengono queste parole chiave
            rows_to_remove = []
            
            for idx, row in df.iterrows():
                row_text = ' '.join([str(val).lower() for val in row.values if pd.notna(val)])
                
                # Se la riga contiene parole chiave di pivot, rimuovila
                if any(keyword in row_text for keyword in pivot_keywords):
                    rows_to_remove.append(idx)
                    continue
                
                # Se la riga ha troppe celle vuote consecutive, potrebbe essere separatore
                non_null_count = row.count()
                if non_null_count <= 2 and len(df.columns) > 5:
                    rows_to_remove.append(idx)
            
            if rows_to_remove:
                logger.info(f"🗑️ Rimosse {len(rows_to_remove)} righe di metadati/pivot")
                df = df.drop(rows_to_remove)
            
            return df
            
        except Exception as e:
            logger.warning(f"Errore nella rimozione dati pivot: {e}")
            return df
    
    def _clean_column_name(self, col_name: str) -> str:
        """Pulisce il nome di una colonna."""
        if pd.isna(col_name):
            return "Colonna_Senza_Nome"
        
        col_name = str(col_name).strip()
        
        # Rimuovi caratteri speciali alla fine
        col_name = re.sub(r'[^\w\s]+$', '', col_name)
        
        return col_name if col_name else "Colonna_Senza_Nome"
    
    def _manual_csv_read(self, file_path: str, encoding: str, separator: str) -> Optional[pd.DataFrame]:
        """Lettura manuale del CSV per casi complessi."""
        try:
            logger.info("📖 Lettura manuale del CSV...")
            
            with open(file_path, 'r', encoding=encoding) as f:
                lines = f.readlines()
            
            if not lines:
                return None
            
            # Trova la riga header (prima riga non vuota con separatori)
            header_line = None
            header_idx = 0
            
            for i, line in enumerate(lines):
                line = line.strip()
                if line and separator in line:
                    header_line = line
                    header_idx = i
                    break
            
            if not header_line:
                return None
            
            # Estrai headers
            headers = [self._clean_column_name(h) for h in header_line.split(separator)]
            
            # Estrai dati
            data_rows = []
            for line in lines[header_idx + 1:]:
                line = line.strip()
                if not line:
                    continue
                
                values = line.split(separator)
                
                # Assicurati che abbiamo il numero giusto di colonne
                while len(values) < len(headers):
                    values.append('')
                values = values[:len(headers)]
                
                # Salta righe che sembrano metadati
                if any(keyword in line.lower() for keyword in ['somma di', 'etichette', 'totale complessivo']):
                    continue
                
                data_rows.append(values)
            
            if not data_rows:
                return None
            
            # Crea DataFrame
            df = pd.DataFrame(data_rows, columns=headers)
            
            # Pulisci il DataFrame
            return self._clean_dataframe(df)
            
        except Exception as e:
            logger.error(f"Errore nella lettura manuale: {e}")
            return None
