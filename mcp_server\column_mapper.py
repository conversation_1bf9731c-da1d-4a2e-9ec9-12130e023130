#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import logging
from typing import Dict, List, Optional, Any, Tuple

# Configurazione del logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ColumnMapper:
    """
    Classe per la standardizzazione dei nomi delle colonne dei file importati.
    Gestisce la mappatura delle colonne in base al tipo di file e rileva colonne simili.
    """

    def __init__(self):
        # Definizione delle mappature standard per i diversi tipi di file
        self.COLUMN_MAPPINGS = {
            # Mappatura per file di attività
            "attivita": {
                "Contratto": "Cliente",
                "Id Ticket": "ID_Ticket",
                "Iniziata il": "Data_Ora_Inizio",
                "Conclusa il": "Data_Ora_Fine",
                "Durata": "Durata_Attivita_Ore",
                "Creato da": "Tecnico",
                "Descrizione": "Descrizione_Attivita",
                "Stato": "Stato_Attivita",
                "Tipo": "Tipo_Attivita"
            },
            # Mappatura per file TeamViewer
            "teamviewer": {
                "Utente": "Tecnico",
                "Computer": "Cliente",
                "ID": "ID_Sessione",
                "Tipo di sessione": "Tipo_Sessione",
                "Gruppo": "Gruppo",
                "Inizio": "Data_Ora_Inizio",
                "Fine": "Data_Ora_Fine",
                "Durata": "Durata_Attivita_Ore",
                "Note": "Note",
                "Classificazione": "Valutazione",
                "Commenti del cliente": "Commenti_Cliente"
            },
            # Mappatura per file TeamViewer BAIT
            "teamviewer_bait": {
                "Utente": "Tecnico",
                "Computer": "Cliente",
                "ID": "ID_Sessione",
                "Tipo di sessione": "Tipo_Sessione",
                "Gruppo": "Gruppo",
                "Inizio": "Data_Ora_Inizio",
                "Fine": "Data_Ora_Fine",
                "Durata": "Durata_Attivita_Ore",
                "Note": "Note"
            },
            # Mappatura per file TeamViewer Gruppo
            "teamviewer_gruppo": {
                "Gruppo": "Gruppo",
                "Nome": "Nome_Gruppo",
                "Descrizione": "Descrizione_Gruppo",
                "Policy": "Policy_Gruppo"
            },
            # Mappatura per file Calendario (formato iCalendar)
            "calendario": {
                "SUMMARY": "Titolo_Evento",
                "DTSTART": "Data_Ora_Inizio",
                "DTEND": "Data_Ora_Fine",
                "LOCATION": "Luogo",
                "ATTENDEE": "Partecipanti",
                "NOTES": "Descrizione_Evento",
                "DESCRIPTION": "Descrizione_Evento",
                "ORGANIZER": "Organizzatore",
                "CATEGORIES": "Categoria",
                "DURATION": "Durata_Attivita_Ore",
                "UID": "ID_Evento",
                "CREATED": "Data_Creazione",
                "URL": "URL_Evento",
                "PRIORITY": "Priorita",
                "CALENDAR": "Calendario_Origine",
                # Mappature alternative per formati diversi
                "Data": "Data",
                "Ora inizio": "Ora_Inizio",
                "Ora fine": "Ora_Fine",
                "Titolo": "Titolo_Evento",
                "Descrizione": "Descrizione_Evento"
            },
            # Mappatura per file Timbrature
            "timbrature": {
                "Data": "Data",
                "Dipendente": "Dipendente",
                "Entrata": "Ora_Entrata",
                "Uscita": "Ora_Uscita",
                "Ore lavorate": "Ore_Lavorate"
            },
            # Mappatura per file Permessi
            "permessi": {
                "Dipendente": "Dipendente",
                "Data inizio": "Data_Inizio",
                "Data fine": "Data_Fine",
                "Tipo permesso": "Tipo_Permesso",
                "Ore": "Ore_Permesso",
                "Approvato da": "Approvato_Da",
                "Note": "Note"
            }
        }

        # Soglia di similarità per il rilevamento di colonne simili
        self.SIMILARITY_THRESHOLD = 0.7

    def map_columns(self, df: pd.DataFrame, file_type: str) -> Tuple[pd.DataFrame, Dict[str, str], List[str]]:
        """
        Applica la mappatura standard alle colonne del DataFrame in base al tipo di file.

        Args:
            df: DataFrame pandas da elaborare
            file_type: Tipo di file (es. "attivita", "teamviewer", ecc.)

        Returns:
            Tuple contenente:
            - DataFrame con colonne rinominate
            - Dizionario con la mappatura applicata {colonna_originale: colonna_standard}
            - Lista di colonne mancanti (presenti nella mappatura ma non nel DataFrame)
        """
        if df is None or df.empty:
            logger.warning("DataFrame vuoto o None fornito al mapper di colonne")
            return df, {}, []

        if file_type not in self.COLUMN_MAPPINGS:
            logger.warning(f"Tipo di file non supportato: {file_type}")
            return df, {}, []

        # Ottieni la mappatura per il tipo di file specificato
        mapping = self.COLUMN_MAPPINGS[file_type]

        # Inizializza il dizionario per la mappatura effettiva e la lista delle colonne mancanti
        applied_mapping = {}
        missing_columns = []

        # Crea una copia del DataFrame per non modificare l'originale
        df_mapped = df.copy()

        # Applica la mappatura standard
        for original_col, standard_col in mapping.items():
            if original_col in df.columns:
                df_mapped = df_mapped.rename(columns={original_col: standard_col})
                applied_mapping[original_col] = standard_col
            else:
                # Cerca colonne simili se la colonna originale non è presente
                similar_col = self._find_similar_column(original_col, df.columns)
                if similar_col:
                    df_mapped = df_mapped.rename(columns={similar_col: standard_col})
                    applied_mapping[similar_col] = standard_col
                    logger.info(f"Colonna simile trovata: '{similar_col}' mappata a '{standard_col}'")
                else:
                    missing_columns.append(original_col)
                    logger.warning(f"Colonna mancante: '{original_col}'")

        # Aggiungi colonne mancanti con valori None (JSON-compatibili)
        for col in missing_columns:
            standard_col = mapping[col]
            if standard_col not in df_mapped.columns:
                df_mapped[standard_col] = None
                logger.info(f"Aggiunta colonna mancante: '{standard_col}' con valori None")

        return df_mapped, applied_mapping, missing_columns

    def _find_similar_column(self, target_col: str, available_cols: List[str]) -> Optional[str]:
        """
        Cerca una colonna simile al target tra le colonne disponibili.

        Args:
            target_col: Colonna target da cercare
            available_cols: Lista di colonne disponibili

        Returns:
            Nome della colonna simile trovata o None
        """
        best_match = None
        best_score = 0

        # Normalizza il nome della colonna target
        target_col_lower = target_col.lower().strip()

        for col in available_cols:
            # Normalizza il nome della colonna disponibile
            col_lower = col.lower().strip()

            # Calcola un punteggio di similarità
            score = self._calculate_similarity(target_col_lower, col_lower)

            if score > best_score and score >= self.SIMILARITY_THRESHOLD:
                best_match = col
                best_score = score

        return best_match

    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """
        Calcola un punteggio di similarità tra due stringhe.

        Args:
            str1: Prima stringa
            str2: Seconda stringa

        Returns:
            Punteggio di similarità (da 0 a 1)
        """
        # Caso più semplice: corrispondenza esatta
        if str1 == str2:
            return 1.0

        # Caso: una stringa è contenuta nell'altra
        if str1 in str2 or str2 in str1:
            return 0.8

        # Implementazione semplice di similarità basata su caratteri comuni
        # In un'implementazione reale, si potrebbe usare una metrica più sofisticata
        # come la distanza di Levenshtein o la similarità di Jaccard
        common_chars = set(str1) & set(str2)
        all_chars = set(str1) | set(str2)

        if not all_chars:
            return 0.0

        return len(common_chars) / len(all_chars)

    def get_standard_columns(self, file_type: str) -> List[str]:
        """
        Restituisce la lista delle colonne standard per un tipo di file.

        Args:
            file_type: Tipo di file

        Returns:
            Lista delle colonne standard
        """
        if file_type not in self.COLUMN_MAPPINGS:
            return []

        return list(self.COLUMN_MAPPINGS[file_type].values())
