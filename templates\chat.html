{% extends "base.html" %}

{% block title %}Chat AI - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<main class="row mb-4">
    <section class="col-12">
        <article class="card shadow">
            <header class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-comments me-2"></i>Chat AI</h4>
            </header>
            <div class="card-body">
                <!-- Messaggio stato dati -->
                {% if not data %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Nessun dato da analizzare. Carica un file o completa il setup wizard.
                </div>
                {% else %}
                <div class="alert alert-info d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-info-circle me-2"></i>
                        Analisi dati: <strong>{{ filename }}</strong>
                        <span class="badge {% if file_type == 'unknown' or file_type == 'generico' %}bg-warning{% else %}bg-success{% endif %} text-white ms-2">
                            <i class="fas {% if file_type == 'attivita' %}fa-tasks{% elif 'teamviewer' in file_type %}fa-desktop{% elif file_type == 'calendario' %}fa-calendar{% elif file_type == 'timbrature' %}fa-clock{% elif file_type == 'permessi' %}fa-calendar-check{% elif file_type == 'normalized' %}fa-database{% elif file_type == 'wizard' %}fa-magic{% else %}fa-file{% endif %} me-1"></i>
                            Tipo: {{ file_type }}
                        </span>
                        {% if data_source %}
                        <span class="badge {% if data_source == 'supabase' %}bg-primary{% elif data_source == 'wizard' %}bg-info{% else %}bg-secondary{% endif %} text-white ms-2" title="Fonte dati: {{ data_source }}">
                            <i class="fas {% if data_source == 'supabase' %}fa-database{% elif data_source == 'wizard' %}fa-magic{% elif data_source == 'processed_file' %}fa-cogs{% else %}fa-file{% endif %} me-1"></i>
                            {% if data_source == 'supabase' %}Supabase{% elif data_source == 'wizard' %}Wizard{% elif data_source == 'processed_file' %}Elaborato{% else %}Sessione{% endif %}
                        </span>
                        {% endif %}
                        {% if session.get('mcp_file_id') %}
                        <span class="badge bg-info text-white ms-2" title="Elaborato con MCP">
                            <i class="fas fa-server me-1"></i>MCP
                        </span>
                        {% endif %}
                    </div>
                    <div>
                        <a href="{{ url_for('dashboard') }}" class="btn btn-outline-primary btn-sm me-2">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                        <a href="{{ url_for('advanced_dashboard') }}" class="btn btn-outline-primary btn-sm me-2">
                            <i class="fas fa-chart-line me-1"></i>Dashboard Avanzata
                        </a>
                        <a href="{{ url_for('interactive_charts') }}" class="btn btn-outline-primary btn-sm me-2">
                            <i class="fas fa-chart-bar me-1"></i>Grafici Interattivi
                        </a>
                        <a href="/agents/dashboard" class="btn btn-outline-primary btn-sm me-2">
                            <i class="fas fa-robot me-1"></i>Agenti AI
                        </a>
                    </div>
                </div>
                {% endif %}

                <!-- Selezione Modello LLM - SEMPRE DISPONIBILE -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0"><i class="fas fa-robot me-2"></i>Modello AI</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="model-selector" class="form-label">Seleziona il modello AI da utilizzare:</label>
                                    <select id="model-selector" class="form-select">
                                        <option value="" disabled selected>Caricamento modelli...</option>
                                    </select>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        I modelli più potenti possono fornire risposte più dettagliate ma potrebbero essere più lenti.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Suggerimenti</h5>
                            </div>
                            <div class="card-body">
                                {% if data %}
                                <p class="mb-2">Esempi di domande sui tuoi dati:</p>
                                <ul class="mb-0">
                                    <li>Quali sono i tecnici più attivi?</li>
                                    <li>Qual è la durata media delle sessioni?</li>
                                    <li>Quali clienti hanno richiesto più assistenza?</li>
                                    <li>Quali sono i giorni con più attività?</li>
                                    <li>Puoi riassumere i dati principali?</li>
                                </ul>
                                {% else %}
                                <p class="mb-2">Esempi di domande generali:</p>
                                <ul class="mb-0">
                                    <li>Come analizzi file CSV aziendali?</li>
                                    <li>Quali metriche sono importanti per le timbrature?</li>
                                    <li>Come interpretare dati di TeamViewer?</li>
                                    <li>Quali report posso generare?</li>
                                    <li>Come configurare l'analisi dati?</li>
                                </ul>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Area Chat - SEMPRE DISPONIBILE -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="fas fa-comments me-2"></i>Conversazione</h5>
                    </div>
                    <div class="card-body">
                        <div id="chat-messages" class="chat-container mb-3">
                            <!-- I messaggi verranno aggiunti qui dinamicamente -->
                            <div class="message system-message">
                                <div class="message-content">
                                    {% if data %}
                                    <p>Ciao! Sono l'assistente AI per l'analisi dei dati. Posso aiutarti a comprendere e analizzare i dati caricati. Cosa vorresti sapere?</p>
                                    {% else %}
                                    <p>Ciao! Sono l'assistente AI per l'analisi dei dati. Posso aiutarti con domande generali sull'analisi dati o guidarti nel caricamento file. Cosa posso fare per te?</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="input-group">
                            <input type="text" id="message-input" class="form-control" placeholder="Scrivi un messaggio..." aria-label="Messaggio">
                            <button type="button" id="send-button" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i>Invia
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </article>
    </section>
</main>
{% endblock %}

{% block extra_css %}
<style>
    /* Chat Container - Migliorato */
    .chat-container {
        height: 450px;
        overflow-y: auto;
        border: 2px solid #e9ecef;
        border-radius: 0.5rem;
        padding: 1.25rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        box-shadow: inset 0 2px 4px rgba(0,0,0,0.05);
        scroll-behavior: smooth;
    }

    /* Scrollbar personalizzata */
    .chat-container::-webkit-scrollbar {
        width: 8px;
    }

    .chat-container::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .chat-container::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
    }

    .chat-container::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Messaggi - Design moderno */
    .message {
        margin-bottom: 1.25rem;
        padding: 1rem 1.25rem;
        border-radius: 1rem;
        max-width: 75%;
        position: relative;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: all 0.2s ease;
    }

    .message:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    /* Messaggio utente - Blu moderno */
    .user-message {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        margin-left: auto;
        border-bottom-right-radius: 0.25rem;
    }

    .user-message::after {
        content: '';
        position: absolute;
        bottom: 0;
        right: -8px;
        width: 0;
        height: 0;
        border: 8px solid transparent;
        border-left-color: #0056b3;
        border-bottom: 0;
        border-right: 0;
    }

    /* Messaggio AI - Verde moderno */
    .ai-message {
        background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        color: white;
        margin-right: auto;
        border-bottom-left-radius: 0.25rem;
    }

    .ai-message::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: -8px;
        width: 0;
        height: 0;
        border: 8px solid transparent;
        border-right-color: #1e7e34;
        border-bottom: 0;
        border-left: 0;
    }

    /* Messaggio sistema - Grigio elegante */
    .system-message {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
        border: 2px solid #adb5bd;
        margin-left: auto;
        margin-right: auto;
        text-align: center;
        max-width: 85%;
        font-style: italic;
    }

    /* Contenuto messaggio */
    .message-content {
        word-wrap: break-word;
        line-height: 1.5;
        font-size: 0.95rem;
    }

    .message-content p:last-child {
        margin-bottom: 0;
    }

    .message-content code {
        background-color: rgba(255,255,255,0.2);
        padding: 0.2rem 0.4rem;
        border-radius: 0.25rem;
        font-family: 'Courier New', monospace;
        font-size: 0.85rem;
    }

    .message-content pre {
        background-color: rgba(255,255,255,0.1);
        padding: 0.75rem;
        border-radius: 0.5rem;
        overflow-x: auto;
        margin: 0.5rem 0;
    }

    /* Timestamp messaggi */
    .message-time {
        font-size: 0.7rem;
        color: rgba(255, 255, 255, 0.8);
        text-align: right;
        margin-top: 0.5rem;
        font-weight: 300;
    }

    /* Indicatore digitazione - Migliorato */
    .typing-indicator {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.75rem 1rem;
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        color: white;
        border-radius: 1rem;
        margin-right: auto;
        max-width: 120px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .typing-indicator::before {
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: white;
        animation: typing-dot 1.4s infinite ease-in-out;
    }

    .typing-indicator::after {
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: white;
        animation: typing-dot 1.4s infinite ease-in-out 0.2s;
        margin-left: 4px;
    }

    /* Stili modelli LLM - Migliorati */
    .free-model {
        color: #0d6efd;
        font-weight: 600;
        background: linear-gradient(135deg, rgba(13,110,253,0.1) 0%, rgba(13,110,253,0.05) 100%);
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        border: 1px solid rgba(13,110,253,0.2);
    }

    .free-quota-model {
        color: #198754;
        font-weight: 600;
        background: linear-gradient(135deg, rgba(25,135,84,0.1) 0%, rgba(25,135,84,0.05) 100%);
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        border: 1px solid rgba(25,135,84,0.2);
    }

    /* Filtri modelli - Design moderno */
    .model-filters {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 1rem;
        border-radius: 0.75rem;
        border: 2px solid #dee2e6;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        margin-bottom: 1rem;
    }

    .model-filters .form-check {
        margin-bottom: 0.5rem;
    }

    .model-filters .form-check-input:checked {
        background-color: #007bff;
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    }

    .model-filters .form-check-label {
        font-weight: 500;
        color: #495057;
    }

    /* Input area - Migliorata */
    .input-group {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border-radius: 0.5rem;
        overflow: hidden;
        transition: all 0.2s ease;
    }

    .input-group:focus-within {
        box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        transform: translateY(-1px);
    }

    .input-group .form-control {
        border: none;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
        transition: all 0.2s ease;
    }

    .input-group .form-control:focus {
        box-shadow: none;
        border-color: transparent;
    }

    .input-group .form-control.is-invalid {
        border: 2px solid #dc3545;
        animation: shake 0.5s ease-in-out;
    }

    .input-group .btn {
        border: none;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
    }

    .input-group .btn:hover:not(:disabled) {
        background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
        transform: translateY(-1px);
    }

    .input-group .btn:disabled {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        cursor: not-allowed;
    }

    .input-group .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .input-group .btn:hover::before {
        left: 100%;
    }

    /* Animazioni */
    @keyframes typing-dot {
        0%, 60%, 100% {
            transform: scale(0.8);
            opacity: 0.5;
        }
        30% {
            transform: scale(1);
            opacity: 1;
        }
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }

    /* Responsive */
    @media (max-width: 768px) {
        .message {
            max-width: 90%;
            padding: 0.75rem 1rem;
        }

        .chat-container {
            height: 350px;
            padding: 1rem;
        }

        .model-filters {
            padding: 0.75rem;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/chat.js') }}?v=1.0.0"></script>
{% endblock %}
