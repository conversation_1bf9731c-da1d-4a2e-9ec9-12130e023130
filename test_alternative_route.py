#!/usr/bin/env python3
"""
Test per la route alternativa
"""

import requests
import json

def test_alternative_route():
    """Test route alternativa"""
    
    print("🔍 TEST ROUTE ALTERNATIVA")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:5000"
    
    # Test 1: Verifica connettività
    print("1️⃣ Test connettività...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   Homepage: {response.status_code} ({'✅' if response.status_code == 200 else '❌'})")
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
        return
    
    # Test 2: Test route alternativa
    print("\n2️⃣ Test route alternativa...")
    try:
        response = requests.post(
            f"{base_url}/api/wizard-get-processed-data",
            json={"filename": "test.csv"},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Route alternativa funziona!")
            try:
                data = response.json()
                print(f"   📄 Risposta: {data.get('success', 'N/A')}")
            except:
                print("   📄 Risposta non JSON")
        elif response.status_code == 404:
            print("   ❌ Route alternativa NON registrata!")
            try:
                data = response.json()
                print(f"   📄 Errore: {data.get('error', 'N/A')}")
            except:
                print("   📄 Risposta non JSON")
        else:
            print(f"   ⚠️ Status inaspettato: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    # Test 3: Test route originale
    print("\n3️⃣ Test route originale...")
    try:
        response = requests.post(
            f"{base_url}/api/get-processed-data",
            json={"filename": "test.csv"},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Route originale ora funziona!")
            try:
                data = response.json()
                print(f"   📄 Risposta: {data.get('success', 'N/A')}")
            except:
                print("   📄 Risposta non JSON")
        elif response.status_code == 404:
            print("   ❌ Route originale ancora 404!")
            try:
                data = response.json()
                print(f"   📄 Errore: {data.get('error', 'N/A')}")
            except:
                print("   📄 Risposta non JSON")
        else:
            print(f"   ⚠️ Status inaspettato: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    # Test 4: Test route semplificata
    print("\n4️⃣ Test route semplificata...")
    try:
        response = requests.post(
            f"{base_url}/api/get-processed-data-simple",
            json={"test": True},
            headers={"Content-Type": "application/json"},
            timeout=5
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Route semplificata funziona!")
        elif response.status_code == 404:
            print("   ❌ Route semplificata NON registrata!")
        else:
            print(f"   ⚠️ Status inaspettato: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Errore: {str(e)}")
    
    print("\n" + "=" * 50)
    print("📊 RISULTATI")
    print("=" * 50)
    print("Se la route alternativa funziona ma quella originale no:")
    print("- Il problema è specifico del path /api/get-processed-data")
    print("- Potrebbe esserci un conflitto con route esistenti")
    print("- Potrebbe essere un problema di registrazione Flask")
    
    return True

if __name__ == "__main__":
    test_alternative_route()
