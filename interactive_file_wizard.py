#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
🧙‍♂️ INTERACTIVE FILE WIZARD - APP ROBERTO
Sistema di caricamento file interattivo con validazione step-by-step.

IMPLEMENTA TASK 3 DEL PROMPT:
- Caricamento file uno alla volta
- Analisi interattiva con feedback utente
- Validazione e modifica colonne
- Ciclo di revisione fino a conferma
- Normalizzazione entità guidata
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import logging

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InteractiveFileWizard:
    """
    Wizard interattivo per il caricamento e validazione file.
    Implementa il processo step-by-step richiesto nel prompt.
    """
    
    def __init__(self, supabase_manager=None, db_manager=None):
        """
        Inizializza il wizard interattivo.
        
        Args:
            supabase_manager: Istanza di SupabaseManager
            db_manager: Istanza di AdvancedDatabaseManager
        """
        self.supabase_manager = supabase_manager
        self.db_manager = db_manager
        
        # Stato del wizard
        self.current_file = None
        self.current_dataframe = None
        self.detected_columns = {}
        self.user_validated_columns = {}
        self.detected_entities = {}
        self.normalized_entities = {}
        
        # Configurazioni supportate
        self.supported_formats = ['.csv', '.xlsx', '.xls']
        self.supported_delimiters = [',', ';', '\t', '|']
        
        # Patterns per riconoscimento colonne
        self.column_patterns = {
            'employee_name': ['nome', 'dipendente', 'tecnico', 'employee', 'name', 'utente'],
            'client_name': ['cliente', 'client', 'azienda', 'company', 'ditta'],
            'project_name': ['progetto', 'project', 'commessa', 'lavoro'],
            'vehicle_plate': ['targa', 'plate', 'veicolo', 'auto', 'macchina'],
            'date': ['data', 'date', 'giorno', 'day'],
            'time_start': ['ora_inizio', 'start', 'inizio', 'from'],
            'time_end': ['ora_fine', 'end', 'fine', 'to'],
            'duration': ['durata', 'duration', 'ore', 'hours'],
            'description': ['descrizione', 'description', 'note', 'dettagli']
        }
        
        logger.info("InteractiveFileWizard inizializzato")

    def start_file_analysis(self, file_path: str, original_filename: str) -> Dict[str, Any]:
        """
        Inizia l'analisi interattiva di un file.
        
        Args:
            file_path: Percorso del file
            original_filename: Nome originale del file
            
        Returns:
            Dict con risultati dell'analisi iniziale
        """
        try:
            sys.stdout.write(f"\n🧙‍♂️ === WIZARD INTERATTIVO: {original_filename} ===\n")
            sys.stdout.flush()
            
            self.current_file = {
                'path': file_path,
                'original_name': original_filename,
                'extension': Path(file_path).suffix.lower()
            }
            
            # STEP 1: Rilevamento formato e lettura iniziale
            analysis_result = self._analyze_file_format()
            
            if not analysis_result['success']:
                return analysis_result
            
            # STEP 2: Rilevamento colonne automatico
            column_analysis = self._detect_columns_automatically()
            
            # STEP 3: Preparazione per feedback utente
            user_feedback_data = self._prepare_user_feedback_data(column_analysis)
            
            result = {
                'success': True,
                'file_info': self.current_file,
                'format_analysis': analysis_result,
                'column_analysis': column_analysis,
                'user_feedback_required': True,
                'feedback_data': user_feedback_data,
                'next_step': 'user_validation',
                'timestamp': datetime.now().isoformat()
            }
            
            sys.stdout.write(f"✅ Analisi iniziale completata per {original_filename}\n")
            sys.stdout.flush()
            
            return result
            
        except Exception as e:
            logger.error(f"Errore analisi file: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'file_info': self.current_file,
                'next_step': 'error_handling'
            }

    def _analyze_file_format(self) -> Dict[str, Any]:
        """
        Analizza il formato del file e tenta la lettura.
        
        Returns:
            Dict con risultati dell'analisi formato
        """
        try:
            file_path = self.current_file['path']
            extension = self.current_file['extension']
            
            sys.stdout.write(f"🔍 Analisi formato file: {extension}\n")
            sys.stdout.flush()
            
            if extension not in self.supported_formats:
                return {
                    'success': False,
                    'error': f"Formato {extension} non supportato",
                    'supported_formats': self.supported_formats
                }
            
            # Tentativo di lettura basato sull'estensione
            if extension == '.csv':
                return self._analyze_csv_format(file_path)
            elif extension in ['.xlsx', '.xls']:
                return self._analyze_excel_format(file_path)
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Errore analisi formato: {str(e)}"
            }

    def _analyze_csv_format(self, file_path: str) -> Dict[str, Any]:
        """
        Analizza un file CSV per rilevare delimitatore e header.
        
        Args:
            file_path: Percorso del file CSV
            
        Returns:
            Dict con risultati analisi CSV
        """
        try:
            # Leggi le prime righe per analisi
            with open(file_path, 'r', encoding='utf-8') as f:
                sample_lines = [f.readline().strip() for _ in range(5)]
            
            # Rileva delimitatore
            delimiter_scores = {}
            for delimiter in self.supported_delimiters:
                score = sum(line.count(delimiter) for line in sample_lines)
                delimiter_scores[delimiter] = score
            
            best_delimiter = max(delimiter_scores, key=delimiter_scores.get)
            
            sys.stdout.write(f"🔍 Delimitatore rilevato: '{best_delimiter}'\n")
            sys.stdout.flush()
            
            # Prova a leggere con il delimitatore rilevato
            try:
                df = pd.read_csv(file_path, delimiter=best_delimiter, nrows=10)
                header_row = 0
                
                # Verifica se la prima riga sembra un header
                first_row_is_header = self._check_if_header(df.iloc[0] if len(df) > 0 else None)
                
                if not first_row_is_header:
                    # Prova a cercare header nelle prime righe
                    for i in range(min(3, len(df))):
                        if self._check_if_header(df.iloc[i]):
                            header_row = i
                            break
                
                # Rileggi con header corretto
                df = pd.read_csv(file_path, delimiter=best_delimiter, header=header_row)
                self.current_dataframe = df
                
                return {
                    'success': True,
                    'format': 'CSV',
                    'delimiter': best_delimiter,
                    'header_row': header_row,
                    'total_rows': len(df),
                    'total_columns': len(df.columns),
                    'sample_data': df.head(5).to_dict('records'),
                    'column_names': list(df.columns)
                }
                
            except Exception as read_error:
                return {
                    'success': False,
                    'error': f"Errore lettura CSV: {str(read_error)}",
                    'delimiter_scores': delimiter_scores
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"Errore analisi CSV: {str(e)}"
            }

    def _analyze_excel_format(self, file_path: str) -> Dict[str, Any]:
        """
        Analizza un file Excel.
        
        Args:
            file_path: Percorso del file Excel
            
        Returns:
            Dict con risultati analisi Excel
        """
        try:
            # Leggi il primo foglio
            df = pd.read_excel(file_path, nrows=10)
            
            # Cerca header nelle prime righe
            header_row = 0
            for i in range(min(3, len(df))):
                if self._check_if_header(df.iloc[i]):
                    header_row = i
                    break
            
            # Rileggi con header corretto
            df = pd.read_excel(file_path, header=header_row)
            self.current_dataframe = df
            
            return {
                'success': True,
                'format': 'Excel',
                'header_row': header_row,
                'total_rows': len(df),
                'total_columns': len(df.columns),
                'sample_data': df.head(5).to_dict('records'),
                'column_names': list(df.columns)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"Errore analisi Excel: {str(e)}"
            }

    def _check_if_header(self, row) -> bool:
        """
        Verifica se una riga sembra essere un header.
        
        Args:
            row: Riga da verificare
            
        Returns:
            True se sembra un header
        """
        if row is None:
            return False
        
        # Converti in stringa e controlla
        row_str = [str(val).lower() for val in row if pd.notna(val)]
        
        # Cerca pattern tipici di header
        header_indicators = ['nome', 'data', 'ora', 'cliente', 'progetto', 'descrizione', 
                           'name', 'date', 'time', 'client', 'project', 'description']
        
        matches = sum(1 for cell in row_str for indicator in header_indicators if indicator in cell)
        
        return matches >= 2  # Almeno 2 match per considerarlo header

    def _detect_columns_automatically(self) -> Dict[str, Any]:
        """
        Rileva automaticamente il tipo delle colonne.
        
        Returns:
            Dict con analisi delle colonne
        """
        try:
            if self.current_dataframe is None:
                return {'success': False, 'error': 'Nessun dataframe caricato'}
            
            df = self.current_dataframe
            detected_columns = {}
            
            sys.stdout.write(f"🔍 Rilevamento automatico colonne...\n")
            sys.stdout.flush()
            
            for col in df.columns:
                col_analysis = self._analyze_single_column(col, df[col])
                detected_columns[col] = col_analysis
                
                sys.stdout.write(f"   📊 {col}: {col_analysis['detected_type']} (confidenza: {col_analysis['confidence']:.2f})\n")
                sys.stdout.flush()
            
            self.detected_columns = detected_columns
            
            return {
                'success': True,
                'detected_columns': detected_columns,
                'total_columns': len(detected_columns),
                'high_confidence_count': len([c for c in detected_columns.values() if c['confidence'] > 0.7])
            }
            
        except Exception as e:
            logger.error(f"Errore rilevamento colonne: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def _analyze_single_column(self, column_name: str, column_data: pd.Series) -> Dict[str, Any]:
        """
        Analizza una singola colonna per rilevarne il tipo.
        
        Args:
            column_name: Nome della colonna
            column_data: Dati della colonna
            
        Returns:
            Dict con analisi della colonna
        """
        col_name_lower = column_name.lower()
        
        # Analisi pattern nome colonna
        detected_type = 'unknown'
        confidence = 0.0
        
        for pattern_type, patterns in self.column_patterns.items():
            for pattern in patterns:
                if pattern in col_name_lower:
                    detected_type = pattern_type
                    confidence = 0.8
                    break
            if confidence > 0:
                break
        
        # Analisi contenuto colonna
        content_analysis = self._analyze_column_content(column_data)
        
        # Se non trovato dal nome, usa analisi contenuto
        if confidence == 0:
            if content_analysis['is_date']:
                detected_type = 'date'
                confidence = 0.6
            elif content_analysis['is_time']:
                detected_type = 'time'
                confidence = 0.6
            elif content_analysis['is_numeric']:
                detected_type = 'numeric'
                confidence = 0.5
            else:
                detected_type = 'text'
                confidence = 0.3
        
        return {
            'original_name': column_name,
            'detected_type': detected_type,
            'confidence': confidence,
            'content_analysis': content_analysis,
            'sample_values': column_data.dropna().head(3).tolist(),
            'null_count': column_data.isnull().sum(),
            'unique_count': column_data.nunique()
        }

    def _analyze_column_content(self, column_data: pd.Series) -> Dict[str, Any]:
        """
        Analizza il contenuto di una colonna.
        
        Args:
            column_data: Dati della colonna
            
        Returns:
            Dict con analisi del contenuto
        """
        non_null_data = column_data.dropna()
        
        if len(non_null_data) == 0:
            return {
                'is_date': False,
                'is_time': False,
                'is_numeric': False,
                'data_type': 'empty'
            }
        
        # Test per date
        date_count = 0
        for val in non_null_data.head(10):
            try:
                pd.to_datetime(str(val))
                date_count += 1
            except:
                pass
        
        is_date = date_count / min(len(non_null_data), 10) > 0.7
        
        # Test per numeri
        numeric_count = 0
        for val in non_null_data.head(10):
            try:
                float(str(val).replace(',', '.'))
                numeric_count += 1
            except:
                pass
        
        is_numeric = numeric_count / min(len(non_null_data), 10) > 0.7
        
        # Test per orari
        time_patterns = [':', 'h', 'H', 'am', 'pm', 'AM', 'PM']
        time_count = sum(1 for val in non_null_data.head(10) 
                        if any(pattern in str(val) for pattern in time_patterns))
        
        is_time = time_count / min(len(non_null_data), 10) > 0.5
        
        return {
            'is_date': is_date,
            'is_time': is_time,
            'is_numeric': is_numeric,
            'data_type': column_data.dtype.name
        }

    def _prepare_user_feedback_data(self, column_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepara i dati per il feedback utente.
        
        Args:
            column_analysis: Risultati analisi colonne
            
        Returns:
            Dict con dati per feedback utente
        """
        if not column_analysis.get('success'):
            return {'error': 'Analisi colonne fallita'}
        
        feedback_data = {
            'file_info': self.current_file,
            'sample_data': self.current_dataframe.head(5).to_dict('records') if self.current_dataframe is not None else [],
            'detected_columns': self.detected_columns,
            'suggestions': self._generate_column_suggestions(),
            'validation_questions': self._generate_validation_questions(),
            'available_types': list(self.column_patterns.keys()) + ['date', 'time', 'numeric', 'text', 'ignore']
        }
        
        return feedback_data

    def _generate_column_suggestions(self) -> List[Dict[str, Any]]:
        """
        Genera suggerimenti per le colonne rilevate.
        
        Returns:
            Lista di suggerimenti
        """
        suggestions = []
        
        for col_name, col_info in self.detected_columns.items():
            suggestion = {
                'column': col_name,
                'current_type': col_info['detected_type'],
                'confidence': col_info['confidence'],
                'suggestion': self._get_column_suggestion(col_info),
                'requires_attention': col_info['confidence'] < 0.6
            }
            suggestions.append(suggestion)
        
        return suggestions

    def _get_column_suggestion(self, col_info: Dict[str, Any]) -> str:
        """
        Genera un suggerimento per una colonna.
        
        Args:
            col_info: Informazioni sulla colonna
            
        Returns:
            Stringa con suggerimento
        """
        if col_info['confidence'] > 0.7:
            return f"Rilevato come '{col_info['detected_type']}' con alta confidenza"
        elif col_info['confidence'] > 0.4:
            return f"Probabilmente '{col_info['detected_type']}', verifica se corretto"
        else:
            return f"Tipo incerto, specificare manualmente"

    def _generate_validation_questions(self) -> List[Dict[str, Any]]:
        """
        Genera domande di validazione per l'utente.
        
        Returns:
            Lista di domande
        """
        questions = [
            {
                'id': 'format_correct',
                'question': 'Il file è stato letto correttamente?',
                'type': 'yes_no',
                'context': 'Verifica che i dati mostrati corrispondano al contenuto del file'
            },
            {
                'id': 'columns_correct',
                'question': 'Le colonne sono state rilevate correttamente?',
                'type': 'yes_no',
                'context': 'Controlla che i nomi e tipi delle colonne siano giusti'
            },
            {
                'id': 'need_modifications',
                'question': 'Vuoi modificare qualcosa?',
                'type': 'yes_no',
                'context': 'Delimitatore, header, nomi colonne, tipi di dato'
            }
        ]
        
        return questions


def main():
    """Funzione di test per il wizard interattivo."""
    wizard = InteractiveFileWizard()
    
    print("🧙‍♂️ INTERACTIVE FILE WIZARD - TEST")
    print("=" * 50)
    
    # Test con file di esempio
    test_file = "test_data.csv"
    if os.path.exists(test_file):
        result = wizard.start_file_analysis(test_file, "test_data.csv")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        print("⚠️ File di test non trovato")


if __name__ == "__main__":
    main()
