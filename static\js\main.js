/**
 * Script principale per l'applicazione di analisi dati
 */

// Funzione per formattare le date in formato italiano
function formatDateIT(dateStr) {
    if (!dateStr) return '';
    
    // Supporta diversi formati di input
    let date;
    
    // Prova formato ISO (YYYY-MM-DD)
    if (dateStr.includes('-')) {
        date = new Date(dateStr);
    } 
    // Prova formato italiano (DD/MM/YYYY)
    else if (dateStr.includes('/')) {
        const parts = dateStr.split('/');
        // Assicurati che ci siano 3 parti
        if (parts.length === 3) {
            // Se la prima parte è un giorno (1-31), assume formato italiano
            if (parseInt(parts[0]) >= 1 && parseInt(parts[0]) <= 31) {
                date = new Date(parts[2], parts[1] - 1, parts[0]);
            } else {
                // Altrimenti prova formato americano (MM/DD/YYYY)
                date = new Date(dateStr);
            }
        } else {
            date = new Date(dateStr);
        }
    } else {
        // Altri formati
        date = new Date(dateStr);
    }
    
    // Verifica se la data è valida
    if (isNaN(date.getTime())) {
        return dateStr; // Restituisci la stringa originale se non è una data valida
    }
    
    // Formatta in italiano (DD/MM/YYYY)
    return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
}

// Funzione per formattare i numeri in formato italiano
function formatNumberIT(num) {
    if (num === null || num === undefined || isNaN(num)) return '';
    
    return num.toString().replace('.', ',');
}

// Funzione per convertire durate in minuti
function parseDuration(durationStr) {
    if (!durationStr) return 0;
    
    // Se è già un numero, restituiscilo
    if (!isNaN(durationStr)) {
        return parseInt(durationStr);
    }
    
    // Formato "1h 30m"
    if (durationStr.includes('h') || durationStr.includes('m')) {
        let hours = 0;
        let minutes = 0;
        
        // Estrai ore
        const hoursMatch = durationStr.match(/(\d+)h/);
        if (hoursMatch) {
            hours = parseInt(hoursMatch[1]);
        }
        
        // Estrai minuti
        const minutesMatch = durationStr.match(/(\d+)m/);
        if (minutesMatch) {
            minutes = parseInt(minutesMatch[1]);
        }
        
        return hours * 60 + minutes;
    }
    
    // Formato semplice (solo minuti)
    return parseInt(durationStr);
}

// Funzione per calcolare statistiche di base
function calculateStats(data, field) {
    if (!data || !data.length) return { min: 0, max: 0, avg: 0, sum: 0, count: 0 };
    
    const values = data.map(item => {
        const val = item[field];
        return !isNaN(val) ? parseFloat(val) : 0;
    }).filter(val => val !== 0);
    
    if (!values.length) return { min: 0, max: 0, avg: 0, sum: 0, count: 0 };
    
    const sum = values.reduce((acc, val) => acc + val, 0);
    
    return {
        min: Math.min(...values),
        max: Math.max(...values),
        avg: sum / values.length,
        sum: sum,
        count: values.length
    };
}

// Funzione per contare valori unici
function countUniqueValues(data, field) {
    if (!data || !data.length) return {};
    
    const counts = {};
    
    data.forEach(item => {
        const value = item[field];
        if (value) {
            counts[value] = (counts[value] || 0) + 1;
        }
    });
    
    return counts;
}

// Funzione per filtrare i dati
function filterData(data, filters) {
    if (!data || !data.length) return [];
    
    return data.filter(item => {
        // Filtra per data
        if (filters.startDate && filters.endDate) {
            const itemDate = new Date(item.date);
            const startDate = new Date(filters.startDate);
            const endDate = new Date(filters.endDate);
            
            if (itemDate < startDate || itemDate > endDate) {
                return false;
            }
        }
        
        // Filtra per tecnico
        if (filters.technician && item.technician !== filters.technician) {
            return false;
        }
        
        // Filtra per tipo di attività
        if (filters.activityType && item.activityType !== filters.activityType) {
            return false;
        }
        
        return true;
    });
}

// Funzione per aggiornare la dashboard
function updateDashboard(data) {
    if (!data || !data.length) {
        console.log('Nessun dato disponibile per aggiornare la dashboard');
        return;
    }
    
    // Aggiorna i KPI
    document.getElementById('total-sessions').textContent = data.length;
    
    // Calcola durata media
    const durationStats = calculateStats(data, 'duration');
    document.getElementById('avg-duration').textContent = Math.round(durationStats.avg) + ' min';
    
    // Conta tecnici unici
    const technicians = countUniqueValues(data, 'technician');
    document.getElementById('active-technicians').textContent = Object.keys(technicians).length;
    
    // Calcola tasso di soddisfazione (esempio)
    document.getElementById('satisfaction-rate').textContent = '4.8/5';
    
    // Qui aggiungeremo il codice per i grafici quando avremo i dati reali
}

// Inizializzazione quando il documento è pronto
document.addEventListener('DOMContentLoaded', function() {
    console.log('Applicazione inizializzata');
    
    // Qui aggiungeremo il codice per caricare i dati e inizializzare la dashboard
});
