#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test per verificare che la correzione del riconoscimento permessi funzioni.
"""

import pandas as pd
import sys
import os
from enhanced_file_detector import EnhancedFileDetector

def test_correzione_permessi():
    """Testa la correzione per il riconoscimento dei file di permessi."""
    
    print("🔧 TEST CORREZIONE RICONOSCIMENTO PERMESSI")
    print("=" * 50)
    
    # Crea un DataFrame di esempio che simula un file di permessi
    permessi_data = {
        'Data della richiesta': ['2025-03-25 10:14:45', '2025-04-01 10:16:50', '2025-04-01 21:02:55'],
        'Dipendente': ['Hoxha, <PERSON>rlin<PERSON>', '<PERSON><PERSON>, <PERSON>', '<PERSON><PERSON>, <PERSON>'],
        'Tipo': ['Ferie', 'Ferie', 'ROL'],
        'Data inizio': ['28/04/2025', '02/05/2025', '05/05/2025'],
        'Data fine': ['05/05/2025', '02/05/2025', '05/05/2025'],
        'Stato': ['Approvata', 'Approvata', 'Approvata'],
        'Note': ['Causa dovrò sottopormi ad una operazione', '', '']
    }
    
    df = pd.DataFrame(permessi_data)
    
    print("📊 DATI DI TEST:")
    print(f"Dimensioni: {df.shape[0]} righe x {df.shape[1]} colonne")
    print(f"Colonne: {df.columns.tolist()}")
    print()
    
    # Test 1: Senza nome file (comportamento originale)
    print("🧪 TEST 1: Riconoscimento SENZA nome file")
    print("-" * 40)
    
    detector = EnhancedFileDetector()
    detected_type_1, confidence_1, scores_1 = detector.detect_file_type(df)
    
    print(f"Tipo rilevato: {detected_type_1}")
    print(f"Confidenza: {confidence_1:.3f}")
    print("Top 3 punteggi:")
    sorted_scores_1 = sorted(scores_1.items(), key=lambda x: x[1], reverse=True)[:3]
    for tipo, punteggio in sorted_scores_1:
        print(f"  {tipo}: {punteggio:.3f}")
    print()
    
    # Test 2: Con nome file di permessi (comportamento corretto)
    print("🧪 TEST 2: Riconoscimento CON nome file di permessi")
    print("-" * 40)
    
    filename_permessi = "apprilevazionepresenze-richieste-2025-05-01-2025-05-31_3.xlsx"
    detected_type_2, confidence_2, scores_2 = detector.detect_file_type(df, filename_permessi)
    
    print(f"Nome file: {filename_permessi}")
    print(f"Tipo rilevato: {detected_type_2}")
    print(f"Confidenza: {confidence_2:.3f}")
    print("Top 3 punteggi:")
    sorted_scores_2 = sorted(scores_2.items(), key=lambda x: x[1], reverse=True)[:3]
    for tipo, punteggio in sorted_scores_2:
        print(f"  {tipo}: {punteggio:.3f}")
    print()
    
    # Test 3: Con nome file generico (comportamento normale)
    print("🧪 TEST 3: Riconoscimento CON nome file generico")
    print("-" * 40)
    
    filename_generico = "dati_export_2025.xlsx"
    detected_type_3, confidence_3, scores_3 = detector.detect_file_type(df, filename_generico)
    
    print(f"Nome file: {filename_generico}")
    print(f"Tipo rilevato: {detected_type_3}")
    print(f"Confidenza: {confidence_3:.3f}")
    print("Top 3 punteggi:")
    sorted_scores_3 = sorted(scores_3.items(), key=lambda x: x[1], reverse=True)[:3]
    for tipo, punteggio in sorted_scores_3:
        print(f"  {tipo}: {punteggio:.3f}")
    print()
    
    # Analisi risultati
    print("📋 ANALISI RISULTATI")
    print("=" * 50)
    
    print(f"Test 1 (senza filename): {detected_type_1} (confidenza: {confidence_1:.3f})")
    print(f"Test 2 (con filename permessi): {detected_type_2} (confidenza: {confidence_2:.3f})")
    print(f"Test 3 (con filename generico): {detected_type_3} (confidenza: {confidence_3:.3f})")
    print()
    
    # Verifica correzione
    if detected_type_2 == "permessi" and confidence_2 > 0.9:
        print("✅ CORREZIONE FUNZIONA!")
        print("   - Il file con nome 'apprilevazionepresenze-richieste' viene riconosciuto come 'permessi'")
        print(f"   - Confidenza alta: {confidence_2:.3f}")
        print("   - Il sistema ora riconosce correttamente i file di permessi dal nome")
        success = True
    else:
        print("❌ CORREZIONE NON FUNZIONA")
        print(f"   - Tipo rilevato: {detected_type_2} (dovrebbe essere 'permessi')")
        print(f"   - Confidenza: {confidence_2:.3f} (dovrebbe essere > 0.9)")
        success = False
    
    # Verifica che non influenzi altri file
    if detected_type_3 != "permessi" or confidence_3 < 0.9:
        print("✅ CORREZIONE NON INFLUENZA ALTRI FILE")
        print("   - File con nome generico non viene forzato come 'permessi'")
    else:
        print("⚠️ CORREZIONE POTREBBE INFLUENZARE ALTRI FILE")
        success = False
    
    print()
    
    # Test aggiuntivo: File con colonne diverse
    print("🧪 TEST 4: File con colonne NON di permessi ma nome di permessi")
    print("-" * 40)
    
    # Crea un DataFrame che NON è di permessi
    fake_data = {
        'Auto': ['Fiat 500', 'BMW X3', 'Audi A4'],
        'Cliente': ['Cliente A', 'Cliente B', 'Cliente C'],
        'Data Presa': ['2025-01-01', '2025-01-02', '2025-01-03'],
        'Km': [100, 200, 150]
    }
    
    df_fake = pd.DataFrame(fake_data)
    detected_type_4, confidence_4, scores_4 = detector.detect_file_type(df_fake, filename_permessi)
    
    print(f"Colonne fake: {df_fake.columns.tolist()}")
    print(f"Tipo rilevato: {detected_type_4}")
    print(f"Confidenza: {confidence_4:.3f}")
    
    if detected_type_4 != "permessi":
        print("✅ CORREZIONE È INTELLIGENTE")
        print("   - Non forza 'permessi' se le colonne non corrispondono")
    else:
        print("⚠️ CORREZIONE TROPPO AGGRESSIVA")
        print("   - Forza 'permessi' anche senza colonne appropriate")
        success = False
    
    return success

def main():
    """Esegue il test completo."""
    success = test_correzione_permessi()
    
    print("\n" + "=" * 50)
    print("🎯 RISULTATO FINALE")
    print("=" * 50)
    
    if success:
        print("🎉 CORREZIONE IMPLEMENTATA CON SUCCESSO!")
        print("✅ Il sistema ora riconosce correttamente i file di permessi")
        print("✅ La correzione è intelligente e non influenza altri file")
        print("✅ Il problema di classificazione errata è risolto")
        print("\n💡 Il file 'apprilevazionepresenze-richieste-*' sarà ora")
        print("   classificato correttamente come 'permessi' invece di 'registro_auto'")
    else:
        print("❌ CORREZIONE NON FUNZIONA CORRETTAMENTE")
        print("🔧 Necessarie ulteriori modifiche al sistema")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
