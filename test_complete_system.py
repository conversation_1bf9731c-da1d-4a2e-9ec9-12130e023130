#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test completo del sistema app-roberto con tutte le nuove funzionalità.
Verifica l'integrazione di tutti i componenti: Supabase, Agenti AI, Automazione.
"""

import os
import sys
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_system_status():
    """
    Testa lo stato generale del sistema.
    """
    print("🔍 Test stato generale del sistema...")
    
    try:
        from system_status_dashboard import system_dashboard
        
        # Verifica stato sistema
        system_dashboard.check_all_systems()
        system_dashboard.print_status_report()
        
        print("✅ Test stato sistema completato")
        return True
        
    except Exception as e:
        print(f"❌ Errore test stato sistema: {str(e)}")
        return False

def test_supabase_integration():
    """
    Testa l'integrazione Supabase.
    """
    print("\n🗄️ Test integrazione Supabase...")
    
    try:
        from supabase_integration import supabase_manager
        
        if not supabase_manager.is_connected:
            print("⚠️ Supabase non connesso")
            return False
        
        # Test connessione
        connection_test = supabase_manager.test_connection()
        if connection_test:
            print("✅ Connessione Supabase OK")
        else:
            print("❌ Test connessione Supabase fallito")
            return False
        
        # Test operazioni base
        test_file_info = {
            "filename": f"test_system_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
            "original_filename": "test_system.xlsx",
            "file_type": "test",
            "file_path": "/test/path/test_system.xlsx",
            "file_size": 2048,
            "status": "test",
            "session_id": f"test_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        }
        
        # Test inserimento
        file_id = supabase_manager.save_file_upload(test_file_info)
        if file_id:
            print(f"✅ Test inserimento file: ID {file_id}")
            
            # Test lettura
            recent_files = supabase_manager.get_file_uploads(limit=5)
            if recent_files:
                print(f"✅ Test lettura file: {len(recent_files)} file trovati")
            else:
                print("⚠️ Nessun file trovato")
            
            return True
        else:
            print("❌ Test inserimento file fallito")
            return False
        
    except Exception as e:
        print(f"❌ Errore test Supabase: {str(e)}")
        return False

def test_enhanced_config():
    """
    Testa il sistema di configurazione avanzato.
    """
    print("\n⚙️ Test Enhanced Config Manager...")
    
    try:
        from enhanced_config_manager import enhanced_config_manager
        
        # Test informazioni sistema
        system_info = enhanced_config_manager.get_system_info()
        print(f"📊 Informazioni sistema: {system_info}")
        
        # Test configurazione
        test_key = "test_complete_system.test_value"
        test_value = f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Test set
        success = enhanced_config_manager.set_advanced(test_key, test_value, save_immediately=False)
        if success:
            print(f"✅ Test set configurazione: {test_key} = {test_value}")
        else:
            print("❌ Test set configurazione fallito")
            return False
        
        # Test get
        retrieved_value = enhanced_config_manager.get_advanced(test_key)
        if retrieved_value == test_value:
            print(f"✅ Test get configurazione: {retrieved_value}")
        else:
            print(f"❌ Test get configurazione fallito: atteso {test_value}, ottenuto {retrieved_value}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Errore test Enhanced Config: {str(e)}")
        return False

async def test_ai_agents():
    """
    Testa il framework degli agenti AI.
    """
    print("\n🤖 Test Agenti AI...")
    
    try:
        from ai_agents_framework import agent_orchestrator
        
        # Test stato agenti
        agents_status = agent_orchestrator.get_agents_status()
        print(f"📊 Stato agenti: {len(agents_status)} agenti disponibili")
        
        for agent_name, status in agents_status.items():
            print(f"   {agent_name}: {'✅' if status['is_active'] else '❌'} {status['description']}")
        
        # Test esecuzione task su agente di elaborazione dati
        test_task = {
            "data": {"test": "data", "rows": 100},
            "operation": "analyze"
        }
        
        result = await agent_orchestrator.execute_task("data_processing", test_task)
        if result.get("success"):
            print(f"✅ Test agente data_processing: {result.get('analysis', {}).get('summary', 'OK')}")
        else:
            print(f"❌ Test agente data_processing fallito: {result.get('error')}")
            return False
        
        # Test generazione report
        report_task = {
            "data": {"test_data": [1, 2, 3, 4, 5]},
            "report_type": "test_summary"
        }
        
        result = await agent_orchestrator.execute_task("report_generation", report_task)
        if result.get("success"):
            print(f"✅ Test agente report_generation: {result.get('report', {}).get('type', 'OK')}")
        else:
            print(f"❌ Test agente report_generation fallito: {result.get('error')}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Errore test Agenti AI: {str(e)}")
        return False

async def test_intelligent_automation():
    """
    Testa il sistema di automazione intelligente.
    """
    print("\n🔄 Test Automazione Intelligente...")
    
    try:
        from intelligent_automation import intelligent_automation
        
        # Test stato automazione
        stats = intelligent_automation.get_automation_stats()
        print(f"📊 Statistiche automazione: {stats['total_rules']} regole, {stats['active_rules']} attive")
        
        # Test lista regole
        rules = intelligent_automation.list_rules()
        print(f"📋 Regole disponibili:")
        for rule in rules:
            print(f"   {rule['rule_id']}: {rule['name']} ({'✅' if rule['is_active'] else '❌'})")
        
        # Test trigger evento
        test_event_data = {
            "file_path": "/test/path/test_file.xlsx",
            "file_extension": "xlsx",
            "rows": 150,
            "data_size": 100
        }
        
        results = await intelligent_automation.trigger_event("file_upload", test_event_data)
        print(f"✅ Test trigger evento: {len(results)} regole attivate")
        
        for result in results:
            if result.get("success"):
                print(f"   ✅ Regola {result.get('rule_id')}: OK")
            else:
                print(f"   ❌ Regola {result.get('rule_id')}: {result.get('error')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore test Automazione: {str(e)}")
        return False

def test_real_file_analyzer():
    """
    Testa l'Enhanced Real File Analyzer.
    """
    print("\n🔍 Test Enhanced Real File Analyzer...")
    
    try:
        from enhanced_real_file_analyzer import enhanced_analyzer
        
        # Crea un file di test temporaneo
        import pandas as pd
        import tempfile
        
        # Crea dati di test
        test_data = {
            'Data': ['2024-01-01', '2024-01-02', '2024-01-03'],
            'Dipendente': ['Mario Rossi', 'Giulia Bianchi', 'Luca Verdi'],
            'Ore': [8.0, 7.5, 8.0],
            'Progetto': ['Progetto A', 'Progetto B', 'Progetto A']
        }
        
        df = pd.DataFrame(test_data)
        
        # Salva in un file temporaneo
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            temp_file_path = f.name
        
        try:
            # Test analisi avanzata
            result = enhanced_analyzer.analyze_file_enhanced(
                file_path=temp_file_path,
                save_to_cloud=False  # Non salvare su cloud per il test
            )
            
            if result.get("success"):
                print(f"✅ Test analisi file: {result.get('detected_type')} (confidenza: {result.get('confidence_score', 0):.2f})")
                
                # Verifica presenza di informazioni avanzate
                if "ai_insights" in result:
                    print(f"   🤖 AI Insights: {'✅' if result['ai_insights'].get('available') else '❌'}")
                
                if "cloud_storage" in result:
                    print(f"   ☁️ Cloud Storage: {'✅' if result['cloud_storage'].get('saved') else '❌'}")
                
                if "performance_metrics" in result:
                    print(f"   📊 Performance Metrics: ✅")
                
            else:
                print(f"❌ Test analisi file fallito: {result.get('error')}")
                return False
            
        finally:
            # Pulisci il file temporaneo
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
        
        return True
        
    except Exception as e:
        print(f"❌ Errore test Real File Analyzer: {str(e)}")
        return False

async def test_complete_workflow():
    """
    Testa un workflow completo del sistema.
    """
    print("\n🔄 Test Workflow Completo...")
    
    try:
        from ai_agents_framework import agent_orchestrator
        
        # Workflow: Analisi file -> Elaborazione dati -> Generazione report
        workflow = [
            {
                "agent_name": "data_processing",
                "task_data": {
                    "data": {"sample": "data", "rows": 200},
                    "operation": "clean"
                }
            },
            {
                "agent_name": "data_processing", 
                "task_data": {
                    "data": {"cleaned": "data", "rows": 195},
                    "operation": "analyze"
                }
            },
            {
                "agent_name": "report_generation",
                "task_data": {
                    "data": {"analyzed": "data"},
                    "report_type": "workflow_summary"
                }
            }
        ]
        
        results = await agent_orchestrator.process_workflow(workflow)
        
        print(f"📊 Workflow completato: {len(results)} step eseguiti")
        
        success_count = sum(1 for r in results if r.get("success"))
        print(f"✅ Step riusciti: {success_count}/{len(results)}")
        
        if success_count == len(results):
            print("🎉 Workflow completo eseguito con successo!")
            return True
        else:
            print("⚠️ Alcuni step del workflow sono falliti")
            return False
        
    except Exception as e:
        print(f"❌ Errore test workflow: {str(e)}")
        return False

async def main():
    """
    Esegue tutti i test del sistema completo.
    """
    print("🚀 AVVIO TEST COMPLETO SISTEMA APP-ROBERTO")
    print("=" * 60)
    
    tests = [
        ("Stato Sistema", test_system_status),
        ("Integrazione Supabase", test_supabase_integration),
        ("Enhanced Config Manager", test_enhanced_config),
        ("Agenti AI", test_ai_agents),
        ("Automazione Intelligente", test_intelligent_automation),
        ("Enhanced Real File Analyzer", test_real_file_analyzer),
        ("Workflow Completo", test_complete_workflow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Errore durante {test_name}: {str(e)}")
            results.append((test_name, False))
    
    # Riepilogo risultati
    print("\n" + "=" * 60)
    print("📊 RIEPILOGO TEST COMPLETO")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Risultato finale: {passed}/{total} test superati")
    
    if passed == total:
        print("🎉 TUTTI I TEST SONO STATI SUPERATI!")
        print("💡 Il sistema app-roberto è completamente operativo")
        print("\n🚀 FUNZIONALITÀ DISPONIBILI:")
        print("   ✅ Real File Analyzer con AI")
        print("   ✅ Integrazione Supabase cloud")
        print("   ✅ Agenti AI specializzati")
        print("   ✅ Automazione intelligente")
        print("   ✅ Configurazioni avanzate")
        print("   ✅ Monitoraggio sistema")
    else:
        print("⚠️ ALCUNI TEST SONO FALLITI")
        print("💡 Verifica la configurazione e riprova")
    
    return passed == total

if __name__ == "__main__":
    # Imposta variabili d'ambiente per il test
    os.environ.setdefault("SUPABASE_URL", "https://zqjllwxqjxjhdkbcawfr.supabase.co")
    os.environ.setdefault("SUPABASE_KEY", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxamxsd3hxanhqaGRrYmNhd2ZyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc4MjE4NzQsImV4cCI6MjA2MzM5Nzg3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8")
    
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
