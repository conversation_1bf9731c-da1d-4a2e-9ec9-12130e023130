#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test per il modulo file_detector.py.
"""

import pytest
import pandas as pd
from io import StringIO

from file_detector import FileTypeDetector

class TestFileTypeDetector:
    """Test per la classe FileTypeDetector."""
    
    def setup_method(self):
        """Setup per i test."""
        self.detector = FileTypeDetector()
    
    def test_init(self):
        """Test per il costruttore."""
        assert isinstance(self.detector, FileTypeDetector)
        assert hasattr(self.detector, 'FILE_SIGNATURES')
        assert hasattr(self.detector, 'MIN_MATCH_THRESHOLD')
    
    def test_normalize_column_name(self):
        """Test per il metodo _normalize_column_name."""
        assert self.detector._normalize_column_name("Colonna") == "colonna"
        assert self.detector._normalize_column_name(" Colonna ") == "colonna"
        assert self.detector._normalize_column_name("COLONNA") == "colonna"
        assert self.detector._normalize_column_name(123) == "123"
    
    def test_calculate_column_similarity(self):
        """Test per il metodo _calculate_column_similarity."""
        # Corrispondenza esatta
        assert self.detector._calculate_column_similarity("colonna", "colonna") == 1.0
        
        # Una colonna è contenuta nell'altra
        assert self.detector._calculate_column_similarity("colonna", "colonna_estesa") == 0.8
        
        # Similarità parziale
        similarity = self.detector._calculate_column_similarity("colonna", "colonna_diversa")
        assert 0.0 < similarity < 1.0
        
        # Nessuna similarità
        similarity = self.detector._calculate_column_similarity("colonna", "xyz")
        assert similarity < 0.5
    
    def test_calculate_match_score(self):
        """Test per il metodo _calculate_match_score."""
        # Crea una firma di test
        test_signature = {
            "required_columns": ["colonna1", "colonna2"],
            "optional_columns": ["colonna3"],
            "weight": {
                "colonna1": 1.0,
                "colonna2": 1.0,
                "colonna3": 0.5
            }
        }
        
        # Corrispondenza perfetta
        df_columns = ["colonna1", "colonna2", "colonna3"]
        score = self.detector._calculate_match_score(df_columns, test_signature)
        assert score == 1.0
        
        # Corrispondenza parziale (manca una colonna opzionale)
        df_columns = ["colonna1", "colonna2"]
        score = self.detector._calculate_match_score(df_columns, test_signature)
        assert 0.7 < score < 0.9
        
        # Corrispondenza parziale (manca una colonna richiesta)
        df_columns = ["colonna1", "colonna3"]
        score = self.detector._calculate_match_score(df_columns, test_signature)
        assert score < 0.7
        
        # Nessuna corrispondenza
        df_columns = ["colonna4", "colonna5"]
        score = self.detector._calculate_match_score(df_columns, test_signature)
        assert score == 0.0
    
    def test_detect_file_type_attivita(self):
        """Test per il metodo detect_file_type con un file di attività."""
        # Crea un DataFrame di test per un file di attività
        data = """
        Contratto,Id Ticket,Iniziata il,Conclusa il,Durata,Creato da,Descrizione,Stato,Tipo
        Contratto A,123,01/01/2023 09:00,01/01/2023 10:30,1:30,Utente A,Descrizione A,Completato,Supporto
        Contratto B,124,01/01/2023 11:00,01/01/2023 12:30,1:30,Utente B,Descrizione B,In corso,Sviluppo
        """
        df = pd.read_csv(StringIO(data.strip()))
        
        # Rileva il tipo di file
        file_type, confidence, scores = self.detector.detect_file_type(df)
        
        # Verifica che il tipo di file rilevato sia "attivita"
        assert file_type == "attivita"
        assert confidence > 0.8
        assert "attivita" in scores
    
    def test_detect_file_type_teamviewer(self):
        """Test per il metodo detect_file_type con un file di TeamViewer."""
        # Crea un DataFrame di test per un file di TeamViewer
        data = """
        Utente,Computer,Inizio,Fine,Durata,ID,Tipo di sessione,Gruppo,Note
        Utente A,PC-001,01/01/2023 09:00,01/01/2023 10:30,1:30,12345,Controllo remoto,Gruppo A,Nota A
        Utente B,PC-002,01/01/2023 11:00,01/01/2023 12:30,1:30,12346,Controllo remoto,Gruppo B,Nota B
        """
        df = pd.read_csv(StringIO(data.strip()))
        
        # Rileva il tipo di file
        file_type, confidence, scores = self.detector.detect_file_type(df)
        
        # Verifica che il tipo di file rilevato sia "teamviewer"
        assert file_type == "teamviewer"
        assert confidence > 0.8
        assert "teamviewer" in scores
    
    def test_detect_file_type_unknown(self):
        """Test per il metodo detect_file_type con un file sconosciuto."""
        # Crea un DataFrame di test per un file sconosciuto
        data = """
        Colonna1,Colonna2,Colonna3
        Valore1,Valore2,Valore3
        Valore4,Valore5,Valore6
        """
        df = pd.read_csv(StringIO(data.strip()))
        
        # Rileva il tipo di file
        file_type, confidence, scores = self.detector.detect_file_type(df)
        
        # Verifica che il tipo di file rilevato sia "unknown"
        assert file_type == "unknown"
        assert confidence < self.detector.MIN_MATCH_THRESHOLD
    
    def test_detect_file_type_empty(self):
        """Test per il metodo detect_file_type con un DataFrame vuoto."""
        # Crea un DataFrame vuoto
        df = pd.DataFrame()
        
        # Rileva il tipo di file
        file_type, confidence, scores = self.detector.detect_file_type(df)
        
        # Verifica che il tipo di file rilevato sia "unknown"
        assert file_type == "unknown"
        assert confidence == 0.0
        assert scores == {}
    
    def test_get_column_mapping_suggestions(self):
        """Test per il metodo get_column_mapping_suggestions."""
        # Crea un DataFrame di test per un file di attività
        data = """
        Contratto,Id Ticket,Iniziata il,Conclusa il,Durata,Creato da,Descrizione,Stato,Tipo
        Contratto A,123,01/01/2023 09:00,01/01/2023 10:30,1:30,Utente A,Descrizione A,Completato,Supporto
        Contratto B,124,01/01/2023 11:00,01/01/2023 12:30,1:30,Utente B,Descrizione B,In corso,Sviluppo
        """
        df = pd.read_csv(StringIO(data.strip()))
        
        # Ottieni le suggestioni di mappatura
        mapping = self.detector.get_column_mapping_suggestions(df, "attivita")
        
        # Verifica che la mappatura contenga le colonne attese
        assert "Contratto" in mapping
        assert "Id Ticket" in mapping
        assert "Iniziata il" in mapping
        assert "Conclusa il" in mapping
        assert "Durata" in mapping
        
        # Verifica che la mappatura sia corretta
        assert mapping["Contratto"] == "Contratto"
        assert mapping["Id Ticket"] == "Id Ticket"
        assert mapping["Iniziata il"] == "Iniziata il"
        assert mapping["Conclusa il"] == "Conclusa il"
        assert mapping["Durata"] == "Durata"
