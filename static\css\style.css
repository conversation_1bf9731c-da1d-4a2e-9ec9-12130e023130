/* ===== CSS CUSTOM PROPERTIES - CROSS-BROWSER THEME SUPPORT ===== */
:root {
    /* Colori tema principale */
    --browser-theme-color: #0d6efd;
    --accent-primary: #0d6efd;
    --accent-secondary: #6610f2;
    --accent-success: #198754;
    --accent-warning: #ffc107;
    --accent-danger: #dc3545;

    /* Colori di sfondo */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;

    /* Colori testo */
    --text-primary: #212529;
    --text-secondary: #6c757d;

    /* Bordi */
    --border-color: #dee2e6;
}

/* Fallback per Firefox - Simulazione theme-color tramite CSS */
@supports (-moz-appearance:none) {
    /* Firefox non supporta meta theme-color, usiamo CSS per simulare l'effetto */
    html {
        background-color: var(--browser-theme-color);
    }

    body {
        background-color: #f8f9fa;
        margin: 0;
    }
}

/* ===== STILI GENERALI - UI/UX ENHANCED ===== */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Accessibilità - Rispetta le preferenze utente per animazioni */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Focus indicators migliorati per accessibilità */
*:focus {
    outline: 2px solid var(--accent-primary, #0d6efd);
    outline-offset: 2px;
}

/* Skip link per screen readers */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--accent-primary, #0d6efd);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 9999;
}

.skip-link:focus {
    top: 6px;
}

/* Stili per la navbar */
.navbar-brand {
    font-weight: 600;
}

/* ===== STILI CARD ENHANCED ===== */
.card {
    border-radius: 0.75rem;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    border-top-left-radius: 0.75rem !important;
    border-top-right-radius: 0.75rem !important;
    font-weight: 600;
    position: relative;
    background: linear-gradient(135deg, var(--accent-primary, #0d6efd) 0%, var(--accent-secondary, #6610f2) 100%);
    border: none;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: var(--bg-secondary, #f8f9fa);
    border-top: 1px solid var(--border-color, #dee2e6);
    border-bottom-left-radius: 0.75rem !important;
    border-bottom-right-radius: 0.75rem !important;
}

/* Card variants */
.card-interactive {
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.card-interactive:hover {
    transform: translateY(-4px);
    box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.2);
}

.card-interactive:active {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

/* ===== AREA UPLOAD ENHANCED ===== */
.upload-area {
    border: 2px dashed var(--border-color, #ccc);
    border-radius: 1rem;
    padding: 3rem 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,249,250,0.9) 100%);
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(13, 110, 253, 0.1), transparent);
    transition: left 0.5s ease;
}

.upload-area:hover::before {
    left: 100%;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: var(--accent-primary, #0d6efd);
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.05) 0%, rgba(102, 16, 242, 0.05) 100%);
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(13, 110, 253, 0.15);
}

.upload-area.dragover {
    border-style: solid;
    border-width: 3px;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% {
        transform: translateY(-2px) scale(1);
        will-change: transform;
    }
    50% {
        transform: translateY(-2px) scale(1.01);
    }
    100% {
        transform: translateY(-2px) scale(1);
        will-change: auto;
    }
}

.upload-icon {
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.upload-area:hover .upload-icon {
    transform: scale(1.1);
}

.upload-text {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-primary, #212529);
}

.upload-subtext {
    color: var(--text-secondary, #6c757d);
    font-size: 0.9rem;
}

.upload-text {
    font-size: 1.1rem;
    color: #6c757d;
}

/* Stili per le tabelle */
.table-responsive {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    white-space: nowrap;
}

/* Stili per i grafici */
.chart-container {
    height: 300px;
    position: relative;
}

/* Stili per i filtri */
.filter-section {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

/* Stili per i KPI cards */
.kpi-card {
    border-radius: 0.5rem;
    padding: 1.5rem;
    color: white;
    height: 100%;
}

.kpi-card .icon {
    font-size: 2.5rem;
    opacity: 0.5;
}

.kpi-card .value {
    font-size: 2rem;
    font-weight: 700;
}

.kpi-card .label {
    font-size: 1rem;
    opacity: 0.8;
}

/* Stili per i bottoni */
.btn {
    border-radius: 0.4rem;
    font-weight: 500;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
}

.btn-info {
    background-color: #0dcaf0;
    border-color: #0dcaf0;
}

.btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Stili per i form */
.form-control, .form-select {
    border-radius: 0.4rem;
    padding: 0.5rem 0.75rem;
}

.form-label {
    font-weight: 500;
}

/* Stili per le notifiche */
.alert {
    border-radius: 0.4rem;
    border: none;
}

/* Stili per il footer */
footer {
    border-top: 1px solid #e9ecef;
}

/* ===== COMPONENTI UI MODERNI ===== */

/* Progress bars enhanced */
.progress {
    height: 0.75rem;
    border-radius: 0.5rem;
    background-color: var(--bg-tertiary, #e9ecef);
    overflow: hidden;
}

.progress-bar {
    border-radius: 0.5rem;
    transition: width 0.6s ease;
    background: linear-gradient(45deg, var(--accent-primary, #0d6efd), var(--accent-secondary, #6610f2));
}

/* Badges enhanced */
.badge {
    border-radius: 0.5rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
}

/* Tooltips custom */
.tooltip-custom {
    position: relative;
    cursor: help;
}

.tooltip-custom::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-primary, #212529);
    color: var(--text-primary, white);
    padding: 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.tooltip-custom:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Loading states */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid var(--border-color, #dee2e6);
    border-radius: 50%;
    border-top-color: var(--accent-primary, #0d6efd);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-online { background-color: var(--accent-success, #198754); }
.status-offline { background-color: var(--accent-danger, #dc3545); }
.status-warning { background-color: var(--accent-warning, #ffc107); }

/* Breadcrumbs enhanced */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: var(--text-secondary, #6c757d);
}

/* List groups enhanced */
.list-group-item {
    border-radius: 0.5rem !important;
    margin-bottom: 0.25rem;
    border: 1px solid var(--border-color, #dee2e6);
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background-color: var(--bg-secondary, #f8f9fa);
    transform: translateX(4px);
}

/* Stili per dispositivi mobili */
@media (max-width: 768px) {
    .card-title {
        font-size: 1rem;
    }

    .kpi-card .value {
        font-size: 1.5rem;
    }

    .kpi-card .icon {
        font-size: 2rem;
    }

    .upload-area {
        padding: 2rem 1rem;
    }

    .card-interactive:hover {
        transform: none;
    }

    .list-group-item:hover {
        transform: none;
    }
}

/* ===== UTILITY CLASSES ===== */
.wizard-hidden {
    display: none !important;
}

.text-gradient {
    background: linear-gradient(45deg, var(--accent-primary, #0d6efd), var(--accent-secondary, #6610f2));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-soft {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow-medium {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-strong {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(45deg, var(--accent-primary, #0d6efd), var(--accent-secondary, #6610f2)) 1;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--accent-primary, #0d6efd) 0%, var(--accent-secondary, #6610f2) 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--accent-success, #198754) 0%, #20c997 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--accent-warning, #ffc107) 0%, #fd7e14 100%);
}

.bg-gradient-danger {
    background: linear-gradient(135deg, var(--accent-danger, #dc3545) 0%, #e74c3c 100%);
}

