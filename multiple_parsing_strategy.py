#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Multiple Parsing Strategy - Strategie multiple di parsing per massimizzare l'accuratezza.
Combina diversi approcci di riconoscimento e parsing per ottenere i migliori risultati.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple, Any, Optional, Union
from datetime import datetime
import json

# Import dei moduli esistenti
try:
    from file_type_detector import FileTypeDetector
    from enhanced_file_detector import EnhancedFileDetector
    from real_file_analyzer import RealFileAnalyzer
    from content_based_file_analyzer import ContentBasedFileAnalyzer
except ImportError as e:
    logging.warning(f"Alcuni moduli non disponibili: {e}")

# Configurazione logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MultipleParsingStrategy:
    """
    Strategia di parsing multipla che combina diversi approcci per massimizzare
    l'accuratezza del riconoscimento e dell'elaborazione dei file.

    Approcci integrati:
    1. FileTypeDetector - Riconoscimento basico su firme colonne
    2. EnhancedFileDetector - Riconoscimento avanzato con fuzzy matching
    3. RealFileAnalyzer - Analisi completa per file reali
    4. ContentBasedFileAnalyzer - Riconoscimento intelligente basato sul contenuto
    """

    def __init__(self):
        # Inizializza tutti gli analizzatori disponibili
        self.analyzers = {}

        # Carica analizzatori disponibili
        self._initialize_analyzers()

        # Configurazione pesi per il voto finale
        self.ANALYZER_WEIGHTS = {
            "content_based": 0.4,      # Peso maggiore per l'analisi content-based
            "real_file": 0.3,          # Peso alto per l'analizzatore reale
            "enhanced": 0.2,           # Peso medio per l'enhanced detector
            "basic": 0.1               # Peso minore per il detector basico
        }

        # Soglie di confidenza
        self.MIN_CONFIDENCE_THRESHOLD = 0.6
        self.HIGH_CONFIDENCE_THRESHOLD = 0.8

        # Configurazione strategie
        self.PARSING_STRATEGIES = {
            "conservative": {
                "description": "Strategia conservativa - richiede alta confidenza",
                "min_confidence": 0.8,
                "min_analyzers_agreement": 2
            },
            "balanced": {
                "description": "Strategia bilanciata - confidenza media",
                "min_confidence": 0.6,
                "min_analyzers_agreement": 2
            },
            "aggressive": {
                "description": "Strategia aggressiva - accetta confidenza bassa",
                "min_confidence": 0.4,
                "min_analyzers_agreement": 1
            }
        }

    def _initialize_analyzers(self):
        """Inizializza tutti gli analizzatori disponibili."""
        try:
            # Content-based analyzer (priorità alta)
            self.analyzers["content_based"] = ContentBasedFileAnalyzer()
            logger.info("✅ ContentBasedFileAnalyzer caricato")
        except Exception as e:
            logger.warning(f"❌ ContentBasedFileAnalyzer non disponibile: {e}")

        try:
            # Real file analyzer
            self.analyzers["real_file"] = RealFileAnalyzer()
            logger.info("✅ RealFileAnalyzer caricato")
        except Exception as e:
            logger.warning(f"❌ RealFileAnalyzer non disponibile: {e}")

        try:
            # Enhanced detector
            self.analyzers["enhanced"] = EnhancedFileDetector()
            logger.info("✅ EnhancedFileDetector caricato")
        except Exception as e:
            logger.warning(f"❌ EnhancedFileDetector non disponibile: {e}")

        try:
            # Basic detector
            self.analyzers["basic"] = FileTypeDetector()
            logger.info("✅ FileTypeDetector caricato")
        except Exception as e:
            logger.warning(f"❌ FileTypeDetector non disponibile: {e}")

        logger.info(f"🔧 Analizzatori caricati: {len(self.analyzers)}")

    def analyze_with_multiple_strategies(self, df: pd.DataFrame, filename: str = "",
                                       strategy: str = "balanced") -> Dict[str, Any]:
        """
        Analizza un file usando strategie multiple di parsing.

        Args:
            df: DataFrame da analizzare
            filename: Nome del file (opzionale)
            strategy: Strategia da utilizzare ("conservative", "balanced", "aggressive")

        Returns:
            Risultato dell'analisi combinata
        """
        logger.info(f"🔍 Analisi multi-strategia per: {filename}")
        logger.info(f"📊 Strategia: {strategy}")

        if df is None or df.empty:
            return self._create_empty_result("DataFrame vuoto o None")

        # Verifica strategia
        if strategy not in self.PARSING_STRATEGIES:
            strategy = "balanced"
            logger.warning(f"Strategia non valida, uso 'balanced'")

        strategy_config = self.PARSING_STRATEGIES[strategy]

        # Risultato finale
        result = {
            "filename": filename,
            "strategy_used": strategy,
            "strategy_config": strategy_config,
            "total_rows": len(df),
            "total_columns": len(df.columns),
            "analysis_timestamp": datetime.now().isoformat(),
            "analyzers_used": list(self.analyzers.keys()),
            "individual_results": {},
            "consensus_analysis": {},
            "final_result": {
                "detected_type": "unknown",
                "confidence_score": 0.0,
                "method_used": "none"
            },
            "recommendations": [],
            "performance_metrics": {}
        }

        # Esegui analisi con tutti gli analizzatori disponibili
        individual_results = self._run_all_analyzers(df, filename)
        result["individual_results"] = individual_results

        # Analisi del consenso
        consensus = self._analyze_consensus(individual_results)
        result["consensus_analysis"] = consensus

        # Determina risultato finale basato sulla strategia
        final_result = self._determine_final_result(consensus, strategy_config)
        result["final_result"] = final_result

        # Genera raccomandazioni
        recommendations = self._generate_strategy_recommendations(
            individual_results, consensus, final_result, strategy
        )
        result["recommendations"] = recommendations

        # Metriche di performance
        performance = self._calculate_performance_metrics(individual_results, consensus)
        result["performance_metrics"] = performance

        logger.info(f"✅ Analisi completata: {final_result['detected_type']} "
                   f"(confidenza: {final_result['confidence_score']:.3f})")

        return result

    def _run_all_analyzers(self, df: pd.DataFrame, filename: str) -> Dict[str, Any]:
        """Esegue l'analisi con tutti gli analizzatori disponibili."""
        results = {}

        for analyzer_name, analyzer in self.analyzers.items():
            try:
                start_time = datetime.now()

                # Esegui analisi specifica per tipo di analizzatore
                if analyzer_name == "content_based":
                    analysis_result = analyzer.analyze_content(df, filename)
                elif analyzer_name == "real_file":
                    analysis_result = analyzer.analyze_file_content(df, filename)
                elif analyzer_name == "enhanced":
                    # Adatta il risultato per compatibilità
                    detection_result = analyzer.detect_file_type(df)
                    analysis_result = self._adapt_enhanced_result(detection_result, df)
                elif analyzer_name == "basic":
                    # Adatta il risultato per compatibilità
                    detection_result = analyzer.detect_file_type(df.columns.tolist())
                    analysis_result = self._adapt_basic_result(detection_result, df)
                else:
                    continue

                end_time = datetime.now()
                processing_time = (end_time - start_time).total_seconds()

                # Standardizza il risultato
                standardized_result = self._standardize_result(analysis_result, analyzer_name)
                standardized_result["processing_time_seconds"] = processing_time

                results[analyzer_name] = standardized_result

                logger.info(f"✅ {analyzer_name}: {standardized_result['detected_type']} "
                           f"({standardized_result['confidence_score']:.3f})")

            except Exception as e:
                logger.error(f"❌ Errore in {analyzer_name}: {str(e)}")
                results[analyzer_name] = {
                    "detected_type": "error",
                    "confidence_score": 0.0,
                    "error": str(e),
                    "processing_time_seconds": 0.0
                }

        return results

    def _adapt_enhanced_result(self, detection_result: Dict[str, Any], df: pd.DataFrame) -> Dict[str, Any]:
        """Adatta il risultato dell'EnhancedFileDetector al formato standard."""
        return {
            "detected_type": detection_result.get("detected_type", "unknown"),
            "confidence_score": detection_result.get("confidence", 0.0),
            "total_rows": len(df),
            "total_columns": len(df.columns),
            "analysis_details": detection_result
        }

    def _adapt_basic_result(self, detection_result: Dict[str, Any], df: pd.DataFrame) -> Dict[str, Any]:
        """Adatta il risultato del FileTypeDetector al formato standard."""
        return {
            "detected_type": detection_result.get("type", "unknown"),
            "confidence_score": detection_result.get("confidence", 0.0),
            "total_rows": len(df),
            "total_columns": len(df.columns),
            "analysis_details": detection_result
        }

    def _standardize_result(self, result: Dict[str, Any], analyzer_name: str) -> Dict[str, Any]:
        """Standardizza il risultato di un analizzatore."""
        return {
            "analyzer": analyzer_name,
            "detected_type": result.get("detected_type", "unknown"),
            "confidence_score": float(result.get("confidence_score", 0.0)),
            "total_rows": result.get("total_rows", 0),
            "total_columns": result.get("total_columns", 0),
            "raw_result": result
        }

    def _analyze_consensus(self, individual_results: Dict[str, Any]) -> Dict[str, Any]:
        """Analizza il consenso tra i diversi analizzatori."""
        consensus = {
            "total_analyzers": len(individual_results),
            "successful_analyzers": 0,
            "type_votes": {},
            "confidence_scores": {},
            "agreement_level": 0.0,
            "dominant_type": "unknown",
            "weighted_scores": {}
        }

        # Conta analizzatori con successo
        successful_results = {
            name: result for name, result in individual_results.items()
            if result.get("detected_type") not in ["unknown", "error", None]
        }

        consensus["successful_analyzers"] = len(successful_results)

        if not successful_results:
            return consensus

        # Raccoglie voti per tipo
        for analyzer_name, result in successful_results.items():
            detected_type = result["detected_type"]
            confidence = result["confidence_score"]

            # Voto semplice
            if detected_type not in consensus["type_votes"]:
                consensus["type_votes"][detected_type] = []
            consensus["type_votes"][detected_type].append(analyzer_name)

            # Punteggio di confidenza
            if detected_type not in consensus["confidence_scores"]:
                consensus["confidence_scores"][detected_type] = []
            consensus["confidence_scores"][detected_type].append(confidence)

        # Calcola punteggi ponderati
        for file_type, analyzers in consensus["type_votes"].items():
            weighted_score = 0.0
            total_weight = 0.0

            for analyzer_name in analyzers:
                weight = self.ANALYZER_WEIGHTS.get(analyzer_name, 0.1)
                confidence = individual_results[analyzer_name]["confidence_score"]

                weighted_score += weight * confidence
                total_weight += weight

            # Normalizza il punteggio
            if total_weight > 0:
                consensus["weighted_scores"][file_type] = weighted_score / total_weight
            else:
                consensus["weighted_scores"][file_type] = 0.0

        # Determina tipo dominante
        if consensus["weighted_scores"]:
            dominant_type = max(consensus["weighted_scores"], key=consensus["weighted_scores"].get)
            consensus["dominant_type"] = dominant_type

            # Calcola livello di accordo
            total_votes = sum(len(analyzers) for analyzers in consensus["type_votes"].values())
            dominant_votes = len(consensus["type_votes"].get(dominant_type, []))
            consensus["agreement_level"] = dominant_votes / total_votes if total_votes > 0 else 0.0

        return consensus

    def _determine_final_result(self, consensus: Dict[str, Any],
                               strategy_config: Dict[str, Any]) -> Dict[str, Any]:
        """Determina il risultato finale basato sul consenso e la strategia."""
        final_result = {
            "detected_type": "unknown",
            "confidence_score": 0.0,
            "method_used": "none",
            "decision_rationale": []
        }

        min_confidence = strategy_config["min_confidence"]
        min_agreement = strategy_config["min_analyzers_agreement"]

        # Verifica se abbiamo abbastanza analizzatori di successo
        if consensus["successful_analyzers"] < min_agreement:
            final_result["decision_rationale"].append(
                f"Analizzatori insufficienti: {consensus['successful_analyzers']} < {min_agreement}"
            )
            return final_result

        # Verifica tipo dominante
        dominant_type = consensus["dominant_type"]
        if dominant_type == "unknown":
            final_result["decision_rationale"].append("Nessun tipo dominante identificato")
            return final_result

        # Verifica confidenza
        weighted_confidence = consensus["weighted_scores"].get(dominant_type, 0.0)
        if weighted_confidence < min_confidence:
            final_result["decision_rationale"].append(
                f"Confidenza insufficiente: {weighted_confidence:.3f} < {min_confidence}"
            )
            return final_result

        # Verifica livello di accordo
        agreement_level = consensus["agreement_level"]
        min_agreement_ratio = min_agreement / consensus["total_analyzers"]

        if agreement_level < min_agreement_ratio:
            final_result["decision_rationale"].append(
                f"Accordo insufficiente: {agreement_level:.3f} < {min_agreement_ratio:.3f}"
            )
            return final_result

        # Risultato positivo
        final_result["detected_type"] = dominant_type
        final_result["confidence_score"] = weighted_confidence
        final_result["method_used"] = "weighted_consensus"
        final_result["decision_rationale"].append(
            f"Consenso raggiunto: {dominant_type} con confidenza {weighted_confidence:.3f}"
        )

        return final_result

    def _generate_strategy_recommendations(self, individual_results: Dict[str, Any],
                                         consensus: Dict[str, Any], final_result: Dict[str, Any],
                                         strategy: str) -> List[str]:
        """Genera raccomandazioni basate sull'analisi multi-strategia."""
        recommendations = []

        # Raccomandazioni basate sul risultato finale
        if final_result["detected_type"] == "unknown":
            recommendations.append("❌ Nessun tipo rilevato con sufficiente confidenza")
            recommendations.append("💡 Suggerimento: Verificare manualmente il contenuto del file")

            if strategy != "aggressive":
                recommendations.append("🔧 Prova con strategia 'aggressive' per soglie più basse")

        # Raccomandazioni basate sul consenso
        if consensus["successful_analyzers"] < len(individual_results):
            failed_count = len(individual_results) - consensus["successful_analyzers"]
            recommendations.append(f"⚠️ {failed_count} analizzatori hanno fallito")

        if consensus["agreement_level"] < 0.5:
            recommendations.append("🤔 Basso accordo tra analizzatori - risultato incerto")

        # Raccomandazioni specifiche per strategia
        if strategy == "conservative" and final_result["confidence_score"] < 0.9:
            recommendations.append("🔒 Strategia conservativa: considera verifica manuale")

        # Raccomandazioni per migliorare l'accuratezza
        if len(individual_results) < 3:
            recommendations.append("📈 Più analizzatori disponibili potrebbero migliorare l'accuratezza")

        return recommendations

    def _calculate_performance_metrics(self, individual_results: Dict[str, Any],
                                     consensus: Dict[str, Any]) -> Dict[str, Any]:
        """Calcola metriche di performance dell'analisi."""
        metrics = {
            "total_processing_time": 0.0,
            "average_processing_time": 0.0,
            "fastest_analyzer": None,
            "slowest_analyzer": None,
            "success_rate": 0.0,
            "consensus_strength": 0.0
        }

        # Calcola tempi di elaborazione
        processing_times = {}
        for analyzer_name, result in individual_results.items():
            time_taken = result.get("processing_time_seconds", 0.0)
            processing_times[analyzer_name] = time_taken
            metrics["total_processing_time"] += time_taken

        if processing_times:
            metrics["average_processing_time"] = metrics["total_processing_time"] / len(processing_times)
            metrics["fastest_analyzer"] = min(processing_times, key=processing_times.get)
            metrics["slowest_analyzer"] = max(processing_times, key=processing_times.get)

        # Calcola tasso di successo
        successful = consensus["successful_analyzers"]
        total = consensus["total_analyzers"]
        metrics["success_rate"] = (successful / total) * 100 if total > 0 else 0.0

        # Forza del consenso
        metrics["consensus_strength"] = consensus["agreement_level"] * 100

        return metrics

    def _create_empty_result(self, error_message: str) -> Dict[str, Any]:
        """Crea un risultato vuoto con messaggio di errore."""
        return {
            "filename": "",
            "strategy_used": "none",
            "total_rows": 0,
            "total_columns": 0,
            "analysis_timestamp": datetime.now().isoformat(),
            "analyzers_used": [],
            "individual_results": {},
            "consensus_analysis": {},
            "final_result": {
                "detected_type": "unknown",
                "confidence_score": 0.0,
                "method_used": "none"
            },
            "recommendations": [f"❌ Errore: {error_message}"],
            "performance_metrics": {},
            "error": error_message
        }

    def get_available_strategies(self) -> Dict[str, str]:
        """Restituisce le strategie disponibili con descrizioni."""
        return {
            strategy: config["description"]
            for strategy, config in self.PARSING_STRATEGIES.items()
        }

    def get_analyzer_status(self) -> Dict[str, bool]:
        """Restituisce lo stato di disponibilità degli analizzatori."""
        return {
            "content_based": "content_based" in self.analyzers,
            "real_file": "real_file" in self.analyzers,
            "enhanced": "enhanced" in self.analyzers,
            "basic": "basic" in self.analyzers
        }

    def benchmark_analyzers(self, test_datasets: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        Esegue un benchmark degli analizzatori su dataset di test.

        Args:
            test_datasets: Dizionario con nome_tipo -> DataFrame di test

        Returns:
            Risultati del benchmark
        """
        benchmark_results = {
            "test_summary": {
                "total_tests": len(test_datasets),
                "analyzers_tested": len(self.analyzers)
            },
            "analyzer_performance": {},
            "accuracy_by_type": {},
            "overall_accuracy": 0.0
        }

        total_correct = 0
        total_tests = 0

        for expected_type, df in test_datasets.items():
            for analyzer_name, analyzer in self.analyzers.items():
                try:
                    # Esegui test
                    if analyzer_name == "content_based":
                        result = analyzer.analyze_content(df, f"test_{expected_type}.xlsx")
                    elif analyzer_name == "real_file":
                        result = analyzer.analyze_file_content(df, f"test_{expected_type}.xlsx")
                    else:
                        continue  # Skip per ora

                    detected_type = result.get("detected_type", "unknown")
                    is_correct = detected_type == expected_type

                    # Registra risultato
                    if analyzer_name not in benchmark_results["analyzer_performance"]:
                        benchmark_results["analyzer_performance"][analyzer_name] = {
                            "correct": 0,
                            "total": 0,
                            "accuracy": 0.0
                        }

                    benchmark_results["analyzer_performance"][analyzer_name]["total"] += 1
                    if is_correct:
                        benchmark_results["analyzer_performance"][analyzer_name]["correct"] += 1
                        total_correct += 1

                    total_tests += 1

                except Exception as e:
                    logger.error(f"Errore nel benchmark {analyzer_name} su {expected_type}: {e}")

        # Calcola accuratezza
        for analyzer_name, perf in benchmark_results["analyzer_performance"].items():
            if perf["total"] > 0:
                perf["accuracy"] = (perf["correct"] / perf["total"]) * 100

        if total_tests > 0:
            benchmark_results["overall_accuracy"] = (total_correct / total_tests) * 100

        return benchmark_results

# Istanza globale
multiple_parsing_strategy = MultipleParsingStrategy()