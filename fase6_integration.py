#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Integrazione Fase 6 - Funzionalità Agentiche e Avanzate.
Integra il framework avanzato agenti con l'applicazione principale.
"""

import sys
import os
import asyncio
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from flask import Blueprint, request, jsonify, render_template

# Aggiungi il percorso del progetto
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advanced_agent_framework import (
    get_advanced_orchestrator,
    AgentTask,
    AgentType,
    TaskPriority,
    TaskStatus
)

# Blueprint per le API degli agenti
agents_bp = Blueprint('agents', __name__, url_prefix='/api/agents')

class Fase6Integration:
    """Classe per integrazione Fase 6 con app principale."""

    def __init__(self, app=None):
        self.app = app
        self.orchestrator = get_advanced_orchestrator()

        if app:
            self.init_app(app)

    def init_app(self, app):
        """Inizializza l'integrazione con l'app Flask."""
        app.register_blueprint(agents_bp)

        # Aggiungi context processor per template
        @app.context_processor
        def inject_agents_info():
            return {
                'agents_available': len(self.orchestrator.agents),
                'agents_status': 'active'
            }

# API Endpoints per Agenti

@agents_bp.route('/list', methods=['GET'])
def list_agents():
    """Lista tutti gli agenti disponibili."""
    try:
        import asyncio
        orchestrator = get_advanced_orchestrator()

        agents_info = []
        for agent_name, agent in orchestrator.agents.items():
            try:
                health = asyncio.run(agent.health_check())
            except:
                # Fallback se health_check non è disponibile
                health = {
                    'agent_type': agent_name,
                    'is_active': True,
                    'tasks_completed': 0,
                    'tasks_failed': 0
                }

            agent_info = {
                'name': agent_name,
                'type': health.get('agent_type', agent_name),
                'is_active': health.get('is_active', True),
                'capabilities': len(agent.get_capabilities()) if hasattr(agent, 'get_capabilities') else 0,
                'tasks_completed': health.get('tasks_completed', 0),
                'tasks_failed': health.get('tasks_failed', 0)
            }

            # Aggiungi info specifiche per tipo agente
            if agent_name == 'data_cleaning':
                if hasattr(agent, 'get_capabilities'):
                    agent_info['capabilities_list'] = [cap.name for cap in agent.get_capabilities()]
            elif agent_name == 'export_management':
                if hasattr(agent, 'supported_formats'):
                    agent_info['supported_formats'] = agent.supported_formats
                else:
                    agent_info['supported_formats'] = ['xlsx', 'csv', 'json', 'pdf', 'xml']

            agents_info.append(agent_info)

        return jsonify({
            'success': True,
            'agents': agents_info,
            'total_agents': len(agents_info)
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@agents_bp.route('/health', methods=['GET'])
def agents_health():
    """Health check di tutti gli agenti."""
    try:
        import asyncio
        orchestrator = get_advanced_orchestrator()
        health_status = asyncio.run(orchestrator.health_check())

        return jsonify({
            'success': True,
            'health': health_status
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@agents_bp.route('/submit-task', methods=['POST'])
def submit_task():
    """Sottomette un task per esecuzione."""
    try:
        import asyncio
        data = request.get_json()

        # Validazione input
        required_fields = ['agent_type', 'task_data']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'Campo richiesto mancante: {field}'
                }), 400

        # Crea task
        task_id = str(uuid.uuid4())

        # Mappa tipo agente
        agent_type_map = {
            'data_cleaning': AgentType.DATA_CLEANING,
            'export_management': AgentType.EXPORT_MANAGEMENT
        }

        agent_type = agent_type_map.get(data['agent_type'])
        if not agent_type:
            return jsonify({
                'success': False,
                'error': f'Tipo agente non supportato: {data["agent_type"]}'
            }), 400

        # Mappa priorità
        priority_map = {
            'critical': TaskPriority.CRITICAL,
            'high': TaskPriority.HIGH,
            'medium': TaskPriority.MEDIUM,
            'low': TaskPriority.LOW
        }

        priority = priority_map.get(data.get('priority', 'medium'), TaskPriority.MEDIUM)

        # Crea task
        task = AgentTask(
            id=task_id,
            agent_type=agent_type,
            priority=priority,
            data=data['task_data']
        )

        # Sottometti task
        orchestrator = get_advanced_orchestrator()
        submitted_id = asyncio.run(orchestrator.submit_task(task))

        return jsonify({
            'success': True,
            'task_id': submitted_id,
            'status': 'submitted'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@agents_bp.route('/task-status/<task_id>', methods=['GET'])
def get_task_status(task_id):
    """Ottiene lo status di un task."""
    try:
        orchestrator = get_advanced_orchestrator()
        task = orchestrator.get_task_status(task_id)

        if not task:
            return jsonify({
                'success': False,
                'error': 'Task non trovato'
            }), 404

        task_info = {
            'task_id': task.id,
            'agent_type': task.agent_type.value,
            'priority': task.priority.value,
            'status': task.status.value,
            'progress': task.progress,
            'created_at': task.created_at.isoformat(),
            'data': task.data
        }

        if task.result:
            task_info['result'] = task.result

        if task.error:
            task_info['error'] = task.error

        return jsonify({
            'success': True,
            'task': task_info
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@agents_bp.route('/data-cleaning/capabilities', methods=['GET'])
def get_data_cleaning_capabilities():
    """Ottiene le capacità dell'agente di pulizia dati."""
    try:
        orchestrator = get_advanced_orchestrator()

        if 'data_cleaning' not in orchestrator.agents:
            return jsonify({
                'success': False,
                'error': 'Agente pulizia dati non disponibile'
            }), 404

        agent = orchestrator.agents['data_cleaning']

        # Fallback se get_capabilities non è disponibile
        if hasattr(agent, 'get_capabilities'):
            capabilities = agent.get_capabilities()
            caps_info = []
            for cap in capabilities:
                caps_info.append({
                    'name': cap.name,
                    'description': cap.description,
                    'input_schema': cap.input_schema,
                    'output_schema': cap.output_schema,
                    'estimated_time': cap.estimated_time,
                    'resource_requirements': cap.resource_requirements
                })
        else:
            # Capacità di default
            caps_info = [
                {
                    'name': 'missing_value_imputation',
                    'description': 'Imputazione valori mancanti',
                    'input_schema': {'file_id': 'string'},
                    'output_schema': {'cleaned_data': 'object'},
                    'estimated_time': 120,
                    'resource_requirements': 'Medium CPU/Memory'
                },
                {
                    'name': 'outlier_detection',
                    'description': 'Rilevamento outlier',
                    'input_schema': {'file_id': 'string'},
                    'output_schema': {'outliers': 'array'},
                    'estimated_time': 180,
                    'resource_requirements': 'High CPU/Memory'
                },
                {
                    'name': 'data_standardization',
                    'description': 'Standardizzazione dati',
                    'input_schema': {'file_id': 'string'},
                    'output_schema': {'standardized_data': 'object'},
                    'estimated_time': 90,
                    'resource_requirements': 'Low Memory/Medium CPU'
                },
                {
                    'name': 'duplicate_resolution',
                    'description': 'Risoluzione duplicati',
                    'input_schema': {'file_id': 'string'},
                    'output_schema': {'deduplicated_data': 'object'},
                    'estimated_time': 150,
                    'resource_requirements': 'Medium Memory/High CPU'
                }
            ]

        return jsonify({
            'success': True,
            'capabilities': caps_info
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@agents_bp.route('/export/formats', methods=['GET'])
def get_export_formats():
    """Ottiene i formati supportati per l'export."""
    try:
        orchestrator = get_advanced_orchestrator()

        if 'export_management' not in orchestrator.agents:
            return jsonify({
                'success': False,
                'error': 'Agente export non disponibile'
            }), 404

        agent = orchestrator.agents['export_management']

        # Fallback se supported_formats non è disponibile
        if hasattr(agent, 'supported_formats'):
            supported_formats = agent.supported_formats
        else:
            supported_formats = ['xlsx', 'csv', 'json', 'pdf', 'xml']

        return jsonify({
            'success': True,
            'supported_formats': supported_formats,
            'export_types': ['standard', 'filtered', 'report', 'dashboard']
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Funzioni helper per integrazione

async def clean_data_with_agent(file_id: str, operation: str = 'full_cleaning_pipeline', **kwargs) -> Dict[str, Any]:
    """
    Pulisce dati usando l'agente di pulizia.

    Args:
        file_id: ID del file da pulire
        operation: Tipo di operazione di pulizia
        **kwargs: Parametri aggiuntivi per l'operazione

    Returns:
        Risultato dell'operazione di pulizia
    """
    try:
        # Crea task
        task_id = str(uuid.uuid4())
        task_data = {
            'file_id': file_id,
            'operation': operation,
            **kwargs
        }

        task = AgentTask(
            id=task_id,
            agent_type=AgentType.DATA_CLEANING,
            priority=TaskPriority.HIGH,
            data=task_data
        )

        # Esegui task
        orchestrator = get_advanced_orchestrator()
        agent = orchestrator.agents['data_cleaning']
        result = await agent.execute_task(task)

        return {
            'success': task.status == TaskStatus.COMPLETED,
            'task_id': task_id,
            'result': result,
            'error': task.error
        }

    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

async def export_data_with_agent(file_id: str, export_type: str = 'standard', format_type: str = 'xlsx', **kwargs) -> Dict[str, Any]:
    """
    Esporta dati usando l'agente di export.

    Args:
        file_id: ID del file da esportare
        export_type: Tipo di export
        format_type: Formato di export
        **kwargs: Parametri aggiuntivi per l'export

    Returns:
        Risultato dell'operazione di export
    """
    try:
        # Crea task
        task_id = str(uuid.uuid4())
        task_data = {
            'file_id': file_id,
            'export_type': export_type,
            'format': format_type,
            **kwargs
        }

        task = AgentTask(
            id=task_id,
            agent_type=AgentType.EXPORT_MANAGEMENT,
            priority=TaskPriority.MEDIUM,
            data=task_data
        )

        # Esegui task
        orchestrator = get_advanced_orchestrator()
        agent = orchestrator.agents['export_management']
        result = await agent.execute_task(task)

        return {
            'success': task.status == TaskStatus.COMPLETED,
            'task_id': task_id,
            'result': result,
            'error': task.error
        }

    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def get_agents_dashboard_data() -> Dict[str, Any]:
    """
    Ottiene dati per dashboard agenti.

    Returns:
        Dati per dashboard agenti
    """
    try:
        orchestrator = get_advanced_orchestrator()

        # Statistiche generali
        total_agents = len(orchestrator.agents)
        queue_size = len(orchestrator.task_queue)
        running_tasks = len(orchestrator.running_tasks)
        completed_tasks = len(orchestrator.completed_tasks)

        # Statistiche per agente
        agents_stats = {}
        for agent_name, agent in orchestrator.agents.items():
            if hasattr(agent, 'task_history'):
                history = agent.task_history
                agents_stats[agent_name] = {
                    'total_tasks': len(history),
                    'completed': len([t for t in history if t.status == TaskStatus.COMPLETED]),
                    'failed': len([t for t in history if t.status == TaskStatus.FAILED]),
                    'success_rate': 0
                }

                if agents_stats[agent_name]['total_tasks'] > 0:
                    agents_stats[agent_name]['success_rate'] = (
                        agents_stats[agent_name]['completed'] /
                        agents_stats[agent_name]['total_tasks'] * 100
                    )

        return {
            'overview': {
                'total_agents': total_agents,
                'queue_size': queue_size,
                'running_tasks': running_tasks,
                'completed_tasks': completed_tasks
            },
            'agents_stats': agents_stats,
            'system_status': 'healthy' if total_agents > 0 else 'warning'
        }

    except Exception as e:
        return {
            'overview': {
                'total_agents': 0,
                'queue_size': 0,
                'running_tasks': 0,
                'completed_tasks': 0
            },
            'agents_stats': {},
            'system_status': 'error',
            'error': str(e)
        }

# Inizializzazione per app principale
def init_fase6_integration(app):
    """Inizializza l'integrazione Fase 6 con l'app principale."""
    integration = Fase6Integration(app)

    # Aggiungi route per dashboard agenti
    @app.route('/agents-dashboard')
    def agents_dashboard():
        """Dashboard agenti."""
        dashboard_data = get_agents_dashboard_data()
        return render_template('agents_dashboard.html', data=dashboard_data)

    return integration
