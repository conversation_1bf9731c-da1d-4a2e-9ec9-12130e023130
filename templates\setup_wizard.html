<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Wizard - App Roberto</title>
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='favicon.svg') }}">
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/dark-theme.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/wizard.css') }}" rel="stylesheet">

    <!-- Caricamento immediato tema per evitare flash -->
    <script>
        (function() {
            try {
                // Controllo sicuro del localStorage
                let savedTheme = null;
                if (typeof Storage !== 'undefined' && localStorage) {
                    try {
                        savedTheme = localStorage.getItem('app-roberto-theme');
                    } catch (storageError) {
                        console.warn('⚠️ localStorage non accessibile:', storageError);
                    }
                }
                
                if (savedTheme === 'dark') {
                    // Applica attributo data-theme in modo sicuro
                    if (document.documentElement && typeof document.documentElement.setAttribute === 'function') {
                        document.documentElement.setAttribute('data-theme', 'dark');
                    }
                    
                    // Applica classe al body solo se disponibile
                    if (document.body && document.body.classList && typeof document.body.classList.add === 'function') {
                        document.body.classList.add('dark-theme');
                    } else {
                        // Se il body non è ancora disponibile, aspetta che sia pronto
                        if (document.readyState === 'loading') {
                            document.addEventListener('DOMContentLoaded', function() {
                                if (document.body && document.body.classList && typeof document.body.classList.add === 'function') {
                                    document.body.classList.add('dark-theme');
                                }
                            });
                        }
                    }
                }
            } catch (error) {
                console.warn('⚠️ Errore caricamento tema immediato:', error);
                // Fallback silenzioso - non bloccare il caricamento
            }
        })();
    </script>
</head>
<body class="bg-light">
    <!-- Theme Toggle (Fixed Position) -->
    <div class="theme-toggle-fixed">
        <button type="button" id="theme-toggle" class="btn btn-outline-secondary btn-sm" title="Cambia Tema">
            <i class="fas fa-moon" id="theme-icon"></i>
        </button>
    </div>

    <!-- Wizard Container -->
    <div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
        <div class="wizard-container">
            <!-- Wizard Header -->
            <div class="wizard-header text-center mb-4">
                <div class="wizard-logo mb-3">
                    <i class="fas fa-magic fa-3x text-primary"></i>
                </div>
                <h1 class="display-6 fw-bold text-primary">
                    Benvenuto in App Roberto
                </h1>
                <p class="lead text-muted">
                    Configuriamo insieme il tuo sistema di gestione intelligente
                </p>
            </div>

            <!-- Progress Bar -->
            <div class="wizard-progress mb-4">
                <div class="progress wizard-progress-bar">
                    <div class="progress-bar progress-bar-striped progress-bar-animated wizard-progress-value wizard-progress-initial"
                         aria-label="Progresso configurazione"
                         role="progressbar"
                         id="wizard-progress-bar">
                    </div>
                </div>
                <div class="progress-steps mt-2">
                    <div class="step active" data-step="1">
                        <i class="fas fa-play-circle"></i>
                        <span>Inizio</span>
                    </div>
                    <div class="step" data-step="2">
                        <i class="fas fa-upload"></i>
                        <span>Dati</span>
                    </div>
                    <div class="step" data-step="3">
                        <i class="fas fa-cogs"></i>
                        <span>Config</span>
                    </div>
                    <div class="step" data-step="4">
                        <i class="fas fa-users"></i>
                        <span>Team</span>
                    </div>
                    <div class="step" data-step="5">
                        <i class="fas fa-check-circle"></i>
                        <span>Fine</span>
                    </div>
                </div>
            </div>

            <!-- Wizard Content -->
            <div class="wizard-content">
                <!-- Step 1: Welcome -->
                <div class="wizard-step active" id="step-1">
                    <div class="card shadow-lg border-0">
                        <div class="card-body p-5 text-center">
                            <div class="mb-4">
                                <i class="fas fa-rocket fa-4x text-primary mb-3"></i>
                                <h2 class="card-title">Iniziamo il Setup!</h2>
                                <p class="card-text text-muted">
                                    Ti guideremo attraverso 5 semplici passaggi per configurare
                                    il tuo sistema di gestione intelligente.
                                </p>
                            </div>

                            <div class="row text-start">
                                <div class="col-md-6">
                                    <div class="feature-item mb-3">
                                        <i class="fas fa-brain text-success me-2"></i>
                                        <strong>Sistema Intelligente</strong>
                                        <p class="text-muted small mb-0">
                                            AI avanzata per analisi automatiche
                                        </p>
                                    </div>
                                    <div class="feature-item mb-3">
                                        <i class="fas fa-chart-line text-info me-2"></i>
                                        <strong>Dashboard Avanzate</strong>
                                        <p class="text-muted small mb-0">
                                            Grafici interattivi e report intelligenti
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="feature-item mb-3">
                                        <i class="fas fa-robot text-warning me-2"></i>
                                        <strong>Automazione</strong>
                                        <p class="text-muted small mb-0">
                                            Processi automatizzati e ottimizzati
                                        </p>
                                    </div>
                                    <div class="feature-item mb-3">
                                        <i class="fas fa-shield-alt text-danger me-2"></i>
                                        <strong>Sicurezza</strong>
                                        <p class="text-muted small mb-0">
                                            Dati protetti e backup automatici
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <button type="button" class="btn btn-primary btn-lg px-5" onclick="nextStep()">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    Inizia Setup
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Data Upload -->
                <div class="wizard-step" id="step-2">
                    <div class="card shadow-lg border-0">
                        <div class="card-header bg-primary text-white">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-upload me-2"></i>
                                Carica i Tuoi Primi Dati
                            </h3>
                        </div>
                        <div class="card-body p-4">
                            <p class="text-muted mb-4">
                                Carica un file Excel, CSV o JSON per iniziare. Il sistema analizzerà
                                automaticamente la struttura e configurerà tutto per te.
                            </p>

                            <!-- Drag & Drop Area -->
                            <div class="upload-area" id="upload-area">
                                <div class="upload-content">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                    <h4>Trascina qui i tuoi file</h4>
                                    <p class="text-muted">oppure clicca per selezionare</p>
                                    <div class="supported-formats mt-3">
                                        <span class="badge bg-success me-1">Excel</span>
                                        <span class="badge bg-info me-1">CSV</span>
                                        <span class="badge bg-warning me-1">JSON</span>
                                        <span class="badge bg-secondary">TXT</span>
                                    </div>
                                </div>
                                <input type="file" id="file-input" multiple accept=".xlsx,.xls,.csv,.json,.txt" hidden>
                            </div>

                            <!-- File List -->
                            <div class="uploaded-files mt-4 d-none" id="uploaded-files">
                                <h5>File Caricati:</h5>
                                <div class="file-list" id="file-list"></div>
                            </div>

                            <!-- Data Preview -->
                            <div class="data-preview mt-4 d-none" id="data-preview">
                                <h5>Anteprima Dati:</h5>
                                <div class="preview-content" id="preview-content"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Configuration -->
                <div class="wizard-step" id="step-3">
                    <div class="card shadow-lg border-0">
                        <div class="card-header bg-success text-white">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-cogs me-2"></i>
                                Configurazione Automatica
                            </h3>
                        </div>
                        <div class="card-body p-4">
                            <p class="text-muted mb-4">
                                Basandoci sui tuoi dati, abbiamo preparato una configurazione ottimale.
                                Puoi personalizzarla secondo le tue esigenze.
                            </p>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="config-section">
                                        <h5>
                                            <i class="fas fa-chart-bar text-primary me-2"></i>
                                            Analisi Rilevate
                                        </h5>
                                        <div id="detected-analysis" class="analysis-list">
                                            <!-- Popolato dinamicamente -->
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="config-section">
                                        <h5>
                                            <i class="fas fa-robot text-warning me-2"></i>
                                            Automazioni Suggerite
                                        </h5>
                                        <div id="suggested-automations" class="automation-list">
                                            <!-- Popolato dinamicamente -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <h5>
                                    <i class="fas fa-sliders-h text-info me-2"></i>
                                    Configurazioni Personalizzate
                                </h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="form-label">Fuso Orario</label>
                                            <select class="form-select" id="timezone" aria-label="Seleziona fuso orario">
                                                <option value="Europe/Rome" selected>Europa/Roma</option>
                                                <option value="Europe/London">Europa/Londra</option>
                                                <option value="America/New_York">America/New York</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="form-label">Valuta</label>
                                            <select class="form-select" id="currency" aria-label="Seleziona valuta">
                                                <option value="EUR" selected>Euro (€)</option>
                                                <option value="USD">Dollaro ($)</option>
                                                <option value="GBP">Sterlina (£)</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="form-label">Formato Data</label>
                                            <select class="form-select" id="date-format" aria-label="Seleziona formato data">
                                                <option value="DD/MM/YYYY" selected>DD/MM/YYYY</option>
                                                <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                                                <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Team Setup -->
                <div class="wizard-step" id="step-4">
                    <div class="card shadow-lg border-0">
                        <div class="card-header bg-info text-white">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-users me-2"></i>
                                Configurazione Team
                            </h3>
                        </div>
                        <div class="card-body p-4">
                            <p class="text-muted mb-4">
                                Configura il tuo team e i veicoli. Abbiamo rilevato automaticamente
                                alcuni dati dai tuoi file.
                            </p>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="team-section">
                                        <h5>
                                            <i class="fas fa-user-friends text-primary me-2"></i>
                                            Dipendenti Rilevati
                                        </h5>
                                        <div id="detected-employees" class="employee-list">
                                            <!-- Popolato dinamicamente -->
                                        </div>
                                        <button type="button" class="btn btn-outline-primary btn-sm mt-2" onclick="addEmployee()">
                                            <i class="fas fa-plus me-1"></i>
                                            Aggiungi Dipendente
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="vehicle-section">
                                        <h5>
                                            <i class="fas fa-car text-success me-2"></i>
                                            Veicoli Rilevati
                                        </h5>
                                        <div id="detected-vehicles" class="vehicle-list">
                                            <!-- Popolato dinamicamente -->
                                        </div>
                                        <button type="button" class="btn btn-outline-success btn-sm mt-2" onclick="addVehicle()">
                                            <i class="fas fa-plus me-1"></i>
                                            Aggiungi Veicolo
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 5: Completion -->
                <div class="wizard-step" id="step-5">
                    <div class="card shadow-lg border-0">
                        <div class="card-body p-5 text-center">
                            <div class="completion-animation mb-4">
                                <i class="fas fa-check-circle fa-5x text-success mb-3"></i>
                                <h2 class="text-success">Setup Completato!</h2>
                                <p class="lead text-muted">
                                    Il tuo sistema è ora configurato e pronto all'uso.
                                </p>
                            </div>

                            <div class="setup-summary">
                                <h5>Riepilogo Configurazione:</h5>
                                <div class="row text-start">
                                    <div class="col-md-6">
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success me-2"></i><span id="summary-files">0 file caricati</span></li>
                                            <li><i class="fas fa-check text-success me-2"></i><span id="summary-employees">0 dipendenti configurati</span></li>
                                            <li><i class="fas fa-check text-success me-2"></i><span id="summary-vehicles">0 veicoli configurati</span></li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success me-2"></i>Sistema intelligente attivato</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Dashboard configurate</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Automazioni abilitate</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <button type="button" class="btn btn-success btn-lg px-5 me-3" onclick="completeSetup()">
                                    <i class="fas fa-rocket me-2"></i>
                                    Vai alla Dashboard
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="generateReport()">
                                    <i class="fas fa-download me-2"></i>
                                    Scarica Report Setup
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Wizard Navigation -->
            <div class="wizard-navigation mt-4">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="nav-left">
                        <button type="button" class="btn btn-outline-secondary" id="prev-btn" onclick="prevStep()" disabled>
                            <i class="fas fa-arrow-left me-2"></i>
                            Indietro
                        </button>
                    </div>

                    <div class="nav-center">
                        <button type="button" class="btn btn-outline-info btn-sm wizard-hidden" id="restart-btn" onclick="restartWizard()">
                            <i class="fas fa-home me-2"></i>
                            Torna all'inizio
                        </button>
                    </div>

                    <div class="nav-right">
                        <button type="button" class="btn btn-primary" id="next-btn" onclick="nextStep()">
                            Avanti
                            <i class="fas fa-arrow-right ms-2"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/setup-wizard.js') }}"></script>
</body>
</html>
